{"compilerOptions": {"target": "ESNext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "types": ["vite/client", "vitest/globals"], "isolatedModules": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["src"], "exclude": ["node_modules"]}