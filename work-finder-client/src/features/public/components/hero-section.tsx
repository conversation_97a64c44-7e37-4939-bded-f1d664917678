import React from "react";
import { useNavigate } from "react-router-dom";

import {
  HeroSearchForm,
  JobSearchParams,
} from "@/components/ui/search/hero-search-form";
import heroImage from "@/assets/hero_right.png";

interface HeroSectionProps {
  onSearch?: (params: JobSearchParams) => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({ onSearch }) => {
  const navigate = useNavigate();

  // Handle search submission
  const handleSearch = (params: JobSearchParams) => {
    // Build URL and navigate
    const queryString = new URLSearchParams();
    if (params.keywords) queryString.set("q", params.keywords);
    if (params.location) queryString.set("location", params.location);

    navigate(`/jobs?${queryString.toString()}`);

    // Call parent callback
    onSearch?.(params);
  };

  return (
    <section className="relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f8faff] to-[#f0f4ff]">
      {/* Background Shapes */}
      <div className="absolute inset-0">
        {/* Main gradient background shape */}
        <div
          className="absolute -left-[675px] -top-[1202px] w-[3546.63px] h-[3368.71px] opacity-20"
          style={{
            background:
              "linear-gradient(135deg, rgba(25, 103, 210, 0.1) 0%, rgba(25, 103, 210, 0.05) 50%, rgba(25, 103, 210, 0.02) 100%)",
            clipPath: "polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%)",
            transform: "rotate(-15deg)",
          }}
        />
      </div>

      <div className="container mx-auto max-w-7xl relative z-10 px-4 pt-20 md:pt-24 pb-16">
        <div className="grid lg:grid-cols-2 gap-8 items-center min-h-[500px]">
          {/* Left Content */}
          <div className="space-y-6">
            {/* Main Heading */}
            <div className="space-y-4">
              <h1 className="text-4xl md:text-5xl font-medium text-[#202124] leading-tight">
                There Are <span className="text-[#1967d2]">93,178</span>{" "}
                Postings <span className="block">Here For you!</span>
              </h1>
              <p className="text-[#696969] text-lg">
                Find Jobs, Employment & Career Opportunities
              </p>
            </div>

            {/* Search Form */}
            <div className="w-full">
              <HeroSearchForm onSearch={handleSearch} />
            </div>

            {/* Popular Searches */}
            <div className="space-y-2">
              <p className="text-[#202124] text-sm md:text-base">
                <span className="font-medium">Popular Searches : </span>
                <span className="text-[#696969] font-normal">
                  Designer, Developer, Web, IOS, PHP, Senior, Engineer
                </span>
              </p>
            </div>
          </div>

          {/* Right Content - Hero Image with Organized Cards */}
          <div className="relative flex justify-center items-center">
            {/* Hero Image */}
            <div className="w-full max-w-lg relative z-10">
              <img
                src={heroImage}
                alt="Professional team collaboration"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
