import { HeroSection } from "./hero-section";
import { FeaturedJobs } from "@/features/jobs/components/FeaturedJobs";
import { FeaturedCompanies } from "@/features/companies/components/FeaturedCompanies";
import { recentJobs, featuredCompanies } from "@/lib/mock-data";
import type { JobSearchParams } from "@/components/ui/search/hero-search-form";

export function HomePage() {
  const handleSearch = (params: JobSearchParams) => {
    console.log("Search:", params);
    // This will be handled by the navigation in HeroSection
  };

  const handleSaveJob = (jobId: string) => {
    console.log("Save job:", jobId);
    // TODO: Implement save job functionality
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <HeroSection onSearch={handleSearch} />

      {/* Featured Jobs Section */}
      <FeaturedJobs
        jobs={recentJobs}
        onSaveJob={handleSaveJob}
        isLoading={false}
      />

      {/* Featured Companies Section */}
      <FeaturedCompanies companies={featuredCompanies} isLoading={false} />
    </main>
  );
}
