import React, { useState } from "react";
import { Search, MapPin } from "lucide-react";
import { Button } from "@/components/ui/button";

// Define the interface for job search parameters
export interface JobSearchParams {
  keywords: string;
  location: string;
}

interface HeroSearchFormProps {
  onSearch?: (params: JobSearchParams) => void;
  className?: string;
}

export const HeroSearchForm: React.FC<HeroSearchFormProps> = ({
  onSearch,
  className = "",
}) => {
  const [keywords, setKeywords] = useState("");
  const [location, setLocation] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch?.({ keywords, location });
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main container with responsive sizing */}
      <div className="relative bg-white h-[80px] w-full max-w-[600px] rounded-lg shadow-[0px_6px_15px_0px_rgba(64,79,104,0.05)] border border-[#ecedf2]">
        <form onSubmit={handleSubmit} className="h-full flex">
          {/* Keywords section */}
          <div className="flex items-center px-[20px] py-[15px] border-r border-[#ecedf2]">
            <div className="flex items-center w-full">
              <Search className="w-4 h-4 text-[#696969] mr-[15px] flex-shrink-0" />
              <input
                type="text"
                placeholder="Job title, keywords..."
                value={keywords}
                onChange={(e) => setKeywords(e.target.value)}
                className="w-full text-[14px] text-[#696969] placeholder:text-[#696969] bg-transparent border-none outline-none font-normal font-jost"
              />
            </div>
          </div>

          {/* Location section */}
          <div className="flex-1 flex items-center px-[20px] py-[15px]">
            <div className="flex items-center w-full">
              <MapPin className="w-4 h-4 text-[#696969] mr-[15px] flex-shrink-0" />
              <input
                type="text"
                placeholder="City or postcode"
                value={location}
                onChange={(e) => setLocation(e.target.value)}
                className="w-full text-[14px] text-[#696969] placeholder:text-[#696969] bg-transparent border-none outline-none font-normal font-jost"
              />
            </div>
          </div>

          {/* Find Jobs Button */}
          <div className="flex items-center pr-[15px]">
            <Button
              type="submit"
              className="bg-[#1967d2] hover:bg-[#1557b8] text-white h-[50px] w-[120px] rounded-lg text-[14px] font-normal font-jost transition-colors duration-200 border-none shadow-none"
            >
              Find Jobs
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
