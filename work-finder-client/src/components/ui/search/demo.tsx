import React from "react";
import { HeroSearchForm, JobSearchParams } from "./hero-search-form";

export const SearchFormDemo: React.FC = () => {
  const handleSearch = (params: JobSearchParams) => {
    console.log("Search params:", params);
    alert(`Searching for: ${params.keywords} in ${params.location}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-8">
      <div className="max-w-4xl w-full">
        <h1 className="text-3xl font-bold text-center mb-8 text-gray-900">
          Figma Search Form Demo
        </h1>
        <div className="flex justify-center">
          <HeroSearchForm onSearch={handleSearch} />
        </div>
        <div className="mt-8 text-center text-gray-600">
          <p>This search form is designed to match the Figma design exactly:</p>
          <ul className="mt-4 space-y-2">
            <li>• Exact dimensions: 740px × 100px</li>
            <li>• Colors: Border #ECEDF2, Primary #1967D2, Text #696969</li>
            <li>• Font: Jost (15px, normal weight)</li>
            <li>• Shadow: 0px 6px 15px rgba(64,79,104,0.05)</li>
            <li>• Vertical separator at 346px from left</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
