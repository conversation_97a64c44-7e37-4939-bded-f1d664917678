import Axios, { InternalAxiosRequestConfig } from "axios";

// TODO: Add notifications system
// import { useNotifications } from '@/components/ui/notifications';

function authRequestInterceptor(config: InternalAxiosRequestConfig) {
  if (config.headers) {
    config.headers.Accept = "application/json";
  }

  config.withCredentials = true;
  return config;
}

export const api = Axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

api.interceptors.request.use(authRequestInterceptor);
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    const message = error.response?.data?.message || error.message;

    // TODO: Add notification system
    console.error("API Error:", message);

    if (error.response?.status === 401) {
      // Redirect to login on unauthorized
      window.location.href = "/auth/login";
    }

    return Promise.reject(error);
  }
);
