import{u as c,a as n,j as e,L as t,p as s,O as x}from"./index-BOPfWTHD.js";const h=()=>{const{isAuthenticated:a,user:r,logout:i}=c(),l=n(),o=()=>{i(),l(s.home.getHref())};return e.jsx("header",{className:"bg-white shadow-sm border-b",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsxs("div",{className:"flex justify-between items-center h-16",children:[e.jsxs(t,{to:s.home.getHref(),className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-lg",children:"W"})}),e.jsx("span",{className:"text-xl font-bold text-gray-900",children:"WorkFinder"})]}),e.jsxs("nav",{className:"hidden md:flex items-center space-x-8",children:[e.jsx(t,{to:s.jobs.root.getHref(),className:"text-gray-700 hover:text-blue-600 transition-colors font-medium",children:"Find Jobs"}),e.jsx(t,{to:s.companies.root.getHref(),className:"text-gray-700 hover:text-blue-600 transition-colors font-medium",children:"Companies"}),e.jsx(t,{to:s.about.getHref(),className:"text-gray-700 hover:text-blue-600 transition-colors font-medium",children:"About"}),e.jsx(t,{to:s.contact.getHref(),className:"text-gray-700 hover:text-blue-600 transition-colors font-medium",children:"Contact"})]}),e.jsx("div",{className:"flex items-center",children:a?e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-gray-700 font-medium",children:["Welcome, ",r?.name||r?.email]}),e.jsxs("div",{className:"relative group",children:[e.jsx("button",{className:"flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors",children:e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center",children:e.jsx("span",{className:"text-white text-sm font-medium",children:r?.name?.charAt(0)||r?.email?.charAt(0)||"U"})})}),e.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:e.jsxs("div",{className:"py-1",children:[e.jsx(t,{to:s.app.profile.getHref(),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Complete Profile"}),e.jsx(t,{to:s.app.applications.getHref(),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"My Applications"}),e.jsx(t,{to:s.app.savedJobs.getHref(),className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:"Saved Jobs"}),e.jsx("div",{className:"border-t border-gray-100"}),e.jsx("button",{onClick:o,className:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100",children:"Sign Out"})]})})]})]}):e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(t,{to:s.auth.login.getHref(),className:"text-gray-700 hover:text-blue-600 transition-colors font-medium",children:"Sign In"}),e.jsx(t,{to:s.auth.register.getHref(),className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium",children:"Sign Up"})]})})]})})})};function d(){return e.jsx("footer",{className:"bg-gray-900 text-white",children:e.jsxs("div",{className:"container mx-auto px-4 py-12",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-lg",children:"W"})}),e.jsx("span",{className:"text-xl font-bold",children:"WorkFinder"})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Find your dream job and connect with top employers. Your next career move starts here."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"For Job Seekers"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(t,{to:s.jobs.root.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Browse Jobs"})}),e.jsx("li",{children:e.jsx(t,{to:s.companies.root.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Browse Companies"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Career Advice"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Resume Builder"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Salary Guide"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"For Employers"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Post a Job"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Browse Resumes"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Recruiter Tools"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Pricing"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Enterprise Solutions"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(t,{to:s.about.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"About Us"})}),e.jsx("li",{children:e.jsx(t,{to:s.contact.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-12 pt-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"© 2024 WorkFinder. All rights reserved."}),e.jsxs("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Privacy"}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Terms"}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Cookies"})]})]})})]})})}const j=()=>e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(h,{}),e.jsx("main",{className:"flex-1",children:e.jsx(x,{})}),e.jsx(d,{})]});export{j as default};
