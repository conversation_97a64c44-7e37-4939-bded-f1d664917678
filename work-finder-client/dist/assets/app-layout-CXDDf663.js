import{d as y,b as N,p as s,j as e,e as f,L as l,u as b,a as w,r as h,B as C,H as k,S as E,f as H,g as S,O as U}from"./index-Bxm2R2OW.js";import{u as p}from"./useTranslation-DLY10C-4.js";import{C as A}from"./chevron-down-Bu8lQHGF.js";import{X as O}from"./x-Ci9JSY07.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]],M=y("log-out",B);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const z=[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]],P=y("menu",z);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]],L=y("user",R),I="/assets/icon-DCka_AoB.svg",W=({className:t})=>{const n=N(),{t:c}=p(),r=[{name:c("header.navigation.findJobs"),href:s.jobs.root.getHref(),isActive:n.pathname.startsWith(s.jobs.root.getHref())},{name:c("header.navigation.employers"),href:s.companies.root.getHref(),isActive:n.pathname.startsWith(s.companies.root.getHref())},{name:c("header.navigation.candidates"),href:"#",isActive:!1}];return e.jsx("nav",{className:f("flex items-center space-x-8",t),role:"navigation",children:r.map(i=>e.jsx(l,{to:i.href,className:f("relative text-[15px] font-medium transition-all duration-200 hover:text-[#1967d2] py-2",i.isActive?"text-[#1967d2] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#1967d2] after:rounded-full":"text-[#202124] hover:after:absolute hover:after:bottom-0 hover:after:left-0 hover:after:right-0 hover:after:h-0.5 hover:after:bg-[#1967d2]/30 hover:after:rounded-full"),"aria-current":i.isActive?"page":void 0,children:i.name},i.name))})},F=()=>{const t=N(),{t:n}=p();return[{name:n("header.navigation.findJobs"),href:s.jobs.root.getHref(),isActive:t.pathname.startsWith(s.jobs.root.getHref())},{name:n("header.navigation.employers"),href:s.companies.root.getHref(),isActive:t.pathname.startsWith(s.companies.root.getHref())},{name:n("header.navigation.candidates"),href:"#",isActive:!1}]},J=()=>{const{user:t,logout:n}=b(),c=w(),{t:r}=p(),[i,o]=h.useState(!1),d=h.useRef(null),m=async()=>{try{await n(),o(!1),c(s.home.getHref())}catch(a){console.error("Logout failed:",a)}};return h.useEffect(()=>{const a=x=>{d.current&&!d.current.contains(x.target)&&o(!1)};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[]),e.jsxs("div",{className:"relative",ref:d,children:[e.jsxs("button",{onClick:()=>o(!i),className:"flex items-center space-x-2 text-gray-700 hover:text-[#1967d2] transition-colors p-2 rounded-lg hover:bg-gray-50","aria-expanded":i,"aria-haspopup":"true",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-[#1967d2] to-[#1557b8] rounded-full flex items-center justify-center ring-2 ring-white shadow-sm",children:t?.avatar?e.jsx("img",{src:t.avatar,alt:t.name||t.email,className:"w-full h-full rounded-full object-cover"}):e.jsx("span",{className:"text-white text-sm font-medium",children:t?.name?.charAt(0)?.toUpperCase()||t?.email?.charAt(0)?.toUpperCase()||"U"})}),e.jsxs("div",{className:"hidden md:block text-left",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:t?.name||"User"}),e.jsx("div",{className:"text-xs text-gray-500 capitalize",children:t?.role||"Member"})]}),e.jsx(A,{className:f("w-4 h-4 transition-transform duration-200",i&&"rotate-180")})]}),i&&e.jsxs("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg border border-gray-100 z-50 animate-in slide-in-from-top-2 duration-200",children:[e.jsx("div",{className:"p-3 border-b border-gray-100",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-[#1967d2] to-[#1557b8] rounded-full flex items-center justify-center",children:t?.avatar?e.jsx("img",{src:t.avatar,alt:t.name||t.email,className:"w-full h-full rounded-full object-cover"}):e.jsx("span",{className:"text-white text-sm font-medium",children:t?.name?.charAt(0)?.toUpperCase()||t?.email?.charAt(0)?.toUpperCase()||"U"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 truncate",children:t?.name||"User"}),e.jsx("div",{className:"text-xs text-gray-500 truncate",children:t?.email})]})]})}),e.jsxs("div",{className:"py-2",children:[e.jsxs(l,{to:s.app.profile.getHref(),className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors",onClick:()=>o(!1),children:[e.jsx(L,{className:"w-4 h-4 mr-3 text-gray-400"}),r("header.userMenu.completeProfile")]}),e.jsxs(l,{to:s.app.applications.getHref(),className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors",onClick:()=>o(!1),children:[e.jsx(C,{className:"w-4 h-4 mr-3 text-gray-400"}),r("header.userMenu.myApplications")]}),e.jsxs(l,{to:s.app.savedJobs.getHref(),className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors",onClick:()=>o(!1),children:[e.jsx(k,{className:"w-4 h-4 mr-3 text-gray-400"}),r("header.userMenu.savedJobs")]}),e.jsxs(l,{to:"#",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors",onClick:()=>o(!1),children:[e.jsx(E,{className:"w-4 h-4 mr-3 text-gray-400"}),r("header.userMenu.settings")]})]}),e.jsx("div",{className:"border-t border-gray-100",children:e.jsxs("button",{onClick:m,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[e.jsx(M,{className:"w-4 h-4 mr-3"}),r("header.userMenu.signOut")]})})]})]})},_=({navigationItems:t,onClose:n})=>{const{isAuthenticated:c,user:r,logout:i}=b(),o=w(),{t:d}=p(),m=async()=>{try{await i(),n(),o(s.home.getHref())}catch(a){console.error("Logout failed:",a)}};return e.jsx("div",{className:"lg:hidden border-t border-gray-100 py-4 animate-in slide-in-from-top-2 duration-200",children:e.jsxs("nav",{className:"flex flex-col space-y-2",role:"navigation",children:[t.map(a=>e.jsx(l,{to:a.href,className:f("text-[15px] font-medium transition-all duration-200 px-4 py-3 rounded-lg mx-2",a.isActive?"text-[#1967d2] bg-[#1967d2]/10 font-semibold":"text-[#202124] hover:text-[#1967d2] hover:bg-gray-50"),onClick:n,"aria-current":a.isActive?"page":void 0,children:a.name},a.name)),e.jsx(l,{to:"#",className:"text-[#1967d2] text-[15px] font-medium px-4 py-3 rounded-lg mx-2 hover:bg-[#1967d2]/10 transition-colors md:hidden",onClick:n,children:d("header.actions.uploadCV")}),c?e.jsxs("div",{className:"px-2 pt-4 border-t border-gray-100 mt-4 md:hidden",children:[e.jsxs("div",{className:"flex items-center space-x-3 px-4 py-3 bg-gray-50 rounded-lg mb-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-[#1967d2] to-[#1557b8] rounded-full flex items-center justify-center",children:r?.avatar?e.jsx("img",{src:r.avatar,alt:r.name||r.email,className:"w-full h-full rounded-full object-cover"}):e.jsx("span",{className:"text-white text-sm font-medium",children:r?.name?.charAt(0)?.toUpperCase()||r?.email?.charAt(0)?.toUpperCase()||"U"})}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900 truncate",children:r?.name||"User"}),e.jsx("div",{className:"text-xs text-gray-500 truncate",children:r?.email})]})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs(l,{to:s.app.profile.getHref(),className:"flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors",onClick:n,children:[e.jsx(L,{className:"w-4 h-4 mr-3 text-gray-400"}),d("header.userMenu.completeProfile")]}),e.jsxs(l,{to:s.app.applications.getHref(),className:"flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors",onClick:n,children:[e.jsx(C,{className:"w-4 h-4 mr-3 text-gray-400"}),d("header.userMenu.myApplications")]}),e.jsxs(l,{to:s.app.savedJobs.getHref(),className:"flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors",onClick:n,children:[e.jsx(k,{className:"w-4 h-4 mr-3 text-gray-400"}),d("header.userMenu.savedJobs")]}),e.jsxs("button",{onClick:m,className:"flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 rounded-lg transition-colors",children:[e.jsx(M,{className:"w-4 h-4 mr-3"}),d("header.userMenu.signOut")]})]})]}):e.jsx("div",{className:"flex flex-col space-y-3 px-2 pt-4 border-t border-gray-100 mt-4 md:hidden",children:e.jsx(H,{variant:"outline",className:"bg-[#1967d2]/7 text-[#1967d2] border-[#1967d2]/20 hover:bg-[#1967d2]/10 justify-center h-11 text-[15px] font-medium rounded-lg transition-all duration-200",onClick:()=>{o(s.auth.login.getHref()),n()},children:d("header.actions.loginRegister")})})]})})},j={en:"EN",vi:"VI"},V=()=>{const{i18n:t,t:n}=p(),[c,r]=h.useState(!1),i=h.useRef(null),o=t.language,d=j[o]||j.en;h.useEffect(()=>{const a=x=>{i.current&&!i.current.contains(x.target)&&r(!1)};return document.addEventListener("mousedown",a),()=>document.removeEventListener("mousedown",a)},[]);const m=a=>{t.changeLanguage(a),r(!1)};return e.jsxs("div",{className:"relative",ref:i,children:[e.jsxs("button",{onClick:()=>r(!c),className:"flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-[#1967d2] transition-colors rounded-lg hover:bg-gray-50","aria-expanded":c,"aria-haspopup":"true","aria-label":n("header.language.switchLanguage"),children:[e.jsx("span",{className:"text-sm font-semibold text-gray-600",children:d}),e.jsx(A,{className:f("w-4 h-4 transition-transform duration-200",c&&"rotate-180")})]}),c&&e.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 z-50 animate-in slide-in-from-top-2 duration-200",children:e.jsx("div",{className:"py-2",children:Object.entries(S).map(([a,x])=>{const v=a,g=j[v],u=o===v;return e.jsxs("button",{onClick:()=>m(v),className:f("flex items-center w-full px-4 py-2 text-sm transition-colors",u?"text-[#1967d2] bg-[#1967d2]/10 font-medium":"text-gray-700 hover:bg-gray-50"),children:[e.jsx("span",{className:"text-sm font-semibold text-gray-600 w-6",children:g}),e.jsx("span",{className:"ml-3",children:x}),u&&e.jsx("div",{className:"ml-auto w-2 h-2 bg-[#1967d2] rounded-full"})]},a)})})})]})},T=()=>{const{isAuthenticated:t}=b(),n=w(),c=N(),{t:r}=p(),[i,o]=h.useState(!1),[d,m]=h.useState(!1),a=F(),x=c.pathname==="/"||c.pathname===s.home.getHref();h.useEffect(()=>{const g=()=>{const u=window.scrollY;m(u>50)};if(x)return window.addEventListener("scroll",g),()=>window.removeEventListener("scroll",g)},[x]);const v=()=>{const g="transition-all duration-300";if(x){const u=`${g} fixed top-0 left-0 right-0 z-50`;return d?`${u} bg-white/95 backdrop-blur-md border-b border-gray-100 shadow-sm`:`${u} bg-white md:bg-transparent border-b md:border-transparent border-gray-100`}else return`${g} sticky top-0 z-50 bg-white border-b border-gray-100`};return e.jsx("header",{className:v(),children:e.jsxs("div",{className:"container mx-auto",children:[e.jsxs("div",{className:"flex justify-between items-center h-[80px]",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(l,{to:s.home.getHref(),className:"flex items-center hover:opacity-80 transition-opacity","aria-label":"WorkFinder Home",children:e.jsx("img",{src:I,alt:"WorkFinder",className:"h-12 w-auto"})}),e.jsx(W,{className:"hidden lg:flex ml-8"})]}),e.jsxs("div",{className:"flex items-center space-x-6",children:[e.jsx(l,{to:"#",className:"hidden md:block text-[#1967d2] text-[15px] font-normal hover:underline transition-all",children:r("header.actions.uploadCV")}),e.jsx(V,{}),t?e.jsx(J,{}):e.jsx(H,{variant:"outline",className:"hidden md:flex bg-[#1967d2]/7 text-[#1967d2] border-[#1967d2]/20 hover:bg-[#1967d2]/10 px-6 py-2 h-10 text-[15px] font-medium rounded-lg transition-all duration-200",onClick:()=>n(s.auth.login.getHref()),children:r("header.actions.loginRegister")}),e.jsx("button",{className:"lg:hidden p-2 text-gray-600 hover:text-[#1967d2] transition-colors",onClick:()=>o(!i),children:i?e.jsx(O,{className:"w-6 h-6"}):e.jsx(P,{className:"w-6 h-6"})})]})]}),i&&e.jsx(_,{navigationItems:a,onClose:()=>o(!1)})]})})};function $(){return e.jsx("footer",{className:"bg-gray-900 text-white",children:e.jsxs("div",{className:"container mx-auto px-4 py-12",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:e.jsx("span",{className:"text-white font-bold text-lg",children:"W"})}),e.jsx("span",{className:"text-xl font-bold",children:"WorkFinder"})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Find your dream job and connect with top employers. Your next career move starts here."}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})})}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"For Job Seekers"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(l,{to:s.jobs.root.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Browse Jobs"})}),e.jsx("li",{children:e.jsx(l,{to:s.companies.root.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Browse Companies"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Career Advice"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Resume Builder"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Salary Guide"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"For Employers"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Post a Job"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Browse Resumes"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Recruiter Tools"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Pricing"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Enterprise Solutions"})})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold mb-4",children:"Company"}),e.jsxs("ul",{className:"space-y-2 text-sm",children:[e.jsx("li",{children:e.jsx(l,{to:s.about.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"About Us"})}),e.jsx("li",{children:e.jsx(l,{to:s.contact.getHref(),className:"text-gray-400 hover:text-white transition-colors",children:"Contact"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Terms of Service"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:"Help Center"})})]})]})]}),e.jsx("div",{className:"border-t border-gray-800 mt-12 pt-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"© 2024 WorkFinder. All rights reserved."}),e.jsxs("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Privacy"}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Terms"}),e.jsx("a",{href:"#",className:"text-gray-400 hover:text-white text-sm transition-colors",children:"Cookies"})]})]})})]})})}const q=()=>e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(T,{}),e.jsx("main",{className:"flex-1",children:e.jsx(U,{})}),e.jsx($,{})]});export{q as default};
