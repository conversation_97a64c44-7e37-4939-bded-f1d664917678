import{j as e,e as t,a1 as o,a2 as d}from"./index-Bxm2R2OW.js";function l({className:r,...a}){return e.jsx("div",{"data-slot":"card",className:t("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...a})}function g({className:r,...a}){return e.jsx("div",{"data-slot":"card-header",className:t("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function v({className:r,...a}){return e.jsx("div",{"data-slot":"card-title",className:t("leading-none font-semibold",r),...a})}function b({className:r,...a}){return e.jsx("div",{"data-slot":"card-content",className:t("px-6",r),...a})}const c=d("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function f({className:r,variant:a,asChild:s=!1,...n}){const i=s?o:"span";return e.jsx(i,{"data-slot":"badge",className:t(c({variant:a}),r),...n})}export{f as B,l as C,b as a,g as b,v as c};
