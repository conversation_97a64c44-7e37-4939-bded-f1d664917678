import{r as d,I as A,h as P,i as k,k as F}from"./index-Bxm2R2OW.js";const M=(e,n,t,a)=>{const o=[t,{code:n,...a||{}}];if(e?.services?.logger?.forward)return e.services.logger.forward(o,"warn","react-i18next::",!0);N(o[0])&&(o[0]=`react-i18next:: ${o[0]}`),e?.services?.logger?.warn?e.services.logger.warn(...o):console?.warn&&console.warn(...o)},R={},x=(e,n,t,a)=>{N(t)&&R[t]||(N(t)&&(R[t]=new Date),M(e,n,t,a))},z=(e,n)=>()=>{if(e.isInitialized)n();else{const t=()=>{setTimeout(()=>{e.off("initialized",t)},0),n()};e.on("initialized",t)}},E=(e,n,t)=>{e.loadNamespaces(n,z(e,t))},v=(e,n,t,a)=>{if(N(t)&&(t=[t]),e.options.preload&&e.options.preload.indexOf(n)>-1)return E(e,t,a);t.forEach(o=>{e.options.ns.indexOf(o)<0&&e.options.ns.push(o)}),e.loadLanguages(n,z(e,a))},_=(e,n,t={})=>!n.languages||!n.languages.length?(x(n,"NO_LANGUAGES","i18n.languages were undefined or empty",{languages:n.languages}),!0):n.hasLoadedNamespace(e,{lng:t.lng,precheck:(a,o)=>{if(t.bindI18n&&t.bindI18n.indexOf("languageChanging")>-1&&a.services.backendConnector.backend&&a.isLanguageChangingTo&&!o(a.isLanguageChangingTo,e))return!1}}),N=e=>typeof e=="string",j=e=>typeof e=="object"&&e!==null,G=(e,n)=>{const t=d.useRef();return d.useEffect(()=>{t.current=e},[e,n]),t.current},L=(e,n,t,a)=>e.getFixedT(n,t,a),$=(e,n,t,a)=>d.useCallback(L(e,n,t,a),[e,n,t,a]),J=(e,n={})=>{const{i18n:t}=n,{i18n:a,defaultNS:o}=d.useContext(A)||{},s=t||a||P();if(s&&!s.reportNamespaces&&(s.reportNamespaces=new k),!s){x(s,"NO_I18NEXT_INSTANCE","useTranslation: You will need to pass in an i18next instance by using initReactI18next");const i=(l,u)=>N(u)?u:j(u)&&N(u.defaultValue)?u.defaultValue:Array.isArray(l)?l[l.length-1]:l,c=[i,{},!1];return c.t=i,c.i18n={},c.ready=!1,c}s.options.react?.wait&&x(s,"DEPRECATED_OPTION","useTranslation: It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const w={...F(),...s.options.react,...n},{useSuspense:S,keyPrefix:p}=w;let r=o||s.options?.defaultNS;r=N(r)?[r]:r||["translation"],s.reportNamespaces.addUsedNamespaces?.(r);const f=(s.isInitialized||s.initializedStoreOnce)&&r.every(i=>_(i,s,w)),O=$(s,n.lng||null,w.nsMode==="fallback"?r:r[0],p),b=()=>O,y=()=>L(s,n.lng||null,w.nsMode==="fallback"?r:r[0],p),[C,m]=d.useState(b);let T=r.join();n.lng&&(T=`${n.lng}${T}`);const I=G(T),g=d.useRef(!0);d.useEffect(()=>{const{bindI18n:i,bindI18nStore:c}=w;g.current=!0,!f&&!S&&(n.lng?v(s,n.lng,r,()=>{g.current&&m(y)}):E(s,r,()=>{g.current&&m(y)})),f&&I&&I!==T&&g.current&&m(y);const l=()=>{g.current&&m(y)};return i&&s?.on(i,l),c&&s?.store.on(c,l),()=>{g.current=!1,s&&i&&i?.split(" ").forEach(u=>s.off(u,l)),c&&s&&c.split(" ").forEach(u=>s.store.off(u,l))}},[s,T]),d.useEffect(()=>{g.current&&f&&m(b)},[s,p,f]);const h=[C,s,f];if(h.t=C,h.i18n=s,h.ready=f,f||!f&&!S)return h;throw new Promise(i=>{n.lng?v(s,n.lng,r,()=>i()):E(s,r,()=>i())})};export{J as u};
