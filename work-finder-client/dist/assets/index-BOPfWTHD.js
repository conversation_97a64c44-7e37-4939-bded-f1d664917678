const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/register-eb8iiTDM.js","assets/routes-CUJ3oYE0.js","assets/login-Dk40jRo6.js","assets/landing-DWY5xz1B.js","assets/mock-data-CbJZR1PP.js","assets/search-C4Jn7o1m.js","assets/map-pin-BR21nmoc.js","assets/trending-up-B7H7FfFe.js","assets/FeaturedJobs-BwajVF4Z.js","assets/badge-Bzyz8_QN.js","assets/bookmark-DWvq3fkz.js","assets/dollar-sign-C4uAz_6M.js","assets/formatDistanceToNow-Bm5-CVp4.js","assets/users-DpHMEXct.js","assets/FeaturedCompanies-C1dOrvM4.js","assets/jobs-Dhvce3fG.js","assets/list-B10iYFZg.js","assets/job-detail-BoHAZB1Q.js","assets/arrow-left-BaQg7S-i.js","assets/share-2-B0mcIQr4.js","assets/calendar-jrt4hqPn.js","assets/circle-check-big-CBdbYUSk.js","assets/external-link-CApOk594.js","assets/companies-DaklL1th.js","assets/company-detail-tIb1biaf.js","assets/about-Bl1BGm1_.js","assets/placeholder-page-xL1iW2DD.js","assets/contact-wFkBKXle.js","assets/privacy-CXKLxjwX.js","assets/terms-DiAt30ct.js","assets/applications-DmdnGKQQ.js","assets/trash-2-DiK6xcQR.js","assets/saved-jobs-CxlyyZWr.js"])))=>i.map(i=>d[i]);
function bw(t,r){for(var o=0;o<r.length;o++){const a=r[o];if(typeof a!="string"&&!Array.isArray(a)){for(const s in a)if(s!=="default"&&!(s in t)){const u=Object.getOwnPropertyDescriptor(a,s);u&&Object.defineProperty(t,s,u.get?u:{enumerable:!0,get:()=>a[s]})}}}return Object.freeze(Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))a(s);new MutationObserver(s=>{for(const u of s)if(u.type==="childList")for(const d of u.addedNodes)d.tagName==="LINK"&&d.rel==="modulepreload"&&a(d)}).observe(document,{childList:!0,subtree:!0});function o(s){const u={};return s.integrity&&(u.integrity=s.integrity),s.referrerPolicy&&(u.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?u.credentials="include":s.crossOrigin==="anonymous"?u.credentials="omit":u.credentials="same-origin",u}function a(s){if(s.ep)return;s.ep=!0;const u=o(s);fetch(s.href,u)}})();function tm(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var Bu={exports:{}},Di={},Vu={exports:{}},De={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jh;function Mw(){if(jh)return De;jh=1;var t=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),d=Symbol.for("react.context"),h=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),m=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),w=Symbol.iterator;function x(T){return T===null||typeof T!="object"?null:(T=w&&T[w]||T["@@iterator"],typeof T=="function"?T:null)}var E={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},k=Object.assign,S={};function R(T,B,ne){this.props=T,this.context=B,this.refs=S,this.updater=ne||E}R.prototype.isReactComponent={},R.prototype.setState=function(T,B){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,B,"setState")},R.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function P(){}P.prototype=R.prototype;function j(T,B,ne){this.props=T,this.context=B,this.refs=S,this.updater=ne||E}var L=j.prototype=new P;L.constructor=j,k(L,R.prototype),L.isPureReactComponent=!0;var U=Array.isArray,H=Object.prototype.hasOwnProperty,_={current:null},K={key:!0,ref:!0,__self:!0,__source:!0};function V(T,B,ne){var ue,be={},Re=null,ie=null;if(B!=null)for(ue in B.ref!==void 0&&(ie=B.ref),B.key!==void 0&&(Re=""+B.key),B)H.call(B,ue)&&!K.hasOwnProperty(ue)&&(be[ue]=B[ue]);var Oe=arguments.length-2;if(Oe===1)be.children=ne;else if(1<Oe){for(var je=Array(Oe),Ae=0;Ae<Oe;Ae++)je[Ae]=arguments[Ae+2];be.children=je}if(T&&T.defaultProps)for(ue in Oe=T.defaultProps,Oe)be[ue]===void 0&&(be[ue]=Oe[ue]);return{$$typeof:t,type:T,key:Re,ref:ie,props:be,_owner:_.current}}function te(T,B){return{$$typeof:t,type:T.type,key:B,ref:T.ref,props:T.props,_owner:T._owner}}function ve(T){return typeof T=="object"&&T!==null&&T.$$typeof===t}function Te(T){var B={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(ne){return B[ne]})}var _e=/\/+/g;function oe(T,B){return typeof T=="object"&&T!==null&&T.key!=null?Te(""+T.key):B.toString(36)}function ae(T,B,ne,ue,be){var Re=typeof T;(Re==="undefined"||Re==="boolean")&&(T=null);var ie=!1;if(T===null)ie=!0;else switch(Re){case"string":case"number":ie=!0;break;case"object":switch(T.$$typeof){case t:case r:ie=!0}}if(ie)return ie=T,be=be(ie),T=ue===""?"."+oe(ie,0):ue,U(be)?(ne="",T!=null&&(ne=T.replace(_e,"$&/")+"/"),ae(be,B,ne,"",function(Ae){return Ae})):be!=null&&(ve(be)&&(be=te(be,ne+(!be.key||ie&&ie.key===be.key?"":(""+be.key).replace(_e,"$&/")+"/")+T)),B.push(be)),1;if(ie=0,ue=ue===""?".":ue+":",U(T))for(var Oe=0;Oe<T.length;Oe++){Re=T[Oe];var je=ue+oe(Re,Oe);ie+=ae(Re,B,ne,je,be)}else if(je=x(T),typeof je=="function")for(T=je.call(T),Oe=0;!(Re=T.next()).done;)Re=Re.value,je=ue+oe(Re,Oe++),ie+=ae(Re,B,ne,je,be);else if(Re==="object")throw B=String(T),Error("Objects are not valid as a React child (found: "+(B==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":B)+"). If you meant to render a collection of children, use an array instead.");return ie}function Pe(T,B,ne){if(T==null)return T;var ue=[],be=0;return ae(T,ue,"","",function(Re){return B.call(ne,Re,be++)}),ue}function Z(T){if(T._status===-1){var B=T._result;B=B(),B.then(function(ne){(T._status===0||T._status===-1)&&(T._status=1,T._result=ne)},function(ne){(T._status===0||T._status===-1)&&(T._status=2,T._result=ne)}),T._status===-1&&(T._status=0,T._result=B)}if(T._status===1)return T._result.default;throw T._result}var se={current:null},N={transition:null},W={ReactCurrentDispatcher:se,ReactCurrentBatchConfig:N,ReactCurrentOwner:_};function G(){throw Error("act(...) is not supported in production builds of React.")}return De.Children={map:Pe,forEach:function(T,B,ne){Pe(T,function(){B.apply(this,arguments)},ne)},count:function(T){var B=0;return Pe(T,function(){B++}),B},toArray:function(T){return Pe(T,function(B){return B})||[]},only:function(T){if(!ve(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},De.Component=R,De.Fragment=o,De.Profiler=s,De.PureComponent=j,De.StrictMode=a,De.Suspense=p,De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=W,De.act=G,De.cloneElement=function(T,B,ne){if(T==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+T+".");var ue=k({},T.props),be=T.key,Re=T.ref,ie=T._owner;if(B!=null){if(B.ref!==void 0&&(Re=B.ref,ie=_.current),B.key!==void 0&&(be=""+B.key),T.type&&T.type.defaultProps)var Oe=T.type.defaultProps;for(je in B)H.call(B,je)&&!K.hasOwnProperty(je)&&(ue[je]=B[je]===void 0&&Oe!==void 0?Oe[je]:B[je])}var je=arguments.length-2;if(je===1)ue.children=ne;else if(1<je){Oe=Array(je);for(var Ae=0;Ae<je;Ae++)Oe[Ae]=arguments[Ae+2];ue.children=Oe}return{$$typeof:t,type:T.type,key:be,ref:Re,props:ue,_owner:ie}},De.createContext=function(T){return T={$$typeof:d,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},T.Provider={$$typeof:u,_context:T},T.Consumer=T},De.createElement=V,De.createFactory=function(T){var B=V.bind(null,T);return B.type=T,B},De.createRef=function(){return{current:null}},De.forwardRef=function(T){return{$$typeof:h,render:T}},De.isValidElement=ve,De.lazy=function(T){return{$$typeof:v,_payload:{_status:-1,_result:T},_init:Z}},De.memo=function(T,B){return{$$typeof:m,type:T,compare:B===void 0?null:B}},De.startTransition=function(T){var B=N.transition;N.transition={};try{T()}finally{N.transition=B}},De.unstable_act=G,De.useCallback=function(T,B){return se.current.useCallback(T,B)},De.useContext=function(T){return se.current.useContext(T)},De.useDebugValue=function(){},De.useDeferredValue=function(T){return se.current.useDeferredValue(T)},De.useEffect=function(T,B){return se.current.useEffect(T,B)},De.useId=function(){return se.current.useId()},De.useImperativeHandle=function(T,B,ne){return se.current.useImperativeHandle(T,B,ne)},De.useInsertionEffect=function(T,B){return se.current.useInsertionEffect(T,B)},De.useLayoutEffect=function(T,B){return se.current.useLayoutEffect(T,B)},De.useMemo=function(T,B){return se.current.useMemo(T,B)},De.useReducer=function(T,B,ne){return se.current.useReducer(T,B,ne)},De.useRef=function(T){return se.current.useRef(T)},De.useState=function(T){return se.current.useState(T)},De.useSyncExternalStore=function(T,B,ne){return se.current.useSyncExternalStore(T,B,ne)},De.useTransition=function(){return se.current.useTransition()},De.version="18.3.1",De}var zh;function Ic(){return zh||(zh=1,Vu.exports=Mw()),Vu.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh;function _w(){if(Uh)return Di;Uh=1;var t=Ic(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function d(h,p,m){var v,w={},x=null,E=null;m!==void 0&&(x=""+m),p.key!==void 0&&(x=""+p.key),p.ref!==void 0&&(E=p.ref);for(v in p)a.call(p,v)&&!u.hasOwnProperty(v)&&(w[v]=p[v]);if(h&&h.defaultProps)for(v in p=h.defaultProps,p)w[v]===void 0&&(w[v]=p[v]);return{$$typeof:r,type:h,key:x,ref:E,props:w,_owner:s.current}}return Di.Fragment=o,Di.jsx=d,Di.jsxs=d,Di}var $h;function Tw(){return $h||($h=1,Bu.exports=_w()),Bu.exports}var D=Tw(),g=Ic();const It=tm(g),nm=bw({__proto__:null,default:It},[g]);var pl={},Wu={exports:{}},At={},Ku={exports:{}},Qu={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hh;function Ow(){return Hh||(Hh=1,function(t){function r(N,W){var G=N.length;N.push(W);e:for(;0<G;){var T=G-1>>>1,B=N[T];if(0<s(B,W))N[T]=W,N[G]=B,G=T;else break e}}function o(N){return N.length===0?null:N[0]}function a(N){if(N.length===0)return null;var W=N[0],G=N.pop();if(G!==W){N[0]=G;e:for(var T=0,B=N.length,ne=B>>>1;T<ne;){var ue=2*(T+1)-1,be=N[ue],Re=ue+1,ie=N[Re];if(0>s(be,G))Re<B&&0>s(ie,be)?(N[T]=ie,N[Re]=G,T=Re):(N[T]=be,N[ue]=G,T=ue);else if(Re<B&&0>s(ie,G))N[T]=ie,N[Re]=G,T=Re;else break e}}return W}function s(N,W){var G=N.sortIndex-W.sortIndex;return G!==0?G:N.id-W.id}if(typeof performance=="object"&&typeof performance.now=="function"){var u=performance;t.unstable_now=function(){return u.now()}}else{var d=Date,h=d.now();t.unstable_now=function(){return d.now()-h}}var p=[],m=[],v=1,w=null,x=3,E=!1,k=!1,S=!1,R=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function L(N){for(var W=o(m);W!==null;){if(W.callback===null)a(m);else if(W.startTime<=N)a(m),W.sortIndex=W.expirationTime,r(p,W);else break;W=o(m)}}function U(N){if(S=!1,L(N),!k)if(o(p)!==null)k=!0,Z(H);else{var W=o(m);W!==null&&se(U,W.startTime-N)}}function H(N,W){k=!1,S&&(S=!1,P(V),V=-1),E=!0;var G=x;try{for(L(W),w=o(p);w!==null&&(!(w.expirationTime>W)||N&&!Te());){var T=w.callback;if(typeof T=="function"){w.callback=null,x=w.priorityLevel;var B=T(w.expirationTime<=W);W=t.unstable_now(),typeof B=="function"?w.callback=B:w===o(p)&&a(p),L(W)}else a(p);w=o(p)}if(w!==null)var ne=!0;else{var ue=o(m);ue!==null&&se(U,ue.startTime-W),ne=!1}return ne}finally{w=null,x=G,E=!1}}var _=!1,K=null,V=-1,te=5,ve=-1;function Te(){return!(t.unstable_now()-ve<te)}function _e(){if(K!==null){var N=t.unstable_now();ve=N;var W=!0;try{W=K(!0,N)}finally{W?oe():(_=!1,K=null)}}else _=!1}var oe;if(typeof j=="function")oe=function(){j(_e)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,Pe=ae.port2;ae.port1.onmessage=_e,oe=function(){Pe.postMessage(null)}}else oe=function(){R(_e,0)};function Z(N){K=N,_||(_=!0,oe())}function se(N,W){V=R(function(){N(t.unstable_now())},W)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(N){N.callback=null},t.unstable_continueExecution=function(){k||E||(k=!0,Z(H))},t.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):te=0<N?Math.floor(1e3/N):5},t.unstable_getCurrentPriorityLevel=function(){return x},t.unstable_getFirstCallbackNode=function(){return o(p)},t.unstable_next=function(N){switch(x){case 1:case 2:case 3:var W=3;break;default:W=x}var G=x;x=W;try{return N()}finally{x=G}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(N,W){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var G=x;x=N;try{return W()}finally{x=G}},t.unstable_scheduleCallback=function(N,W,G){var T=t.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?T+G:T):G=T,N){case 1:var B=-1;break;case 2:B=250;break;case 5:B=**********;break;case 4:B=1e4;break;default:B=5e3}return B=G+B,N={id:v++,callback:W,priorityLevel:N,startTime:G,expirationTime:B,sortIndex:-1},G>T?(N.sortIndex=G,r(m,N),o(p)===null&&N===o(m)&&(S?(P(V),V=-1):S=!0,se(U,G-T))):(N.sortIndex=B,r(p,N),k||E||(k=!0,Z(H))),N},t.unstable_shouldYield=Te,t.unstable_wrapCallback=function(N){var W=x;return function(){var G=x;x=W;try{return N.apply(this,arguments)}finally{x=G}}}}(Qu)),Qu}var Bh;function Nw(){return Bh||(Bh=1,Ku.exports=Ow()),Ku.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vh;function Dw(){if(Vh)return At;Vh=1;var t=Ic(),r=Nw();function o(e){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+e,i=1;i<arguments.length;i++)n+="&args[]="+encodeURIComponent(arguments[i]);return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function u(e,n){d(e,n),d(e+"Capture",n)}function d(e,n){for(s[e]=n,e=0;e<n.length;e++)a.add(n[e])}var h=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),p=Object.prototype.hasOwnProperty,m=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,v={},w={};function x(e){return p.call(w,e)?!0:p.call(v,e)?!1:m.test(e)?w[e]=!0:(v[e]=!0,!1)}function E(e,n,i,l){if(i!==null&&i.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return l?!1:i!==null?!i.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function k(e,n,i,l){if(n===null||typeof n>"u"||E(e,n,i,l))return!0;if(l)return!1;if(i!==null)switch(i.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function S(e,n,i,l,c,f,y){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=l,this.attributeNamespace=c,this.mustUseProperty=i,this.propertyName=e,this.type=n,this.sanitizeURL=f,this.removeEmptyString=y}var R={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){R[e]=new S(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var n=e[0];R[n]=new S(n,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){R[e]=new S(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){R[e]=new S(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){R[e]=new S(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){R[e]=new S(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){R[e]=new S(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){R[e]=new S(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){R[e]=new S(e,5,!1,e.toLowerCase(),null,!1,!1)});var P=/[\-:]([a-z])/g;function j(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var n=e.replace(P,j);R[n]=new S(n,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var n=e.replace(P,j);R[n]=new S(n,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var n=e.replace(P,j);R[n]=new S(n,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){R[e]=new S(e,1,!1,e.toLowerCase(),null,!1,!1)}),R.xlinkHref=new S("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){R[e]=new S(e,1,!1,e.toLowerCase(),null,!0,!0)});function L(e,n,i,l){var c=R.hasOwnProperty(n)?R[n]:null;(c!==null?c.type!==0:l||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(k(n,i,c,l)&&(i=null),l||c===null?x(n)&&(i===null?e.removeAttribute(n):e.setAttribute(n,""+i)):c.mustUseProperty?e[c.propertyName]=i===null?c.type===3?!1:"":i:(n=c.attributeName,l=c.attributeNamespace,i===null?e.removeAttribute(n):(c=c.type,i=c===3||c===4&&i===!0?"":""+i,l?e.setAttributeNS(l,n,i):e.setAttribute(n,i))))}var U=t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=Symbol.for("react.element"),_=Symbol.for("react.portal"),K=Symbol.for("react.fragment"),V=Symbol.for("react.strict_mode"),te=Symbol.for("react.profiler"),ve=Symbol.for("react.provider"),Te=Symbol.for("react.context"),_e=Symbol.for("react.forward_ref"),oe=Symbol.for("react.suspense"),ae=Symbol.for("react.suspense_list"),Pe=Symbol.for("react.memo"),Z=Symbol.for("react.lazy"),se=Symbol.for("react.offscreen"),N=Symbol.iterator;function W(e){return e===null||typeof e!="object"?null:(e=N&&e[N]||e["@@iterator"],typeof e=="function"?e:null)}var G=Object.assign,T;function B(e){if(T===void 0)try{throw Error()}catch(i){var n=i.stack.trim().match(/\n( *(at )?)/);T=n&&n[1]||""}return`
`+T+e}var ne=!1;function ue(e,n){if(!e||ne)return"";ne=!0;var i=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch(z){var l=z}Reflect.construct(e,[],n)}else{try{n.call()}catch(z){l=z}e.call(n.prototype)}else{try{throw Error()}catch(z){l=z}e()}}catch(z){if(z&&l&&typeof z.stack=="string"){for(var c=z.stack.split(`
`),f=l.stack.split(`
`),y=c.length-1,C=f.length-1;1<=y&&0<=C&&c[y]!==f[C];)C--;for(;1<=y&&0<=C;y--,C--)if(c[y]!==f[C]){if(y!==1||C!==1)do if(y--,C--,0>C||c[y]!==f[C]){var M=`
`+c[y].replace(" at new "," at ");return e.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",e.displayName)),M}while(1<=y&&0<=C);break}}}finally{ne=!1,Error.prepareStackTrace=i}return(e=e?e.displayName||e.name:"")?B(e):""}function be(e){switch(e.tag){case 5:return B(e.type);case 16:return B("Lazy");case 13:return B("Suspense");case 19:return B("SuspenseList");case 0:case 2:case 15:return e=ue(e.type,!1),e;case 11:return e=ue(e.type.render,!1),e;case 1:return e=ue(e.type,!0),e;default:return""}}function Re(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case K:return"Fragment";case _:return"Portal";case te:return"Profiler";case V:return"StrictMode";case oe:return"Suspense";case ae:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Te:return(e.displayName||"Context")+".Consumer";case ve:return(e._context.displayName||"Context")+".Provider";case _e:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Pe:return n=e.displayName||null,n!==null?n:Re(e.type)||"Memo";case Z:n=e._payload,e=e._init;try{return Re(e(n))}catch{}}return null}function ie(e){var n=e.type;switch(e.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=n.render,e=e.displayName||e.name||"",n.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Re(n);case 8:return n===V?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function Oe(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function je(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Ae(e){var n=je(e)?"checked":"value",i=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),l=""+e[n];if(!e.hasOwnProperty(n)&&typeof i<"u"&&typeof i.get=="function"&&typeof i.set=="function"){var c=i.get,f=i.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return c.call(this)},set:function(y){l=""+y,f.call(this,y)}}),Object.defineProperty(e,n,{enumerable:i.enumerable}),{getValue:function(){return l},setValue:function(y){l=""+y},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function pt(e){e._valueTracker||(e._valueTracker=Ae(e))}function Qt(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var i=n.getValue(),l="";return e&&(l=je(e)?e.checked?"true":"false":e.value),e=l,e!==i?(n.setValue(e),!0):!1}function ln(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function sn(e,n){var i=n.checked;return G({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:i??e._wrapperState.initialChecked})}function ia(e,n){var i=n.defaultValue==null?"":n.defaultValue,l=n.checked!=null?n.checked:n.defaultChecked;i=Oe(n.value!=null?n.value:i),e._wrapperState={initialChecked:l,initialValue:i,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function aa(e,n){n=n.checked,n!=null&&L(e,"checked",n,!1)}function oo(e,n){aa(e,n);var i=Oe(n.value),l=n.type;if(i!=null)l==="number"?(i===0&&e.value===""||e.value!=i)&&(e.value=""+i):e.value!==""+i&&(e.value=""+i);else if(l==="submit"||l==="reset"){e.removeAttribute("value");return}n.hasOwnProperty("value")?Qo(e,n.type,i):n.hasOwnProperty("defaultValue")&&Qo(e,n.type,Oe(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(e.defaultChecked=!!n.defaultChecked)}function la(e,n,i){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var l=n.type;if(!(l!=="submit"&&l!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+e._wrapperState.initialValue,i||n===e.value||(e.value=n),e.defaultValue=n}i=e.name,i!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,i!==""&&(e.name=i)}function Qo(e,n,i){(n!=="number"||ln(e.ownerDocument)!==e)&&(i==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+i&&(e.defaultValue=""+i))}var Or=Array.isArray;function qn(e,n,i,l){if(e=e.options,n){n={};for(var c=0;c<i.length;c++)n["$"+i[c]]=!0;for(i=0;i<e.length;i++)c=n.hasOwnProperty("$"+e[i].value),e[i].selected!==c&&(e[i].selected=c),c&&l&&(e[i].defaultSelected=!0)}else{for(i=""+Oe(i),n=null,c=0;c<e.length;c++){if(e[c].value===i){e[c].selected=!0,l&&(e[c].defaultSelected=!0);return}n!==null||e[c].disabled||(n=e[c])}n!==null&&(n.selected=!0)}}function un(e,n){if(n.dangerouslySetInnerHTML!=null)throw Error(o(91));return G({},n,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Jn(e,n){var i=n.value;if(i==null){if(i=n.children,n=n.defaultValue,i!=null){if(n!=null)throw Error(o(92));if(Or(i)){if(1<i.length)throw Error(o(93));i=i[0]}n=i}n==null&&(n=""),i=n}e._wrapperState={initialValue:Oe(i)}}function Go(e,n){var i=Oe(n.value),l=Oe(n.defaultValue);i!=null&&(i=""+i,i!==e.value&&(e.value=i),n.defaultValue==null&&e.defaultValue!==i&&(e.defaultValue=i)),l!=null&&(e.defaultValue=""+l)}function io(e){var n=e.textContent;n===e._wrapperState.initialValue&&n!==""&&n!==null&&(e.value=n)}function cn(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zt(e,n){return e==null||e==="http://www.w3.org/1999/xhtml"?cn(n):e==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Nr,ao=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,i,l,c){MSApp.execUnsafeLocalFunction(function(){return e(n,i,l,c)})}:e}(function(e,n){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=n;else{for(Nr=Nr||document.createElement("div"),Nr.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=Nr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;n.firstChild;)e.appendChild(n.firstChild)}});function Dr(e,n){if(n){var i=e.firstChild;if(i&&i===e.lastChild&&i.nodeType===3){i.nodeValue=n;return}}e.textContent=n}var Mt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},sa=["Webkit","ms","Moz","O"];Object.keys(Mt).forEach(function(e){sa.forEach(function(n){n=n+e.charAt(0).toUpperCase()+e.substring(1),Mt[n]=Mt[e]})});function Yo(e,n,i){return n==null||typeof n=="boolean"||n===""?"":i||typeof n!="number"||n===0||Mt.hasOwnProperty(e)&&Mt[e]?(""+n).trim():n+"px"}function Xo(e,n){e=e.style;for(var i in n)if(n.hasOwnProperty(i)){var l=i.indexOf("--")===0,c=Yo(i,n[i],l);i==="float"&&(i="cssFloat"),l?e.setProperty(i,c):e[i]=c}}var ss=G({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function lo(e,n){if(n){if(ss[e]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(o(137,e));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(o(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(o(61))}if(n.style!=null&&typeof n.style!="object")throw Error(o(62))}}function Zn(e,n){if(e.indexOf("-")===-1)return typeof n.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var so=null;function Lr(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var qo=null,Fn=null,er=null;function Jo(e){if(e=yi(e)){if(typeof qo!="function")throw Error(o(280));var n=e.stateNode;n&&(n=_a(n),qo(e.stateNode,e.type,n))}}function Ar(e){Fn?er?er.push(e):er=[e]:Fn=e}function Ir(){if(Fn){var e=Fn,n=er;if(er=Fn=null,Jo(e),n)for(e=0;e<n.length;e++)Jo(n[e])}}function ua(e,n){return e(n)}function ca(){}var b=!1;function A(e,n,i){if(b)return e(n,i);b=!0;try{return ua(e,n,i)}finally{b=!1,(Fn!==null||er!==null)&&(ca(),Ir())}}function $(e,n){var i=e.stateNode;if(i===null)return null;var l=_a(i);if(l===null)return null;i=l[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(i&&typeof i!="function")throw Error(o(231,n,typeof i));return i}var q=!1;if(h)try{var ee={};Object.defineProperty(ee,"passive",{get:function(){q=!0}}),window.addEventListener("test",ee,ee),window.removeEventListener("test",ee,ee)}catch{q=!1}function we(e,n,i,l,c,f,y,C,M){var z=Array.prototype.slice.call(arguments,3);try{n.apply(i,z)}catch(Y){this.onError(Y)}}var me=!1,ce=null,ye=!1,Ee=null,xe={onError:function(e){me=!0,ce=e}};function Se(e,n,i,l,c,f,y,C,M){me=!1,ce=null,we.apply(xe,arguments)}function Ie(e,n,i,l,c,f,y,C,M){if(Se.apply(this,arguments),me){if(me){var z=ce;me=!1,ce=null}else throw Error(o(198));ye||(ye=!0,Ee=z)}}function He(e){var n=e,i=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(i=n.return),e=n.return;while(e)}return n.tag===3?i:null}function it(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function ut(e){if(He(e)!==e)throw Error(o(188))}function qe(e){var n=e.alternate;if(!n){if(n=He(e),n===null)throw Error(o(188));return n!==e?null:e}for(var i=e,l=n;;){var c=i.return;if(c===null)break;var f=c.alternate;if(f===null){if(l=c.return,l!==null){i=l;continue}break}if(c.child===f.child){for(f=c.child;f;){if(f===i)return ut(c),e;if(f===l)return ut(c),n;f=f.sibling}throw Error(o(188))}if(i.return!==l.return)i=c,l=f;else{for(var y=!1,C=c.child;C;){if(C===i){y=!0,i=c,l=f;break}if(C===l){y=!0,l=c,i=f;break}C=C.sibling}if(!y){for(C=f.child;C;){if(C===i){y=!0,i=f,l=c;break}if(C===l){y=!0,l=f,i=c;break}C=C.sibling}if(!y)throw Error(o(189))}}if(i.alternate!==l)throw Error(o(190))}if(i.tag!==3)throw Error(o(188));return i.stateNode.current===i?e:n}function We(e){return e=qe(e),e!==null?Cn(e):null}function Cn(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var n=Cn(e);if(n!==null)return n;e=e.sibling}return null}var tr=r.unstable_scheduleCallback,dn=r.unstable_cancelCallback,Gt=r.unstable_shouldYield,Zo=r.unstable_requestPaint,Ke=r.unstable_now,jn=r.unstable_getCurrentPriorityLevel,nr=r.unstable_ImmediatePriority,Fr=r.unstable_UserBlockingPriority,ze=r.unstable_NormalPriority,ct=r.unstable_LowPriority,jr=r.unstable_IdlePriority,kn=null,Be=null;function uo(e){if(Be&&typeof Be.onCommitFiberRoot=="function")try{Be.onCommitFiberRoot(kn,e,void 0,(e.current.flags&128)===128)}catch{}}var _t=Math.clz32?Math.clz32:Wg,us=Math.log,Vg=Math.LN2;function Wg(e){return e>>>=0,e===0?32:31-(us(e)/Vg|0)|0}var da=64,fa=4194304;function ei(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ha(e,n){var i=e.pendingLanes;if(i===0)return 0;var l=0,c=e.suspendedLanes,f=e.pingedLanes,y=i&268435455;if(y!==0){var C=y&~c;C!==0?l=ei(C):(f&=y,f!==0&&(l=ei(f)))}else y=i&~c,y!==0?l=ei(y):f!==0&&(l=ei(f));if(l===0)return 0;if(n!==0&&n!==l&&(n&c)===0&&(c=l&-l,f=n&-n,c>=f||c===16&&(f&4194240)!==0))return n;if((l&4)!==0&&(l|=i&16),n=e.entangledLanes,n!==0)for(e=e.entanglements,n&=l;0<n;)i=31-_t(n),c=1<<i,l|=e[i],n&=~c;return l}function Kg(e,n){switch(e){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Qg(e,n){for(var i=e.suspendedLanes,l=e.pingedLanes,c=e.expirationTimes,f=e.pendingLanes;0<f;){var y=31-_t(f),C=1<<y,M=c[y];M===-1?((C&i)===0||(C&l)!==0)&&(c[y]=Kg(C,n)):M<=n&&(e.expiredLanes|=C),f&=~C}}function cs(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function pd(){var e=da;return da<<=1,(da&4194240)===0&&(da=64),e}function ds(e){for(var n=[],i=0;31>i;i++)n.push(e);return n}function ti(e,n,i){e.pendingLanes|=n,n!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,n=31-_t(n),e[n]=i}function Gg(e,n){var i=e.pendingLanes&~n;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=n,e.mutableReadLanes&=n,e.entangledLanes&=n,n=e.entanglements;var l=e.eventTimes;for(e=e.expirationTimes;0<i;){var c=31-_t(i),f=1<<c;n[c]=0,l[c]=-1,e[c]=-1,i&=~f}}function fs(e,n){var i=e.entangledLanes|=n;for(e=e.entanglements;i;){var l=31-_t(i),c=1<<l;c&n|e[l]&n&&(e[l]|=n),i&=~c}}var Ve=0;function md(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var vd,hs,gd,yd,wd,ps=!1,pa=[],rr=null,or=null,ir=null,ni=new Map,ri=new Map,ar=[],Yg="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function xd(e,n){switch(e){case"focusin":case"focusout":rr=null;break;case"dragenter":case"dragleave":or=null;break;case"mouseover":case"mouseout":ir=null;break;case"pointerover":case"pointerout":ni.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":ri.delete(n.pointerId)}}function oi(e,n,i,l,c,f){return e===null||e.nativeEvent!==f?(e={blockedOn:n,domEventName:i,eventSystemFlags:l,nativeEvent:f,targetContainers:[c]},n!==null&&(n=yi(n),n!==null&&hs(n)),e):(e.eventSystemFlags|=l,n=e.targetContainers,c!==null&&n.indexOf(c)===-1&&n.push(c),e)}function Xg(e,n,i,l,c){switch(n){case"focusin":return rr=oi(rr,e,n,i,l,c),!0;case"dragenter":return or=oi(or,e,n,i,l,c),!0;case"mouseover":return ir=oi(ir,e,n,i,l,c),!0;case"pointerover":var f=c.pointerId;return ni.set(f,oi(ni.get(f)||null,e,n,i,l,c)),!0;case"gotpointercapture":return f=c.pointerId,ri.set(f,oi(ri.get(f)||null,e,n,i,l,c)),!0}return!1}function Sd(e){var n=zr(e.target);if(n!==null){var i=He(n);if(i!==null){if(n=i.tag,n===13){if(n=it(i),n!==null){e.blockedOn=n,wd(e.priority,function(){gd(i)});return}}else if(n===3&&i.stateNode.current.memoizedState.isDehydrated){e.blockedOn=i.tag===3?i.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ma(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var i=vs(e.domEventName,e.eventSystemFlags,n[0],e.nativeEvent);if(i===null){i=e.nativeEvent;var l=new i.constructor(i.type,i);so=l,i.target.dispatchEvent(l),so=null}else return n=yi(i),n!==null&&hs(n),e.blockedOn=i,!1;n.shift()}return!0}function Ed(e,n,i){ma(e)&&i.delete(n)}function qg(){ps=!1,rr!==null&&ma(rr)&&(rr=null),or!==null&&ma(or)&&(or=null),ir!==null&&ma(ir)&&(ir=null),ni.forEach(Ed),ri.forEach(Ed)}function ii(e,n){e.blockedOn===n&&(e.blockedOn=null,ps||(ps=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,qg)))}function ai(e){function n(c){return ii(c,e)}if(0<pa.length){ii(pa[0],e);for(var i=1;i<pa.length;i++){var l=pa[i];l.blockedOn===e&&(l.blockedOn=null)}}for(rr!==null&&ii(rr,e),or!==null&&ii(or,e),ir!==null&&ii(ir,e),ni.forEach(n),ri.forEach(n),i=0;i<ar.length;i++)l=ar[i],l.blockedOn===e&&(l.blockedOn=null);for(;0<ar.length&&(i=ar[0],i.blockedOn===null);)Sd(i),i.blockedOn===null&&ar.shift()}var co=U.ReactCurrentBatchConfig,va=!0;function Jg(e,n,i,l){var c=Ve,f=co.transition;co.transition=null;try{Ve=1,ms(e,n,i,l)}finally{Ve=c,co.transition=f}}function Zg(e,n,i,l){var c=Ve,f=co.transition;co.transition=null;try{Ve=4,ms(e,n,i,l)}finally{Ve=c,co.transition=f}}function ms(e,n,i,l){if(va){var c=vs(e,n,i,l);if(c===null)Ds(e,n,l,ga,i),xd(e,l);else if(Xg(c,e,n,i,l))l.stopPropagation();else if(xd(e,l),n&4&&-1<Yg.indexOf(e)){for(;c!==null;){var f=yi(c);if(f!==null&&vd(f),f=vs(e,n,i,l),f===null&&Ds(e,n,l,ga,i),f===c)break;c=f}c!==null&&l.stopPropagation()}else Ds(e,n,l,null,i)}}var ga=null;function vs(e,n,i,l){if(ga=null,e=Lr(l),e=zr(e),e!==null)if(n=He(e),n===null)e=null;else if(i=n.tag,i===13){if(e=it(n),e!==null)return e;e=null}else if(i===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null);return ga=e,null}function Cd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(jn()){case nr:return 1;case Fr:return 4;case ze:case ct:return 16;case jr:return 536870912;default:return 16}default:return 16}}var lr=null,gs=null,ya=null;function kd(){if(ya)return ya;var e,n=gs,i=n.length,l,c="value"in lr?lr.value:lr.textContent,f=c.length;for(e=0;e<i&&n[e]===c[e];e++);var y=i-e;for(l=1;l<=y&&n[i-l]===c[f-l];l++);return ya=c.slice(e,1<l?1-l:void 0)}function wa(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function xa(){return!0}function Rd(){return!1}function Ut(e){function n(i,l,c,f,y){this._reactName=i,this._targetInst=c,this.type=l,this.nativeEvent=f,this.target=y,this.currentTarget=null;for(var C in e)e.hasOwnProperty(C)&&(i=e[C],this[C]=i?i(f):f[C]);return this.isDefaultPrevented=(f.defaultPrevented!=null?f.defaultPrevented:f.returnValue===!1)?xa:Rd,this.isPropagationStopped=Rd,this}return G(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var i=this.nativeEvent;i&&(i.preventDefault?i.preventDefault():typeof i.returnValue!="unknown"&&(i.returnValue=!1),this.isDefaultPrevented=xa)},stopPropagation:function(){var i=this.nativeEvent;i&&(i.stopPropagation?i.stopPropagation():typeof i.cancelBubble!="unknown"&&(i.cancelBubble=!0),this.isPropagationStopped=xa)},persist:function(){},isPersistent:xa}),n}var fo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ys=Ut(fo),li=G({},fo,{view:0,detail:0}),ey=Ut(li),ws,xs,si,Sa=G({},li,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Es,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==si&&(si&&e.type==="mousemove"?(ws=e.screenX-si.screenX,xs=e.screenY-si.screenY):xs=ws=0,si=e),ws)},movementY:function(e){return"movementY"in e?e.movementY:xs}}),Pd=Ut(Sa),ty=G({},Sa,{dataTransfer:0}),ny=Ut(ty),ry=G({},li,{relatedTarget:0}),Ss=Ut(ry),oy=G({},fo,{animationName:0,elapsedTime:0,pseudoElement:0}),iy=Ut(oy),ay=G({},fo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),ly=Ut(ay),sy=G({},fo,{data:0}),bd=Ut(sy),uy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},cy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fy(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=dy[e])?!!n[e]:!1}function Es(){return fy}var hy=G({},li,{key:function(e){if(e.key){var n=uy[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=wa(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?cy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Es,charCode:function(e){return e.type==="keypress"?wa(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?wa(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),py=Ut(hy),my=G({},Sa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Md=Ut(my),vy=G({},li,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Es}),gy=Ut(vy),yy=G({},fo,{propertyName:0,elapsedTime:0,pseudoElement:0}),wy=Ut(yy),xy=G({},Sa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Sy=Ut(xy),Ey=[9,13,27,32],Cs=h&&"CompositionEvent"in window,ui=null;h&&"documentMode"in document&&(ui=document.documentMode);var Cy=h&&"TextEvent"in window&&!ui,_d=h&&(!Cs||ui&&8<ui&&11>=ui),Td=" ",Od=!1;function Nd(e,n){switch(e){case"keyup":return Ey.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Dd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var ho=!1;function ky(e,n){switch(e){case"compositionend":return Dd(n);case"keypress":return n.which!==32?null:(Od=!0,Td);case"textInput":return e=n.data,e===Td&&Od?null:e;default:return null}}function Ry(e,n){if(ho)return e==="compositionend"||!Cs&&Nd(e,n)?(e=kd(),ya=gs=lr=null,ho=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return _d&&n.locale!=="ko"?null:n.data;default:return null}}var Py={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ld(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!Py[e.type]:n==="textarea"}function Ad(e,n,i,l){Ar(l),n=Pa(n,"onChange"),0<n.length&&(i=new ys("onChange","change",null,i,l),e.push({event:i,listeners:n}))}var ci=null,di=null;function by(e){Zd(e,0)}function Ea(e){var n=yo(e);if(Qt(n))return e}function My(e,n){if(e==="change")return n}var Id=!1;if(h){var ks;if(h){var Rs="oninput"in document;if(!Rs){var Fd=document.createElement("div");Fd.setAttribute("oninput","return;"),Rs=typeof Fd.oninput=="function"}ks=Rs}else ks=!1;Id=ks&&(!document.documentMode||9<document.documentMode)}function jd(){ci&&(ci.detachEvent("onpropertychange",zd),di=ci=null)}function zd(e){if(e.propertyName==="value"&&Ea(di)){var n=[];Ad(n,di,e,Lr(e)),A(by,n)}}function _y(e,n,i){e==="focusin"?(jd(),ci=n,di=i,ci.attachEvent("onpropertychange",zd)):e==="focusout"&&jd()}function Ty(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ea(di)}function Oy(e,n){if(e==="click")return Ea(n)}function Ny(e,n){if(e==="input"||e==="change")return Ea(n)}function Dy(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var fn=typeof Object.is=="function"?Object.is:Dy;function fi(e,n){if(fn(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var i=Object.keys(e),l=Object.keys(n);if(i.length!==l.length)return!1;for(l=0;l<i.length;l++){var c=i[l];if(!p.call(n,c)||!fn(e[c],n[c]))return!1}return!0}function Ud(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function $d(e,n){var i=Ud(e);e=0;for(var l;i;){if(i.nodeType===3){if(l=e+i.textContent.length,e<=n&&l>=n)return{node:i,offset:n-e};e=l}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Ud(i)}}function Hd(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?Hd(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function Bd(){for(var e=window,n=ln();n instanceof e.HTMLIFrameElement;){try{var i=typeof n.contentWindow.location.href=="string"}catch{i=!1}if(i)e=n.contentWindow;else break;n=ln(e.document)}return n}function Ps(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}function Ly(e){var n=Bd(),i=e.focusedElem,l=e.selectionRange;if(n!==i&&i&&i.ownerDocument&&Hd(i.ownerDocument.documentElement,i)){if(l!==null&&Ps(i)){if(n=l.start,e=l.end,e===void 0&&(e=n),"selectionStart"in i)i.selectionStart=n,i.selectionEnd=Math.min(e,i.value.length);else if(e=(n=i.ownerDocument||document)&&n.defaultView||window,e.getSelection){e=e.getSelection();var c=i.textContent.length,f=Math.min(l.start,c);l=l.end===void 0?f:Math.min(l.end,c),!e.extend&&f>l&&(c=l,l=f,f=c),c=$d(i,f);var y=$d(i,l);c&&y&&(e.rangeCount!==1||e.anchorNode!==c.node||e.anchorOffset!==c.offset||e.focusNode!==y.node||e.focusOffset!==y.offset)&&(n=n.createRange(),n.setStart(c.node,c.offset),e.removeAllRanges(),f>l?(e.addRange(n),e.extend(y.node,y.offset)):(n.setEnd(y.node,y.offset),e.addRange(n)))}}for(n=[],e=i;e=e.parentNode;)e.nodeType===1&&n.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof i.focus=="function"&&i.focus(),i=0;i<n.length;i++)e=n[i],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Ay=h&&"documentMode"in document&&11>=document.documentMode,po=null,bs=null,hi=null,Ms=!1;function Vd(e,n,i){var l=i.window===i?i.document:i.nodeType===9?i:i.ownerDocument;Ms||po==null||po!==ln(l)||(l=po,"selectionStart"in l&&Ps(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),hi&&fi(hi,l)||(hi=l,l=Pa(bs,"onSelect"),0<l.length&&(n=new ys("onSelect","select",null,n,i),e.push({event:n,listeners:l}),n.target=po)))}function Ca(e,n){var i={};return i[e.toLowerCase()]=n.toLowerCase(),i["Webkit"+e]="webkit"+n,i["Moz"+e]="moz"+n,i}var mo={animationend:Ca("Animation","AnimationEnd"),animationiteration:Ca("Animation","AnimationIteration"),animationstart:Ca("Animation","AnimationStart"),transitionend:Ca("Transition","TransitionEnd")},_s={},Wd={};h&&(Wd=document.createElement("div").style,"AnimationEvent"in window||(delete mo.animationend.animation,delete mo.animationiteration.animation,delete mo.animationstart.animation),"TransitionEvent"in window||delete mo.transitionend.transition);function ka(e){if(_s[e])return _s[e];if(!mo[e])return e;var n=mo[e],i;for(i in n)if(n.hasOwnProperty(i)&&i in Wd)return _s[e]=n[i];return e}var Kd=ka("animationend"),Qd=ka("animationiteration"),Gd=ka("animationstart"),Yd=ka("transitionend"),Xd=new Map,qd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function sr(e,n){Xd.set(e,n),u(n,[e])}for(var Ts=0;Ts<qd.length;Ts++){var Os=qd[Ts],Iy=Os.toLowerCase(),Fy=Os[0].toUpperCase()+Os.slice(1);sr(Iy,"on"+Fy)}sr(Kd,"onAnimationEnd"),sr(Qd,"onAnimationIteration"),sr(Gd,"onAnimationStart"),sr("dblclick","onDoubleClick"),sr("focusin","onFocus"),sr("focusout","onBlur"),sr(Yd,"onTransitionEnd"),d("onMouseEnter",["mouseout","mouseover"]),d("onMouseLeave",["mouseout","mouseover"]),d("onPointerEnter",["pointerout","pointerover"]),d("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jy=new Set("cancel close invalid load scroll toggle".split(" ").concat(pi));function Jd(e,n,i){var l=e.type||"unknown-event";e.currentTarget=i,Ie(l,n,void 0,e),e.currentTarget=null}function Zd(e,n){n=(n&4)!==0;for(var i=0;i<e.length;i++){var l=e[i],c=l.event;l=l.listeners;e:{var f=void 0;if(n)for(var y=l.length-1;0<=y;y--){var C=l[y],M=C.instance,z=C.currentTarget;if(C=C.listener,M!==f&&c.isPropagationStopped())break e;Jd(c,C,z),f=M}else for(y=0;y<l.length;y++){if(C=l[y],M=C.instance,z=C.currentTarget,C=C.listener,M!==f&&c.isPropagationStopped())break e;Jd(c,C,z),f=M}}}if(ye)throw e=Ee,ye=!1,Ee=null,e}function Ge(e,n){var i=n[zs];i===void 0&&(i=n[zs]=new Set);var l=e+"__bubble";i.has(l)||(ef(n,e,2,!1),i.add(l))}function Ns(e,n,i){var l=0;n&&(l|=4),ef(i,e,l,n)}var Ra="_reactListening"+Math.random().toString(36).slice(2);function mi(e){if(!e[Ra]){e[Ra]=!0,a.forEach(function(i){i!=="selectionchange"&&(jy.has(i)||Ns(i,!1,e),Ns(i,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[Ra]||(n[Ra]=!0,Ns("selectionchange",!1,n))}}function ef(e,n,i,l){switch(Cd(n)){case 1:var c=Jg;break;case 4:c=Zg;break;default:c=ms}i=c.bind(null,n,i,e),c=void 0,!q||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(c=!0),l?c!==void 0?e.addEventListener(n,i,{capture:!0,passive:c}):e.addEventListener(n,i,!0):c!==void 0?e.addEventListener(n,i,{passive:c}):e.addEventListener(n,i,!1)}function Ds(e,n,i,l,c){var f=l;if((n&1)===0&&(n&2)===0&&l!==null)e:for(;;){if(l===null)return;var y=l.tag;if(y===3||y===4){var C=l.stateNode.containerInfo;if(C===c||C.nodeType===8&&C.parentNode===c)break;if(y===4)for(y=l.return;y!==null;){var M=y.tag;if((M===3||M===4)&&(M=y.stateNode.containerInfo,M===c||M.nodeType===8&&M.parentNode===c))return;y=y.return}for(;C!==null;){if(y=zr(C),y===null)return;if(M=y.tag,M===5||M===6){l=f=y;continue e}C=C.parentNode}}l=l.return}A(function(){var z=f,Y=Lr(i),X=[];e:{var Q=Xd.get(e);if(Q!==void 0){var re=ys,de=e;switch(e){case"keypress":if(wa(i)===0)break e;case"keydown":case"keyup":re=py;break;case"focusin":de="focus",re=Ss;break;case"focusout":de="blur",re=Ss;break;case"beforeblur":case"afterblur":re=Ss;break;case"click":if(i.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":re=Pd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":re=ny;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":re=gy;break;case Kd:case Qd:case Gd:re=iy;break;case Yd:re=wy;break;case"scroll":re=ey;break;case"wheel":re=Sy;break;case"copy":case"cut":case"paste":re=ly;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":re=Md}var pe=(n&4)!==0,rt=!pe&&e==="scroll",I=pe?Q!==null?Q+"Capture":null:Q;pe=[];for(var O=z,F;O!==null;){F=O;var J=F.stateNode;if(F.tag===5&&J!==null&&(F=J,I!==null&&(J=$(O,I),J!=null&&pe.push(vi(O,J,F)))),rt)break;O=O.return}0<pe.length&&(Q=new re(Q,de,null,i,Y),X.push({event:Q,listeners:pe}))}}if((n&7)===0){e:{if(Q=e==="mouseover"||e==="pointerover",re=e==="mouseout"||e==="pointerout",Q&&i!==so&&(de=i.relatedTarget||i.fromElement)&&(zr(de)||de[zn]))break e;if((re||Q)&&(Q=Y.window===Y?Y:(Q=Y.ownerDocument)?Q.defaultView||Q.parentWindow:window,re?(de=i.relatedTarget||i.toElement,re=z,de=de?zr(de):null,de!==null&&(rt=He(de),de!==rt||de.tag!==5&&de.tag!==6)&&(de=null)):(re=null,de=z),re!==de)){if(pe=Pd,J="onMouseLeave",I="onMouseEnter",O="mouse",(e==="pointerout"||e==="pointerover")&&(pe=Md,J="onPointerLeave",I="onPointerEnter",O="pointer"),rt=re==null?Q:yo(re),F=de==null?Q:yo(de),Q=new pe(J,O+"leave",re,i,Y),Q.target=rt,Q.relatedTarget=F,J=null,zr(Y)===z&&(pe=new pe(I,O+"enter",de,i,Y),pe.target=F,pe.relatedTarget=rt,J=pe),rt=J,re&&de)t:{for(pe=re,I=de,O=0,F=pe;F;F=vo(F))O++;for(F=0,J=I;J;J=vo(J))F++;for(;0<O-F;)pe=vo(pe),O--;for(;0<F-O;)I=vo(I),F--;for(;O--;){if(pe===I||I!==null&&pe===I.alternate)break t;pe=vo(pe),I=vo(I)}pe=null}else pe=null;re!==null&&tf(X,Q,re,pe,!1),de!==null&&rt!==null&&tf(X,rt,de,pe,!0)}}e:{if(Q=z?yo(z):window,re=Q.nodeName&&Q.nodeName.toLowerCase(),re==="select"||re==="input"&&Q.type==="file")var ge=My;else if(Ld(Q))if(Id)ge=Ny;else{ge=Ty;var Ce=_y}else(re=Q.nodeName)&&re.toLowerCase()==="input"&&(Q.type==="checkbox"||Q.type==="radio")&&(ge=Oy);if(ge&&(ge=ge(e,z))){Ad(X,ge,i,Y);break e}Ce&&Ce(e,Q,z),e==="focusout"&&(Ce=Q._wrapperState)&&Ce.controlled&&Q.type==="number"&&Qo(Q,"number",Q.value)}switch(Ce=z?yo(z):window,e){case"focusin":(Ld(Ce)||Ce.contentEditable==="true")&&(po=Ce,bs=z,hi=null);break;case"focusout":hi=bs=po=null;break;case"mousedown":Ms=!0;break;case"contextmenu":case"mouseup":case"dragend":Ms=!1,Vd(X,i,Y);break;case"selectionchange":if(Ay)break;case"keydown":case"keyup":Vd(X,i,Y)}var ke;if(Cs)e:{switch(e){case"compositionstart":var Me="onCompositionStart";break e;case"compositionend":Me="onCompositionEnd";break e;case"compositionupdate":Me="onCompositionUpdate";break e}Me=void 0}else ho?Nd(e,i)&&(Me="onCompositionEnd"):e==="keydown"&&i.keyCode===229&&(Me="onCompositionStart");Me&&(_d&&i.locale!=="ko"&&(ho||Me!=="onCompositionStart"?Me==="onCompositionEnd"&&ho&&(ke=kd()):(lr=Y,gs="value"in lr?lr.value:lr.textContent,ho=!0)),Ce=Pa(z,Me),0<Ce.length&&(Me=new bd(Me,e,null,i,Y),X.push({event:Me,listeners:Ce}),ke?Me.data=ke:(ke=Dd(i),ke!==null&&(Me.data=ke)))),(ke=Cy?ky(e,i):Ry(e,i))&&(z=Pa(z,"onBeforeInput"),0<z.length&&(Y=new bd("onBeforeInput","beforeinput",null,i,Y),X.push({event:Y,listeners:z}),Y.data=ke))}Zd(X,n)})}function vi(e,n,i){return{instance:e,listener:n,currentTarget:i}}function Pa(e,n){for(var i=n+"Capture",l=[];e!==null;){var c=e,f=c.stateNode;c.tag===5&&f!==null&&(c=f,f=$(e,i),f!=null&&l.unshift(vi(e,f,c)),f=$(e,n),f!=null&&l.push(vi(e,f,c))),e=e.return}return l}function vo(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tf(e,n,i,l,c){for(var f=n._reactName,y=[];i!==null&&i!==l;){var C=i,M=C.alternate,z=C.stateNode;if(M!==null&&M===l)break;C.tag===5&&z!==null&&(C=z,c?(M=$(i,f),M!=null&&y.unshift(vi(i,M,C))):c||(M=$(i,f),M!=null&&y.push(vi(i,M,C)))),i=i.return}y.length!==0&&e.push({event:n,listeners:y})}var zy=/\r\n?/g,Uy=/\u0000|\uFFFD/g;function nf(e){return(typeof e=="string"?e:""+e).replace(zy,`
`).replace(Uy,"")}function ba(e,n,i){if(n=nf(n),nf(e)!==n&&i)throw Error(o(425))}function Ma(){}var Ls=null,As=null;function Is(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Fs=typeof setTimeout=="function"?setTimeout:void 0,$y=typeof clearTimeout=="function"?clearTimeout:void 0,rf=typeof Promise=="function"?Promise:void 0,Hy=typeof queueMicrotask=="function"?queueMicrotask:typeof rf<"u"?function(e){return rf.resolve(null).then(e).catch(By)}:Fs;function By(e){setTimeout(function(){throw e})}function js(e,n){var i=n,l=0;do{var c=i.nextSibling;if(e.removeChild(i),c&&c.nodeType===8)if(i=c.data,i==="/$"){if(l===0){e.removeChild(c),ai(n);return}l--}else i!=="$"&&i!=="$?"&&i!=="$!"||l++;i=c}while(i);ai(n)}function ur(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return e}function of(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="$"||i==="$!"||i==="$?"){if(n===0)return e;n--}else i==="/$"&&n++}e=e.previousSibling}return null}var go=Math.random().toString(36).slice(2),Rn="__reactFiber$"+go,gi="__reactProps$"+go,zn="__reactContainer$"+go,zs="__reactEvents$"+go,Vy="__reactListeners$"+go,Wy="__reactHandles$"+go;function zr(e){var n=e[Rn];if(n)return n;for(var i=e.parentNode;i;){if(n=i[zn]||i[Rn]){if(i=n.alternate,n.child!==null||i!==null&&i.child!==null)for(e=of(e);e!==null;){if(i=e[Rn])return i;e=of(e)}return n}e=i,i=e.parentNode}return null}function yi(e){return e=e[Rn]||e[zn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function yo(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(o(33))}function _a(e){return e[gi]||null}var Us=[],wo=-1;function cr(e){return{current:e}}function Ye(e){0>wo||(e.current=Us[wo],Us[wo]=null,wo--)}function Qe(e,n){wo++,Us[wo]=e.current,e.current=n}var dr={},gt=cr(dr),Tt=cr(!1),Ur=dr;function xo(e,n){var i=e.type.contextTypes;if(!i)return dr;var l=e.stateNode;if(l&&l.__reactInternalMemoizedUnmaskedChildContext===n)return l.__reactInternalMemoizedMaskedChildContext;var c={},f;for(f in i)c[f]=n[f];return l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=n,e.__reactInternalMemoizedMaskedChildContext=c),c}function Ot(e){return e=e.childContextTypes,e!=null}function Ta(){Ye(Tt),Ye(gt)}function af(e,n,i){if(gt.current!==dr)throw Error(o(168));Qe(gt,n),Qe(Tt,i)}function lf(e,n,i){var l=e.stateNode;if(n=n.childContextTypes,typeof l.getChildContext!="function")return i;l=l.getChildContext();for(var c in l)if(!(c in n))throw Error(o(108,ie(e)||"Unknown",c));return G({},i,l)}function Oa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||dr,Ur=gt.current,Qe(gt,e),Qe(Tt,Tt.current),!0}function sf(e,n,i){var l=e.stateNode;if(!l)throw Error(o(169));i?(e=lf(e,n,Ur),l.__reactInternalMemoizedMergedChildContext=e,Ye(Tt),Ye(gt),Qe(gt,e)):Ye(Tt),Qe(Tt,i)}var Un=null,Na=!1,$s=!1;function uf(e){Un===null?Un=[e]:Un.push(e)}function Ky(e){Na=!0,uf(e)}function fr(){if(!$s&&Un!==null){$s=!0;var e=0,n=Ve;try{var i=Un;for(Ve=1;e<i.length;e++){var l=i[e];do l=l(!0);while(l!==null)}Un=null,Na=!1}catch(c){throw Un!==null&&(Un=Un.slice(e+1)),tr(nr,fr),c}finally{Ve=n,$s=!1}}return null}var So=[],Eo=0,Da=null,La=0,Yt=[],Xt=0,$r=null,$n=1,Hn="";function Hr(e,n){So[Eo++]=La,So[Eo++]=Da,Da=e,La=n}function cf(e,n,i){Yt[Xt++]=$n,Yt[Xt++]=Hn,Yt[Xt++]=$r,$r=e;var l=$n;e=Hn;var c=32-_t(l)-1;l&=~(1<<c),i+=1;var f=32-_t(n)+c;if(30<f){var y=c-c%5;f=(l&(1<<y)-1).toString(32),l>>=y,c-=y,$n=1<<32-_t(n)+c|i<<c|l,Hn=f+e}else $n=1<<f|i<<c|l,Hn=e}function Hs(e){e.return!==null&&(Hr(e,1),cf(e,1,0))}function Bs(e){for(;e===Da;)Da=So[--Eo],So[Eo]=null,La=So[--Eo],So[Eo]=null;for(;e===$r;)$r=Yt[--Xt],Yt[Xt]=null,Hn=Yt[--Xt],Yt[Xt]=null,$n=Yt[--Xt],Yt[Xt]=null}var $t=null,Ht=null,Je=!1,hn=null;function df(e,n){var i=en(5,null,null,0);i.elementType="DELETED",i.stateNode=n,i.return=e,n=e.deletions,n===null?(e.deletions=[i],e.flags|=16):n.push(i)}function ff(e,n){switch(e.tag){case 5:var i=e.type;return n=n.nodeType!==1||i.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(e.stateNode=n,$t=e,Ht=ur(n.firstChild),!0):!1;case 6:return n=e.pendingProps===""||n.nodeType!==3?null:n,n!==null?(e.stateNode=n,$t=e,Ht=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(i=$r!==null?{id:$n,overflow:Hn}:null,e.memoizedState={dehydrated:n,treeContext:i,retryLane:1073741824},i=en(18,null,null,0),i.stateNode=n,i.return=e,e.child=i,$t=e,Ht=null,!0):!1;default:return!1}}function Vs(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ws(e){if(Je){var n=Ht;if(n){var i=n;if(!ff(e,n)){if(Vs(e))throw Error(o(418));n=ur(i.nextSibling);var l=$t;n&&ff(e,n)?df(l,i):(e.flags=e.flags&-4097|2,Je=!1,$t=e)}}else{if(Vs(e))throw Error(o(418));e.flags=e.flags&-4097|2,Je=!1,$t=e}}}function hf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;$t=e}function Aa(e){if(e!==$t)return!1;if(!Je)return hf(e),Je=!0,!1;var n;if((n=e.tag!==3)&&!(n=e.tag!==5)&&(n=e.type,n=n!=="head"&&n!=="body"&&!Is(e.type,e.memoizedProps)),n&&(n=Ht)){if(Vs(e))throw pf(),Error(o(418));for(;n;)df(e,n),n=ur(n.nextSibling)}if(hf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8){var i=e.data;if(i==="/$"){if(n===0){Ht=ur(e.nextSibling);break e}n--}else i!=="$"&&i!=="$!"&&i!=="$?"||n++}e=e.nextSibling}Ht=null}}else Ht=$t?ur(e.stateNode.nextSibling):null;return!0}function pf(){for(var e=Ht;e;)e=ur(e.nextSibling)}function Co(){Ht=$t=null,Je=!1}function Ks(e){hn===null?hn=[e]:hn.push(e)}var Qy=U.ReactCurrentBatchConfig;function wi(e,n,i){if(e=i.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(i._owner){if(i=i._owner,i){if(i.tag!==1)throw Error(o(309));var l=i.stateNode}if(!l)throw Error(o(147,e));var c=l,f=""+e;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===f?n.ref:(n=function(y){var C=c.refs;y===null?delete C[f]:C[f]=y},n._stringRef=f,n)}if(typeof e!="string")throw Error(o(284));if(!i._owner)throw Error(o(290,e))}return e}function Ia(e,n){throw e=Object.prototype.toString.call(n),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e))}function mf(e){var n=e._init;return n(e._payload)}function vf(e){function n(I,O){if(e){var F=I.deletions;F===null?(I.deletions=[O],I.flags|=16):F.push(O)}}function i(I,O){if(!e)return null;for(;O!==null;)n(I,O),O=O.sibling;return null}function l(I,O){for(I=new Map;O!==null;)O.key!==null?I.set(O.key,O):I.set(O.index,O),O=O.sibling;return I}function c(I,O){return I=xr(I,O),I.index=0,I.sibling=null,I}function f(I,O,F){return I.index=F,e?(F=I.alternate,F!==null?(F=F.index,F<O?(I.flags|=2,O):F):(I.flags|=2,O)):(I.flags|=1048576,O)}function y(I){return e&&I.alternate===null&&(I.flags|=2),I}function C(I,O,F,J){return O===null||O.tag!==6?(O=Fu(F,I.mode,J),O.return=I,O):(O=c(O,F),O.return=I,O)}function M(I,O,F,J){var ge=F.type;return ge===K?Y(I,O,F.props.children,J,F.key):O!==null&&(O.elementType===ge||typeof ge=="object"&&ge!==null&&ge.$$typeof===Z&&mf(ge)===O.type)?(J=c(O,F.props),J.ref=wi(I,O,F),J.return=I,J):(J=al(F.type,F.key,F.props,null,I.mode,J),J.ref=wi(I,O,F),J.return=I,J)}function z(I,O,F,J){return O===null||O.tag!==4||O.stateNode.containerInfo!==F.containerInfo||O.stateNode.implementation!==F.implementation?(O=ju(F,I.mode,J),O.return=I,O):(O=c(O,F.children||[]),O.return=I,O)}function Y(I,O,F,J,ge){return O===null||O.tag!==7?(O=Xr(F,I.mode,J,ge),O.return=I,O):(O=c(O,F),O.return=I,O)}function X(I,O,F){if(typeof O=="string"&&O!==""||typeof O=="number")return O=Fu(""+O,I.mode,F),O.return=I,O;if(typeof O=="object"&&O!==null){switch(O.$$typeof){case H:return F=al(O.type,O.key,O.props,null,I.mode,F),F.ref=wi(I,null,O),F.return=I,F;case _:return O=ju(O,I.mode,F),O.return=I,O;case Z:var J=O._init;return X(I,J(O._payload),F)}if(Or(O)||W(O))return O=Xr(O,I.mode,F,null),O.return=I,O;Ia(I,O)}return null}function Q(I,O,F,J){var ge=O!==null?O.key:null;if(typeof F=="string"&&F!==""||typeof F=="number")return ge!==null?null:C(I,O,""+F,J);if(typeof F=="object"&&F!==null){switch(F.$$typeof){case H:return F.key===ge?M(I,O,F,J):null;case _:return F.key===ge?z(I,O,F,J):null;case Z:return ge=F._init,Q(I,O,ge(F._payload),J)}if(Or(F)||W(F))return ge!==null?null:Y(I,O,F,J,null);Ia(I,F)}return null}function re(I,O,F,J,ge){if(typeof J=="string"&&J!==""||typeof J=="number")return I=I.get(F)||null,C(O,I,""+J,ge);if(typeof J=="object"&&J!==null){switch(J.$$typeof){case H:return I=I.get(J.key===null?F:J.key)||null,M(O,I,J,ge);case _:return I=I.get(J.key===null?F:J.key)||null,z(O,I,J,ge);case Z:var Ce=J._init;return re(I,O,F,Ce(J._payload),ge)}if(Or(J)||W(J))return I=I.get(F)||null,Y(O,I,J,ge,null);Ia(O,J)}return null}function de(I,O,F,J){for(var ge=null,Ce=null,ke=O,Me=O=0,ht=null;ke!==null&&Me<F.length;Me++){ke.index>Me?(ht=ke,ke=null):ht=ke.sibling;var $e=Q(I,ke,F[Me],J);if($e===null){ke===null&&(ke=ht);break}e&&ke&&$e.alternate===null&&n(I,ke),O=f($e,O,Me),Ce===null?ge=$e:Ce.sibling=$e,Ce=$e,ke=ht}if(Me===F.length)return i(I,ke),Je&&Hr(I,Me),ge;if(ke===null){for(;Me<F.length;Me++)ke=X(I,F[Me],J),ke!==null&&(O=f(ke,O,Me),Ce===null?ge=ke:Ce.sibling=ke,Ce=ke);return Je&&Hr(I,Me),ge}for(ke=l(I,ke);Me<F.length;Me++)ht=re(ke,I,Me,F[Me],J),ht!==null&&(e&&ht.alternate!==null&&ke.delete(ht.key===null?Me:ht.key),O=f(ht,O,Me),Ce===null?ge=ht:Ce.sibling=ht,Ce=ht);return e&&ke.forEach(function(Sr){return n(I,Sr)}),Je&&Hr(I,Me),ge}function pe(I,O,F,J){var ge=W(F);if(typeof ge!="function")throw Error(o(150));if(F=ge.call(F),F==null)throw Error(o(151));for(var Ce=ge=null,ke=O,Me=O=0,ht=null,$e=F.next();ke!==null&&!$e.done;Me++,$e=F.next()){ke.index>Me?(ht=ke,ke=null):ht=ke.sibling;var Sr=Q(I,ke,$e.value,J);if(Sr===null){ke===null&&(ke=ht);break}e&&ke&&Sr.alternate===null&&n(I,ke),O=f(Sr,O,Me),Ce===null?ge=Sr:Ce.sibling=Sr,Ce=Sr,ke=ht}if($e.done)return i(I,ke),Je&&Hr(I,Me),ge;if(ke===null){for(;!$e.done;Me++,$e=F.next())$e=X(I,$e.value,J),$e!==null&&(O=f($e,O,Me),Ce===null?ge=$e:Ce.sibling=$e,Ce=$e);return Je&&Hr(I,Me),ge}for(ke=l(I,ke);!$e.done;Me++,$e=F.next())$e=re(ke,I,Me,$e.value,J),$e!==null&&(e&&$e.alternate!==null&&ke.delete($e.key===null?Me:$e.key),O=f($e,O,Me),Ce===null?ge=$e:Ce.sibling=$e,Ce=$e);return e&&ke.forEach(function(Pw){return n(I,Pw)}),Je&&Hr(I,Me),ge}function rt(I,O,F,J){if(typeof F=="object"&&F!==null&&F.type===K&&F.key===null&&(F=F.props.children),typeof F=="object"&&F!==null){switch(F.$$typeof){case H:e:{for(var ge=F.key,Ce=O;Ce!==null;){if(Ce.key===ge){if(ge=F.type,ge===K){if(Ce.tag===7){i(I,Ce.sibling),O=c(Ce,F.props.children),O.return=I,I=O;break e}}else if(Ce.elementType===ge||typeof ge=="object"&&ge!==null&&ge.$$typeof===Z&&mf(ge)===Ce.type){i(I,Ce.sibling),O=c(Ce,F.props),O.ref=wi(I,Ce,F),O.return=I,I=O;break e}i(I,Ce);break}else n(I,Ce);Ce=Ce.sibling}F.type===K?(O=Xr(F.props.children,I.mode,J,F.key),O.return=I,I=O):(J=al(F.type,F.key,F.props,null,I.mode,J),J.ref=wi(I,O,F),J.return=I,I=J)}return y(I);case _:e:{for(Ce=F.key;O!==null;){if(O.key===Ce)if(O.tag===4&&O.stateNode.containerInfo===F.containerInfo&&O.stateNode.implementation===F.implementation){i(I,O.sibling),O=c(O,F.children||[]),O.return=I,I=O;break e}else{i(I,O);break}else n(I,O);O=O.sibling}O=ju(F,I.mode,J),O.return=I,I=O}return y(I);case Z:return Ce=F._init,rt(I,O,Ce(F._payload),J)}if(Or(F))return de(I,O,F,J);if(W(F))return pe(I,O,F,J);Ia(I,F)}return typeof F=="string"&&F!==""||typeof F=="number"?(F=""+F,O!==null&&O.tag===6?(i(I,O.sibling),O=c(O,F),O.return=I,I=O):(i(I,O),O=Fu(F,I.mode,J),O.return=I,I=O),y(I)):i(I,O)}return rt}var ko=vf(!0),gf=vf(!1),Fa=cr(null),ja=null,Ro=null,Qs=null;function Gs(){Qs=Ro=ja=null}function Ys(e){var n=Fa.current;Ye(Fa),e._currentValue=n}function Xs(e,n,i){for(;e!==null;){var l=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,l!==null&&(l.childLanes|=n)):l!==null&&(l.childLanes&n)!==n&&(l.childLanes|=n),e===i)break;e=e.return}}function Po(e,n){ja=e,Qs=Ro=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&n)!==0&&(Nt=!0),e.firstContext=null)}function qt(e){var n=e._currentValue;if(Qs!==e)if(e={context:e,memoizedValue:n,next:null},Ro===null){if(ja===null)throw Error(o(308));Ro=e,ja.dependencies={lanes:0,firstContext:e}}else Ro=Ro.next=e;return n}var Br=null;function qs(e){Br===null?Br=[e]:Br.push(e)}function yf(e,n,i,l){var c=n.interleaved;return c===null?(i.next=i,qs(n)):(i.next=c.next,c.next=i),n.interleaved=i,Bn(e,l)}function Bn(e,n){e.lanes|=n;var i=e.alternate;for(i!==null&&(i.lanes|=n),i=e,e=e.return;e!==null;)e.childLanes|=n,i=e.alternate,i!==null&&(i.childLanes|=n),i=e,e=e.return;return i.tag===3?i.stateNode:null}var hr=!1;function Js(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function wf(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Vn(e,n){return{eventTime:e,lane:n,tag:0,payload:null,callback:null,next:null}}function pr(e,n,i){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Ue&2)!==0){var c=l.pending;return c===null?n.next=n:(n.next=c.next,c.next=n),l.pending=n,Bn(e,i)}return c=l.interleaved,c===null?(n.next=n,qs(l)):(n.next=c.next,c.next=n),l.interleaved=n,Bn(e,i)}function za(e,n,i){if(n=n.updateQueue,n!==null&&(n=n.shared,(i&4194240)!==0)){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,fs(e,i)}}function xf(e,n){var i=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,i===l)){var c=null,f=null;if(i=i.firstBaseUpdate,i!==null){do{var y={eventTime:i.eventTime,lane:i.lane,tag:i.tag,payload:i.payload,callback:i.callback,next:null};f===null?c=f=y:f=f.next=y,i=i.next}while(i!==null);f===null?c=f=n:f=f.next=n}else c=f=n;i={baseState:l.baseState,firstBaseUpdate:c,lastBaseUpdate:f,shared:l.shared,effects:l.effects},e.updateQueue=i;return}e=i.lastBaseUpdate,e===null?i.firstBaseUpdate=n:e.next=n,i.lastBaseUpdate=n}function Ua(e,n,i,l){var c=e.updateQueue;hr=!1;var f=c.firstBaseUpdate,y=c.lastBaseUpdate,C=c.shared.pending;if(C!==null){c.shared.pending=null;var M=C,z=M.next;M.next=null,y===null?f=z:y.next=z,y=M;var Y=e.alternate;Y!==null&&(Y=Y.updateQueue,C=Y.lastBaseUpdate,C!==y&&(C===null?Y.firstBaseUpdate=z:C.next=z,Y.lastBaseUpdate=M))}if(f!==null){var X=c.baseState;y=0,Y=z=M=null,C=f;do{var Q=C.lane,re=C.eventTime;if((l&Q)===Q){Y!==null&&(Y=Y.next={eventTime:re,lane:0,tag:C.tag,payload:C.payload,callback:C.callback,next:null});e:{var de=e,pe=C;switch(Q=n,re=i,pe.tag){case 1:if(de=pe.payload,typeof de=="function"){X=de.call(re,X,Q);break e}X=de;break e;case 3:de.flags=de.flags&-65537|128;case 0:if(de=pe.payload,Q=typeof de=="function"?de.call(re,X,Q):de,Q==null)break e;X=G({},X,Q);break e;case 2:hr=!0}}C.callback!==null&&C.lane!==0&&(e.flags|=64,Q=c.effects,Q===null?c.effects=[C]:Q.push(C))}else re={eventTime:re,lane:Q,tag:C.tag,payload:C.payload,callback:C.callback,next:null},Y===null?(z=Y=re,M=X):Y=Y.next=re,y|=Q;if(C=C.next,C===null){if(C=c.shared.pending,C===null)break;Q=C,C=Q.next,Q.next=null,c.lastBaseUpdate=Q,c.shared.pending=null}}while(!0);if(Y===null&&(M=X),c.baseState=M,c.firstBaseUpdate=z,c.lastBaseUpdate=Y,n=c.shared.interleaved,n!==null){c=n;do y|=c.lane,c=c.next;while(c!==n)}else f===null&&(c.shared.lanes=0);Kr|=y,e.lanes=y,e.memoizedState=X}}function Sf(e,n,i){if(e=n.effects,n.effects=null,e!==null)for(n=0;n<e.length;n++){var l=e[n],c=l.callback;if(c!==null){if(l.callback=null,l=i,typeof c!="function")throw Error(o(191,c));c.call(l)}}}var xi={},Pn=cr(xi),Si=cr(xi),Ei=cr(xi);function Vr(e){if(e===xi)throw Error(o(174));return e}function Zs(e,n){switch(Qe(Ei,n),Qe(Si,e),Qe(Pn,xi),e=n.nodeType,e){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:zt(null,"");break;default:e=e===8?n.parentNode:n,n=e.namespaceURI||null,e=e.tagName,n=zt(n,e)}Ye(Pn),Qe(Pn,n)}function bo(){Ye(Pn),Ye(Si),Ye(Ei)}function Ef(e){Vr(Ei.current);var n=Vr(Pn.current),i=zt(n,e.type);n!==i&&(Qe(Si,e),Qe(Pn,i))}function eu(e){Si.current===e&&(Ye(Pn),Ye(Si))}var Ze=cr(0);function $a(e){for(var n=e;n!==null;){if(n.tag===13){var i=n.memoizedState;if(i!==null&&(i=i.dehydrated,i===null||i.data==="$?"||i.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var tu=[];function nu(){for(var e=0;e<tu.length;e++)tu[e]._workInProgressVersionPrimary=null;tu.length=0}var Ha=U.ReactCurrentDispatcher,ru=U.ReactCurrentBatchConfig,Wr=0,et=null,at=null,dt=null,Ba=!1,Ci=!1,ki=0,Gy=0;function yt(){throw Error(o(321))}function ou(e,n){if(n===null)return!1;for(var i=0;i<n.length&&i<e.length;i++)if(!fn(e[i],n[i]))return!1;return!0}function iu(e,n,i,l,c,f){if(Wr=f,et=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,Ha.current=e===null||e.memoizedState===null?Jy:Zy,e=i(l,c),Ci){f=0;do{if(Ci=!1,ki=0,25<=f)throw Error(o(301));f+=1,dt=at=null,n.updateQueue=null,Ha.current=ew,e=i(l,c)}while(Ci)}if(Ha.current=Ka,n=at!==null&&at.next!==null,Wr=0,dt=at=et=null,Ba=!1,n)throw Error(o(300));return e}function au(){var e=ki!==0;return ki=0,e}function bn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return dt===null?et.memoizedState=dt=e:dt=dt.next=e,dt}function Jt(){if(at===null){var e=et.alternate;e=e!==null?e.memoizedState:null}else e=at.next;var n=dt===null?et.memoizedState:dt.next;if(n!==null)dt=n,at=e;else{if(e===null)throw Error(o(310));at=e,e={memoizedState:at.memoizedState,baseState:at.baseState,baseQueue:at.baseQueue,queue:at.queue,next:null},dt===null?et.memoizedState=dt=e:dt=dt.next=e}return dt}function Ri(e,n){return typeof n=="function"?n(e):n}function lu(e){var n=Jt(),i=n.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var l=at,c=l.baseQueue,f=i.pending;if(f!==null){if(c!==null){var y=c.next;c.next=f.next,f.next=y}l.baseQueue=c=f,i.pending=null}if(c!==null){f=c.next,l=l.baseState;var C=y=null,M=null,z=f;do{var Y=z.lane;if((Wr&Y)===Y)M!==null&&(M=M.next={lane:0,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),l=z.hasEagerState?z.eagerState:e(l,z.action);else{var X={lane:Y,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null};M===null?(C=M=X,y=l):M=M.next=X,et.lanes|=Y,Kr|=Y}z=z.next}while(z!==null&&z!==f);M===null?y=l:M.next=C,fn(l,n.memoizedState)||(Nt=!0),n.memoizedState=l,n.baseState=y,n.baseQueue=M,i.lastRenderedState=l}if(e=i.interleaved,e!==null){c=e;do f=c.lane,et.lanes|=f,Kr|=f,c=c.next;while(c!==e)}else c===null&&(i.lanes=0);return[n.memoizedState,i.dispatch]}function su(e){var n=Jt(),i=n.queue;if(i===null)throw Error(o(311));i.lastRenderedReducer=e;var l=i.dispatch,c=i.pending,f=n.memoizedState;if(c!==null){i.pending=null;var y=c=c.next;do f=e(f,y.action),y=y.next;while(y!==c);fn(f,n.memoizedState)||(Nt=!0),n.memoizedState=f,n.baseQueue===null&&(n.baseState=f),i.lastRenderedState=f}return[f,l]}function Cf(){}function kf(e,n){var i=et,l=Jt(),c=n(),f=!fn(l.memoizedState,c);if(f&&(l.memoizedState=c,Nt=!0),l=l.queue,uu(bf.bind(null,i,l,e),[e]),l.getSnapshot!==n||f||dt!==null&&dt.memoizedState.tag&1){if(i.flags|=2048,Pi(9,Pf.bind(null,i,l,c,n),void 0,null),ft===null)throw Error(o(349));(Wr&30)!==0||Rf(i,n,c)}return c}function Rf(e,n,i){e.flags|=16384,e={getSnapshot:n,value:i},n=et.updateQueue,n===null?(n={lastEffect:null,stores:null},et.updateQueue=n,n.stores=[e]):(i=n.stores,i===null?n.stores=[e]:i.push(e))}function Pf(e,n,i,l){n.value=i,n.getSnapshot=l,Mf(n)&&_f(e)}function bf(e,n,i){return i(function(){Mf(n)&&_f(e)})}function Mf(e){var n=e.getSnapshot;e=e.value;try{var i=n();return!fn(e,i)}catch{return!0}}function _f(e){var n=Bn(e,1);n!==null&&gn(n,e,1,-1)}function Tf(e){var n=bn();return typeof e=="function"&&(e=e()),n.memoizedState=n.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ri,lastRenderedState:e},n.queue=e,e=e.dispatch=qy.bind(null,et,e),[n.memoizedState,e]}function Pi(e,n,i,l){return e={tag:e,create:n,destroy:i,deps:l,next:null},n=et.updateQueue,n===null?(n={lastEffect:null,stores:null},et.updateQueue=n,n.lastEffect=e.next=e):(i=n.lastEffect,i===null?n.lastEffect=e.next=e:(l=i.next,i.next=e,e.next=l,n.lastEffect=e)),e}function Of(){return Jt().memoizedState}function Va(e,n,i,l){var c=bn();et.flags|=e,c.memoizedState=Pi(1|n,i,void 0,l===void 0?null:l)}function Wa(e,n,i,l){var c=Jt();l=l===void 0?null:l;var f=void 0;if(at!==null){var y=at.memoizedState;if(f=y.destroy,l!==null&&ou(l,y.deps)){c.memoizedState=Pi(n,i,f,l);return}}et.flags|=e,c.memoizedState=Pi(1|n,i,f,l)}function Nf(e,n){return Va(8390656,8,e,n)}function uu(e,n){return Wa(2048,8,e,n)}function Df(e,n){return Wa(4,2,e,n)}function Lf(e,n){return Wa(4,4,e,n)}function Af(e,n){if(typeof n=="function")return e=e(),n(e),function(){n(null)};if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function If(e,n,i){return i=i!=null?i.concat([e]):null,Wa(4,4,Af.bind(null,n,e),i)}function cu(){}function Ff(e,n){var i=Jt();n=n===void 0?null:n;var l=i.memoizedState;return l!==null&&n!==null&&ou(n,l[1])?l[0]:(i.memoizedState=[e,n],e)}function jf(e,n){var i=Jt();n=n===void 0?null:n;var l=i.memoizedState;return l!==null&&n!==null&&ou(n,l[1])?l[0]:(e=e(),i.memoizedState=[e,n],e)}function zf(e,n,i){return(Wr&21)===0?(e.baseState&&(e.baseState=!1,Nt=!0),e.memoizedState=i):(fn(i,n)||(i=pd(),et.lanes|=i,Kr|=i,e.baseState=!0),n)}function Yy(e,n){var i=Ve;Ve=i!==0&&4>i?i:4,e(!0);var l=ru.transition;ru.transition={};try{e(!1),n()}finally{Ve=i,ru.transition=l}}function Uf(){return Jt().memoizedState}function Xy(e,n,i){var l=yr(e);if(i={lane:l,action:i,hasEagerState:!1,eagerState:null,next:null},$f(e))Hf(n,i);else if(i=yf(e,n,i,l),i!==null){var c=kt();gn(i,e,l,c),Bf(i,n,l)}}function qy(e,n,i){var l=yr(e),c={lane:l,action:i,hasEagerState:!1,eagerState:null,next:null};if($f(e))Hf(n,c);else{var f=e.alternate;if(e.lanes===0&&(f===null||f.lanes===0)&&(f=n.lastRenderedReducer,f!==null))try{var y=n.lastRenderedState,C=f(y,i);if(c.hasEagerState=!0,c.eagerState=C,fn(C,y)){var M=n.interleaved;M===null?(c.next=c,qs(n)):(c.next=M.next,M.next=c),n.interleaved=c;return}}catch{}finally{}i=yf(e,n,c,l),i!==null&&(c=kt(),gn(i,e,l,c),Bf(i,n,l))}}function $f(e){var n=e.alternate;return e===et||n!==null&&n===et}function Hf(e,n){Ci=Ba=!0;var i=e.pending;i===null?n.next=n:(n.next=i.next,i.next=n),e.pending=n}function Bf(e,n,i){if((i&4194240)!==0){var l=n.lanes;l&=e.pendingLanes,i|=l,n.lanes=i,fs(e,i)}}var Ka={readContext:qt,useCallback:yt,useContext:yt,useEffect:yt,useImperativeHandle:yt,useInsertionEffect:yt,useLayoutEffect:yt,useMemo:yt,useReducer:yt,useRef:yt,useState:yt,useDebugValue:yt,useDeferredValue:yt,useTransition:yt,useMutableSource:yt,useSyncExternalStore:yt,useId:yt,unstable_isNewReconciler:!1},Jy={readContext:qt,useCallback:function(e,n){return bn().memoizedState=[e,n===void 0?null:n],e},useContext:qt,useEffect:Nf,useImperativeHandle:function(e,n,i){return i=i!=null?i.concat([e]):null,Va(4194308,4,Af.bind(null,n,e),i)},useLayoutEffect:function(e,n){return Va(4194308,4,e,n)},useInsertionEffect:function(e,n){return Va(4,2,e,n)},useMemo:function(e,n){var i=bn();return n=n===void 0?null:n,e=e(),i.memoizedState=[e,n],e},useReducer:function(e,n,i){var l=bn();return n=i!==void 0?i(n):n,l.memoizedState=l.baseState=n,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Xy.bind(null,et,e),[l.memoizedState,e]},useRef:function(e){var n=bn();return e={current:e},n.memoizedState=e},useState:Tf,useDebugValue:cu,useDeferredValue:function(e){return bn().memoizedState=e},useTransition:function(){var e=Tf(!1),n=e[0];return e=Yy.bind(null,e[1]),bn().memoizedState=e,[n,e]},useMutableSource:function(){},useSyncExternalStore:function(e,n,i){var l=et,c=bn();if(Je){if(i===void 0)throw Error(o(407));i=i()}else{if(i=n(),ft===null)throw Error(o(349));(Wr&30)!==0||Rf(l,n,i)}c.memoizedState=i;var f={value:i,getSnapshot:n};return c.queue=f,Nf(bf.bind(null,l,f,e),[e]),l.flags|=2048,Pi(9,Pf.bind(null,l,f,i,n),void 0,null),i},useId:function(){var e=bn(),n=ft.identifierPrefix;if(Je){var i=Hn,l=$n;i=(l&~(1<<32-_t(l)-1)).toString(32)+i,n=":"+n+"R"+i,i=ki++,0<i&&(n+="H"+i.toString(32)),n+=":"}else i=Gy++,n=":"+n+"r"+i.toString(32)+":";return e.memoizedState=n},unstable_isNewReconciler:!1},Zy={readContext:qt,useCallback:Ff,useContext:qt,useEffect:uu,useImperativeHandle:If,useInsertionEffect:Df,useLayoutEffect:Lf,useMemo:jf,useReducer:lu,useRef:Of,useState:function(){return lu(Ri)},useDebugValue:cu,useDeferredValue:function(e){var n=Jt();return zf(n,at.memoizedState,e)},useTransition:function(){var e=lu(Ri)[0],n=Jt().memoizedState;return[e,n]},useMutableSource:Cf,useSyncExternalStore:kf,useId:Uf,unstable_isNewReconciler:!1},ew={readContext:qt,useCallback:Ff,useContext:qt,useEffect:uu,useImperativeHandle:If,useInsertionEffect:Df,useLayoutEffect:Lf,useMemo:jf,useReducer:su,useRef:Of,useState:function(){return su(Ri)},useDebugValue:cu,useDeferredValue:function(e){var n=Jt();return at===null?n.memoizedState=e:zf(n,at.memoizedState,e)},useTransition:function(){var e=su(Ri)[0],n=Jt().memoizedState;return[e,n]},useMutableSource:Cf,useSyncExternalStore:kf,useId:Uf,unstable_isNewReconciler:!1};function pn(e,n){if(e&&e.defaultProps){n=G({},n),e=e.defaultProps;for(var i in e)n[i]===void 0&&(n[i]=e[i]);return n}return n}function du(e,n,i,l){n=e.memoizedState,i=i(l,n),i=i==null?n:G({},n,i),e.memoizedState=i,e.lanes===0&&(e.updateQueue.baseState=i)}var Qa={isMounted:function(e){return(e=e._reactInternals)?He(e)===e:!1},enqueueSetState:function(e,n,i){e=e._reactInternals;var l=kt(),c=yr(e),f=Vn(l,c);f.payload=n,i!=null&&(f.callback=i),n=pr(e,f,c),n!==null&&(gn(n,e,c,l),za(n,e,c))},enqueueReplaceState:function(e,n,i){e=e._reactInternals;var l=kt(),c=yr(e),f=Vn(l,c);f.tag=1,f.payload=n,i!=null&&(f.callback=i),n=pr(e,f,c),n!==null&&(gn(n,e,c,l),za(n,e,c))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var i=kt(),l=yr(e),c=Vn(i,l);c.tag=2,n!=null&&(c.callback=n),n=pr(e,c,l),n!==null&&(gn(n,e,l,i),za(n,e,l))}};function Vf(e,n,i,l,c,f,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,f,y):n.prototype&&n.prototype.isPureReactComponent?!fi(i,l)||!fi(c,f):!0}function Wf(e,n,i){var l=!1,c=dr,f=n.contextType;return typeof f=="object"&&f!==null?f=qt(f):(c=Ot(n)?Ur:gt.current,l=n.contextTypes,f=(l=l!=null)?xo(e,c):dr),n=new n(i,f),e.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=Qa,e.stateNode=n,n._reactInternals=e,l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=c,e.__reactInternalMemoizedMaskedChildContext=f),n}function Kf(e,n,i,l){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(i,l),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(i,l),n.state!==e&&Qa.enqueueReplaceState(n,n.state,null)}function fu(e,n,i,l){var c=e.stateNode;c.props=i,c.state=e.memoizedState,c.refs={},Js(e);var f=n.contextType;typeof f=="object"&&f!==null?c.context=qt(f):(f=Ot(n)?Ur:gt.current,c.context=xo(e,f)),c.state=e.memoizedState,f=n.getDerivedStateFromProps,typeof f=="function"&&(du(e,n,f,i),c.state=e.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(n=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),n!==c.state&&Qa.enqueueReplaceState(c,c.state,null),Ua(e,i,c,l),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308)}function Mo(e,n){try{var i="",l=n;do i+=be(l),l=l.return;while(l);var c=i}catch(f){c=`
Error generating stack: `+f.message+`
`+f.stack}return{value:e,source:n,stack:c,digest:null}}function hu(e,n,i){return{value:e,source:null,stack:i??null,digest:n??null}}function pu(e,n){try{console.error(n.value)}catch(i){setTimeout(function(){throw i})}}var tw=typeof WeakMap=="function"?WeakMap:Map;function Qf(e,n,i){i=Vn(-1,i),i.tag=3,i.payload={element:null};var l=n.value;return i.callback=function(){el||(el=!0,_u=l),pu(e,n)},i}function Gf(e,n,i){i=Vn(-1,i),i.tag=3;var l=e.type.getDerivedStateFromError;if(typeof l=="function"){var c=n.value;i.payload=function(){return l(c)},i.callback=function(){pu(e,n)}}var f=e.stateNode;return f!==null&&typeof f.componentDidCatch=="function"&&(i.callback=function(){pu(e,n),typeof l!="function"&&(vr===null?vr=new Set([this]):vr.add(this));var y=n.stack;this.componentDidCatch(n.value,{componentStack:y!==null?y:""})}),i}function Yf(e,n,i){var l=e.pingCache;if(l===null){l=e.pingCache=new tw;var c=new Set;l.set(n,c)}else c=l.get(n),c===void 0&&(c=new Set,l.set(n,c));c.has(i)||(c.add(i),e=mw.bind(null,e,n,i),n.then(e,e))}function Xf(e){do{var n;if((n=e.tag===13)&&(n=e.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return e;e=e.return}while(e!==null);return null}function qf(e,n,i,l,c){return(e.mode&1)===0?(e===n?e.flags|=65536:(e.flags|=128,i.flags|=131072,i.flags&=-52805,i.tag===1&&(i.alternate===null?i.tag=17:(n=Vn(-1,1),n.tag=2,pr(i,n,1))),i.lanes|=1),e):(e.flags|=65536,e.lanes=c,e)}var nw=U.ReactCurrentOwner,Nt=!1;function Ct(e,n,i,l){n.child=e===null?gf(n,null,i,l):ko(n,e.child,i,l)}function Jf(e,n,i,l,c){i=i.render;var f=n.ref;return Po(n,c),l=iu(e,n,i,l,f,c),i=au(),e!==null&&!Nt?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~c,Wn(e,n,c)):(Je&&i&&Hs(n),n.flags|=1,Ct(e,n,l,c),n.child)}function Zf(e,n,i,l,c){if(e===null){var f=i.type;return typeof f=="function"&&!Iu(f)&&f.defaultProps===void 0&&i.compare===null&&i.defaultProps===void 0?(n.tag=15,n.type=f,eh(e,n,f,l,c)):(e=al(i.type,null,l,n,n.mode,c),e.ref=n.ref,e.return=n,n.child=e)}if(f=e.child,(e.lanes&c)===0){var y=f.memoizedProps;if(i=i.compare,i=i!==null?i:fi,i(y,l)&&e.ref===n.ref)return Wn(e,n,c)}return n.flags|=1,e=xr(f,l),e.ref=n.ref,e.return=n,n.child=e}function eh(e,n,i,l,c){if(e!==null){var f=e.memoizedProps;if(fi(f,l)&&e.ref===n.ref)if(Nt=!1,n.pendingProps=l=f,(e.lanes&c)!==0)(e.flags&131072)!==0&&(Nt=!0);else return n.lanes=e.lanes,Wn(e,n,c)}return mu(e,n,i,l,c)}function th(e,n,i){var l=n.pendingProps,c=l.children,f=e!==null?e.memoizedState:null;if(l.mode==="hidden")if((n.mode&1)===0)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Qe(To,Bt),Bt|=i;else{if((i&1073741824)===0)return e=f!==null?f.baseLanes|i:i,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:e,cachePool:null,transitions:null},n.updateQueue=null,Qe(To,Bt),Bt|=e,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},l=f!==null?f.baseLanes:i,Qe(To,Bt),Bt|=l}else f!==null?(l=f.baseLanes|i,n.memoizedState=null):l=i,Qe(To,Bt),Bt|=l;return Ct(e,n,c,i),n.child}function nh(e,n){var i=n.ref;(e===null&&i!==null||e!==null&&e.ref!==i)&&(n.flags|=512,n.flags|=2097152)}function mu(e,n,i,l,c){var f=Ot(i)?Ur:gt.current;return f=xo(n,f),Po(n,c),i=iu(e,n,i,l,f,c),l=au(),e!==null&&!Nt?(n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~c,Wn(e,n,c)):(Je&&l&&Hs(n),n.flags|=1,Ct(e,n,i,c),n.child)}function rh(e,n,i,l,c){if(Ot(i)){var f=!0;Oa(n)}else f=!1;if(Po(n,c),n.stateNode===null)Ya(e,n),Wf(n,i,l),fu(n,i,l,c),l=!0;else if(e===null){var y=n.stateNode,C=n.memoizedProps;y.props=C;var M=y.context,z=i.contextType;typeof z=="object"&&z!==null?z=qt(z):(z=Ot(i)?Ur:gt.current,z=xo(n,z));var Y=i.getDerivedStateFromProps,X=typeof Y=="function"||typeof y.getSnapshotBeforeUpdate=="function";X||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(C!==l||M!==z)&&Kf(n,y,l,z),hr=!1;var Q=n.memoizedState;y.state=Q,Ua(n,l,y,c),M=n.memoizedState,C!==l||Q!==M||Tt.current||hr?(typeof Y=="function"&&(du(n,i,Y,l),M=n.memoizedState),(C=hr||Vf(n,i,C,l,Q,M,z))?(X||typeof y.UNSAFE_componentWillMount!="function"&&typeof y.componentWillMount!="function"||(typeof y.componentWillMount=="function"&&y.componentWillMount(),typeof y.UNSAFE_componentWillMount=="function"&&y.UNSAFE_componentWillMount()),typeof y.componentDidMount=="function"&&(n.flags|=4194308)):(typeof y.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=l,n.memoizedState=M),y.props=l,y.state=M,y.context=z,l=C):(typeof y.componentDidMount=="function"&&(n.flags|=4194308),l=!1)}else{y=n.stateNode,wf(e,n),C=n.memoizedProps,z=n.type===n.elementType?C:pn(n.type,C),y.props=z,X=n.pendingProps,Q=y.context,M=i.contextType,typeof M=="object"&&M!==null?M=qt(M):(M=Ot(i)?Ur:gt.current,M=xo(n,M));var re=i.getDerivedStateFromProps;(Y=typeof re=="function"||typeof y.getSnapshotBeforeUpdate=="function")||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(C!==X||Q!==M)&&Kf(n,y,l,M),hr=!1,Q=n.memoizedState,y.state=Q,Ua(n,l,y,c);var de=n.memoizedState;C!==X||Q!==de||Tt.current||hr?(typeof re=="function"&&(du(n,i,re,l),de=n.memoizedState),(z=hr||Vf(n,i,z,l,Q,de,M)||!1)?(Y||typeof y.UNSAFE_componentWillUpdate!="function"&&typeof y.componentWillUpdate!="function"||(typeof y.componentWillUpdate=="function"&&y.componentWillUpdate(l,de,M),typeof y.UNSAFE_componentWillUpdate=="function"&&y.UNSAFE_componentWillUpdate(l,de,M)),typeof y.componentDidUpdate=="function"&&(n.flags|=4),typeof y.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof y.componentDidUpdate!="function"||C===e.memoizedProps&&Q===e.memoizedState||(n.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||C===e.memoizedProps&&Q===e.memoizedState||(n.flags|=1024),n.memoizedProps=l,n.memoizedState=de),y.props=l,y.state=de,y.context=M,l=z):(typeof y.componentDidUpdate!="function"||C===e.memoizedProps&&Q===e.memoizedState||(n.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||C===e.memoizedProps&&Q===e.memoizedState||(n.flags|=1024),l=!1)}return vu(e,n,i,l,f,c)}function vu(e,n,i,l,c,f){nh(e,n);var y=(n.flags&128)!==0;if(!l&&!y)return c&&sf(n,i,!1),Wn(e,n,f);l=n.stateNode,nw.current=n;var C=y&&typeof i.getDerivedStateFromError!="function"?null:l.render();return n.flags|=1,e!==null&&y?(n.child=ko(n,e.child,null,f),n.child=ko(n,null,C,f)):Ct(e,n,C,f),n.memoizedState=l.state,c&&sf(n,i,!0),n.child}function oh(e){var n=e.stateNode;n.pendingContext?af(e,n.pendingContext,n.pendingContext!==n.context):n.context&&af(e,n.context,!1),Zs(e,n.containerInfo)}function ih(e,n,i,l,c){return Co(),Ks(c),n.flags|=256,Ct(e,n,i,l),n.child}var gu={dehydrated:null,treeContext:null,retryLane:0};function yu(e){return{baseLanes:e,cachePool:null,transitions:null}}function ah(e,n,i){var l=n.pendingProps,c=Ze.current,f=!1,y=(n.flags&128)!==0,C;if((C=y)||(C=e!==null&&e.memoizedState===null?!1:(c&2)!==0),C?(f=!0,n.flags&=-129):(e===null||e.memoizedState!==null)&&(c|=1),Qe(Ze,c&1),e===null)return Ws(n),e=n.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((n.mode&1)===0?n.lanes=1:e.data==="$!"?n.lanes=8:n.lanes=1073741824,null):(y=l.children,e=l.fallback,f?(l=n.mode,f=n.child,y={mode:"hidden",children:y},(l&1)===0&&f!==null?(f.childLanes=0,f.pendingProps=y):f=ll(y,l,0,null),e=Xr(e,l,i,null),f.return=n,e.return=n,f.sibling=e,n.child=f,n.child.memoizedState=yu(i),n.memoizedState=gu,e):wu(n,y));if(c=e.memoizedState,c!==null&&(C=c.dehydrated,C!==null))return rw(e,n,y,l,C,c,i);if(f){f=l.fallback,y=n.mode,c=e.child,C=c.sibling;var M={mode:"hidden",children:l.children};return(y&1)===0&&n.child!==c?(l=n.child,l.childLanes=0,l.pendingProps=M,n.deletions=null):(l=xr(c,M),l.subtreeFlags=c.subtreeFlags&14680064),C!==null?f=xr(C,f):(f=Xr(f,y,i,null),f.flags|=2),f.return=n,l.return=n,l.sibling=f,n.child=l,l=f,f=n.child,y=e.child.memoizedState,y=y===null?yu(i):{baseLanes:y.baseLanes|i,cachePool:null,transitions:y.transitions},f.memoizedState=y,f.childLanes=e.childLanes&~i,n.memoizedState=gu,l}return f=e.child,e=f.sibling,l=xr(f,{mode:"visible",children:l.children}),(n.mode&1)===0&&(l.lanes=i),l.return=n,l.sibling=null,e!==null&&(i=n.deletions,i===null?(n.deletions=[e],n.flags|=16):i.push(e)),n.child=l,n.memoizedState=null,l}function wu(e,n){return n=ll({mode:"visible",children:n},e.mode,0,null),n.return=e,e.child=n}function Ga(e,n,i,l){return l!==null&&Ks(l),ko(n,e.child,null,i),e=wu(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function rw(e,n,i,l,c,f,y){if(i)return n.flags&256?(n.flags&=-257,l=hu(Error(o(422))),Ga(e,n,y,l)):n.memoizedState!==null?(n.child=e.child,n.flags|=128,null):(f=l.fallback,c=n.mode,l=ll({mode:"visible",children:l.children},c,0,null),f=Xr(f,c,y,null),f.flags|=2,l.return=n,f.return=n,l.sibling=f,n.child=l,(n.mode&1)!==0&&ko(n,e.child,null,y),n.child.memoizedState=yu(y),n.memoizedState=gu,f);if((n.mode&1)===0)return Ga(e,n,y,null);if(c.data==="$!"){if(l=c.nextSibling&&c.nextSibling.dataset,l)var C=l.dgst;return l=C,f=Error(o(419)),l=hu(f,l,void 0),Ga(e,n,y,l)}if(C=(y&e.childLanes)!==0,Nt||C){if(l=ft,l!==null){switch(y&-y){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}c=(c&(l.suspendedLanes|y))!==0?0:c,c!==0&&c!==f.retryLane&&(f.retryLane=c,Bn(e,c),gn(l,e,c,-1))}return Au(),l=hu(Error(o(421))),Ga(e,n,y,l)}return c.data==="$?"?(n.flags|=128,n.child=e.child,n=vw.bind(null,e),c._reactRetry=n,null):(e=f.treeContext,Ht=ur(c.nextSibling),$t=n,Je=!0,hn=null,e!==null&&(Yt[Xt++]=$n,Yt[Xt++]=Hn,Yt[Xt++]=$r,$n=e.id,Hn=e.overflow,$r=n),n=wu(n,l.children),n.flags|=4096,n)}function lh(e,n,i){e.lanes|=n;var l=e.alternate;l!==null&&(l.lanes|=n),Xs(e.return,n,i)}function xu(e,n,i,l,c){var f=e.memoizedState;f===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:l,tail:i,tailMode:c}:(f.isBackwards=n,f.rendering=null,f.renderingStartTime=0,f.last=l,f.tail=i,f.tailMode=c)}function sh(e,n,i){var l=n.pendingProps,c=l.revealOrder,f=l.tail;if(Ct(e,n,l.children,i),l=Ze.current,(l&2)!==0)l=l&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&lh(e,i,n);else if(e.tag===19)lh(e,i,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break e;for(;e.sibling===null;){if(e.return===null||e.return===n)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}if(Qe(Ze,l),(n.mode&1)===0)n.memoizedState=null;else switch(c){case"forwards":for(i=n.child,c=null;i!==null;)e=i.alternate,e!==null&&$a(e)===null&&(c=i),i=i.sibling;i=c,i===null?(c=n.child,n.child=null):(c=i.sibling,i.sibling=null),xu(n,!1,c,i,f);break;case"backwards":for(i=null,c=n.child,n.child=null;c!==null;){if(e=c.alternate,e!==null&&$a(e)===null){n.child=c;break}e=c.sibling,c.sibling=i,i=c,c=e}xu(n,!0,i,null,f);break;case"together":xu(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function Ya(e,n){(n.mode&1)===0&&e!==null&&(e.alternate=null,n.alternate=null,n.flags|=2)}function Wn(e,n,i){if(e!==null&&(n.dependencies=e.dependencies),Kr|=n.lanes,(i&n.childLanes)===0)return null;if(e!==null&&n.child!==e.child)throw Error(o(153));if(n.child!==null){for(e=n.child,i=xr(e,e.pendingProps),n.child=i,i.return=n;e.sibling!==null;)e=e.sibling,i=i.sibling=xr(e,e.pendingProps),i.return=n;i.sibling=null}return n.child}function ow(e,n,i){switch(n.tag){case 3:oh(n),Co();break;case 5:Ef(n);break;case 1:Ot(n.type)&&Oa(n);break;case 4:Zs(n,n.stateNode.containerInfo);break;case 10:var l=n.type._context,c=n.memoizedProps.value;Qe(Fa,l._currentValue),l._currentValue=c;break;case 13:if(l=n.memoizedState,l!==null)return l.dehydrated!==null?(Qe(Ze,Ze.current&1),n.flags|=128,null):(i&n.child.childLanes)!==0?ah(e,n,i):(Qe(Ze,Ze.current&1),e=Wn(e,n,i),e!==null?e.sibling:null);Qe(Ze,Ze.current&1);break;case 19:if(l=(i&n.childLanes)!==0,(e.flags&128)!==0){if(l)return sh(e,n,i);n.flags|=128}if(c=n.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),Qe(Ze,Ze.current),l)break;return null;case 22:case 23:return n.lanes=0,th(e,n,i)}return Wn(e,n,i)}var uh,Su,ch,dh;uh=function(e,n){for(var i=n.child;i!==null;){if(i.tag===5||i.tag===6)e.appendChild(i.stateNode);else if(i.tag!==4&&i.child!==null){i.child.return=i,i=i.child;continue}if(i===n)break;for(;i.sibling===null;){if(i.return===null||i.return===n)return;i=i.return}i.sibling.return=i.return,i=i.sibling}},Su=function(){},ch=function(e,n,i,l){var c=e.memoizedProps;if(c!==l){e=n.stateNode,Vr(Pn.current);var f=null;switch(i){case"input":c=sn(e,c),l=sn(e,l),f=[];break;case"select":c=G({},c,{value:void 0}),l=G({},l,{value:void 0}),f=[];break;case"textarea":c=un(e,c),l=un(e,l),f=[];break;default:typeof c.onClick!="function"&&typeof l.onClick=="function"&&(e.onclick=Ma)}lo(i,l);var y;i=null;for(z in c)if(!l.hasOwnProperty(z)&&c.hasOwnProperty(z)&&c[z]!=null)if(z==="style"){var C=c[z];for(y in C)C.hasOwnProperty(y)&&(i||(i={}),i[y]="")}else z!=="dangerouslySetInnerHTML"&&z!=="children"&&z!=="suppressContentEditableWarning"&&z!=="suppressHydrationWarning"&&z!=="autoFocus"&&(s.hasOwnProperty(z)?f||(f=[]):(f=f||[]).push(z,null));for(z in l){var M=l[z];if(C=c?.[z],l.hasOwnProperty(z)&&M!==C&&(M!=null||C!=null))if(z==="style")if(C){for(y in C)!C.hasOwnProperty(y)||M&&M.hasOwnProperty(y)||(i||(i={}),i[y]="");for(y in M)M.hasOwnProperty(y)&&C[y]!==M[y]&&(i||(i={}),i[y]=M[y])}else i||(f||(f=[]),f.push(z,i)),i=M;else z==="dangerouslySetInnerHTML"?(M=M?M.__html:void 0,C=C?C.__html:void 0,M!=null&&C!==M&&(f=f||[]).push(z,M)):z==="children"?typeof M!="string"&&typeof M!="number"||(f=f||[]).push(z,""+M):z!=="suppressContentEditableWarning"&&z!=="suppressHydrationWarning"&&(s.hasOwnProperty(z)?(M!=null&&z==="onScroll"&&Ge("scroll",e),f||C===M||(f=[])):(f=f||[]).push(z,M))}i&&(f=f||[]).push("style",i);var z=f;(n.updateQueue=z)&&(n.flags|=4)}},dh=function(e,n,i,l){i!==l&&(n.flags|=4)};function bi(e,n){if(!Je)switch(e.tailMode){case"hidden":n=e.tail;for(var i=null;n!==null;)n.alternate!==null&&(i=n),n=n.sibling;i===null?e.tail=null:i.sibling=null;break;case"collapsed":i=e.tail;for(var l=null;i!==null;)i.alternate!==null&&(l=i),i=i.sibling;l===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function wt(e){var n=e.alternate!==null&&e.alternate.child===e.child,i=0,l=0;if(n)for(var c=e.child;c!==null;)i|=c.lanes|c.childLanes,l|=c.subtreeFlags&14680064,l|=c.flags&14680064,c.return=e,c=c.sibling;else for(c=e.child;c!==null;)i|=c.lanes|c.childLanes,l|=c.subtreeFlags,l|=c.flags,c.return=e,c=c.sibling;return e.subtreeFlags|=l,e.childLanes=i,n}function iw(e,n,i){var l=n.pendingProps;switch(Bs(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return wt(n),null;case 1:return Ot(n.type)&&Ta(),wt(n),null;case 3:return l=n.stateNode,bo(),Ye(Tt),Ye(gt),nu(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Aa(n)?n.flags|=4:e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,hn!==null&&(Nu(hn),hn=null))),Su(e,n),wt(n),null;case 5:eu(n);var c=Vr(Ei.current);if(i=n.type,e!==null&&n.stateNode!=null)ch(e,n,i,l,c),e.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!l){if(n.stateNode===null)throw Error(o(166));return wt(n),null}if(e=Vr(Pn.current),Aa(n)){l=n.stateNode,i=n.type;var f=n.memoizedProps;switch(l[Rn]=n,l[gi]=f,e=(n.mode&1)!==0,i){case"dialog":Ge("cancel",l),Ge("close",l);break;case"iframe":case"object":case"embed":Ge("load",l);break;case"video":case"audio":for(c=0;c<pi.length;c++)Ge(pi[c],l);break;case"source":Ge("error",l);break;case"img":case"image":case"link":Ge("error",l),Ge("load",l);break;case"details":Ge("toggle",l);break;case"input":ia(l,f),Ge("invalid",l);break;case"select":l._wrapperState={wasMultiple:!!f.multiple},Ge("invalid",l);break;case"textarea":Jn(l,f),Ge("invalid",l)}lo(i,f),c=null;for(var y in f)if(f.hasOwnProperty(y)){var C=f[y];y==="children"?typeof C=="string"?l.textContent!==C&&(f.suppressHydrationWarning!==!0&&ba(l.textContent,C,e),c=["children",C]):typeof C=="number"&&l.textContent!==""+C&&(f.suppressHydrationWarning!==!0&&ba(l.textContent,C,e),c=["children",""+C]):s.hasOwnProperty(y)&&C!=null&&y==="onScroll"&&Ge("scroll",l)}switch(i){case"input":pt(l),la(l,f,!0);break;case"textarea":pt(l),io(l);break;case"select":case"option":break;default:typeof f.onClick=="function"&&(l.onclick=Ma)}l=c,n.updateQueue=l,l!==null&&(n.flags|=4)}else{y=c.nodeType===9?c:c.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=cn(i)),e==="http://www.w3.org/1999/xhtml"?i==="script"?(e=y.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof l.is=="string"?e=y.createElement(i,{is:l.is}):(e=y.createElement(i),i==="select"&&(y=e,l.multiple?y.multiple=!0:l.size&&(y.size=l.size))):e=y.createElementNS(e,i),e[Rn]=n,e[gi]=l,uh(e,n,!1,!1),n.stateNode=e;e:{switch(y=Zn(i,l),i){case"dialog":Ge("cancel",e),Ge("close",e),c=l;break;case"iframe":case"object":case"embed":Ge("load",e),c=l;break;case"video":case"audio":for(c=0;c<pi.length;c++)Ge(pi[c],e);c=l;break;case"source":Ge("error",e),c=l;break;case"img":case"image":case"link":Ge("error",e),Ge("load",e),c=l;break;case"details":Ge("toggle",e),c=l;break;case"input":ia(e,l),c=sn(e,l),Ge("invalid",e);break;case"option":c=l;break;case"select":e._wrapperState={wasMultiple:!!l.multiple},c=G({},l,{value:void 0}),Ge("invalid",e);break;case"textarea":Jn(e,l),c=un(e,l),Ge("invalid",e);break;default:c=l}lo(i,c),C=c;for(f in C)if(C.hasOwnProperty(f)){var M=C[f];f==="style"?Xo(e,M):f==="dangerouslySetInnerHTML"?(M=M?M.__html:void 0,M!=null&&ao(e,M)):f==="children"?typeof M=="string"?(i!=="textarea"||M!=="")&&Dr(e,M):typeof M=="number"&&Dr(e,""+M):f!=="suppressContentEditableWarning"&&f!=="suppressHydrationWarning"&&f!=="autoFocus"&&(s.hasOwnProperty(f)?M!=null&&f==="onScroll"&&Ge("scroll",e):M!=null&&L(e,f,M,y))}switch(i){case"input":pt(e),la(e,l,!1);break;case"textarea":pt(e),io(e);break;case"option":l.value!=null&&e.setAttribute("value",""+Oe(l.value));break;case"select":e.multiple=!!l.multiple,f=l.value,f!=null?qn(e,!!l.multiple,f,!1):l.defaultValue!=null&&qn(e,!!l.multiple,l.defaultValue,!0);break;default:typeof c.onClick=="function"&&(e.onclick=Ma)}switch(i){case"button":case"input":case"select":case"textarea":l=!!l.autoFocus;break e;case"img":l=!0;break e;default:l=!1}}l&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return wt(n),null;case 6:if(e&&n.stateNode!=null)dh(e,n,e.memoizedProps,l);else{if(typeof l!="string"&&n.stateNode===null)throw Error(o(166));if(i=Vr(Ei.current),Vr(Pn.current),Aa(n)){if(l=n.stateNode,i=n.memoizedProps,l[Rn]=n,(f=l.nodeValue!==i)&&(e=$t,e!==null))switch(e.tag){case 3:ba(l.nodeValue,i,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ba(l.nodeValue,i,(e.mode&1)!==0)}f&&(n.flags|=4)}else l=(i.nodeType===9?i:i.ownerDocument).createTextNode(l),l[Rn]=n,n.stateNode=l}return wt(n),null;case 13:if(Ye(Ze),l=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(Je&&Ht!==null&&(n.mode&1)!==0&&(n.flags&128)===0)pf(),Co(),n.flags|=98560,f=!1;else if(f=Aa(n),l!==null&&l.dehydrated!==null){if(e===null){if(!f)throw Error(o(318));if(f=n.memoizedState,f=f!==null?f.dehydrated:null,!f)throw Error(o(317));f[Rn]=n}else Co(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;wt(n),f=!1}else hn!==null&&(Nu(hn),hn=null),f=!0;if(!f)return n.flags&65536?n:null}return(n.flags&128)!==0?(n.lanes=i,n):(l=l!==null,l!==(e!==null&&e.memoizedState!==null)&&l&&(n.child.flags|=8192,(n.mode&1)!==0&&(e===null||(Ze.current&1)!==0?lt===0&&(lt=3):Au())),n.updateQueue!==null&&(n.flags|=4),wt(n),null);case 4:return bo(),Su(e,n),e===null&&mi(n.stateNode.containerInfo),wt(n),null;case 10:return Ys(n.type._context),wt(n),null;case 17:return Ot(n.type)&&Ta(),wt(n),null;case 19:if(Ye(Ze),f=n.memoizedState,f===null)return wt(n),null;if(l=(n.flags&128)!==0,y=f.rendering,y===null)if(l)bi(f,!1);else{if(lt!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(y=$a(e),y!==null){for(n.flags|=128,bi(f,!1),l=y.updateQueue,l!==null&&(n.updateQueue=l,n.flags|=4),n.subtreeFlags=0,l=i,i=n.child;i!==null;)f=i,e=l,f.flags&=14680066,y=f.alternate,y===null?(f.childLanes=0,f.lanes=e,f.child=null,f.subtreeFlags=0,f.memoizedProps=null,f.memoizedState=null,f.updateQueue=null,f.dependencies=null,f.stateNode=null):(f.childLanes=y.childLanes,f.lanes=y.lanes,f.child=y.child,f.subtreeFlags=0,f.deletions=null,f.memoizedProps=y.memoizedProps,f.memoizedState=y.memoizedState,f.updateQueue=y.updateQueue,f.type=y.type,e=y.dependencies,f.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),i=i.sibling;return Qe(Ze,Ze.current&1|2),n.child}e=e.sibling}f.tail!==null&&Ke()>Oo&&(n.flags|=128,l=!0,bi(f,!1),n.lanes=4194304)}else{if(!l)if(e=$a(y),e!==null){if(n.flags|=128,l=!0,i=e.updateQueue,i!==null&&(n.updateQueue=i,n.flags|=4),bi(f,!0),f.tail===null&&f.tailMode==="hidden"&&!y.alternate&&!Je)return wt(n),null}else 2*Ke()-f.renderingStartTime>Oo&&i!==1073741824&&(n.flags|=128,l=!0,bi(f,!1),n.lanes=4194304);f.isBackwards?(y.sibling=n.child,n.child=y):(i=f.last,i!==null?i.sibling=y:n.child=y,f.last=y)}return f.tail!==null?(n=f.tail,f.rendering=n,f.tail=n.sibling,f.renderingStartTime=Ke(),n.sibling=null,i=Ze.current,Qe(Ze,l?i&1|2:i&1),n):(wt(n),null);case 22:case 23:return Lu(),l=n.memoizedState!==null,e!==null&&e.memoizedState!==null!==l&&(n.flags|=8192),l&&(n.mode&1)!==0?(Bt&1073741824)!==0&&(wt(n),n.subtreeFlags&6&&(n.flags|=8192)):wt(n),null;case 24:return null;case 25:return null}throw Error(o(156,n.tag))}function aw(e,n){switch(Bs(n),n.tag){case 1:return Ot(n.type)&&Ta(),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return bo(),Ye(Tt),Ye(gt),nu(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 5:return eu(n),null;case 13:if(Ye(Ze),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(o(340));Co()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return Ye(Ze),null;case 4:return bo(),null;case 10:return Ys(n.type._context),null;case 22:case 23:return Lu(),null;case 24:return null;default:return null}}var Xa=!1,xt=!1,lw=typeof WeakSet=="function"?WeakSet:Set,le=null;function _o(e,n){var i=e.ref;if(i!==null)if(typeof i=="function")try{i(null)}catch(l){tt(e,n,l)}else i.current=null}function Eu(e,n,i){try{i()}catch(l){tt(e,n,l)}}var fh=!1;function sw(e,n){if(Ls=va,e=Bd(),Ps(e)){if("selectionStart"in e)var i={start:e.selectionStart,end:e.selectionEnd};else e:{i=(i=e.ownerDocument)&&i.defaultView||window;var l=i.getSelection&&i.getSelection();if(l&&l.rangeCount!==0){i=l.anchorNode;var c=l.anchorOffset,f=l.focusNode;l=l.focusOffset;try{i.nodeType,f.nodeType}catch{i=null;break e}var y=0,C=-1,M=-1,z=0,Y=0,X=e,Q=null;t:for(;;){for(var re;X!==i||c!==0&&X.nodeType!==3||(C=y+c),X!==f||l!==0&&X.nodeType!==3||(M=y+l),X.nodeType===3&&(y+=X.nodeValue.length),(re=X.firstChild)!==null;)Q=X,X=re;for(;;){if(X===e)break t;if(Q===i&&++z===c&&(C=y),Q===f&&++Y===l&&(M=y),(re=X.nextSibling)!==null)break;X=Q,Q=X.parentNode}X=re}i=C===-1||M===-1?null:{start:C,end:M}}else i=null}i=i||{start:0,end:0}}else i=null;for(As={focusedElem:e,selectionRange:i},va=!1,le=n;le!==null;)if(n=le,e=n.child,(n.subtreeFlags&1028)!==0&&e!==null)e.return=n,le=e;else for(;le!==null;){n=le;try{var de=n.alternate;if((n.flags&1024)!==0)switch(n.tag){case 0:case 11:case 15:break;case 1:if(de!==null){var pe=de.memoizedProps,rt=de.memoizedState,I=n.stateNode,O=I.getSnapshotBeforeUpdate(n.elementType===n.type?pe:pn(n.type,pe),rt);I.__reactInternalSnapshotBeforeUpdate=O}break;case 3:var F=n.stateNode.containerInfo;F.nodeType===1?F.textContent="":F.nodeType===9&&F.documentElement&&F.removeChild(F.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(J){tt(n,n.return,J)}if(e=n.sibling,e!==null){e.return=n.return,le=e;break}le=n.return}return de=fh,fh=!1,de}function Mi(e,n,i){var l=n.updateQueue;if(l=l!==null?l.lastEffect:null,l!==null){var c=l=l.next;do{if((c.tag&e)===e){var f=c.destroy;c.destroy=void 0,f!==void 0&&Eu(n,i,f)}c=c.next}while(c!==l)}}function qa(e,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var i=n=n.next;do{if((i.tag&e)===e){var l=i.create;i.destroy=l()}i=i.next}while(i!==n)}}function Cu(e){var n=e.ref;if(n!==null){var i=e.stateNode;switch(e.tag){case 5:e=i;break;default:e=i}typeof n=="function"?n(e):n.current=e}}function hh(e){var n=e.alternate;n!==null&&(e.alternate=null,hh(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&(delete n[Rn],delete n[gi],delete n[zs],delete n[Vy],delete n[Wy])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ph(e){return e.tag===5||e.tag===3||e.tag===4}function mh(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||ph(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ku(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.nodeType===8?i.parentNode.insertBefore(e,n):i.insertBefore(e,n):(i.nodeType===8?(n=i.parentNode,n.insertBefore(e,i)):(n=i,n.appendChild(e)),i=i._reactRootContainer,i!=null||n.onclick!==null||(n.onclick=Ma));else if(l!==4&&(e=e.child,e!==null))for(ku(e,n,i),e=e.sibling;e!==null;)ku(e,n,i),e=e.sibling}function Ru(e,n,i){var l=e.tag;if(l===5||l===6)e=e.stateNode,n?i.insertBefore(e,n):i.appendChild(e);else if(l!==4&&(e=e.child,e!==null))for(Ru(e,n,i),e=e.sibling;e!==null;)Ru(e,n,i),e=e.sibling}var mt=null,mn=!1;function mr(e,n,i){for(i=i.child;i!==null;)vh(e,n,i),i=i.sibling}function vh(e,n,i){if(Be&&typeof Be.onCommitFiberUnmount=="function")try{Be.onCommitFiberUnmount(kn,i)}catch{}switch(i.tag){case 5:xt||_o(i,n);case 6:var l=mt,c=mn;mt=null,mr(e,n,i),mt=l,mn=c,mt!==null&&(mn?(e=mt,i=i.stateNode,e.nodeType===8?e.parentNode.removeChild(i):e.removeChild(i)):mt.removeChild(i.stateNode));break;case 18:mt!==null&&(mn?(e=mt,i=i.stateNode,e.nodeType===8?js(e.parentNode,i):e.nodeType===1&&js(e,i),ai(e)):js(mt,i.stateNode));break;case 4:l=mt,c=mn,mt=i.stateNode.containerInfo,mn=!0,mr(e,n,i),mt=l,mn=c;break;case 0:case 11:case 14:case 15:if(!xt&&(l=i.updateQueue,l!==null&&(l=l.lastEffect,l!==null))){c=l=l.next;do{var f=c,y=f.destroy;f=f.tag,y!==void 0&&((f&2)!==0||(f&4)!==0)&&Eu(i,n,y),c=c.next}while(c!==l)}mr(e,n,i);break;case 1:if(!xt&&(_o(i,n),l=i.stateNode,typeof l.componentWillUnmount=="function"))try{l.props=i.memoizedProps,l.state=i.memoizedState,l.componentWillUnmount()}catch(C){tt(i,n,C)}mr(e,n,i);break;case 21:mr(e,n,i);break;case 22:i.mode&1?(xt=(l=xt)||i.memoizedState!==null,mr(e,n,i),xt=l):mr(e,n,i);break;default:mr(e,n,i)}}function gh(e){var n=e.updateQueue;if(n!==null){e.updateQueue=null;var i=e.stateNode;i===null&&(i=e.stateNode=new lw),n.forEach(function(l){var c=gw.bind(null,e,l);i.has(l)||(i.add(l),l.then(c,c))})}}function vn(e,n){var i=n.deletions;if(i!==null)for(var l=0;l<i.length;l++){var c=i[l];try{var f=e,y=n,C=y;e:for(;C!==null;){switch(C.tag){case 5:mt=C.stateNode,mn=!1;break e;case 3:mt=C.stateNode.containerInfo,mn=!0;break e;case 4:mt=C.stateNode.containerInfo,mn=!0;break e}C=C.return}if(mt===null)throw Error(o(160));vh(f,y,c),mt=null,mn=!1;var M=c.alternate;M!==null&&(M.return=null),c.return=null}catch(z){tt(c,n,z)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)yh(n,e),n=n.sibling}function yh(e,n){var i=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(vn(n,e),Mn(e),l&4){try{Mi(3,e,e.return),qa(3,e)}catch(pe){tt(e,e.return,pe)}try{Mi(5,e,e.return)}catch(pe){tt(e,e.return,pe)}}break;case 1:vn(n,e),Mn(e),l&512&&i!==null&&_o(i,i.return);break;case 5:if(vn(n,e),Mn(e),l&512&&i!==null&&_o(i,i.return),e.flags&32){var c=e.stateNode;try{Dr(c,"")}catch(pe){tt(e,e.return,pe)}}if(l&4&&(c=e.stateNode,c!=null)){var f=e.memoizedProps,y=i!==null?i.memoizedProps:f,C=e.type,M=e.updateQueue;if(e.updateQueue=null,M!==null)try{C==="input"&&f.type==="radio"&&f.name!=null&&aa(c,f),Zn(C,y);var z=Zn(C,f);for(y=0;y<M.length;y+=2){var Y=M[y],X=M[y+1];Y==="style"?Xo(c,X):Y==="dangerouslySetInnerHTML"?ao(c,X):Y==="children"?Dr(c,X):L(c,Y,X,z)}switch(C){case"input":oo(c,f);break;case"textarea":Go(c,f);break;case"select":var Q=c._wrapperState.wasMultiple;c._wrapperState.wasMultiple=!!f.multiple;var re=f.value;re!=null?qn(c,!!f.multiple,re,!1):Q!==!!f.multiple&&(f.defaultValue!=null?qn(c,!!f.multiple,f.defaultValue,!0):qn(c,!!f.multiple,f.multiple?[]:"",!1))}c[gi]=f}catch(pe){tt(e,e.return,pe)}}break;case 6:if(vn(n,e),Mn(e),l&4){if(e.stateNode===null)throw Error(o(162));c=e.stateNode,f=e.memoizedProps;try{c.nodeValue=f}catch(pe){tt(e,e.return,pe)}}break;case 3:if(vn(n,e),Mn(e),l&4&&i!==null&&i.memoizedState.isDehydrated)try{ai(n.containerInfo)}catch(pe){tt(e,e.return,pe)}break;case 4:vn(n,e),Mn(e);break;case 13:vn(n,e),Mn(e),c=e.child,c.flags&8192&&(f=c.memoizedState!==null,c.stateNode.isHidden=f,!f||c.alternate!==null&&c.alternate.memoizedState!==null||(Mu=Ke())),l&4&&gh(e);break;case 22:if(Y=i!==null&&i.memoizedState!==null,e.mode&1?(xt=(z=xt)||Y,vn(n,e),xt=z):vn(n,e),Mn(e),l&8192){if(z=e.memoizedState!==null,(e.stateNode.isHidden=z)&&!Y&&(e.mode&1)!==0)for(le=e,Y=e.child;Y!==null;){for(X=le=Y;le!==null;){switch(Q=le,re=Q.child,Q.tag){case 0:case 11:case 14:case 15:Mi(4,Q,Q.return);break;case 1:_o(Q,Q.return);var de=Q.stateNode;if(typeof de.componentWillUnmount=="function"){l=Q,i=Q.return;try{n=l,de.props=n.memoizedProps,de.state=n.memoizedState,de.componentWillUnmount()}catch(pe){tt(l,i,pe)}}break;case 5:_o(Q,Q.return);break;case 22:if(Q.memoizedState!==null){Sh(X);continue}}re!==null?(re.return=Q,le=re):Sh(X)}Y=Y.sibling}e:for(Y=null,X=e;;){if(X.tag===5){if(Y===null){Y=X;try{c=X.stateNode,z?(f=c.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none"):(C=X.stateNode,M=X.memoizedProps.style,y=M!=null&&M.hasOwnProperty("display")?M.display:null,C.style.display=Yo("display",y))}catch(pe){tt(e,e.return,pe)}}}else if(X.tag===6){if(Y===null)try{X.stateNode.nodeValue=z?"":X.memoizedProps}catch(pe){tt(e,e.return,pe)}}else if((X.tag!==22&&X.tag!==23||X.memoizedState===null||X===e)&&X.child!==null){X.child.return=X,X=X.child;continue}if(X===e)break e;for(;X.sibling===null;){if(X.return===null||X.return===e)break e;Y===X&&(Y=null),X=X.return}Y===X&&(Y=null),X.sibling.return=X.return,X=X.sibling}}break;case 19:vn(n,e),Mn(e),l&4&&gh(e);break;case 21:break;default:vn(n,e),Mn(e)}}function Mn(e){var n=e.flags;if(n&2){try{e:{for(var i=e.return;i!==null;){if(ph(i)){var l=i;break e}i=i.return}throw Error(o(160))}switch(l.tag){case 5:var c=l.stateNode;l.flags&32&&(Dr(c,""),l.flags&=-33);var f=mh(e);Ru(e,f,c);break;case 3:case 4:var y=l.stateNode.containerInfo,C=mh(e);ku(e,C,y);break;default:throw Error(o(161))}}catch(M){tt(e,e.return,M)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function uw(e,n,i){le=e,wh(e)}function wh(e,n,i){for(var l=(e.mode&1)!==0;le!==null;){var c=le,f=c.child;if(c.tag===22&&l){var y=c.memoizedState!==null||Xa;if(!y){var C=c.alternate,M=C!==null&&C.memoizedState!==null||xt;C=Xa;var z=xt;if(Xa=y,(xt=M)&&!z)for(le=c;le!==null;)y=le,M=y.child,y.tag===22&&y.memoizedState!==null?Eh(c):M!==null?(M.return=y,le=M):Eh(c);for(;f!==null;)le=f,wh(f),f=f.sibling;le=c,Xa=C,xt=z}xh(e)}else(c.subtreeFlags&8772)!==0&&f!==null?(f.return=c,le=f):xh(e)}}function xh(e){for(;le!==null;){var n=le;if((n.flags&8772)!==0){var i=n.alternate;try{if((n.flags&8772)!==0)switch(n.tag){case 0:case 11:case 15:xt||qa(5,n);break;case 1:var l=n.stateNode;if(n.flags&4&&!xt)if(i===null)l.componentDidMount();else{var c=n.elementType===n.type?i.memoizedProps:pn(n.type,i.memoizedProps);l.componentDidUpdate(c,i.memoizedState,l.__reactInternalSnapshotBeforeUpdate)}var f=n.updateQueue;f!==null&&Sf(n,f,l);break;case 3:var y=n.updateQueue;if(y!==null){if(i=null,n.child!==null)switch(n.child.tag){case 5:i=n.child.stateNode;break;case 1:i=n.child.stateNode}Sf(n,y,i)}break;case 5:var C=n.stateNode;if(i===null&&n.flags&4){i=C;var M=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":M.autoFocus&&i.focus();break;case"img":M.src&&(i.src=M.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var z=n.alternate;if(z!==null){var Y=z.memoizedState;if(Y!==null){var X=Y.dehydrated;X!==null&&ai(X)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}xt||n.flags&512&&Cu(n)}catch(Q){tt(n,n.return,Q)}}if(n===e){le=null;break}if(i=n.sibling,i!==null){i.return=n.return,le=i;break}le=n.return}}function Sh(e){for(;le!==null;){var n=le;if(n===e){le=null;break}var i=n.sibling;if(i!==null){i.return=n.return,le=i;break}le=n.return}}function Eh(e){for(;le!==null;){var n=le;try{switch(n.tag){case 0:case 11:case 15:var i=n.return;try{qa(4,n)}catch(M){tt(n,i,M)}break;case 1:var l=n.stateNode;if(typeof l.componentDidMount=="function"){var c=n.return;try{l.componentDidMount()}catch(M){tt(n,c,M)}}var f=n.return;try{Cu(n)}catch(M){tt(n,f,M)}break;case 5:var y=n.return;try{Cu(n)}catch(M){tt(n,y,M)}}}catch(M){tt(n,n.return,M)}if(n===e){le=null;break}var C=n.sibling;if(C!==null){C.return=n.return,le=C;break}le=n.return}}var cw=Math.ceil,Ja=U.ReactCurrentDispatcher,Pu=U.ReactCurrentOwner,Zt=U.ReactCurrentBatchConfig,Ue=0,ft=null,ot=null,vt=0,Bt=0,To=cr(0),lt=0,_i=null,Kr=0,Za=0,bu=0,Ti=null,Dt=null,Mu=0,Oo=1/0,Kn=null,el=!1,_u=null,vr=null,tl=!1,gr=null,nl=0,Oi=0,Tu=null,rl=-1,ol=0;function kt(){return(Ue&6)!==0?Ke():rl!==-1?rl:rl=Ke()}function yr(e){return(e.mode&1)===0?1:(Ue&2)!==0&&vt!==0?vt&-vt:Qy.transition!==null?(ol===0&&(ol=pd()),ol):(e=Ve,e!==0||(e=window.event,e=e===void 0?16:Cd(e.type)),e)}function gn(e,n,i,l){if(50<Oi)throw Oi=0,Tu=null,Error(o(185));ti(e,i,l),((Ue&2)===0||e!==ft)&&(e===ft&&((Ue&2)===0&&(Za|=i),lt===4&&wr(e,vt)),Lt(e,l),i===1&&Ue===0&&(n.mode&1)===0&&(Oo=Ke()+500,Na&&fr()))}function Lt(e,n){var i=e.callbackNode;Qg(e,n);var l=ha(e,e===ft?vt:0);if(l===0)i!==null&&dn(i),e.callbackNode=null,e.callbackPriority=0;else if(n=l&-l,e.callbackPriority!==n){if(i!=null&&dn(i),n===1)e.tag===0?Ky(kh.bind(null,e)):uf(kh.bind(null,e)),Hy(function(){(Ue&6)===0&&fr()}),i=null;else{switch(md(l)){case 1:i=nr;break;case 4:i=Fr;break;case 16:i=ze;break;case 536870912:i=jr;break;default:i=ze}i=Nh(i,Ch.bind(null,e))}e.callbackPriority=n,e.callbackNode=i}}function Ch(e,n){if(rl=-1,ol=0,(Ue&6)!==0)throw Error(o(327));var i=e.callbackNode;if(No()&&e.callbackNode!==i)return null;var l=ha(e,e===ft?vt:0);if(l===0)return null;if((l&30)!==0||(l&e.expiredLanes)!==0||n)n=il(e,l);else{n=l;var c=Ue;Ue|=2;var f=Ph();(ft!==e||vt!==n)&&(Kn=null,Oo=Ke()+500,Gr(e,n));do try{hw();break}catch(C){Rh(e,C)}while(!0);Gs(),Ja.current=f,Ue=c,ot!==null?n=0:(ft=null,vt=0,n=lt)}if(n!==0){if(n===2&&(c=cs(e),c!==0&&(l=c,n=Ou(e,c))),n===1)throw i=_i,Gr(e,0),wr(e,l),Lt(e,Ke()),i;if(n===6)wr(e,l);else{if(c=e.current.alternate,(l&30)===0&&!dw(c)&&(n=il(e,l),n===2&&(f=cs(e),f!==0&&(l=f,n=Ou(e,f))),n===1))throw i=_i,Gr(e,0),wr(e,l),Lt(e,Ke()),i;switch(e.finishedWork=c,e.finishedLanes=l,n){case 0:case 1:throw Error(o(345));case 2:Yr(e,Dt,Kn);break;case 3:if(wr(e,l),(l&130023424)===l&&(n=Mu+500-Ke(),10<n)){if(ha(e,0)!==0)break;if(c=e.suspendedLanes,(c&l)!==l){kt(),e.pingedLanes|=e.suspendedLanes&c;break}e.timeoutHandle=Fs(Yr.bind(null,e,Dt,Kn),n);break}Yr(e,Dt,Kn);break;case 4:if(wr(e,l),(l&4194240)===l)break;for(n=e.eventTimes,c=-1;0<l;){var y=31-_t(l);f=1<<y,y=n[y],y>c&&(c=y),l&=~f}if(l=c,l=Ke()-l,l=(120>l?120:480>l?480:1080>l?1080:1920>l?1920:3e3>l?3e3:4320>l?4320:1960*cw(l/1960))-l,10<l){e.timeoutHandle=Fs(Yr.bind(null,e,Dt,Kn),l);break}Yr(e,Dt,Kn);break;case 5:Yr(e,Dt,Kn);break;default:throw Error(o(329))}}}return Lt(e,Ke()),e.callbackNode===i?Ch.bind(null,e):null}function Ou(e,n){var i=Ti;return e.current.memoizedState.isDehydrated&&(Gr(e,n).flags|=256),e=il(e,n),e!==2&&(n=Dt,Dt=i,n!==null&&Nu(n)),e}function Nu(e){Dt===null?Dt=e:Dt.push.apply(Dt,e)}function dw(e){for(var n=e;;){if(n.flags&16384){var i=n.updateQueue;if(i!==null&&(i=i.stores,i!==null))for(var l=0;l<i.length;l++){var c=i[l],f=c.getSnapshot;c=c.value;try{if(!fn(f(),c))return!1}catch{return!1}}}if(i=n.child,n.subtreeFlags&16384&&i!==null)i.return=n,n=i;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function wr(e,n){for(n&=~bu,n&=~Za,e.suspendedLanes|=n,e.pingedLanes&=~n,e=e.expirationTimes;0<n;){var i=31-_t(n),l=1<<i;e[i]=-1,n&=~l}}function kh(e){if((Ue&6)!==0)throw Error(o(327));No();var n=ha(e,0);if((n&1)===0)return Lt(e,Ke()),null;var i=il(e,n);if(e.tag!==0&&i===2){var l=cs(e);l!==0&&(n=l,i=Ou(e,l))}if(i===1)throw i=_i,Gr(e,0),wr(e,n),Lt(e,Ke()),i;if(i===6)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=n,Yr(e,Dt,Kn),Lt(e,Ke()),null}function Du(e,n){var i=Ue;Ue|=1;try{return e(n)}finally{Ue=i,Ue===0&&(Oo=Ke()+500,Na&&fr())}}function Qr(e){gr!==null&&gr.tag===0&&(Ue&6)===0&&No();var n=Ue;Ue|=1;var i=Zt.transition,l=Ve;try{if(Zt.transition=null,Ve=1,e)return e()}finally{Ve=l,Zt.transition=i,Ue=n,(Ue&6)===0&&fr()}}function Lu(){Bt=To.current,Ye(To)}function Gr(e,n){e.finishedWork=null,e.finishedLanes=0;var i=e.timeoutHandle;if(i!==-1&&(e.timeoutHandle=-1,$y(i)),ot!==null)for(i=ot.return;i!==null;){var l=i;switch(Bs(l),l.tag){case 1:l=l.type.childContextTypes,l!=null&&Ta();break;case 3:bo(),Ye(Tt),Ye(gt),nu();break;case 5:eu(l);break;case 4:bo();break;case 13:Ye(Ze);break;case 19:Ye(Ze);break;case 10:Ys(l.type._context);break;case 22:case 23:Lu()}i=i.return}if(ft=e,ot=e=xr(e.current,null),vt=Bt=n,lt=0,_i=null,bu=Za=Kr=0,Dt=Ti=null,Br!==null){for(n=0;n<Br.length;n++)if(i=Br[n],l=i.interleaved,l!==null){i.interleaved=null;var c=l.next,f=i.pending;if(f!==null){var y=f.next;f.next=c,l.next=y}i.pending=l}Br=null}return e}function Rh(e,n){do{var i=ot;try{if(Gs(),Ha.current=Ka,Ba){for(var l=et.memoizedState;l!==null;){var c=l.queue;c!==null&&(c.pending=null),l=l.next}Ba=!1}if(Wr=0,dt=at=et=null,Ci=!1,ki=0,Pu.current=null,i===null||i.return===null){lt=1,_i=n,ot=null;break}e:{var f=e,y=i.return,C=i,M=n;if(n=vt,C.flags|=32768,M!==null&&typeof M=="object"&&typeof M.then=="function"){var z=M,Y=C,X=Y.tag;if((Y.mode&1)===0&&(X===0||X===11||X===15)){var Q=Y.alternate;Q?(Y.updateQueue=Q.updateQueue,Y.memoizedState=Q.memoizedState,Y.lanes=Q.lanes):(Y.updateQueue=null,Y.memoizedState=null)}var re=Xf(y);if(re!==null){re.flags&=-257,qf(re,y,C,f,n),re.mode&1&&Yf(f,z,n),n=re,M=z;var de=n.updateQueue;if(de===null){var pe=new Set;pe.add(M),n.updateQueue=pe}else de.add(M);break e}else{if((n&1)===0){Yf(f,z,n),Au();break e}M=Error(o(426))}}else if(Je&&C.mode&1){var rt=Xf(y);if(rt!==null){(rt.flags&65536)===0&&(rt.flags|=256),qf(rt,y,C,f,n),Ks(Mo(M,C));break e}}f=M=Mo(M,C),lt!==4&&(lt=2),Ti===null?Ti=[f]:Ti.push(f),f=y;do{switch(f.tag){case 3:f.flags|=65536,n&=-n,f.lanes|=n;var I=Qf(f,M,n);xf(f,I);break e;case 1:C=M;var O=f.type,F=f.stateNode;if((f.flags&128)===0&&(typeof O.getDerivedStateFromError=="function"||F!==null&&typeof F.componentDidCatch=="function"&&(vr===null||!vr.has(F)))){f.flags|=65536,n&=-n,f.lanes|=n;var J=Gf(f,C,n);xf(f,J);break e}}f=f.return}while(f!==null)}Mh(i)}catch(ge){n=ge,ot===i&&i!==null&&(ot=i=i.return);continue}break}while(!0)}function Ph(){var e=Ja.current;return Ja.current=Ka,e===null?Ka:e}function Au(){(lt===0||lt===3||lt===2)&&(lt=4),ft===null||(Kr&268435455)===0&&(Za&268435455)===0||wr(ft,vt)}function il(e,n){var i=Ue;Ue|=2;var l=Ph();(ft!==e||vt!==n)&&(Kn=null,Gr(e,n));do try{fw();break}catch(c){Rh(e,c)}while(!0);if(Gs(),Ue=i,Ja.current=l,ot!==null)throw Error(o(261));return ft=null,vt=0,lt}function fw(){for(;ot!==null;)bh(ot)}function hw(){for(;ot!==null&&!Gt();)bh(ot)}function bh(e){var n=Oh(e.alternate,e,Bt);e.memoizedProps=e.pendingProps,n===null?Mh(e):ot=n,Pu.current=null}function Mh(e){var n=e;do{var i=n.alternate;if(e=n.return,(n.flags&32768)===0){if(i=iw(i,n,Bt),i!==null){ot=i;return}}else{if(i=aw(i,n),i!==null){i.flags&=32767,ot=i;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{lt=6,ot=null;return}}if(n=n.sibling,n!==null){ot=n;return}ot=n=e}while(n!==null);lt===0&&(lt=5)}function Yr(e,n,i){var l=Ve,c=Zt.transition;try{Zt.transition=null,Ve=1,pw(e,n,i,l)}finally{Zt.transition=c,Ve=l}return null}function pw(e,n,i,l){do No();while(gr!==null);if((Ue&6)!==0)throw Error(o(327));i=e.finishedWork;var c=e.finishedLanes;if(i===null)return null;if(e.finishedWork=null,e.finishedLanes=0,i===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var f=i.lanes|i.childLanes;if(Gg(e,f),e===ft&&(ot=ft=null,vt=0),(i.subtreeFlags&2064)===0&&(i.flags&2064)===0||tl||(tl=!0,Nh(ze,function(){return No(),null})),f=(i.flags&15990)!==0,(i.subtreeFlags&15990)!==0||f){f=Zt.transition,Zt.transition=null;var y=Ve;Ve=1;var C=Ue;Ue|=4,Pu.current=null,sw(e,i),yh(i,e),Ly(As),va=!!Ls,As=Ls=null,e.current=i,uw(i),Zo(),Ue=C,Ve=y,Zt.transition=f}else e.current=i;if(tl&&(tl=!1,gr=e,nl=c),f=e.pendingLanes,f===0&&(vr=null),uo(i.stateNode),Lt(e,Ke()),n!==null)for(l=e.onRecoverableError,i=0;i<n.length;i++)c=n[i],l(c.value,{componentStack:c.stack,digest:c.digest});if(el)throw el=!1,e=_u,_u=null,e;return(nl&1)!==0&&e.tag!==0&&No(),f=e.pendingLanes,(f&1)!==0?e===Tu?Oi++:(Oi=0,Tu=e):Oi=0,fr(),null}function No(){if(gr!==null){var e=md(nl),n=Zt.transition,i=Ve;try{if(Zt.transition=null,Ve=16>e?16:e,gr===null)var l=!1;else{if(e=gr,gr=null,nl=0,(Ue&6)!==0)throw Error(o(331));var c=Ue;for(Ue|=4,le=e.current;le!==null;){var f=le,y=f.child;if((le.flags&16)!==0){var C=f.deletions;if(C!==null){for(var M=0;M<C.length;M++){var z=C[M];for(le=z;le!==null;){var Y=le;switch(Y.tag){case 0:case 11:case 15:Mi(8,Y,f)}var X=Y.child;if(X!==null)X.return=Y,le=X;else for(;le!==null;){Y=le;var Q=Y.sibling,re=Y.return;if(hh(Y),Y===z){le=null;break}if(Q!==null){Q.return=re,le=Q;break}le=re}}}var de=f.alternate;if(de!==null){var pe=de.child;if(pe!==null){de.child=null;do{var rt=pe.sibling;pe.sibling=null,pe=rt}while(pe!==null)}}le=f}}if((f.subtreeFlags&2064)!==0&&y!==null)y.return=f,le=y;else e:for(;le!==null;){if(f=le,(f.flags&2048)!==0)switch(f.tag){case 0:case 11:case 15:Mi(9,f,f.return)}var I=f.sibling;if(I!==null){I.return=f.return,le=I;break e}le=f.return}}var O=e.current;for(le=O;le!==null;){y=le;var F=y.child;if((y.subtreeFlags&2064)!==0&&F!==null)F.return=y,le=F;else e:for(y=O;le!==null;){if(C=le,(C.flags&2048)!==0)try{switch(C.tag){case 0:case 11:case 15:qa(9,C)}}catch(ge){tt(C,C.return,ge)}if(C===y){le=null;break e}var J=C.sibling;if(J!==null){J.return=C.return,le=J;break e}le=C.return}}if(Ue=c,fr(),Be&&typeof Be.onPostCommitFiberRoot=="function")try{Be.onPostCommitFiberRoot(kn,e)}catch{}l=!0}return l}finally{Ve=i,Zt.transition=n}}return!1}function _h(e,n,i){n=Mo(i,n),n=Qf(e,n,1),e=pr(e,n,1),n=kt(),e!==null&&(ti(e,1,n),Lt(e,n))}function tt(e,n,i){if(e.tag===3)_h(e,e,i);else for(;n!==null;){if(n.tag===3){_h(n,e,i);break}else if(n.tag===1){var l=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(vr===null||!vr.has(l))){e=Mo(i,e),e=Gf(n,e,1),n=pr(n,e,1),e=kt(),n!==null&&(ti(n,1,e),Lt(n,e));break}}n=n.return}}function mw(e,n,i){var l=e.pingCache;l!==null&&l.delete(n),n=kt(),e.pingedLanes|=e.suspendedLanes&i,ft===e&&(vt&i)===i&&(lt===4||lt===3&&(vt&130023424)===vt&&500>Ke()-Mu?Gr(e,0):bu|=i),Lt(e,n)}function Th(e,n){n===0&&((e.mode&1)===0?n=1:(n=fa,fa<<=1,(fa&130023424)===0&&(fa=4194304)));var i=kt();e=Bn(e,n),e!==null&&(ti(e,n,i),Lt(e,i))}function vw(e){var n=e.memoizedState,i=0;n!==null&&(i=n.retryLane),Th(e,i)}function gw(e,n){var i=0;switch(e.tag){case 13:var l=e.stateNode,c=e.memoizedState;c!==null&&(i=c.retryLane);break;case 19:l=e.stateNode;break;default:throw Error(o(314))}l!==null&&l.delete(n),Th(e,i)}var Oh;Oh=function(e,n,i){if(e!==null)if(e.memoizedProps!==n.pendingProps||Tt.current)Nt=!0;else{if((e.lanes&i)===0&&(n.flags&128)===0)return Nt=!1,ow(e,n,i);Nt=(e.flags&131072)!==0}else Nt=!1,Je&&(n.flags&1048576)!==0&&cf(n,La,n.index);switch(n.lanes=0,n.tag){case 2:var l=n.type;Ya(e,n),e=n.pendingProps;var c=xo(n,gt.current);Po(n,i),c=iu(null,n,l,e,c,i);var f=au();return n.flags|=1,typeof c=="object"&&c!==null&&typeof c.render=="function"&&c.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Ot(l)?(f=!0,Oa(n)):f=!1,n.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,Js(n),c.updater=Qa,n.stateNode=c,c._reactInternals=n,fu(n,l,e,i),n=vu(null,n,l,!0,f,i)):(n.tag=0,Je&&f&&Hs(n),Ct(null,n,c,i),n=n.child),n;case 16:l=n.elementType;e:{switch(Ya(e,n),e=n.pendingProps,c=l._init,l=c(l._payload),n.type=l,c=n.tag=ww(l),e=pn(l,e),c){case 0:n=mu(null,n,l,e,i);break e;case 1:n=rh(null,n,l,e,i);break e;case 11:n=Jf(null,n,l,e,i);break e;case 14:n=Zf(null,n,l,pn(l.type,e),i);break e}throw Error(o(306,l,""))}return n;case 0:return l=n.type,c=n.pendingProps,c=n.elementType===l?c:pn(l,c),mu(e,n,l,c,i);case 1:return l=n.type,c=n.pendingProps,c=n.elementType===l?c:pn(l,c),rh(e,n,l,c,i);case 3:e:{if(oh(n),e===null)throw Error(o(387));l=n.pendingProps,f=n.memoizedState,c=f.element,wf(e,n),Ua(n,l,null,i);var y=n.memoizedState;if(l=y.element,f.isDehydrated)if(f={element:l,isDehydrated:!1,cache:y.cache,pendingSuspenseBoundaries:y.pendingSuspenseBoundaries,transitions:y.transitions},n.updateQueue.baseState=f,n.memoizedState=f,n.flags&256){c=Mo(Error(o(423)),n),n=ih(e,n,l,i,c);break e}else if(l!==c){c=Mo(Error(o(424)),n),n=ih(e,n,l,i,c);break e}else for(Ht=ur(n.stateNode.containerInfo.firstChild),$t=n,Je=!0,hn=null,i=gf(n,null,l,i),n.child=i;i;)i.flags=i.flags&-3|4096,i=i.sibling;else{if(Co(),l===c){n=Wn(e,n,i);break e}Ct(e,n,l,i)}n=n.child}return n;case 5:return Ef(n),e===null&&Ws(n),l=n.type,c=n.pendingProps,f=e!==null?e.memoizedProps:null,y=c.children,Is(l,c)?y=null:f!==null&&Is(l,f)&&(n.flags|=32),nh(e,n),Ct(e,n,y,i),n.child;case 6:return e===null&&Ws(n),null;case 13:return ah(e,n,i);case 4:return Zs(n,n.stateNode.containerInfo),l=n.pendingProps,e===null?n.child=ko(n,null,l,i):Ct(e,n,l,i),n.child;case 11:return l=n.type,c=n.pendingProps,c=n.elementType===l?c:pn(l,c),Jf(e,n,l,c,i);case 7:return Ct(e,n,n.pendingProps,i),n.child;case 8:return Ct(e,n,n.pendingProps.children,i),n.child;case 12:return Ct(e,n,n.pendingProps.children,i),n.child;case 10:e:{if(l=n.type._context,c=n.pendingProps,f=n.memoizedProps,y=c.value,Qe(Fa,l._currentValue),l._currentValue=y,f!==null)if(fn(f.value,y)){if(f.children===c.children&&!Tt.current){n=Wn(e,n,i);break e}}else for(f=n.child,f!==null&&(f.return=n);f!==null;){var C=f.dependencies;if(C!==null){y=f.child;for(var M=C.firstContext;M!==null;){if(M.context===l){if(f.tag===1){M=Vn(-1,i&-i),M.tag=2;var z=f.updateQueue;if(z!==null){z=z.shared;var Y=z.pending;Y===null?M.next=M:(M.next=Y.next,Y.next=M),z.pending=M}}f.lanes|=i,M=f.alternate,M!==null&&(M.lanes|=i),Xs(f.return,i,n),C.lanes|=i;break}M=M.next}}else if(f.tag===10)y=f.type===n.type?null:f.child;else if(f.tag===18){if(y=f.return,y===null)throw Error(o(341));y.lanes|=i,C=y.alternate,C!==null&&(C.lanes|=i),Xs(y,i,n),y=f.sibling}else y=f.child;if(y!==null)y.return=f;else for(y=f;y!==null;){if(y===n){y=null;break}if(f=y.sibling,f!==null){f.return=y.return,y=f;break}y=y.return}f=y}Ct(e,n,c.children,i),n=n.child}return n;case 9:return c=n.type,l=n.pendingProps.children,Po(n,i),c=qt(c),l=l(c),n.flags|=1,Ct(e,n,l,i),n.child;case 14:return l=n.type,c=pn(l,n.pendingProps),c=pn(l.type,c),Zf(e,n,l,c,i);case 15:return eh(e,n,n.type,n.pendingProps,i);case 17:return l=n.type,c=n.pendingProps,c=n.elementType===l?c:pn(l,c),Ya(e,n),n.tag=1,Ot(l)?(e=!0,Oa(n)):e=!1,Po(n,i),Wf(n,l,c),fu(n,l,c,i),vu(null,n,l,!0,e,i);case 19:return sh(e,n,i);case 22:return th(e,n,i)}throw Error(o(156,n.tag))};function Nh(e,n){return tr(e,n)}function yw(e,n,i,l){this.tag=e,this.key=i,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function en(e,n,i,l){return new yw(e,n,i,l)}function Iu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function ww(e){if(typeof e=="function")return Iu(e)?1:0;if(e!=null){if(e=e.$$typeof,e===_e)return 11;if(e===Pe)return 14}return 2}function xr(e,n){var i=e.alternate;return i===null?(i=en(e.tag,n,e.key,e.mode),i.elementType=e.elementType,i.type=e.type,i.stateNode=e.stateNode,i.alternate=e,e.alternate=i):(i.pendingProps=n,i.type=e.type,i.flags=0,i.subtreeFlags=0,i.deletions=null),i.flags=e.flags&14680064,i.childLanes=e.childLanes,i.lanes=e.lanes,i.child=e.child,i.memoizedProps=e.memoizedProps,i.memoizedState=e.memoizedState,i.updateQueue=e.updateQueue,n=e.dependencies,i.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},i.sibling=e.sibling,i.index=e.index,i.ref=e.ref,i}function al(e,n,i,l,c,f){var y=2;if(l=e,typeof e=="function")Iu(e)&&(y=1);else if(typeof e=="string")y=5;else e:switch(e){case K:return Xr(i.children,c,f,n);case V:y=8,c|=8;break;case te:return e=en(12,i,n,c|2),e.elementType=te,e.lanes=f,e;case oe:return e=en(13,i,n,c),e.elementType=oe,e.lanes=f,e;case ae:return e=en(19,i,n,c),e.elementType=ae,e.lanes=f,e;case se:return ll(i,c,f,n);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ve:y=10;break e;case Te:y=9;break e;case _e:y=11;break e;case Pe:y=14;break e;case Z:y=16,l=null;break e}throw Error(o(130,e==null?e:typeof e,""))}return n=en(y,i,n,c),n.elementType=e,n.type=l,n.lanes=f,n}function Xr(e,n,i,l){return e=en(7,e,l,n),e.lanes=i,e}function ll(e,n,i,l){return e=en(22,e,l,n),e.elementType=se,e.lanes=i,e.stateNode={isHidden:!1},e}function Fu(e,n,i){return e=en(6,e,null,n),e.lanes=i,e}function ju(e,n,i){return n=en(4,e.children!==null?e.children:[],e.key,n),n.lanes=i,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}function xw(e,n,i,l,c){this.tag=n,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ds(0),this.expirationTimes=ds(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ds(0),this.identifierPrefix=l,this.onRecoverableError=c,this.mutableSourceEagerHydrationData=null}function zu(e,n,i,l,c,f,y,C,M){return e=new xw(e,n,i,C,M),n===1?(n=1,f===!0&&(n|=8)):n=0,f=en(3,null,null,n),e.current=f,f.stateNode=e,f.memoizedState={element:l,isDehydrated:i,cache:null,transitions:null,pendingSuspenseBoundaries:null},Js(f),e}function Sw(e,n,i){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:_,key:l==null?null:""+l,children:e,containerInfo:n,implementation:i}}function Dh(e){if(!e)return dr;e=e._reactInternals;e:{if(He(e)!==e||e.tag!==1)throw Error(o(170));var n=e;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Ot(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(o(171))}if(e.tag===1){var i=e.type;if(Ot(i))return lf(e,i,n)}return n}function Lh(e,n,i,l,c,f,y,C,M){return e=zu(i,l,!0,e,c,f,y,C,M),e.context=Dh(null),i=e.current,l=kt(),c=yr(i),f=Vn(l,c),f.callback=n??null,pr(i,f,c),e.current.lanes=c,ti(e,c,l),Lt(e,l),e}function sl(e,n,i,l){var c=n.current,f=kt(),y=yr(c);return i=Dh(i),n.context===null?n.context=i:n.pendingContext=i,n=Vn(f,y),n.payload={element:e},l=l===void 0?null:l,l!==null&&(n.callback=l),e=pr(c,n,y),e!==null&&(gn(e,c,y,f),za(e,c,y)),y}function ul(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Ah(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var i=e.retryLane;e.retryLane=i!==0&&i<n?i:n}}function Uu(e,n){Ah(e,n),(e=e.alternate)&&Ah(e,n)}function Ew(){return null}var Ih=typeof reportError=="function"?reportError:function(e){console.error(e)};function $u(e){this._internalRoot=e}cl.prototype.render=$u.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(o(409));sl(e,n,null,null)},cl.prototype.unmount=$u.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;Qr(function(){sl(null,e,null,null)}),n[zn]=null}};function cl(e){this._internalRoot=e}cl.prototype.unstable_scheduleHydration=function(e){if(e){var n=yd();e={blockedOn:null,target:e,priority:n};for(var i=0;i<ar.length&&n!==0&&n<ar[i].priority;i++);ar.splice(i,0,e),i===0&&Sd(e)}};function Hu(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function dl(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Fh(){}function Cw(e,n,i,l,c){if(c){if(typeof l=="function"){var f=l;l=function(){var z=ul(y);f.call(z)}}var y=Lh(n,l,e,0,null,!1,!1,"",Fh);return e._reactRootContainer=y,e[zn]=y.current,mi(e.nodeType===8?e.parentNode:e),Qr(),y}for(;c=e.lastChild;)e.removeChild(c);if(typeof l=="function"){var C=l;l=function(){var z=ul(M);C.call(z)}}var M=zu(e,0,!1,null,null,!1,!1,"",Fh);return e._reactRootContainer=M,e[zn]=M.current,mi(e.nodeType===8?e.parentNode:e),Qr(function(){sl(n,M,i,l)}),M}function fl(e,n,i,l,c){var f=i._reactRootContainer;if(f){var y=f;if(typeof c=="function"){var C=c;c=function(){var M=ul(y);C.call(M)}}sl(n,y,e,c)}else y=Cw(i,n,e,c,l);return ul(y)}vd=function(e){switch(e.tag){case 3:var n=e.stateNode;if(n.current.memoizedState.isDehydrated){var i=ei(n.pendingLanes);i!==0&&(fs(n,i|1),Lt(n,Ke()),(Ue&6)===0&&(Oo=Ke()+500,fr()))}break;case 13:Qr(function(){var l=Bn(e,1);if(l!==null){var c=kt();gn(l,e,1,c)}}),Uu(e,1)}},hs=function(e){if(e.tag===13){var n=Bn(e,134217728);if(n!==null){var i=kt();gn(n,e,134217728,i)}Uu(e,134217728)}},gd=function(e){if(e.tag===13){var n=yr(e),i=Bn(e,n);if(i!==null){var l=kt();gn(i,e,n,l)}Uu(e,n)}},yd=function(){return Ve},wd=function(e,n){var i=Ve;try{return Ve=e,n()}finally{Ve=i}},qo=function(e,n,i){switch(n){case"input":if(oo(e,i),n=i.name,i.type==="radio"&&n!=null){for(i=e;i.parentNode;)i=i.parentNode;for(i=i.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<i.length;n++){var l=i[n];if(l!==e&&l.form===e.form){var c=_a(l);if(!c)throw Error(o(90));Qt(l),oo(l,c)}}}break;case"textarea":Go(e,i);break;case"select":n=i.value,n!=null&&qn(e,!!i.multiple,n,!1)}},ua=Du,ca=Qr;var kw={usingClientEntryPoint:!1,Events:[yi,yo,_a,Ar,Ir,Du]},Ni={findFiberByHostInstance:zr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Rw={bundleType:Ni.bundleType,version:Ni.version,rendererPackageName:Ni.rendererPackageName,rendererConfig:Ni.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:U.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=We(e),e===null?null:e.stateNode},findFiberByHostInstance:Ni.findFiberByHostInstance||Ew,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var hl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!hl.isDisabled&&hl.supportsFiber)try{kn=hl.inject(Rw),Be=hl}catch{}}return At.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=kw,At.createPortal=function(e,n){var i=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Hu(n))throw Error(o(200));return Sw(e,n,null,i)},At.createRoot=function(e,n){if(!Hu(e))throw Error(o(299));var i=!1,l="",c=Ih;return n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(c=n.onRecoverableError)),n=zu(e,1,!1,null,null,i,!1,l,c),e[zn]=n.current,mi(e.nodeType===8?e.parentNode:e),new $u(n)},At.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=We(n),e=e===null?null:e.stateNode,e},At.flushSync=function(e){return Qr(e)},At.hydrate=function(e,n,i){if(!dl(n))throw Error(o(200));return fl(null,e,n,!0,i)},At.hydrateRoot=function(e,n,i){if(!Hu(e))throw Error(o(405));var l=i!=null&&i.hydratedSources||null,c=!1,f="",y=Ih;if(i!=null&&(i.unstable_strictMode===!0&&(c=!0),i.identifierPrefix!==void 0&&(f=i.identifierPrefix),i.onRecoverableError!==void 0&&(y=i.onRecoverableError)),n=Lh(n,null,e,1,i??null,c,!1,f,y),e[zn]=n.current,mi(e),l)for(e=0;e<l.length;e++)i=l[e],c=i._getVersion,c=c(i._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[i,c]:n.mutableSourceEagerHydrationData.push(i,c);return new cl(n)},At.render=function(e,n,i){if(!dl(n))throw Error(o(200));return fl(null,e,n,!1,i)},At.unmountComponentAtNode=function(e){if(!dl(e))throw Error(o(40));return e._reactRootContainer?(Qr(function(){fl(null,null,e,!1,function(){e._reactRootContainer=null,e[zn]=null})}),!0):!1},At.unstable_batchedUpdates=Du,At.unstable_renderSubtreeIntoContainer=function(e,n,i,l){if(!dl(i))throw Error(o(200));if(e==null||e._reactInternals===void 0)throw Error(o(38));return fl(e,n,i,!1,l)},At.version="18.3.1-next-f1338f8080-20240426",At}var Wh;function rm(){if(Wh)return Wu.exports;Wh=1;function t(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(t)}catch(r){console.error(r)}}return t(),Wu.exports=Dw(),Wu.exports}var Kh;function Lw(){if(Kh)return pl;Kh=1;var t=rm();return pl.createRoot=t.createRoot,pl.hydrateRoot=t.hydrateRoot,pl}var Aw=Lw();const Iw="modulepreload",Fw=function(t){return"/"+t},Qh={},St=function(r,o,a){let s=Promise.resolve();if(o&&o.length>0){let p=function(m){return Promise.all(m.map(v=>Promise.resolve(v).then(w=>({status:"fulfilled",value:w}),w=>({status:"rejected",reason:w}))))};document.getElementsByTagName("link");const d=document.querySelector("meta[property=csp-nonce]"),h=d?.nonce||d?.getAttribute("nonce");s=p(o.map(m=>{if(m=Fw(m),m in Qh)return;Qh[m]=!0;const v=m.endsWith(".css"),w=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${m}"]${w}`))return;const x=document.createElement("link");if(x.rel=v?"stylesheet":Iw,v||(x.as="script"),x.crossOrigin="",x.href=m,h&&x.setAttribute("nonce",h),document.head.appendChild(x),v)return new Promise((E,k)=>{x.addEventListener("load",E),x.addEventListener("error",()=>k(new Error(`Unable to preload CSS for ${m}`)))})}))}function u(d){const h=new Event("vite:preloadError",{cancelable:!0});if(h.payload=d,window.dispatchEvent(h),!h.defaultPrevented)throw d}return s.then(d=>{for(const h of d||[])h.status==="rejected"&&u(h.reason);return r().catch(u)})};var Kl=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Ql=typeof window>"u"||"Deno"in globalThis;function yn(){}function jw(t,r){return typeof t=="function"?t(r):t}function zw(t){return typeof t=="number"&&t>=0&&t!==1/0}function Uw(t,r){return Math.max(t+(r||0)-Date.now(),0)}function mc(t,r){return typeof t=="function"?t(r):t}function $w(t,r){return typeof t=="function"?t(r):t}function Gh(t,r){const{type:o="all",exact:a,fetchStatus:s,predicate:u,queryKey:d,stale:h}=t;if(d){if(a){if(r.queryHash!==Fc(d,r.options))return!1}else if(!$i(r.queryKey,d))return!1}if(o!=="all"){const p=r.isActive();if(o==="active"&&!p||o==="inactive"&&p)return!1}return!(typeof h=="boolean"&&r.isStale()!==h||s&&s!==r.state.fetchStatus||u&&!u(r))}function Yh(t,r){const{exact:o,status:a,predicate:s,mutationKey:u}=t;if(u){if(!r.options.mutationKey)return!1;if(o){if(Ui(r.options.mutationKey)!==Ui(u))return!1}else if(!$i(r.options.mutationKey,u))return!1}return!(a&&r.state.status!==a||s&&!s(r))}function Fc(t,r){return(r?.queryKeyHashFn||Ui)(t)}function Ui(t){return JSON.stringify(t,(r,o)=>vc(o)?Object.keys(o).sort().reduce((a,s)=>(a[s]=o[s],a),{}):o)}function $i(t,r){return t===r?!0:typeof t!=typeof r?!1:t&&r&&typeof t=="object"&&typeof r=="object"?Object.keys(r).every(o=>$i(t[o],r[o])):!1}function om(t,r){if(t===r)return t;const o=Xh(t)&&Xh(r);if(o||vc(t)&&vc(r)){const a=o?t:Object.keys(t),s=a.length,u=o?r:Object.keys(r),d=u.length,h=o?[]:{},p=new Set(a);let m=0;for(let v=0;v<d;v++){const w=o?v:u[v];(!o&&p.has(w)||o)&&t[w]===void 0&&r[w]===void 0?(h[w]=void 0,m++):(h[w]=om(t[w],r[w]),h[w]===t[w]&&t[w]!==void 0&&m++)}return s===d&&m===s?t:h}return r}function Xh(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function vc(t){if(!qh(t))return!1;const r=t.constructor;if(r===void 0)return!0;const o=r.prototype;return!(!qh(o)||!o.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(t)!==Object.prototype)}function qh(t){return Object.prototype.toString.call(t)==="[object Object]"}function Hw(t){return new Promise(r=>{setTimeout(r,t)})}function Bw(t,r,o){return typeof o.structuralSharing=="function"?o.structuralSharing(t,r):o.structuralSharing!==!1?om(t,r):r}function Vw(t,r,o=0){const a=[...t,r];return o&&a.length>o?a.slice(1):a}function Ww(t,r,o=0){const a=[r,...t];return o&&a.length>o?a.slice(0,-1):a}var jc=Symbol();function im(t,r){return!t.queryFn&&r?.initialPromise?()=>r.initialPromise:!t.queryFn||t.queryFn===jc?()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`)):t.queryFn}var Kw=class extends Kl{#e;#t;#n;constructor(){super(),this.#n=t=>{if(!Ql&&window.addEventListener){const r=()=>t();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(t){this.#n=t,this.#t?.(),this.#t=t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()})}setFocused(t){this.#e!==t&&(this.#e=t,this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(r=>{r(t)})}isFocused(){return typeof this.#e=="boolean"?this.#e:globalThis.document?.visibilityState!=="hidden"}},am=new Kw,Qw=class extends Kl{#e=!0;#t;#n;constructor(){super(),this.#n=t=>{if(!Ql&&window.addEventListener){const r=()=>t(!0),o=()=>t(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",o,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",o)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(t){this.#n=t,this.#t?.(),this.#t=t(this.setOnline.bind(this))}setOnline(t){this.#e!==t&&(this.#e=t,this.listeners.forEach(o=>{o(t)}))}isOnline(){return this.#e}},Il=new Qw;function Gw(){let t,r;const o=new Promise((s,u)=>{t=s,r=u});o.status="pending",o.catch(()=>{});function a(s){Object.assign(o,s),delete o.resolve,delete o.reject}return o.resolve=s=>{a({status:"fulfilled",value:s}),t(s)},o.reject=s=>{a({status:"rejected",reason:s}),r(s)},o}function Yw(t){return Math.min(1e3*2**t,3e4)}function lm(t){return(t??"online")==="online"?Il.isOnline():!0}var sm=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function Gu(t){return t instanceof sm}function um(t){let r=!1,o=0,a=!1,s;const u=Gw(),d=S=>{a||(x(new sm(S)),t.abort?.())},h=()=>{r=!0},p=()=>{r=!1},m=()=>am.isFocused()&&(t.networkMode==="always"||Il.isOnline())&&t.canRun(),v=()=>lm(t.networkMode)&&t.canRun(),w=S=>{a||(a=!0,t.onSuccess?.(S),s?.(),u.resolve(S))},x=S=>{a||(a=!0,t.onError?.(S),s?.(),u.reject(S))},E=()=>new Promise(S=>{s=R=>{(a||m())&&S(R)},t.onPause?.()}).then(()=>{s=void 0,a||t.onContinue?.()}),k=()=>{if(a)return;let S;const R=o===0?t.initialPromise:void 0;try{S=R??t.fn()}catch(P){S=Promise.reject(P)}Promise.resolve(S).then(w).catch(P=>{if(a)return;const j=t.retry??(Ql?0:3),L=t.retryDelay??Yw,U=typeof L=="function"?L(o,P):L,H=j===!0||typeof j=="number"&&o<j||typeof j=="function"&&j(o,P);if(r||!H){x(P);return}o++,t.onFail?.(o,P),Hw(U).then(()=>m()?void 0:E()).then(()=>{r?x(P):k()})})};return{promise:u,cancel:d,continue:()=>(s?.(),u),cancelRetry:h,continueRetry:p,canStart:v,start:()=>(v()?k():E().then(k),u)}}var Xw=t=>setTimeout(t,0);function qw(){let t=[],r=0,o=h=>{h()},a=h=>{h()},s=Xw;const u=h=>{r?t.push(h):s(()=>{o(h)})},d=()=>{const h=t;t=[],h.length&&s(()=>{a(()=>{h.forEach(p=>{o(p)})})})};return{batch:h=>{let p;r++;try{p=h()}finally{r--,r||d()}return p},batchCalls:h=>(...p)=>{u(()=>{h(...p)})},schedule:u,setNotifyFunction:h=>{o=h},setBatchNotifyFunction:h=>{a=h},setScheduler:h=>{s=h}}}var Rt=qw(),cm=class{#e;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),zw(this.gcTime)&&(this.#e=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(Ql?1/0:300*1e3))}clearGcTimeout(){this.#e&&(clearTimeout(this.#e),this.#e=void 0)}},Jw=class extends cm{#e;#t;#n;#o;#r;#a;#l;constructor(t){super(),this.#l=!1,this.#a=t.defaultOptions,this.setOptions(t.options),this.observers=[],this.#o=t.client,this.#n=this.#o.getQueryCache(),this.queryKey=t.queryKey,this.queryHash=t.queryHash,this.#e=e0(this.options),this.state=t.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#r?.promise}setOptions(t){this.options={...this.#a,...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(t,r){const o=Bw(this.state.data,t,this.options);return this.#i({data:o,type:"success",dataUpdatedAt:r?.updatedAt,manual:r?.manual}),o}setState(t,r){this.#i({type:"setState",state:t,setStateOptions:r})}cancel(t){const r=this.#r?.promise;return this.#r?.cancel(t),r?r.then(yn).catch(yn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(t=>$w(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===jc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(t=>mc(t.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(t=0){return this.state.data===void 0?!0:t==="static"?!1:this.state.isInvalidated?!0:!Uw(this.state.dataUpdatedAt,t)}onFocus(){this.observers.find(r=>r.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#r?.continue()}onOnline(){this.observers.find(r=>r.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#r?.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(r=>r!==t),this.observers.length||(this.#r&&(this.#l?this.#r.cancel({revert:!0}):this.#r.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(t,r){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&r?.cancelRefetch)this.cancel({silent:!0});else if(this.#r)return this.#r.continueRetry(),this.#r.promise}if(t&&this.setOptions(t),!this.options.queryFn){const p=this.observers.find(m=>m.options.queryFn);p&&this.setOptions(p.options)}const o=new AbortController,a=p=>{Object.defineProperty(p,"signal",{enumerable:!0,get:()=>(this.#l=!0,o.signal)})},s=()=>{const p=im(this.options,r),v=(()=>{const w={client:this.#o,queryKey:this.queryKey,meta:this.meta};return a(w),w})();return this.#l=!1,this.options.persister?this.options.persister(p,v,this):p(v)},d=(()=>{const p={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:this.#o,state:this.state,fetchFn:s};return a(p),p})();this.options.behavior?.onFetch(d,this),this.#t=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==d.fetchOptions?.meta)&&this.#i({type:"fetch",meta:d.fetchOptions?.meta});const h=p=>{Gu(p)&&p.silent||this.#i({type:"error",error:p}),Gu(p)||(this.#n.config.onError?.(p,this),this.#n.config.onSettled?.(this.state.data,p,this)),this.scheduleGc()};return this.#r=um({initialPromise:r?.initialPromise,fn:d.fetchFn,abort:o.abort.bind(o),onSuccess:p=>{if(p===void 0){h(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(p)}catch(m){h(m);return}this.#n.config.onSuccess?.(p,this),this.#n.config.onSettled?.(p,this.state.error,this),this.scheduleGc()},onError:h,onFail:(p,m)=>{this.#i({type:"failed",failureCount:p,error:m})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:d.options.retry,retryDelay:d.options.retryDelay,networkMode:d.options.networkMode,canRun:()=>!0}),this.#r.start()}#i(t){const r=o=>{switch(t.type){case"failed":return{...o,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...o,fetchStatus:"paused"};case"continue":return{...o,fetchStatus:"fetching"};case"fetch":return{...o,...Zw(o.data,this.options),fetchMeta:t.meta??null};case"success":return this.#t=void 0,{...o,data:t.data,dataUpdateCount:o.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const a=t.error;return Gu(a)&&a.revert&&this.#t?{...this.#t,fetchStatus:"idle"}:{...o,error:a,errorUpdateCount:o.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:o.fetchFailureCount+1,fetchFailureReason:a,fetchStatus:"idle",status:"error"};case"invalidate":return{...o,isInvalidated:!0};case"setState":return{...o,...t.state}}};this.state=r(this.state),Rt.batch(()=>{this.observers.forEach(o=>{o.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:t})})}};function Zw(t,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:lm(r.networkMode)?"fetching":"paused",...t===void 0&&{error:null,status:"pending"}}}function e0(t){const r=typeof t.initialData=="function"?t.initialData():t.initialData,o=r!==void 0,a=o?typeof t.initialDataUpdatedAt=="function"?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:o?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:o?"success":"pending",fetchStatus:"idle"}}var t0=class extends Kl{constructor(t={}){super(),this.config=t,this.#e=new Map}#e;build(t,r,o){const a=r.queryKey,s=r.queryHash??Fc(a,r);let u=this.get(s);return u||(u=new Jw({client:t,queryKey:a,queryHash:s,options:t.defaultQueryOptions(r),state:o,defaultOptions:t.getQueryDefaults(a)}),this.add(u)),u}add(t){this.#e.has(t.queryHash)||(this.#e.set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const r=this.#e.get(t.queryHash);r&&(t.destroy(),r===t&&this.#e.delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Rt.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return this.#e.get(t)}getAll(){return[...this.#e.values()]}find(t){const r={exact:!0,...t};return this.getAll().find(o=>Gh(r,o))}findAll(t={}){const r=this.getAll();return Object.keys(t).length>0?r.filter(o=>Gh(t,o)):r}notify(t){Rt.batch(()=>{this.listeners.forEach(r=>{r(t)})})}onFocus(){Rt.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Rt.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},n0=class extends cm{#e;#t;#n;constructor(t){super(),this.mutationId=t.mutationId,this.#t=t.mutationCache,this.#e=[],this.state=t.state||r0(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#e.includes(t)||(this.#e.push(t),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#e=this.#e.filter(r=>r!==t),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#e.length||(this.state.status==="pending"?this.scheduleGc():this.#t.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(t){const r=()=>{this.#o({type:"continue"})};this.#n=um({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(s,u)=>{this.#o({type:"failed",failureCount:s,error:u})},onPause:()=>{this.#o({type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});const o=this.state.status==="pending",a=!this.#n.canStart();try{if(o)r();else{this.#o({type:"pending",variables:t,isPaused:a}),await this.#t.config.onMutate?.(t,this);const u=await this.options.onMutate?.(t);u!==this.state.context&&this.#o({type:"pending",context:u,variables:t,isPaused:a})}const s=await this.#n.start();return await this.#t.config.onSuccess?.(s,t,this.state.context,this),await this.options.onSuccess?.(s,t,this.state.context),await this.#t.config.onSettled?.(s,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(s,null,t,this.state.context),this.#o({type:"success",data:s}),s}catch(s){try{throw await this.#t.config.onError?.(s,t,this.state.context,this),await this.options.onError?.(s,t,this.state.context),await this.#t.config.onSettled?.(void 0,s,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,s,t,this.state.context),s}finally{this.#o({type:"error",error:s})}}finally{this.#t.runNext(this)}}#o(t){const r=o=>{switch(t.type){case"failed":return{...o,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...o,isPaused:!0};case"continue":return{...o,isPaused:!1};case"pending":return{...o,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...o,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...o,data:void 0,error:t.error,failureCount:o.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=r(this.state),Rt.batch(()=>{this.#e.forEach(o=>{o.onMutationUpdate(t)}),this.#t.notify({mutation:this,type:"updated",action:t})})}};function r0(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var o0=class extends Kl{constructor(t={}){super(),this.config=t,this.#e=new Set,this.#t=new Map,this.#n=0}#e;#t;#n;build(t,r,o){const a=new n0({mutationCache:this,mutationId:++this.#n,options:t.defaultMutationOptions(r),state:o});return this.add(a),a}add(t){this.#e.add(t);const r=ml(t);if(typeof r=="string"){const o=this.#t.get(r);o?o.push(t):this.#t.set(r,[t])}this.notify({type:"added",mutation:t})}remove(t){if(this.#e.delete(t)){const r=ml(t);if(typeof r=="string"){const o=this.#t.get(r);if(o)if(o.length>1){const a=o.indexOf(t);a!==-1&&o.splice(a,1)}else o[0]===t&&this.#t.delete(r)}}this.notify({type:"removed",mutation:t})}canRun(t){const r=ml(t);if(typeof r=="string"){const a=this.#t.get(r)?.find(s=>s.state.status==="pending");return!a||a===t}else return!0}runNext(t){const r=ml(t);return typeof r=="string"?this.#t.get(r)?.find(a=>a!==t&&a.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){Rt.batch(()=>{this.#e.forEach(t=>{this.notify({type:"removed",mutation:t})}),this.#e.clear(),this.#t.clear()})}getAll(){return Array.from(this.#e)}find(t){const r={exact:!0,...t};return this.getAll().find(o=>Yh(r,o))}findAll(t={}){return this.getAll().filter(r=>Yh(t,r))}notify(t){Rt.batch(()=>{this.listeners.forEach(r=>{r(t)})})}resumePausedMutations(){const t=this.getAll().filter(r=>r.state.isPaused);return Rt.batch(()=>Promise.all(t.map(r=>r.continue().catch(yn))))}};function ml(t){return t.options.scope?.id}function Jh(t){return{onFetch:(r,o)=>{const a=r.options,s=r.fetchOptions?.meta?.fetchMore?.direction,u=r.state.data?.pages||[],d=r.state.data?.pageParams||[];let h={pages:[],pageParams:[]},p=0;const m=async()=>{let v=!1;const w=k=>{Object.defineProperty(k,"signal",{enumerable:!0,get:()=>(r.signal.aborted?v=!0:r.signal.addEventListener("abort",()=>{v=!0}),r.signal)})},x=im(r.options,r.fetchOptions),E=async(k,S,R)=>{if(v)return Promise.reject();if(S==null&&k.pages.length)return Promise.resolve(k);const j=(()=>{const _={client:r.client,queryKey:r.queryKey,pageParam:S,direction:R?"backward":"forward",meta:r.options.meta};return w(_),_})(),L=await x(j),{maxPages:U}=r.options,H=R?Ww:Vw;return{pages:H(k.pages,L,U),pageParams:H(k.pageParams,S,U)}};if(s&&u.length){const k=s==="backward",S=k?i0:Zh,R={pages:u,pageParams:d},P=S(a,R);h=await E(R,P,k)}else{const k=t??u.length;do{const S=p===0?d[0]??a.initialPageParam:Zh(a,h);if(p>0&&S==null)break;h=await E(h,S),p++}while(p<k)}return h};r.options.persister?r.fetchFn=()=>r.options.persister?.(m,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},o):r.fetchFn=m}}}function Zh(t,{pages:r,pageParams:o}){const a=r.length-1;return r.length>0?t.getNextPageParam(r[a],r,o[a],o):void 0}function i0(t,{pages:r,pageParams:o}){return r.length>0?t.getPreviousPageParam?.(r[0],r,o[0],o):void 0}var a0=class{#e;#t;#n;#o;#r;#a;#l;#i;constructor(t={}){this.#e=t.queryCache||new t0,this.#t=t.mutationCache||new o0,this.#n=t.defaultOptions||{},this.#o=new Map,this.#r=new Map,this.#a=0}mount(){this.#a++,this.#a===1&&(this.#l=am.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#i=Il.subscribe(async t=>{t&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#a--,this.#a===0&&(this.#l?.(),this.#l=void 0,this.#i?.(),this.#i=void 0)}isFetching(t){return this.#e.findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return this.#t.findAll({...t,status:"pending"}).length}getQueryData(t){const r=this.defaultQueryOptions({queryKey:t});return this.#e.get(r.queryHash)?.state.data}ensureQueryData(t){const r=this.defaultQueryOptions(t),o=this.#e.build(this,r),a=o.state.data;return a===void 0?this.fetchQuery(t):(t.revalidateIfStale&&o.isStaleByTime(mc(r.staleTime,o))&&this.prefetchQuery(r),Promise.resolve(a))}getQueriesData(t){return this.#e.findAll(t).map(({queryKey:r,state:o})=>{const a=o.data;return[r,a]})}setQueryData(t,r,o){const a=this.defaultQueryOptions({queryKey:t}),u=this.#e.get(a.queryHash)?.state.data,d=jw(r,u);if(d!==void 0)return this.#e.build(this,a).setData(d,{...o,manual:!0})}setQueriesData(t,r,o){return Rt.batch(()=>this.#e.findAll(t).map(({queryKey:a})=>[a,this.setQueryData(a,r,o)]))}getQueryState(t){const r=this.defaultQueryOptions({queryKey:t});return this.#e.get(r.queryHash)?.state}removeQueries(t){const r=this.#e;Rt.batch(()=>{r.findAll(t).forEach(o=>{r.remove(o)})})}resetQueries(t,r){const o=this.#e;return Rt.batch(()=>(o.findAll(t).forEach(a=>{a.reset()}),this.refetchQueries({type:"active",...t},r)))}cancelQueries(t,r={}){const o={revert:!0,...r},a=Rt.batch(()=>this.#e.findAll(t).map(s=>s.cancel(o)));return Promise.all(a).then(yn).catch(yn)}invalidateQueries(t,r={}){return Rt.batch(()=>(this.#e.findAll(t).forEach(o=>{o.invalidate()}),t?.refetchType==="none"?Promise.resolve():this.refetchQueries({...t,type:t?.refetchType??t?.type??"active"},r)))}refetchQueries(t,r={}){const o={...r,cancelRefetch:r.cancelRefetch??!0},a=Rt.batch(()=>this.#e.findAll(t).filter(s=>!s.isDisabled()&&!s.isStatic()).map(s=>{let u=s.fetch(void 0,o);return o.throwOnError||(u=u.catch(yn)),s.state.fetchStatus==="paused"?Promise.resolve():u}));return Promise.all(a).then(yn)}fetchQuery(t){const r=this.defaultQueryOptions(t);r.retry===void 0&&(r.retry=!1);const o=this.#e.build(this,r);return o.isStaleByTime(mc(r.staleTime,o))?o.fetch(r):Promise.resolve(o.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(yn).catch(yn)}fetchInfiniteQuery(t){return t.behavior=Jh(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(yn).catch(yn)}ensureInfiniteQueryData(t){return t.behavior=Jh(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return Il.isOnline()?this.#t.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#t}getDefaultOptions(){return this.#n}setDefaultOptions(t){this.#n=t}setQueryDefaults(t,r){this.#o.set(Ui(t),{queryKey:t,defaultOptions:r})}getQueryDefaults(t){const r=[...this.#o.values()],o={};return r.forEach(a=>{$i(t,a.queryKey)&&Object.assign(o,a.defaultOptions)}),o}setMutationDefaults(t,r){this.#r.set(Ui(t),{mutationKey:t,defaultOptions:r})}getMutationDefaults(t){const r=[...this.#r.values()],o={};return r.forEach(a=>{$i(t,a.mutationKey)&&Object.assign(o,a.defaultOptions)}),o}defaultQueryOptions(t){if(t._defaulted)return t;const r={...this.#n.queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return r.queryHash||(r.queryHash=Fc(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===jc&&(r.enabled=!1),r}defaultMutationOptions(t){return t?._defaulted?t:{...this.#n.mutations,...t?.mutationKey&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){this.#e.clear(),this.#t.clear()}},dm=g.createContext(void 0),l0=t=>{const r=g.useContext(dm);if(!r)throw new Error("No QueryClient set, use QueryClientProvider to set one");return r},s0=({client:t,children:r})=>(g.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),D.jsx(dm.Provider,{value:t,children:r}));/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var fm=t=>{throw TypeError(t)},u0=(t,r,o)=>r.has(t)||fm("Cannot "+o),Yu=(t,r,o)=>(u0(t,r,"read from private field"),o?o.call(t):r.get(t)),c0=(t,r,o)=>r.has(t)?fm("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(t):r.set(t,o),ep="popstate";function d0(t={}){function r(a,s){let{pathname:u,search:d,hash:h}=a.location;return Hi("",{pathname:u,search:d,hash:h},s.state&&s.state.usr||null,s.state&&s.state.key||"default")}function o(a,s){return typeof s=="string"?s:Pr(s)}return h0(r,o,null,t)}function Le(t,r){if(t===!1||t===null||typeof t>"u")throw new Error(r)}function nt(t,r){if(!t){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function f0(){return Math.random().toString(36).substring(2,10)}function tp(t,r){return{usr:t.state,key:t.key,idx:r}}function Hi(t,r,o=null,a){return{pathname:typeof t=="string"?t:t.pathname,search:"",hash:"",...typeof r=="string"?Tr(r):r,state:o,key:r&&r.key||a||f0()}}function Pr({pathname:t="/",search:r="",hash:o=""}){return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),o&&o!=="#"&&(t+=o.charAt(0)==="#"?o:"#"+o),t}function Tr(t){let r={};if(t){let o=t.indexOf("#");o>=0&&(r.hash=t.substring(o),t=t.substring(0,o));let a=t.indexOf("?");a>=0&&(r.search=t.substring(a),t=t.substring(0,a)),t&&(r.pathname=t)}return r}function h0(t,r,o,a={}){let{window:s=document.defaultView,v5Compat:u=!1}=a,d=s.history,h="POP",p=null,m=v();m==null&&(m=0,d.replaceState({...d.state,idx:m},""));function v(){return(d.state||{idx:null}).idx}function w(){h="POP";let R=v(),P=R==null?null:R-m;m=R,p&&p({action:h,location:S.location,delta:P})}function x(R,P){h="PUSH";let j=Hi(S.location,R,P);m=v()+1;let L=tp(j,m),U=S.createHref(j);try{d.pushState(L,"",U)}catch(H){if(H instanceof DOMException&&H.name==="DataCloneError")throw H;s.location.assign(U)}u&&p&&p({action:h,location:S.location,delta:1})}function E(R,P){h="REPLACE";let j=Hi(S.location,R,P);m=v();let L=tp(j,m),U=S.createHref(j);d.replaceState(L,"",U),u&&p&&p({action:h,location:S.location,delta:0})}function k(R){return hm(R)}let S={get action(){return h},get location(){return t(s,d)},listen(R){if(p)throw new Error("A history only accepts one active listener");return s.addEventListener(ep,w),p=R,()=>{s.removeEventListener(ep,w),p=null}},createHref(R){return r(s,R)},createURL:k,encodeLocation(R){let P=k(R);return{pathname:P.pathname,search:P.search,hash:P.hash}},push:x,replace:E,go(R){return d.go(R)}};return S}function hm(t,r=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),Le(o,"No window.location.(origin|href) available to create URL");let a=typeof t=="string"?t:Pr(t);return a=a.replace(/ $/,"%20"),!r&&a.startsWith("//")&&(a=o+a),new URL(a,o)}var ji,np=class{constructor(t){if(c0(this,ji,new Map),t)for(let[r,o]of t)this.set(r,o)}get(t){if(Yu(this,ji).has(t))return Yu(this,ji).get(t);if(t.defaultValue!==void 0)return t.defaultValue;throw new Error("No value found for context")}set(t,r){Yu(this,ji).set(t,r)}};ji=new WeakMap;var p0=new Set(["lazy","caseSensitive","path","id","index","children"]);function m0(t){return p0.has(t)}var v0=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function g0(t){return v0.has(t)}function y0(t){return t.index===!0}function Bi(t,r,o=[],a={},s=!1){return t.map((u,d)=>{let h=[...o,String(d)],p=typeof u.id=="string"?u.id:h.join("-");if(Le(u.index!==!0||!u.children,"Cannot specify children on an index route"),Le(s||!a[p],`Found a route id collision on id "${p}".  Route id's must be globally unique within Data Router usages`),y0(u)){let m={...u,...r(u),id:p};return a[p]=m,m}else{let m={...u,...r(u),id:p,children:void 0};return a[p]=m,u.children&&(m.children=Bi(u.children,r,h,a,s)),m}})}function Rr(t,r,o="/"){return bl(t,r,o,!1)}function bl(t,r,o,a){let s=typeof r=="string"?Tr(r):r,u=on(s.pathname||"/",o);if(u==null)return null;let d=pm(t);x0(d);let h=null;for(let p=0;h==null&&p<d.length;++p){let m=O0(u);h=_0(d[p],m,a)}return h}function w0(t,r){let{route:o,pathname:a,params:s}=t;return{id:o.id,pathname:a,params:s,data:r[o.id],handle:o.handle}}function pm(t,r=[],o=[],a=""){let s=(u,d,h)=>{let p={relativePath:h===void 0?u.path||"":h,caseSensitive:u.caseSensitive===!0,childrenIndex:d,route:u};p.relativePath.startsWith("/")&&(Le(p.relativePath.startsWith(a),`Absolute route path "${p.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),p.relativePath=p.relativePath.slice(a.length));let m=On([a,p.relativePath]),v=o.concat(p);u.children&&u.children.length>0&&(Le(u.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${m}".`),pm(u.children,r,v,m)),!(u.path==null&&!u.index)&&r.push({path:m,score:b0(m,u.index),routesMeta:v})};return t.forEach((u,d)=>{if(u.path===""||!u.path?.includes("?"))s(u,d);else for(let h of mm(u.path))s(u,d,h)}),r}function mm(t){let r=t.split("/");if(r.length===0)return[];let[o,...a]=r,s=o.endsWith("?"),u=o.replace(/\?$/,"");if(a.length===0)return s?[u,""]:[u];let d=mm(a.join("/")),h=[];return h.push(...d.map(p=>p===""?u:[u,p].join("/"))),s&&h.push(...d),h.map(p=>t.startsWith("/")&&p===""?"/":p)}function x0(t){t.sort((r,o)=>r.score!==o.score?o.score-r.score:M0(r.routesMeta.map(a=>a.childrenIndex),o.routesMeta.map(a=>a.childrenIndex)))}var S0=/^:[\w-]+$/,E0=3,C0=2,k0=1,R0=10,P0=-2,rp=t=>t==="*";function b0(t,r){let o=t.split("/"),a=o.length;return o.some(rp)&&(a+=P0),r&&(a+=C0),o.filter(s=>!rp(s)).reduce((s,u)=>s+(S0.test(u)?E0:u===""?k0:R0),a)}function M0(t,r){return t.length===r.length&&t.slice(0,-1).every((a,s)=>a===r[s])?t[t.length-1]-r[r.length-1]:0}function _0(t,r,o=!1){let{routesMeta:a}=t,s={},u="/",d=[];for(let h=0;h<a.length;++h){let p=a[h],m=h===a.length-1,v=u==="/"?r:r.slice(u.length)||"/",w=Fl({path:p.relativePath,caseSensitive:p.caseSensitive,end:m},v),x=p.route;if(!w&&m&&o&&!a[a.length-1].route.index&&(w=Fl({path:p.relativePath,caseSensitive:p.caseSensitive,end:!1},v)),!w)return null;Object.assign(s,w.params),d.push({params:s,pathname:On([u,w.pathname]),pathnameBase:A0(On([u,w.pathnameBase])),route:x}),w.pathnameBase!=="/"&&(u=On([u,w.pathnameBase]))}return d}function Fl(t,r){typeof t=="string"&&(t={path:t,caseSensitive:!1,end:!0});let[o,a]=T0(t.path,t.caseSensitive,t.end),s=r.match(o);if(!s)return null;let u=s[0],d=u.replace(/(.)\/+$/,"$1"),h=s.slice(1);return{params:a.reduce((m,{paramName:v,isOptional:w},x)=>{if(v==="*"){let k=h[x]||"";d=u.slice(0,u.length-k.length).replace(/(.)\/+$/,"$1")}const E=h[x];return w&&!E?m[v]=void 0:m[v]=(E||"").replace(/%2F/g,"/"),m},{}),pathname:u,pathnameBase:d,pattern:t}}function T0(t,r=!1,o=!0){nt(t==="*"||!t.endsWith("*")||t.endsWith("/*"),`Route path "${t}" will be treated as if it were "${t.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${t.replace(/\*$/,"/*")}".`);let a=[],s="^"+t.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(d,h,p)=>(a.push({paramName:h,isOptional:p!=null}),p?"/?([^\\/]+)?":"/([^\\/]+)"));return t.endsWith("*")?(a.push({paramName:"*"}),s+=t==="*"||t==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?s+="\\/*$":t!==""&&t!=="/"&&(s+="(?:(?=\\/|$))"),[new RegExp(s,r?void 0:"i"),a]}function O0(t){try{return t.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return nt(!1,`The URL path "${t}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),t}}function on(t,r){if(r==="/")return t;if(!t.toLowerCase().startsWith(r.toLowerCase()))return null;let o=r.endsWith("/")?r.length-1:r.length,a=t.charAt(o);return a&&a!=="/"?null:t.slice(o)||"/"}function N0({basename:t,pathname:r}){return r==="/"?t:On([t,r])}function D0(t,r="/"){let{pathname:o,search:a="",hash:s=""}=typeof t=="string"?Tr(t):t;return{pathname:o?o.startsWith("/")?o:L0(o,r):r,search:I0(a),hash:F0(s)}}function L0(t,r){let o=r.replace(/\/+$/,"").split("/");return t.split("/").forEach(s=>{s===".."?o.length>1&&o.pop():s!=="."&&o.push(s)}),o.length>1?o.join("/"):"/"}function Xu(t,r,o,a){return`Cannot include a '${t}' character in a manually specified \`to.${r}\` field [${JSON.stringify(a)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function vm(t){return t.filter((r,o)=>o===0||r.route.path&&r.route.path.length>0)}function Gl(t){let r=vm(t);return r.map((o,a)=>a===r.length-1?o.pathname:o.pathnameBase)}function Yl(t,r,o,a=!1){let s;typeof t=="string"?s=Tr(t):(s={...t},Le(!s.pathname||!s.pathname.includes("?"),Xu("?","pathname","search",s)),Le(!s.pathname||!s.pathname.includes("#"),Xu("#","pathname","hash",s)),Le(!s.search||!s.search.includes("#"),Xu("#","search","hash",s)));let u=t===""||s.pathname==="",d=u?"/":s.pathname,h;if(d==null)h=o;else{let w=r.length-1;if(!a&&d.startsWith("..")){let x=d.split("/");for(;x[0]==="..";)x.shift(),w-=1;s.pathname=x.join("/")}h=w>=0?r[w]:"/"}let p=D0(s,h),m=d&&d!=="/"&&d.endsWith("/"),v=(u||d===".")&&o.endsWith("/");return!p.pathname.endsWith("/")&&(m||v)&&(p.pathname+="/"),p}var On=t=>t.join("/").replace(/\/\/+/g,"/"),A0=t=>t.replace(/\/+$/,"").replace(/^\/*/,"/"),I0=t=>!t||t==="?"?"":t.startsWith("?")?t:"?"+t,F0=t=>!t||t==="#"?"":t.startsWith("#")?t:"#"+t,jl=class{constructor(t,r,o,a=!1){this.status=t,this.statusText=r||"",this.internal=a,o instanceof Error?(this.data=o.toString(),this.error=o):this.data=o}};function Vi(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.internal=="boolean"&&"data"in t}var gm=["POST","PUT","PATCH","DELETE"],j0=new Set(gm),z0=["GET",...gm],U0=new Set(z0),$0=new Set([301,302,303,307,308]),H0=new Set([307,308]),qu={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},B0={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Li={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},V0=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,zc=t=>V0.test(t),W0=t=>({hasErrorBoundary:!!t.hasErrorBoundary}),ym="remix-router-transitions",wm=Symbol("ResetLoaderData");function K0(t){const r=t.window?t.window:typeof window<"u"?window:void 0,o=typeof r<"u"&&typeof r.document<"u"&&typeof r.document.createElement<"u";Le(t.routes.length>0,"You must provide a non-empty routes array to createRouter");let a=t.hydrationRouteProperties||[],s=t.mapRouteProperties||W0,u={},d=Bi(t.routes,s,void 0,u),h,p=t.basename||"/",m=t.dataStrategy||q0,v={unstable_middleware:!1,...t.future},w=null,x=new Set,E=null,k=null,S=null,R=t.hydrationData!=null,P=Rr(d,t.history.location,p),j=!1,L=null,U;if(P==null&&!t.patchRoutesOnNavigation){let b=nn(404,{pathname:t.history.location.pathname}),{matches:A,route:$}=mp(d);U=!0,P=A,L={[$.id]:b}}else if(P&&!t.hydrationData&&Ar(P,d,t.history.location.pathname).active&&(P=null),P)if(P.some(b=>b.route.lazy))U=!1;else if(!P.some(b=>b.route.loader))U=!0;else{let b=t.hydrationData?t.hydrationData.loaderData:null,A=t.hydrationData?t.hydrationData.errors:null;if(A){let $=P.findIndex(q=>A[q.route.id]!==void 0);U=P.slice(0,$+1).every(q=>!yc(q.route,b,A))}else U=P.every($=>!yc($.route,b,A))}else{U=!1,P=[];let b=Ar(null,d,t.history.location.pathname);b.active&&b.matches&&(j=!0,P=b.matches)}let H,_={historyAction:t.history.action,location:t.history.location,matches:P,initialized:U,navigation:qu,restoreScrollPosition:t.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:t.hydrationData&&t.hydrationData.loaderData||{},actionData:t.hydrationData&&t.hydrationData.actionData||null,errors:t.hydrationData&&t.hydrationData.errors||L,fetchers:new Map,blockers:new Map},K="POP",V=!1,te,ve=!1,Te=new Map,_e=null,oe=!1,ae=!1,Pe=new Set,Z=new Map,se=0,N=-1,W=new Map,G=new Set,T=new Map,B=new Map,ne=new Set,ue=new Map,be,Re=null;function ie(){if(w=t.history.listen(({action:b,location:A,delta:$})=>{if(be){be(),be=void 0;return}nt(ue.size===0||$!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let q=so({currentLocation:_.location,nextLocation:A,historyAction:b});if(q&&$!=null){let ee=new Promise(we=>{be=we});t.history.go($*-1),Zn(q,{state:"blocked",location:A,proceed(){Zn(q,{state:"proceeding",proceed:void 0,reset:void 0,location:A}),ee.then(()=>t.history.go($))},reset(){let we=new Map(_.blockers);we.set(q,Li),Ae({blockers:we})}});return}return sn(b,A)}),o){sx(r,Te);let b=()=>ux(r,Te);r.addEventListener("pagehide",b),_e=()=>r.removeEventListener("pagehide",b)}return _.initialized||sn("POP",_.location,{initialHydration:!0}),H}function Oe(){w&&w(),_e&&_e(),x.clear(),te&&te.abort(),_.fetchers.forEach((b,A)=>ao(A)),_.blockers.forEach((b,A)=>lo(A))}function je(b){return x.add(b),()=>x.delete(b)}function Ae(b,A={}){b.matches&&(b.matches=b.matches.map(ee=>{let we=u[ee.route.id],me=ee.route;return me.element!==we.element||me.errorElement!==we.errorElement||me.hydrateFallbackElement!==we.hydrateFallbackElement?{...ee,route:we}:ee})),_={..._,...b};let $=[],q=[];_.fetchers.forEach((ee,we)=>{ee.state==="idle"&&(ne.has(we)?$.push(we):q.push(we))}),ne.forEach(ee=>{!_.fetchers.has(ee)&&!Z.has(ee)&&$.push(ee)}),[...x].forEach(ee=>ee(_,{deletedFetchers:$,viewTransitionOpts:A.viewTransitionOpts,flushSync:A.flushSync===!0})),$.forEach(ee=>ao(ee)),q.forEach(ee=>_.fetchers.delete(ee))}function pt(b,A,{flushSync:$}={}){let q=_.actionData!=null&&_.navigation.formMethod!=null&&Ft(_.navigation.formMethod)&&_.navigation.state==="loading"&&b.state?._isRedirect!==!0,ee;A.actionData?Object.keys(A.actionData).length>0?ee=A.actionData:ee=null:q?ee=_.actionData:ee=null;let we=A.loaderData?hp(_.loaderData,A.loaderData,A.matches||[],A.errors):_.loaderData,me=_.blockers;me.size>0&&(me=new Map(me),me.forEach((xe,Se)=>me.set(Se,Li)));let ce=oe?!1:Jo(b,A.matches||_.matches),ye=V===!0||_.navigation.formMethod!=null&&Ft(_.navigation.formMethod)&&b.state?._isRedirect!==!0;h&&(d=h,h=void 0),oe||K==="POP"||(K==="PUSH"?t.history.push(b,b.state):K==="REPLACE"&&t.history.replace(b,b.state));let Ee;if(K==="POP"){let xe=Te.get(_.location.pathname);xe&&xe.has(b.pathname)?Ee={currentLocation:_.location,nextLocation:b}:Te.has(b.pathname)&&(Ee={currentLocation:b,nextLocation:_.location})}else if(ve){let xe=Te.get(_.location.pathname);xe?xe.add(b.pathname):(xe=new Set([b.pathname]),Te.set(_.location.pathname,xe)),Ee={currentLocation:_.location,nextLocation:b}}Ae({...A,actionData:ee,loaderData:we,historyAction:K,location:b,initialized:!0,navigation:qu,revalidation:"idle",restoreScrollPosition:ce,preventScrollReset:ye,blockers:me},{viewTransitionOpts:Ee,flushSync:$===!0}),K="POP",V=!1,ve=!1,oe=!1,ae=!1,Re?.resolve(),Re=null}async function Qt(b,A){if(typeof b=="number"){t.history.go(b);return}let $=gc(_.location,_.matches,p,b,A?.fromRouteId,A?.relative),{path:q,submission:ee,error:we}=op(!1,$,A),me=_.location,ce=Hi(_.location,q,A&&A.state);ce={...ce,...t.history.encodeLocation(ce)};let ye=A&&A.replace!=null?A.replace:void 0,Ee="PUSH";ye===!0?Ee="REPLACE":ye===!1||ee!=null&&Ft(ee.formMethod)&&ee.formAction===_.location.pathname+_.location.search&&(Ee="REPLACE");let xe=A&&"preventScrollReset"in A?A.preventScrollReset===!0:void 0,Se=(A&&A.flushSync)===!0,Ie=so({currentLocation:me,nextLocation:ce,historyAction:Ee});if(Ie){Zn(Ie,{state:"blocked",location:ce,proceed(){Zn(Ie,{state:"proceeding",proceed:void 0,reset:void 0,location:ce}),Qt(b,A)},reset(){let He=new Map(_.blockers);He.set(Ie,Li),Ae({blockers:He})}});return}await sn(Ee,ce,{submission:ee,pendingError:we,preventScrollReset:xe,replace:A&&A.replace,enableViewTransition:A&&A.viewTransition,flushSync:Se})}function ln(){Re||(Re=cx()),io(),Ae({revalidation:"loading"});let b=Re.promise;return _.navigation.state==="submitting"?b:_.navigation.state==="idle"?(sn(_.historyAction,_.location,{startUninterruptedRevalidation:!0}),b):(sn(K||_.historyAction,_.navigation.location,{overrideNavigation:_.navigation,enableViewTransition:ve===!0}),b)}async function sn(b,A,$){te&&te.abort(),te=null,K=b,oe=($&&$.startUninterruptedRevalidation)===!0,er(_.location,_.matches),V=($&&$.preventScrollReset)===!0,ve=($&&$.enableViewTransition)===!0;let q=h||d,ee=$&&$.overrideNavigation,we=$?.initialHydration&&_.matches&&_.matches.length>0&&!j?_.matches:Rr(q,A,p),me=($&&$.flushSync)===!0;if(we&&_.initialized&&!ae&&rx(_.location,A)&&!($&&$.submission&&Ft($.submission.formMethod))){pt(A,{matches:we},{flushSync:me});return}let ce=Ar(we,q,A.pathname);if(ce.active&&ce.matches&&(we=ce.matches),!we){let{error:ut,notFoundMatches:qe,route:We}=Lr(A.pathname);pt(A,{matches:qe,loaderData:{},errors:{[We.id]:ut}},{flushSync:me});return}te=new AbortController;let ye=Fo(t.history,A,te.signal,$&&$.submission),Ee=new np(t.unstable_getContext?await t.unstable_getContext():void 0),xe;if($&&$.pendingError)xe=[Jr(we).route.id,{type:"error",error:$.pendingError}];else if($&&$.submission&&Ft($.submission.formMethod)){let ut=await ia(ye,A,$.submission,we,Ee,ce.active,$&&$.initialHydration===!0,{replace:$.replace,flushSync:me});if(ut.shortCircuited)return;if(ut.pendingActionResult){let[qe,We]=ut.pendingActionResult;if(Vt(We)&&Vi(We.error)&&We.error.status===404){te=null,pt(A,{matches:ut.matches,loaderData:{},errors:{[qe]:We.error}});return}}we=ut.matches||we,xe=ut.pendingActionResult,ee=Ju(A,$.submission),me=!1,ce.active=!1,ye=Fo(t.history,ye.url,ye.signal)}let{shortCircuited:Se,matches:Ie,loaderData:He,errors:it}=await aa(ye,A,we,Ee,ce.active,ee,$&&$.submission,$&&$.fetcherSubmission,$&&$.replace,$&&$.initialHydration===!0,me,xe);Se||(te=null,pt(A,{matches:Ie||we,...pp(xe),loaderData:He,errors:it}))}async function ia(b,A,$,q,ee,we,me,ce={}){io();let ye=ax(A,$);if(Ae({navigation:ye},{flushSync:ce.flushSync===!0}),we){let Se=await Ir(q,A.pathname,b.signal);if(Se.type==="aborted")return{shortCircuited:!0};if(Se.type==="error"){let Ie=Jr(Se.partialMatches).route.id;return{matches:Se.partialMatches,pendingActionResult:[Ie,{type:"error",error:Se.error}]}}else if(Se.matches)q=Se.matches;else{let{notFoundMatches:Ie,error:He,route:it}=Lr(A.pathname);return{matches:Ie,pendingActionResult:[it.id,{type:"error",error:He}]}}}let Ee,xe=Ml(q,A);if(!xe.route.action&&!xe.route.lazy)Ee={type:"error",error:nn(405,{method:b.method,pathname:A.pathname,routeId:xe.route.id})};else{let Se=jo(s,u,b,q,xe,me?[]:a,ee),Ie=await Jn(b,Se,ee,null);if(Ee=Ie[xe.route.id],!Ee){for(let He of q)if(Ie[He.route.id]){Ee=Ie[He.route.id];break}}if(b.signal.aborted)return{shortCircuited:!0}}if(Zr(Ee)){let Se;return ce&&ce.replace!=null?Se=ce.replace:Se=cp(Ee.response.headers.get("Location"),new URL(b.url),p)===_.location.pathname+_.location.search,await un(b,Ee,!0,{submission:$,replace:Se}),{shortCircuited:!0}}if(Vt(Ee)){let Se=Jr(q,xe.route.id);return(ce&&ce.replace)!==!0&&(K="PUSH"),{matches:q,pendingActionResult:[Se.route.id,Ee,xe.route.id]}}return{matches:q,pendingActionResult:[xe.route.id,Ee]}}async function aa(b,A,$,q,ee,we,me,ce,ye,Ee,xe,Se){let Ie=we||Ju(A,me),He=me||ce||gp(Ie),it=!oe&&!Ee;if(ee){if(it){let ct=oo(Se);Ae({navigation:Ie,...ct!==void 0?{actionData:ct}:{}},{flushSync:xe})}let ze=await Ir($,A.pathname,b.signal);if(ze.type==="aborted")return{shortCircuited:!0};if(ze.type==="error"){let ct=Jr(ze.partialMatches).route.id;return{matches:ze.partialMatches,loaderData:{},errors:{[ct]:ze.error}}}else if(ze.matches)$=ze.matches;else{let{error:ct,notFoundMatches:jr,route:kn}=Lr(A.pathname);return{matches:jr,loaderData:{},errors:{[kn.id]:ct}}}}let ut=h||d,{dsMatches:qe,revalidatingFetchers:We}=ip(b,q,s,u,t.history,_,$,He,A,Ee?[]:a,Ee===!0,ae,Pe,ne,T,G,ut,p,t.patchRoutesOnNavigation!=null,Se);if(N=++se,!t.dataStrategy&&!qe.some(ze=>ze.shouldLoad)&&We.length===0){let ze=Yo();return pt(A,{matches:$,loaderData:{},errors:Se&&Vt(Se[1])?{[Se[0]]:Se[1].error}:null,...pp(Se),...ze?{fetchers:new Map(_.fetchers)}:{}},{flushSync:xe}),{shortCircuited:!0}}if(it){let ze={};if(!ee){ze.navigation=Ie;let ct=oo(Se);ct!==void 0&&(ze.actionData=ct)}We.length>0&&(ze.fetchers=la(We)),Ae(ze,{flushSync:xe})}We.forEach(ze=>{Mt(ze.key),ze.controller&&Z.set(ze.key,ze.controller)});let Cn=()=>We.forEach(ze=>Mt(ze.key));te&&te.signal.addEventListener("abort",Cn);let{loaderResults:tr,fetcherResults:dn}=await Go(qe,We,b,q);if(b.signal.aborted)return{shortCircuited:!0};te&&te.signal.removeEventListener("abort",Cn),We.forEach(ze=>Z.delete(ze.key));let Gt=vl(tr);if(Gt)return await un(b,Gt.result,!0,{replace:ye}),{shortCircuited:!0};if(Gt=vl(dn),Gt)return G.add(Gt.key),await un(b,Gt.result,!0,{replace:ye}),{shortCircuited:!0};let{loaderData:Zo,errors:Ke}=fp(_,$,tr,Se,We,dn);Ee&&_.errors&&(Ke={..._.errors,...Ke});let jn=Yo(),nr=Xo(N),Fr=jn||nr||We.length>0;return{matches:$,loaderData:Zo,errors:Ke,...Fr?{fetchers:new Map(_.fetchers)}:{}}}function oo(b){if(b&&!Vt(b[1]))return{[b[0]]:b[1].data};if(_.actionData)return Object.keys(_.actionData).length===0?null:_.actionData}function la(b){return b.forEach(A=>{let $=_.fetchers.get(A.key),q=Ai(void 0,$?$.data:void 0);_.fetchers.set(A.key,q)}),new Map(_.fetchers)}async function Qo(b,A,$,q){Mt(b);let ee=(q&&q.flushSync)===!0,we=h||d,me=gc(_.location,_.matches,p,$,A,q?.relative),ce=Rr(we,me,p),ye=Ar(ce,we,me);if(ye.active&&ye.matches&&(ce=ye.matches),!ce){zt(b,A,nn(404,{pathname:me}),{flushSync:ee});return}let{path:Ee,submission:xe,error:Se}=op(!0,me,q);if(Se){zt(b,A,Se,{flushSync:ee});return}let Ie=new np(t.unstable_getContext?await t.unstable_getContext():void 0),He=(q&&q.preventScrollReset)===!0;if(xe&&Ft(xe.formMethod)){await Or(b,A,Ee,ce,Ie,ye.active,ee,He,xe);return}T.set(b,{routeId:A,path:Ee}),await qn(b,A,Ee,ce,Ie,ye.active,ee,He,xe)}async function Or(b,A,$,q,ee,we,me,ce,ye){io(),T.delete(b);let Ee=_.fetchers.get(b);cn(b,lx(ye,Ee),{flushSync:me});let xe=new AbortController,Se=Fo(t.history,$,xe.signal,ye);if(we){let Be=await Ir(q,new URL(Se.url).pathname,Se.signal,b);if(Be.type==="aborted")return;if(Be.type==="error"){zt(b,A,Be.error,{flushSync:me});return}else if(Be.matches)q=Be.matches;else{zt(b,A,nn(404,{pathname:$}),{flushSync:me});return}}let Ie=Ml(q,$);if(!Ie.route.action&&!Ie.route.lazy){let Be=nn(405,{method:ye.formMethod,pathname:$,routeId:A});zt(b,A,Be,{flushSync:me});return}Z.set(b,xe);let He=se,it=jo(s,u,Se,q,Ie,a,ee),qe=(await Jn(Se,it,ee,b))[Ie.route.id];if(Se.signal.aborted){Z.get(b)===xe&&Z.delete(b);return}if(ne.has(b)){if(Zr(qe)||Vt(qe)){cn(b,Cr(void 0));return}}else{if(Zr(qe))if(Z.delete(b),N>He){cn(b,Cr(void 0));return}else return G.add(b),cn(b,Ai(ye)),un(Se,qe,!1,{fetcherSubmission:ye,preventScrollReset:ce});if(Vt(qe)){zt(b,A,qe.error);return}}let We=_.navigation.location||_.location,Cn=Fo(t.history,We,xe.signal),tr=h||d,dn=_.navigation.state!=="idle"?Rr(tr,_.navigation.location,p):_.matches;Le(dn,"Didn't find any matches after fetcher action");let Gt=++se;W.set(b,Gt);let Zo=Ai(ye,qe.data);_.fetchers.set(b,Zo);let{dsMatches:Ke,revalidatingFetchers:jn}=ip(Cn,ee,s,u,t.history,_,dn,ye,We,a,!1,ae,Pe,ne,T,G,tr,p,t.patchRoutesOnNavigation!=null,[Ie.route.id,qe]);jn.filter(Be=>Be.key!==b).forEach(Be=>{let uo=Be.key,_t=_.fetchers.get(uo),us=Ai(void 0,_t?_t.data:void 0);_.fetchers.set(uo,us),Mt(uo),Be.controller&&Z.set(uo,Be.controller)}),Ae({fetchers:new Map(_.fetchers)});let nr=()=>jn.forEach(Be=>Mt(Be.key));xe.signal.addEventListener("abort",nr);let{loaderResults:Fr,fetcherResults:ze}=await Go(Ke,jn,Cn,ee);if(xe.signal.aborted)return;if(xe.signal.removeEventListener("abort",nr),W.delete(b),Z.delete(b),jn.forEach(Be=>Z.delete(Be.key)),_.fetchers.has(b)){let Be=Cr(qe.data);_.fetchers.set(b,Be)}let ct=vl(Fr);if(ct)return un(Cn,ct.result,!1,{preventScrollReset:ce});if(ct=vl(ze),ct)return G.add(ct.key),un(Cn,ct.result,!1,{preventScrollReset:ce});let{loaderData:jr,errors:kn}=fp(_,dn,Fr,void 0,jn,ze);Xo(Gt),_.navigation.state==="loading"&&Gt>N?(Le(K,"Expected pending action"),te&&te.abort(),pt(_.navigation.location,{matches:dn,loaderData:jr,errors:kn,fetchers:new Map(_.fetchers)})):(Ae({errors:kn,loaderData:hp(_.loaderData,jr,dn,kn),fetchers:new Map(_.fetchers)}),ae=!1)}async function qn(b,A,$,q,ee,we,me,ce,ye){let Ee=_.fetchers.get(b);cn(b,Ai(ye,Ee?Ee.data:void 0),{flushSync:me});let xe=new AbortController,Se=Fo(t.history,$,xe.signal);if(we){let We=await Ir(q,new URL(Se.url).pathname,Se.signal,b);if(We.type==="aborted")return;if(We.type==="error"){zt(b,A,We.error,{flushSync:me});return}else if(We.matches)q=We.matches;else{zt(b,A,nn(404,{pathname:$}),{flushSync:me});return}}let Ie=Ml(q,$);Z.set(b,xe);let He=se,it=jo(s,u,Se,q,Ie,a,ee),qe=(await Jn(Se,it,ee,b))[Ie.route.id];if(Z.get(b)===xe&&Z.delete(b),!Se.signal.aborted){if(ne.has(b)){cn(b,Cr(void 0));return}if(Zr(qe))if(N>He){cn(b,Cr(void 0));return}else{G.add(b),await un(Se,qe,!1,{preventScrollReset:ce});return}if(Vt(qe)){zt(b,A,qe.error);return}cn(b,Cr(qe.data))}}async function un(b,A,$,{submission:q,fetcherSubmission:ee,preventScrollReset:we,replace:me}={}){A.response.headers.has("X-Remix-Revalidate")&&(ae=!0);let ce=A.response.headers.get("Location");Le(ce,"Expected a Location header on the redirect Response"),ce=cp(ce,new URL(b.url),p);let ye=Hi(_.location,ce,{_isRedirect:!0});if(o){let it=!1;if(A.response.headers.has("X-Remix-Reload-Document"))it=!0;else if(zc(ce)){const ut=hm(ce,!0);it=ut.origin!==r.location.origin||on(ut.pathname,p)==null}if(it){me?r.location.replace(ce):r.location.assign(ce);return}}te=null;let Ee=me===!0||A.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:xe,formAction:Se,formEncType:Ie}=_.navigation;!q&&!ee&&xe&&Se&&Ie&&(q=gp(_.navigation));let He=q||ee;if(H0.has(A.response.status)&&He&&Ft(He.formMethod))await sn(Ee,ye,{submission:{...He,formAction:ce},preventScrollReset:we||V,enableViewTransition:$?ve:void 0});else{let it=Ju(ye,q);await sn(Ee,ye,{overrideNavigation:it,fetcherSubmission:ee,preventScrollReset:we||V,enableViewTransition:$?ve:void 0})}}async function Jn(b,A,$,q){let ee,we={};try{ee=await J0(m,b,A,q,$,!1)}catch(me){return A.filter(ce=>ce.shouldLoad).forEach(ce=>{we[ce.route.id]={type:"error",error:me}}),we}if(b.signal.aborted)return we;for(let[me,ce]of Object.entries(ee))if(ox(ce)){let ye=ce.result;we[me]={type:"redirect",response:tx(ye,b,me,A,p)}}else we[me]=await ex(ce);return we}async function Go(b,A,$,q){let ee=Jn($,b,q,null),we=Promise.all(A.map(async ye=>{if(ye.matches&&ye.match&&ye.request&&ye.controller){let xe=(await Jn(ye.request,ye.matches,q,ye.key))[ye.match.route.id];return{[ye.key]:xe}}else return Promise.resolve({[ye.key]:{type:"error",error:nn(404,{pathname:ye.path})}})})),me=await ee,ce=(await we).reduce((ye,Ee)=>Object.assign(ye,Ee),{});return{loaderResults:me,fetcherResults:ce}}function io(){ae=!0,T.forEach((b,A)=>{Z.has(A)&&Pe.add(A),Mt(A)})}function cn(b,A,$={}){_.fetchers.set(b,A),Ae({fetchers:new Map(_.fetchers)},{flushSync:($&&$.flushSync)===!0})}function zt(b,A,$,q={}){let ee=Jr(_.matches,A);ao(b),Ae({errors:{[ee.route.id]:$},fetchers:new Map(_.fetchers)},{flushSync:(q&&q.flushSync)===!0})}function Nr(b){return B.set(b,(B.get(b)||0)+1),ne.has(b)&&ne.delete(b),_.fetchers.get(b)||B0}function ao(b){let A=_.fetchers.get(b);Z.has(b)&&!(A&&A.state==="loading"&&W.has(b))&&Mt(b),T.delete(b),W.delete(b),G.delete(b),ne.delete(b),Pe.delete(b),_.fetchers.delete(b)}function Dr(b){let A=(B.get(b)||0)-1;A<=0?(B.delete(b),ne.add(b)):B.set(b,A),Ae({fetchers:new Map(_.fetchers)})}function Mt(b){let A=Z.get(b);A&&(A.abort(),Z.delete(b))}function sa(b){for(let A of b){let $=Nr(A),q=Cr($.data);_.fetchers.set(A,q)}}function Yo(){let b=[],A=!1;for(let $ of G){let q=_.fetchers.get($);Le(q,`Expected fetcher: ${$}`),q.state==="loading"&&(G.delete($),b.push($),A=!0)}return sa(b),A}function Xo(b){let A=[];for(let[$,q]of W)if(q<b){let ee=_.fetchers.get($);Le(ee,`Expected fetcher: ${$}`),ee.state==="loading"&&(Mt($),W.delete($),A.push($))}return sa(A),A.length>0}function ss(b,A){let $=_.blockers.get(b)||Li;return ue.get(b)!==A&&ue.set(b,A),$}function lo(b){_.blockers.delete(b),ue.delete(b)}function Zn(b,A){let $=_.blockers.get(b)||Li;Le($.state==="unblocked"&&A.state==="blocked"||$.state==="blocked"&&A.state==="blocked"||$.state==="blocked"&&A.state==="proceeding"||$.state==="blocked"&&A.state==="unblocked"||$.state==="proceeding"&&A.state==="unblocked",`Invalid blocker state transition: ${$.state} -> ${A.state}`);let q=new Map(_.blockers);q.set(b,A),Ae({blockers:q})}function so({currentLocation:b,nextLocation:A,historyAction:$}){if(ue.size===0)return;ue.size>1&&nt(!1,"A router only supports one blocker at a time");let q=Array.from(ue.entries()),[ee,we]=q[q.length-1],me=_.blockers.get(ee);if(!(me&&me.state==="proceeding")&&we({currentLocation:b,nextLocation:A,historyAction:$}))return ee}function Lr(b){let A=nn(404,{pathname:b}),$=h||d,{matches:q,route:ee}=mp($);return{notFoundMatches:q,route:ee,error:A}}function qo(b,A,$){if(E=b,S=A,k=$||null,!R&&_.navigation===qu){R=!0;let q=Jo(_.location,_.matches);q!=null&&Ae({restoreScrollPosition:q})}return()=>{E=null,S=null,k=null}}function Fn(b,A){return k&&k(b,A.map(q=>w0(q,_.loaderData)))||b.key}function er(b,A){if(E&&S){let $=Fn(b,A);E[$]=S()}}function Jo(b,A){if(E){let $=Fn(b,A),q=E[$];if(typeof q=="number")return q}return null}function Ar(b,A,$){if(t.patchRoutesOnNavigation)if(b){if(Object.keys(b[0].params).length>0)return{active:!0,matches:bl(A,$,p,!0)}}else return{active:!0,matches:bl(A,$,p,!0)||[]};return{active:!1,matches:null}}async function Ir(b,A,$,q){if(!t.patchRoutesOnNavigation)return{type:"success",matches:b};let ee=b;for(;;){let we=h==null,me=h||d,ce=u;try{await t.patchRoutesOnNavigation({signal:$,path:A,matches:ee,fetcherKey:q,patch:(xe,Se)=>{$.aborted||ap(xe,Se,me,ce,s,!1)}})}catch(xe){return{type:"error",error:xe,partialMatches:ee}}finally{we&&!$.aborted&&(d=[...d])}if($.aborted)return{type:"aborted"};let ye=Rr(me,A,p);if(ye)return{type:"success",matches:ye};let Ee=bl(me,A,p,!0);if(!Ee||ee.length===Ee.length&&ee.every((xe,Se)=>xe.route.id===Ee[Se].route.id))return{type:"success",matches:null};ee=Ee}}function ua(b){u={},h=Bi(b,s,void 0,u)}function ca(b,A,$=!1){let q=h==null;ap(b,A,h||d,u,s,$),q&&(d=[...d],Ae({}))}return H={get basename(){return p},get future(){return v},get state(){return _},get routes(){return d},get window(){return r},initialize:ie,subscribe:je,enableScrollRestoration:qo,navigate:Qt,fetch:Qo,revalidate:ln,createHref:b=>t.history.createHref(b),encodeLocation:b=>t.history.encodeLocation(b),getFetcher:Nr,deleteFetcher:Dr,dispose:Oe,getBlocker:ss,deleteBlocker:lo,patchRoutes:ca,_internalFetchControllers:Z,_internalSetRoutes:ua,_internalSetStateDoNotUseOrYouWillBreakYourApp(b){Ae(b)}},H}function Q0(t){return t!=null&&("formData"in t&&t.formData!=null||"body"in t&&t.body!==void 0)}function gc(t,r,o,a,s,u){let d,h;if(s){d=[];for(let m of r)if(d.push(m),m.route.id===s){h=m;break}}else d=r,h=r[r.length-1];let p=Yl(a||".",Gl(d),on(t.pathname,o)||t.pathname,u==="path");if(a==null&&(p.search=t.search,p.hash=t.hash),(a==null||a===""||a===".")&&h){let m=Uc(p.search);if(h.route.index&&!m)p.search=p.search?p.search.replace(/^\?/,"?index&"):"?index";else if(!h.route.index&&m){let v=new URLSearchParams(p.search),w=v.getAll("index");v.delete("index"),w.filter(E=>E).forEach(E=>v.append("index",E));let x=v.toString();p.search=x?`?${x}`:""}}return o!=="/"&&(p.pathname=N0({basename:o,pathname:p.pathname})),Pr(p)}function op(t,r,o){if(!o||!Q0(o))return{path:r};if(o.formMethod&&!ix(o.formMethod))return{path:r,error:nn(405,{method:o.formMethod})};let a=()=>({path:r,error:nn(400,{type:"invalid-body"})}),u=(o.formMethod||"get").toUpperCase(),d=Rm(r);if(o.body!==void 0){if(o.formEncType==="text/plain"){if(!Ft(u))return a();let w=typeof o.body=="string"?o.body:o.body instanceof FormData||o.body instanceof URLSearchParams?Array.from(o.body.entries()).reduce((x,[E,k])=>`${x}${E}=${k}
`,""):String(o.body);return{path:r,submission:{formMethod:u,formAction:d,formEncType:o.formEncType,formData:void 0,json:void 0,text:w}}}else if(o.formEncType==="application/json"){if(!Ft(u))return a();try{let w=typeof o.body=="string"?JSON.parse(o.body):o.body;return{path:r,submission:{formMethod:u,formAction:d,formEncType:o.formEncType,formData:void 0,json:w,text:void 0}}}catch{return a()}}}Le(typeof FormData=="function","FormData is not available in this environment");let h,p;if(o.formData)h=xc(o.formData),p=o.formData;else if(o.body instanceof FormData)h=xc(o.body),p=o.body;else if(o.body instanceof URLSearchParams)h=o.body,p=dp(h);else if(o.body==null)h=new URLSearchParams,p=new FormData;else try{h=new URLSearchParams(o.body),p=dp(h)}catch{return a()}let m={formMethod:u,formAction:d,formEncType:o&&o.formEncType||"application/x-www-form-urlencoded",formData:p,json:void 0,text:void 0};if(Ft(m.formMethod))return{path:r,submission:m};let v=Tr(r);return t&&v.search&&Uc(v.search)&&h.append("index",""),v.search=`?${h}`,{path:Pr(v),submission:m}}function ip(t,r,o,a,s,u,d,h,p,m,v,w,x,E,k,S,R,P,j,L){let U=L?Vt(L[1])?L[1].error:L[1].data:void 0,H=s.createURL(u.location),_=s.createURL(p),K;if(v&&u.errors){let oe=Object.keys(u.errors)[0];K=d.findIndex(ae=>ae.route.id===oe)}else if(L&&Vt(L[1])){let oe=L[0];K=d.findIndex(ae=>ae.route.id===oe)-1}let V=L?L[1].statusCode:void 0,te=V&&V>=400,ve={currentUrl:H,currentParams:u.matches[0]?.params||{},nextUrl:_,nextParams:d[0].params,...h,actionResult:U,actionStatus:V},Te=d.map((oe,ae)=>{let{route:Pe}=oe,Z=null;if(K!=null&&ae>K?Z=!1:Pe.lazy?Z=!0:Pe.loader==null?Z=!1:v?Z=yc(Pe,u.loaderData,u.errors):G0(u.loaderData,u.matches[ae],oe)&&(Z=!0),Z!==null)return wc(o,a,t,oe,m,r,Z);let se=te?!1:w||H.pathname+H.search===_.pathname+_.search||H.search!==_.search||Y0(u.matches[ae],oe),N={...ve,defaultShouldRevalidate:se},W=zl(oe,N);return wc(o,a,t,oe,m,r,W,N)}),_e=[];return k.forEach((oe,ae)=>{if(v||!d.some(B=>B.route.id===oe.routeId)||E.has(ae))return;let Pe=u.fetchers.get(ae),Z=Pe&&Pe.state!=="idle"&&Pe.data===void 0,se=Rr(R,oe.path,P);if(!se){if(j&&Z)return;_e.push({key:ae,routeId:oe.routeId,path:oe.path,matches:null,match:null,request:null,controller:null});return}if(S.has(ae))return;let N=Ml(se,oe.path),W=new AbortController,G=Fo(s,oe.path,W.signal),T=null;if(x.has(ae))x.delete(ae),T=jo(o,a,G,se,N,m,r);else if(Z)w&&(T=jo(o,a,G,se,N,m,r));else{let B={...ve,defaultShouldRevalidate:te?!1:w};zl(N,B)&&(T=jo(o,a,G,se,N,m,r,B))}T&&_e.push({key:ae,routeId:oe.routeId,path:oe.path,matches:T,match:N,request:G,controller:W})}),{dsMatches:Te,revalidatingFetchers:_e}}function yc(t,r,o){if(t.lazy)return!0;if(!t.loader)return!1;let a=r!=null&&t.id in r,s=o!=null&&o[t.id]!==void 0;return!a&&s?!1:typeof t.loader=="function"&&t.loader.hydrate===!0?!0:!a&&!s}function G0(t,r,o){let a=!r||o.route.id!==r.route.id,s=!t.hasOwnProperty(o.route.id);return a||s}function Y0(t,r){let o=t.route.path;return t.pathname!==r.pathname||o!=null&&o.endsWith("*")&&t.params["*"]!==r.params["*"]}function zl(t,r){if(t.route.shouldRevalidate){let o=t.route.shouldRevalidate(r);if(typeof o=="boolean")return o}return r.defaultShouldRevalidate}function ap(t,r,o,a,s,u){let d;if(t){let m=a[t];Le(m,`No route found to patch children into: routeId = ${t}`),m.children||(m.children=[]),d=m.children}else d=o;let h=[],p=[];if(r.forEach(m=>{let v=d.find(w=>xm(m,w));v?p.push({existingRoute:v,newRoute:m}):h.push(m)}),h.length>0){let m=Bi(h,s,[t||"_","patch",String(d?.length||"0")],a);d.push(...m)}if(u&&p.length>0)for(let m=0;m<p.length;m++){let{existingRoute:v,newRoute:w}=p[m],x=v,[E]=Bi([w],s,[],{},!0);Object.assign(x,{element:E.element?E.element:x.element,errorElement:E.errorElement?E.errorElement:x.errorElement,hydrateFallbackElement:E.hydrateFallbackElement?E.hydrateFallbackElement:x.hydrateFallbackElement})}}function xm(t,r){return"id"in t&&"id"in r&&t.id===r.id?!0:t.index===r.index&&t.path===r.path&&t.caseSensitive===r.caseSensitive?(!t.children||t.children.length===0)&&(!r.children||r.children.length===0)?!0:t.children.every((o,a)=>r.children?.some(s=>xm(o,s))):!1}var lp=new WeakMap,Sm=({key:t,route:r,manifest:o,mapRouteProperties:a})=>{let s=o[r.id];if(Le(s,"No route found in manifest"),!s.lazy||typeof s.lazy!="object")return;let u=s.lazy[t];if(!u)return;let d=lp.get(s);d||(d={},lp.set(s,d));let h=d[t];if(h)return h;let p=(async()=>{let m=m0(t),w=s[t]!==void 0&&t!=="hasErrorBoundary";if(m)nt(!m,"Route property "+t+" is not a supported lazy route property. This property will be ignored."),d[t]=Promise.resolve();else if(w)nt(!1,`Route "${s.id}" has a static property "${t}" defined. The lazy property will be ignored.`);else{let x=await u();x!=null&&(Object.assign(s,{[t]:x}),Object.assign(s,a(s)))}typeof s.lazy=="object"&&(s.lazy[t]=void 0,Object.values(s.lazy).every(x=>x===void 0)&&(s.lazy=void 0))})();return d[t]=p,p},sp=new WeakMap;function X0(t,r,o,a,s){let u=o[t.id];if(Le(u,"No route found in manifest"),!t.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof t.lazy=="function"){let v=sp.get(u);if(v)return{lazyRoutePromise:v,lazyHandlerPromise:v};let w=(async()=>{Le(typeof t.lazy=="function","No lazy route function found");let x=await t.lazy(),E={};for(let k in x){let S=x[k];if(S===void 0)continue;let R=g0(k),j=u[k]!==void 0&&k!=="hasErrorBoundary";R?nt(!R,"Route property "+k+" is not a supported property to be returned from a lazy route function. This property will be ignored."):j?nt(!j,`Route "${u.id}" has a static property "${k}" defined but its lazy function is also returning a value for this property. The lazy route property "${k}" will be ignored.`):E[k]=S}Object.assign(u,E),Object.assign(u,{...a(u),lazy:void 0})})();return sp.set(u,w),w.catch(()=>{}),{lazyRoutePromise:w,lazyHandlerPromise:w}}let d=Object.keys(t.lazy),h=[],p;for(let v of d){if(s&&s.includes(v))continue;let w=Sm({key:v,route:t,manifest:o,mapRouteProperties:a});w&&(h.push(w),v===r&&(p=w))}let m=h.length>0?Promise.all(h).then(()=>{}):void 0;return m?.catch(()=>{}),p?.catch(()=>{}),{lazyRoutePromise:m,lazyHandlerPromise:p}}async function up(t){let r=t.matches.filter(s=>s.shouldLoad),o={};return(await Promise.all(r.map(s=>s.resolve()))).forEach((s,u)=>{o[r[u].route.id]=s}),o}async function q0(t){return t.matches.some(r=>r.route.unstable_middleware)?Em(t,!1,()=>up(t),(r,o)=>({[o]:{type:"error",result:r}})):up(t)}async function Em(t,r,o,a){let{matches:s,request:u,params:d,context:h}=t,p={handlerResult:void 0};try{let m=s.flatMap(w=>w.route.unstable_middleware?w.route.unstable_middleware.map(x=>[w.route.id,x]):[]),v=await Cm({request:u,params:d,context:h},m,r,p,o);return r?v:p.handlerResult}catch(m){if(!p.middlewareError)throw m;let v=await a(p.middlewareError.error,p.middlewareError.routeId);return p.handlerResult?Object.assign(p.handlerResult,v):v}}async function Cm(t,r,o,a,s,u=0){let{request:d}=t;if(d.signal.aborted)throw d.signal.reason?d.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${d.method} ${d.url}`);let h=r[u];if(!h)return a.handlerResult=await s(),a.handlerResult;let[p,m]=h,v=!1,w,x=async()=>{if(v)throw new Error("You may only call `next()` once per middleware");v=!0,await Cm(t,r,o,a,s,u+1)};try{let E=await m({request:t.request,params:t.params,context:t.context},x);return v?E===void 0?w:E:x()}catch(E){throw a.middlewareError?a.middlewareError.error!==E&&(a.middlewareError={routeId:p,error:E}):a.middlewareError={routeId:p,error:E},E}}function km(t,r,o,a,s){let u=Sm({key:"unstable_middleware",route:a.route,manifest:r,mapRouteProperties:t}),d=X0(a.route,Ft(o.method)?"action":"loader",r,t,s);return{middleware:u,route:d.lazyRoutePromise,handler:d.lazyHandlerPromise}}function wc(t,r,o,a,s,u,d,h=null){let p=!1,m=km(t,r,o,a,s);return{...a,_lazyPromises:m,shouldLoad:d,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler(v){return p=!0,h?typeof v=="boolean"?zl(a,{...h,defaultShouldRevalidate:v}):zl(a,h):d},resolve(v){return p||d||v&&!Ft(o.method)&&(a.route.lazy||a.route.loader)?Z0({request:o,match:a,lazyHandlerPromise:m?.handler,lazyRoutePromise:m?.route,handlerOverride:v,scopedContext:u}):Promise.resolve({type:"data",result:void 0})}}}function jo(t,r,o,a,s,u,d,h=null){return a.map(p=>p.route.id!==s.route.id?{...p,shouldLoad:!1,unstable_shouldRevalidateArgs:h,unstable_shouldCallHandler:()=>!1,_lazyPromises:km(t,r,o,p,u),resolve:()=>Promise.resolve({type:"data",result:void 0})}:wc(t,r,o,p,u,d,!0,h))}async function J0(t,r,o,a,s,u){o.some(m=>m._lazyPromises?.middleware)&&await Promise.all(o.map(m=>m._lazyPromises?.middleware));let d={request:r,params:o[0].params,context:s,matches:o},p=await t({...d,fetcherKey:a,unstable_runClientMiddleware:m=>{let v=d;return Em(v,!1,()=>m({...v,fetcherKey:a,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(w,x)=>({[x]:{type:"error",result:w}}))}});try{await Promise.all(o.flatMap(m=>[m._lazyPromises?.handler,m._lazyPromises?.route]))}catch{}return p}async function Z0({request:t,match:r,lazyHandlerPromise:o,lazyRoutePromise:a,handlerOverride:s,scopedContext:u}){let d,h,p=Ft(t.method),m=p?"action":"loader",v=w=>{let x,E=new Promise((R,P)=>x=P);h=()=>x(),t.signal.addEventListener("abort",h);let k=R=>typeof w!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${m}" [routeId: ${r.route.id}]`)):w({request:t,params:r.params,context:u},...R!==void 0?[R]:[]),S=(async()=>{try{return{type:"data",result:await(s?s(P=>k(P)):k())}}catch(R){return{type:"error",result:R}}})();return Promise.race([S,E])};try{let w=p?r.route.action:r.route.loader;if(o||a)if(w){let x,[E]=await Promise.all([v(w).catch(k=>{x=k}),o,a]);if(x!==void 0)throw x;d=E}else{await o;let x=p?r.route.action:r.route.loader;if(x)[d]=await Promise.all([v(x),a]);else if(m==="action"){let E=new URL(t.url),k=E.pathname+E.search;throw nn(405,{method:t.method,pathname:k,routeId:r.route.id})}else return{type:"data",result:void 0}}else if(w)d=await v(w);else{let x=new URL(t.url),E=x.pathname+x.search;throw nn(404,{pathname:E})}}catch(w){return{type:"error",result:w}}finally{h&&t.signal.removeEventListener("abort",h)}return d}async function ex(t){let{result:r,type:o}=t;if(Pm(r)){let a;try{let s=r.headers.get("Content-Type");s&&/\bapplication\/json\b/.test(s)?r.body==null?a=null:a=await r.json():a=await r.text()}catch(s){return{type:"error",error:s}}return o==="error"?{type:"error",error:new jl(r.status,r.statusText,a),statusCode:r.status,headers:r.headers}:{type:"data",data:a,statusCode:r.status,headers:r.headers}}return o==="error"?vp(r)?r.data instanceof Error?{type:"error",error:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:new jl(r.init?.status||500,void 0,r.data),statusCode:Vi(r)?r.status:void 0,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:r,statusCode:Vi(r)?r.status:void 0}:vp(r)?{type:"data",data:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"data",data:r}}function tx(t,r,o,a,s){let u=t.headers.get("Location");if(Le(u,"Redirects returned/thrown from loaders/actions must have a Location header"),!zc(u)){let d=a.slice(0,a.findIndex(h=>h.route.id===o)+1);u=gc(new URL(r.url),d,s,u),t.headers.set("Location",u)}return t}function cp(t,r,o){if(zc(t)){let a=t,s=a.startsWith("//")?new URL(r.protocol+a):new URL(a),u=on(s.pathname,o)!=null;if(s.origin===r.origin&&u)return s.pathname+s.search+s.hash}return t}function Fo(t,r,o,a){let s=t.createURL(Rm(r)).toString(),u={signal:o};if(a&&Ft(a.formMethod)){let{formMethod:d,formEncType:h}=a;u.method=d.toUpperCase(),h==="application/json"?(u.headers=new Headers({"Content-Type":h}),u.body=JSON.stringify(a.json)):h==="text/plain"?u.body=a.text:h==="application/x-www-form-urlencoded"&&a.formData?u.body=xc(a.formData):u.body=a.formData}return new Request(s,u)}function xc(t){let r=new URLSearchParams;for(let[o,a]of t.entries())r.append(o,typeof a=="string"?a:a.name);return r}function dp(t){let r=new FormData;for(let[o,a]of t.entries())r.append(o,a);return r}function nx(t,r,o,a=!1,s=!1){let u={},d=null,h,p=!1,m={},v=o&&Vt(o[1])?o[1].error:void 0;return t.forEach(w=>{if(!(w.route.id in r))return;let x=w.route.id,E=r[x];if(Le(!Zr(E),"Cannot handle redirect results in processLoaderData"),Vt(E)){let k=E.error;if(v!==void 0&&(k=v,v=void 0),d=d||{},s)d[x]=k;else{let S=Jr(t,x);d[S.route.id]==null&&(d[S.route.id]=k)}a||(u[x]=wm),p||(p=!0,h=Vi(E.error)?E.error.status:500),E.headers&&(m[x]=E.headers)}else u[x]=E.data,E.statusCode&&E.statusCode!==200&&!p&&(h=E.statusCode),E.headers&&(m[x]=E.headers)}),v!==void 0&&o&&(d={[o[0]]:v},o[2]&&(u[o[2]]=void 0)),{loaderData:u,errors:d,statusCode:h||200,loaderHeaders:m}}function fp(t,r,o,a,s,u){let{loaderData:d,errors:h}=nx(r,o,a);return s.filter(p=>!p.matches||p.matches.some(m=>m.shouldLoad)).forEach(p=>{let{key:m,match:v,controller:w}=p,x=u[m];if(Le(x,"Did not find corresponding fetcher result"),!(w&&w.signal.aborted))if(Vt(x)){let E=Jr(t.matches,v?.route.id);h&&h[E.route.id]||(h={...h,[E.route.id]:x.error}),t.fetchers.delete(m)}else if(Zr(x))Le(!1,"Unhandled fetcher revalidation redirect");else{let E=Cr(x.data);t.fetchers.set(m,E)}}),{loaderData:d,errors:h}}function hp(t,r,o,a){let s=Object.entries(r).filter(([,u])=>u!==wm).reduce((u,[d,h])=>(u[d]=h,u),{});for(let u of o){let d=u.route.id;if(!r.hasOwnProperty(d)&&t.hasOwnProperty(d)&&u.route.loader&&(s[d]=t[d]),a&&a.hasOwnProperty(d))break}return s}function pp(t){return t?Vt(t[1])?{actionData:{}}:{actionData:{[t[0]]:t[1].data}}:{}}function Jr(t,r){return(r?t.slice(0,t.findIndex(a=>a.route.id===r)+1):[...t]).reverse().find(a=>a.route.hasErrorBoundary===!0)||t[0]}function mp(t){let r=t.length===1?t[0]:t.find(o=>o.index||!o.path||o.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:r}],route:r}}function nn(t,{pathname:r,routeId:o,method:a,type:s,message:u}={}){let d="Unknown Server Error",h="Unknown @remix-run/router error";return t===400?(d="Bad Request",a&&r&&o?h=`You made a ${a} request to "${r}" but did not provide a \`loader\` for route "${o}", so there is no way to handle the request.`:s==="invalid-body"&&(h="Unable to encode submission body")):t===403?(d="Forbidden",h=`Route "${o}" does not match URL "${r}"`):t===404?(d="Not Found",h=`No route matches URL "${r}"`):t===405&&(d="Method Not Allowed",a&&r&&o?h=`You made a ${a.toUpperCase()} request to "${r}" but did not provide an \`action\` for route "${o}", so there is no way to handle the request.`:a&&(h=`Invalid request method "${a.toUpperCase()}"`)),new jl(t||500,d,new Error(h),!0)}function vl(t){let r=Object.entries(t);for(let o=r.length-1;o>=0;o--){let[a,s]=r[o];if(Zr(s))return{key:a,result:s}}}function Rm(t){let r=typeof t=="string"?Tr(t):t;return Pr({...r,hash:""})}function rx(t,r){return t.pathname!==r.pathname||t.search!==r.search?!1:t.hash===""?r.hash!=="":t.hash===r.hash?!0:r.hash!==""}function ox(t){return Pm(t.result)&&$0.has(t.result.status)}function Vt(t){return t.type==="error"}function Zr(t){return(t&&t.type)==="redirect"}function vp(t){return typeof t=="object"&&t!=null&&"type"in t&&"data"in t&&"init"in t&&t.type==="DataWithResponseInit"}function Pm(t){return t!=null&&typeof t.status=="number"&&typeof t.statusText=="string"&&typeof t.headers=="object"&&typeof t.body<"u"}function ix(t){return U0.has(t.toUpperCase())}function Ft(t){return j0.has(t.toUpperCase())}function Uc(t){return new URLSearchParams(t).getAll("index").some(r=>r==="")}function Ml(t,r){let o=typeof r=="string"?Tr(r).search:r.search;if(t[t.length-1].route.index&&Uc(o||""))return t[t.length-1];let a=vm(t);return a[a.length-1]}function gp(t){let{formMethod:r,formAction:o,formEncType:a,text:s,formData:u,json:d}=t;if(!(!r||!o||!a)){if(s!=null)return{formMethod:r,formAction:o,formEncType:a,formData:void 0,json:void 0,text:s};if(u!=null)return{formMethod:r,formAction:o,formEncType:a,formData:u,json:void 0,text:void 0};if(d!==void 0)return{formMethod:r,formAction:o,formEncType:a,formData:void 0,json:d,text:void 0}}}function Ju(t,r){return r?{state:"loading",location:t,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}:{state:"loading",location:t,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function ax(t,r){return{state:"submitting",location:t,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}}function Ai(t,r){return t?{state:"loading",formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text,data:r}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:r}}function lx(t,r){return{state:"submitting",formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text,data:r?r.data:void 0}}function Cr(t){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function sx(t,r){try{let o=t.sessionStorage.getItem(ym);if(o){let a=JSON.parse(o);for(let[s,u]of Object.entries(a||{}))u&&Array.isArray(u)&&r.set(s,new Set(u||[]))}}catch{}}function ux(t,r){if(r.size>0){let o={};for(let[a,s]of r)o[a]=[...s];try{t.sessionStorage.setItem(ym,JSON.stringify(o))}catch(a){nt(!1,`Failed to save applied view transitions in sessionStorage (${a}).`)}}}function cx(){let t,r,o=new Promise((a,s)=>{t=async u=>{a(u);try{await o}catch{}},r=async u=>{s(u);try{await o}catch{}}});return{promise:o,resolve:t,reject:r}}var to=g.createContext(null);to.displayName="DataRouter";var qi=g.createContext(null);qi.displayName="DataRouterState";g.createContext(!1);var $c=g.createContext({isTransitioning:!1});$c.displayName="ViewTransition";var bm=g.createContext(new Map);bm.displayName="Fetchers";var dx=g.createContext(null);dx.displayName="Await";var Sn=g.createContext(null);Sn.displayName="Navigation";var Xl=g.createContext(null);Xl.displayName="Location";var an=g.createContext({outlet:null,matches:[],isDataRoute:!1});an.displayName="Route";var Hc=g.createContext(null);Hc.displayName="RouteError";function fx(t,{relative:r}={}){Le(Ho(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:a}=g.useContext(Sn),{hash:s,pathname:u,search:d}=Ji(t,{relative:r}),h=u;return o!=="/"&&(h=u==="/"?o:On([o,u])),a.createHref({pathname:h,search:d,hash:s})}function Ho(){return g.useContext(Xl)!=null}function An(){return Le(Ho(),"useLocation() may be used only in the context of a <Router> component."),g.useContext(Xl).location}var Mm="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function _m(t){g.useContext(Sn).static||g.useLayoutEffect(t)}function ql(){let{isDataRoute:t}=g.useContext(an);return t?bx():hx()}function hx(){Le(Ho(),"useNavigate() may be used only in the context of a <Router> component.");let t=g.useContext(to),{basename:r,navigator:o}=g.useContext(Sn),{matches:a}=g.useContext(an),{pathname:s}=An(),u=JSON.stringify(Gl(a)),d=g.useRef(!1);return _m(()=>{d.current=!0}),g.useCallback((p,m={})=>{if(nt(d.current,Mm),!d.current)return;if(typeof p=="number"){o.go(p);return}let v=Yl(p,JSON.parse(u),s,m.relative==="path");t==null&&r!=="/"&&(v.pathname=v.pathname==="/"?r:On([r,v.pathname])),(m.replace?o.replace:o.push)(v,m.state,m)},[r,o,u,s,t])}var px=g.createContext(null);function mx(t){let r=g.useContext(an).outlet;return r&&g.createElement(px.Provider,{value:t},r)}function _P(){let{matches:t}=g.useContext(an),r=t[t.length-1];return r?r.params:{}}function Ji(t,{relative:r}={}){let{matches:o}=g.useContext(an),{pathname:a}=An(),s=JSON.stringify(Gl(o));return g.useMemo(()=>Yl(t,JSON.parse(s),a,r==="path"),[t,s,a,r])}function vx(t,r,o,a){Le(Ho(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=g.useContext(Sn),{matches:u}=g.useContext(an),d=u[u.length-1],h=d?d.params:{},p=d?d.pathname:"/",m=d?d.pathnameBase:"/",v=d&&d.route;{let P=v&&v.path||"";Om(p,!v||P.endsWith("*")||P.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${p}" (under <Route path="${P}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${P}"> to <Route path="${P==="/"?"*":`${P}/*`}">.`)}let w=An(),x;x=w;let E=x.pathname||"/",k=E;if(m!=="/"){let P=m.replace(/^\//,"").split("/");k="/"+E.replace(/^\//,"").split("/").slice(P.length).join("/")}let S=Rr(t,{pathname:k});return nt(v||S!=null,`No routes matched location "${x.pathname}${x.search}${x.hash}" `),nt(S==null||S[S.length-1].route.element!==void 0||S[S.length-1].route.Component!==void 0||S[S.length-1].route.lazy!==void 0,`Matched leaf route at location "${x.pathname}${x.search}${x.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),Sx(S&&S.map(P=>Object.assign({},P,{params:Object.assign({},h,P.params),pathname:On([m,s.encodeLocation?s.encodeLocation(P.pathname).pathname:P.pathname]),pathnameBase:P.pathnameBase==="/"?m:On([m,s.encodeLocation?s.encodeLocation(P.pathnameBase).pathname:P.pathnameBase])})),u,o,a)}function gx(){let t=Px(),r=Vi(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),o=t instanceof Error?t.stack:null,a="rgba(200,200,200, 0.5)",s={padding:"0.5rem",backgroundColor:a},u={padding:"2px 4px",backgroundColor:a},d=null;return console.error("Error handled by React Router default ErrorBoundary:",t),d=g.createElement(g.Fragment,null,g.createElement("p",null,"💿 Hey developer 👋"),g.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",g.createElement("code",{style:u},"ErrorBoundary")," or"," ",g.createElement("code",{style:u},"errorElement")," prop on your route.")),g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},r),o?g.createElement("pre",{style:s},o):null,d)}var yx=g.createElement(gx,null),wx=class extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?g.createElement(an.Provider,{value:this.props.routeContext},g.createElement(Hc.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function xx({routeContext:t,match:r,children:o}){let a=g.useContext(to);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),g.createElement(an.Provider,{value:t},o)}function Sx(t,r=[],o=null,a=null){if(t==null){if(!o)return null;if(o.errors)t=o.matches;else if(r.length===0&&!o.initialized&&o.matches.length>0)t=o.matches;else return null}let s=t,u=o?.errors;if(u!=null){let p=s.findIndex(m=>m.route.id&&u?.[m.route.id]!==void 0);Le(p>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(u).join(",")}`),s=s.slice(0,Math.min(s.length,p+1))}let d=!1,h=-1;if(o)for(let p=0;p<s.length;p++){let m=s[p];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(h=p),m.route.id){let{loaderData:v,errors:w}=o,x=m.route.loader&&!v.hasOwnProperty(m.route.id)&&(!w||w[m.route.id]===void 0);if(m.route.lazy||x){d=!0,h>=0?s=s.slice(0,h+1):s=[s[0]];break}}}return s.reduceRight((p,m,v)=>{let w,x=!1,E=null,k=null;o&&(w=u&&m.route.id?u[m.route.id]:void 0,E=m.route.errorElement||yx,d&&(h<0&&v===0?(Om("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,k=null):h===v&&(x=!0,k=m.route.hydrateFallbackElement||null)));let S=r.concat(s.slice(0,v+1)),R=()=>{let P;return w?P=E:x?P=k:m.route.Component?P=g.createElement(m.route.Component,null):m.route.element?P=m.route.element:P=p,g.createElement(xx,{match:m,routeContext:{outlet:p,matches:S,isDataRoute:o!=null},children:P})};return o&&(m.route.ErrorBoundary||m.route.errorElement||v===0)?g.createElement(wx,{location:o.location,revalidation:o.revalidation,component:E,error:w,children:R(),routeContext:{outlet:null,matches:S,isDataRoute:!0}}):R()},null)}function Bc(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Ex(t){let r=g.useContext(to);return Le(r,Bc(t)),r}function Tm(t){let r=g.useContext(qi);return Le(r,Bc(t)),r}function Cx(t){let r=g.useContext(an);return Le(r,Bc(t)),r}function Vc(t){let r=Cx(t),o=r.matches[r.matches.length-1];return Le(o.route.id,`${t} can only be used on routes that contain a unique "id"`),o.route.id}function kx(){return Vc("useRouteId")}function Rx(){return Tm("useNavigation").navigation}function Px(){let t=g.useContext(Hc),r=Tm("useRouteError"),o=Vc("useRouteError");return t!==void 0?t:r.errors?.[o]}function bx(){let{router:t}=Ex("useNavigate"),r=Vc("useNavigate"),o=g.useRef(!1);return _m(()=>{o.current=!0}),g.useCallback(async(s,u={})=>{nt(o.current,Mm),o.current&&(typeof s=="number"?t.navigate(s):await t.navigate(s,{fromRouteId:r,...u}))},[t,r])}var yp={};function Om(t,r,o){!r&&!yp[t]&&(yp[t]=!0,nt(!1,o))}var wp={};function xp(t,r){!t&&!wp[r]&&(wp[r]=!0,console.warn(r))}function Mx(t){let r={hasErrorBoundary:t.hasErrorBoundary||t.ErrorBoundary!=null||t.errorElement!=null};return t.Component&&(t.element&&nt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(r,{element:g.createElement(t.Component),Component:void 0})),t.HydrateFallback&&(t.hydrateFallbackElement&&nt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(r,{hydrateFallbackElement:g.createElement(t.HydrateFallback),HydrateFallback:void 0})),t.ErrorBoundary&&(t.errorElement&&nt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(r,{errorElement:g.createElement(t.ErrorBoundary),ErrorBoundary:void 0})),r}var _x=["HydrateFallback","hydrateFallbackElement"],Tx=class{constructor(){this.status="pending",this.promise=new Promise((t,r)=>{this.resolve=o=>{this.status==="pending"&&(this.status="resolved",t(o))},this.reject=o=>{this.status==="pending"&&(this.status="rejected",r(o))}})}};function Ox({router:t,flushSync:r}){let[o,a]=g.useState(t.state),[s,u]=g.useState(),[d,h]=g.useState({isTransitioning:!1}),[p,m]=g.useState(),[v,w]=g.useState(),[x,E]=g.useState(),k=g.useRef(new Map),S=g.useCallback((L,{deletedFetchers:U,flushSync:H,viewTransitionOpts:_})=>{L.fetchers.forEach((V,te)=>{V.data!==void 0&&k.current.set(te,V.data)}),U.forEach(V=>k.current.delete(V)),xp(H===!1||r!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let K=t.window!=null&&t.window.document!=null&&typeof t.window.document.startViewTransition=="function";if(xp(_==null||K,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!_||!K){r&&H?r(()=>a(L)):g.startTransition(()=>a(L));return}if(r&&H){r(()=>{v&&(p&&p.resolve(),v.skipTransition()),h({isTransitioning:!0,flushSync:!0,currentLocation:_.currentLocation,nextLocation:_.nextLocation})});let V=t.window.document.startViewTransition(()=>{r(()=>a(L))});V.finished.finally(()=>{r(()=>{m(void 0),w(void 0),u(void 0),h({isTransitioning:!1})})}),r(()=>w(V));return}v?(p&&p.resolve(),v.skipTransition(),E({state:L,currentLocation:_.currentLocation,nextLocation:_.nextLocation})):(u(L),h({isTransitioning:!0,flushSync:!1,currentLocation:_.currentLocation,nextLocation:_.nextLocation}))},[t.window,r,v,p]);g.useLayoutEffect(()=>t.subscribe(S),[t,S]),g.useEffect(()=>{d.isTransitioning&&!d.flushSync&&m(new Tx)},[d]),g.useEffect(()=>{if(p&&s&&t.window){let L=s,U=p.promise,H=t.window.document.startViewTransition(async()=>{g.startTransition(()=>a(L)),await U});H.finished.finally(()=>{m(void 0),w(void 0),u(void 0),h({isTransitioning:!1})}),w(H)}},[s,p,t.window]),g.useEffect(()=>{p&&s&&o.location.key===s.location.key&&p.resolve()},[p,v,o.location,s]),g.useEffect(()=>{!d.isTransitioning&&x&&(u(x.state),h({isTransitioning:!0,flushSync:!1,currentLocation:x.currentLocation,nextLocation:x.nextLocation}),E(void 0))},[d.isTransitioning,x]);let R=g.useMemo(()=>({createHref:t.createHref,encodeLocation:t.encodeLocation,go:L=>t.navigate(L),push:(L,U,H)=>t.navigate(L,{state:U,preventScrollReset:H?.preventScrollReset}),replace:(L,U,H)=>t.navigate(L,{replace:!0,state:U,preventScrollReset:H?.preventScrollReset})}),[t]),P=t.basename||"/",j=g.useMemo(()=>({router:t,navigator:R,static:!1,basename:P}),[t,R,P]);return g.createElement(g.Fragment,null,g.createElement(to.Provider,{value:j},g.createElement(qi.Provider,{value:o},g.createElement(bm.Provider,{value:k.current},g.createElement($c.Provider,{value:d},g.createElement(Ix,{basename:P,location:o.location,navigationType:o.historyAction,navigator:R},g.createElement(Nx,{routes:t.routes,future:t.future,state:o})))))),null)}var Nx=g.memo(Dx);function Dx({routes:t,future:r,state:o}){return vx(t,void 0,o,r)}function Lx({to:t,replace:r,state:o,relative:a}){Le(Ho(),"<Navigate> may be used only in the context of a <Router> component.");let{static:s}=g.useContext(Sn);nt(!s,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:u}=g.useContext(an),{pathname:d}=An(),h=ql(),p=Yl(t,Gl(u),d,a==="path"),m=JSON.stringify(p);return g.useEffect(()=>{h(JSON.parse(m),{replace:r,state:o,relative:a})},[h,m,a,r,o]),null}function Ax(t){return mx(t.context)}function Ix({basename:t="/",children:r=null,location:o,navigationType:a="POP",navigator:s,static:u=!1}){Le(!Ho(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let d=t.replace(/^\/*/,"/"),h=g.useMemo(()=>({basename:d,navigator:s,static:u,future:{}}),[d,s,u]);typeof o=="string"&&(o=Tr(o));let{pathname:p="/",search:m="",hash:v="",state:w=null,key:x="default"}=o,E=g.useMemo(()=>{let k=on(p,d);return k==null?null:{location:{pathname:k,search:m,hash:v,state:w,key:x},navigationType:a}},[d,p,m,v,w,x,a]);return nt(E!=null,`<Router basename="${d}"> is not able to match the URL "${p}${m}${v}" because it does not start with the basename, so the <Router> won't render anything.`),E==null?null:g.createElement(Sn.Provider,{value:h},g.createElement(Xl.Provider,{children:r,value:E}))}var _l="get",Tl="application/x-www-form-urlencoded";function Jl(t){return t!=null&&typeof t.tagName=="string"}function Fx(t){return Jl(t)&&t.tagName.toLowerCase()==="button"}function jx(t){return Jl(t)&&t.tagName.toLowerCase()==="form"}function zx(t){return Jl(t)&&t.tagName.toLowerCase()==="input"}function Ux(t){return!!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey)}function $x(t,r){return t.button===0&&(!r||r==="_self")&&!Ux(t)}function Sc(t=""){return new URLSearchParams(typeof t=="string"||Array.isArray(t)||t instanceof URLSearchParams?t:Object.keys(t).reduce((r,o)=>{let a=t[o];return r.concat(Array.isArray(a)?a.map(s=>[o,s]):[[o,a]])},[]))}function Hx(t,r){let o=Sc(t);return r&&r.forEach((a,s)=>{o.has(s)||r.getAll(s).forEach(u=>{o.append(s,u)})}),o}var gl=null;function Bx(){if(gl===null)try{new FormData(document.createElement("form"),0),gl=!1}catch{gl=!0}return gl}var Vx=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Zu(t){return t!=null&&!Vx.has(t)?(nt(!1,`"${t}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Tl}"`),null):t}function Wx(t,r){let o,a,s,u,d;if(jx(t)){let h=t.getAttribute("action");a=h?on(h,r):null,o=t.getAttribute("method")||_l,s=Zu(t.getAttribute("enctype"))||Tl,u=new FormData(t)}else if(Fx(t)||zx(t)&&(t.type==="submit"||t.type==="image")){let h=t.form;if(h==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let p=t.getAttribute("formaction")||h.getAttribute("action");if(a=p?on(p,r):null,o=t.getAttribute("formmethod")||h.getAttribute("method")||_l,s=Zu(t.getAttribute("formenctype"))||Zu(h.getAttribute("enctype"))||Tl,u=new FormData(h,t),!Bx()){let{name:m,type:v,value:w}=t;if(v==="image"){let x=m?`${m}.`:"";u.append(`${x}x`,"0"),u.append(`${x}y`,"0")}else m&&u.append(m,w)}}else{if(Jl(t))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=_l,a=null,s=Tl,d=t}return u&&s==="text/plain"&&(d=u,u=void 0),{action:a,method:o.toLowerCase(),encType:s,formData:u,body:d}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function Wc(t,r){if(t===!1||t===null||typeof t>"u")throw new Error(r)}function Kx(t,r,o){let a=typeof t=="string"?new URL(t,typeof window>"u"?"server://singlefetch/":window.location.origin):t;return a.pathname==="/"?a.pathname=`_root.${o}`:r&&on(a.pathname,r)==="/"?a.pathname=`${r.replace(/\/$/,"")}/_root.${o}`:a.pathname=`${a.pathname.replace(/\/$/,"")}.${o}`,a}async function Qx(t,r){if(t.id in r)return r[t.id];try{let o=await import(t.module);return r[t.id]=o,o}catch(o){return console.error(`Error loading route module \`${t.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Gx(t){return t==null?!1:t.href==null?t.rel==="preload"&&typeof t.imageSrcSet=="string"&&typeof t.imageSizes=="string":typeof t.rel=="string"&&typeof t.href=="string"}async function Yx(t,r,o){let a=await Promise.all(t.map(async s=>{let u=r.routes[s.route.id];if(u){let d=await Qx(u,o);return d.links?d.links():[]}return[]}));return Zx(a.flat(1).filter(Gx).filter(s=>s.rel==="stylesheet"||s.rel==="preload").map(s=>s.rel==="stylesheet"?{...s,rel:"prefetch",as:"style"}:{...s,rel:"prefetch"}))}function Sp(t,r,o,a,s,u){let d=(p,m)=>o[m]?p.route.id!==o[m].route.id:!0,h=(p,m)=>o[m].pathname!==p.pathname||o[m].route.path?.endsWith("*")&&o[m].params["*"]!==p.params["*"];return u==="assets"?r.filter((p,m)=>d(p,m)||h(p,m)):u==="data"?r.filter((p,m)=>{let v=a.routes[p.route.id];if(!v||!v.hasLoader)return!1;if(d(p,m)||h(p,m))return!0;if(p.route.shouldRevalidate){let w=p.route.shouldRevalidate({currentUrl:new URL(s.pathname+s.search+s.hash,window.origin),currentParams:o[0]?.params||{},nextUrl:new URL(t,window.origin),nextParams:p.params,defaultShouldRevalidate:!0});if(typeof w=="boolean")return w}return!0}):[]}function Xx(t,r,{includeHydrateFallback:o}={}){return qx(t.map(a=>{let s=r.routes[a.route.id];if(!s)return[];let u=[s.module];return s.clientActionModule&&(u=u.concat(s.clientActionModule)),s.clientLoaderModule&&(u=u.concat(s.clientLoaderModule)),o&&s.hydrateFallbackModule&&(u=u.concat(s.hydrateFallbackModule)),s.imports&&(u=u.concat(s.imports)),u}).flat(1))}function qx(t){return[...new Set(t)]}function Jx(t){let r={},o=Object.keys(t).sort();for(let a of o)r[a]=t[a];return r}function Zx(t,r){let o=new Set;return new Set(r),t.reduce((a,s)=>{let u=JSON.stringify(Jx(s));return o.has(u)||(o.add(u),a.push({key:u,link:s})),a},[])}function Nm(){let t=g.useContext(to);return Wc(t,"You must render this element inside a <DataRouterContext.Provider> element"),t}function eS(){let t=g.useContext(qi);return Wc(t,"You must render this element inside a <DataRouterStateContext.Provider> element"),t}var Kc=g.createContext(void 0);Kc.displayName="FrameworkContext";function Dm(){let t=g.useContext(Kc);return Wc(t,"You must render this element inside a <HydratedRouter> element"),t}function tS(t,r){let o=g.useContext(Kc),[a,s]=g.useState(!1),[u,d]=g.useState(!1),{onFocus:h,onBlur:p,onMouseEnter:m,onMouseLeave:v,onTouchStart:w}=r,x=g.useRef(null);g.useEffect(()=>{if(t==="render"&&d(!0),t==="viewport"){let S=P=>{P.forEach(j=>{d(j.isIntersecting)})},R=new IntersectionObserver(S,{threshold:.5});return x.current&&R.observe(x.current),()=>{R.disconnect()}}},[t]),g.useEffect(()=>{if(a){let S=setTimeout(()=>{d(!0)},100);return()=>{clearTimeout(S)}}},[a]);let E=()=>{s(!0)},k=()=>{s(!1),d(!1)};return o?t!=="intent"?[u,x,{}]:[u,x,{onFocus:Ii(h,E),onBlur:Ii(p,k),onMouseEnter:Ii(m,E),onMouseLeave:Ii(v,k),onTouchStart:Ii(w,E)}]:[!1,x,{}]}function Ii(t,r){return o=>{t&&t(o),o.defaultPrevented||r(o)}}function nS({page:t,...r}){let{router:o}=Nm(),a=g.useMemo(()=>Rr(o.routes,t,o.basename),[o.routes,t,o.basename]);return a?g.createElement(oS,{page:t,matches:a,...r}):null}function rS(t){let{manifest:r,routeModules:o}=Dm(),[a,s]=g.useState([]);return g.useEffect(()=>{let u=!1;return Yx(t,r,o).then(d=>{u||s(d)}),()=>{u=!0}},[t,r,o]),a}function oS({page:t,matches:r,...o}){let a=An(),{manifest:s,routeModules:u}=Dm(),{basename:d}=Nm(),{loaderData:h,matches:p}=eS(),m=g.useMemo(()=>Sp(t,r,p,s,a,"data"),[t,r,p,s,a]),v=g.useMemo(()=>Sp(t,r,p,s,a,"assets"),[t,r,p,s,a]),w=g.useMemo(()=>{if(t===a.pathname+a.search+a.hash)return[];let k=new Set,S=!1;if(r.forEach(P=>{let j=s.routes[P.route.id];!j||!j.hasLoader||(!m.some(L=>L.route.id===P.route.id)&&P.route.id in h&&u[P.route.id]?.shouldRevalidate||j.hasClientLoader?S=!0:k.add(P.route.id))}),k.size===0)return[];let R=Kx(t,d,"data");return S&&k.size>0&&R.searchParams.set("_routes",r.filter(P=>k.has(P.route.id)).map(P=>P.route.id).join(",")),[R.pathname+R.search]},[d,h,a,s,m,r,t,u]),x=g.useMemo(()=>Xx(v,s),[v,s]),E=rS(v);return g.createElement(g.Fragment,null,w.map(k=>g.createElement("link",{key:k,rel:"prefetch",as:"fetch",href:k,...o})),x.map(k=>g.createElement("link",{key:k,rel:"modulepreload",href:k,...o})),E.map(({key:k,link:S})=>g.createElement("link",{key:k,...S})))}function iS(...t){return r=>{t.forEach(o=>{typeof o=="function"?o(r):o!=null&&(o.current=r)})}}var Lm=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Lm&&(window.__reactRouterVersion="7.7.1")}catch{}function aS(t,r){return K0({basename:r?.basename,unstable_getContext:r?.unstable_getContext,future:r?.future,history:d0({window:r?.window}),hydrationData:lS(),routes:t,mapRouteProperties:Mx,hydrationRouteProperties:_x,dataStrategy:r?.dataStrategy,patchRoutesOnNavigation:r?.patchRoutesOnNavigation,window:r?.window}).initialize()}function lS(){let t=window?.__staticRouterHydrationData;return t&&t.errors&&(t={...t,errors:sS(t.errors)}),t}function sS(t){if(!t)return null;let r=Object.entries(t),o={};for(let[a,s]of r)if(s&&s.__type==="RouteErrorResponse")o[a]=new jl(s.status,s.statusText,s.data,s.internal===!0);else if(s&&s.__type==="Error"){if(s.__subType){let u=window[s.__subType];if(typeof u=="function")try{let d=new u(s.message);d.stack="",o[a]=d}catch{}}if(o[a]==null){let u=new Error(s.message);u.stack="",o[a]=u}}else o[a]=s;return o}var Am=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Qc=g.forwardRef(function({onClick:r,discover:o="render",prefetch:a="none",relative:s,reloadDocument:u,replace:d,state:h,target:p,to:m,preventScrollReset:v,viewTransition:w,...x},E){let{basename:k}=g.useContext(Sn),S=typeof m=="string"&&Am.test(m),R,P=!1;if(typeof m=="string"&&S&&(R=m,Lm))try{let te=new URL(window.location.href),ve=m.startsWith("//")?new URL(te.protocol+m):new URL(m),Te=on(ve.pathname,k);ve.origin===te.origin&&Te!=null?m=Te+ve.search+ve.hash:P=!0}catch{nt(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let j=fx(m,{relative:s}),[L,U,H]=tS(a,x),_=dS(m,{replace:d,state:h,target:p,preventScrollReset:v,relative:s,viewTransition:w});function K(te){r&&r(te),te.defaultPrevented||_(te)}let V=g.createElement("a",{...x,...H,href:R||j,onClick:P||u?r:K,ref:iS(E,U),target:p,"data-discover":!S&&o==="render"?"true":void 0});return L&&!S?g.createElement(g.Fragment,null,V,g.createElement(nS,{page:j})):V});Qc.displayName="Link";var Im=g.forwardRef(function({"aria-current":r="page",caseSensitive:o=!1,className:a="",end:s=!1,style:u,to:d,viewTransition:h,children:p,...m},v){let w=Ji(d,{relative:m.relative}),x=An(),E=g.useContext(qi),{navigator:k,basename:S}=g.useContext(Sn),R=E!=null&&vS(w)&&h===!0,P=k.encodeLocation?k.encodeLocation(w).pathname:w.pathname,j=x.pathname,L=E&&E.navigation&&E.navigation.location?E.navigation.location.pathname:null;o||(j=j.toLowerCase(),L=L?L.toLowerCase():null,P=P.toLowerCase()),L&&S&&(L=on(L,S)||L);const U=P!=="/"&&P.endsWith("/")?P.length-1:P.length;let H=j===P||!s&&j.startsWith(P)&&j.charAt(U)==="/",_=L!=null&&(L===P||!s&&L.startsWith(P)&&L.charAt(P.length)==="/"),K={isActive:H,isPending:_,isTransitioning:R},V=H?r:void 0,te;typeof a=="function"?te=a(K):te=[a,H?"active":null,_?"pending":null,R?"transitioning":null].filter(Boolean).join(" ");let ve=typeof u=="function"?u(K):u;return g.createElement(Qc,{...m,"aria-current":V,className:te,ref:v,style:ve,to:d,viewTransition:h},typeof p=="function"?p(K):p)});Im.displayName="NavLink";var uS=g.forwardRef(({discover:t="render",fetcherKey:r,navigate:o,reloadDocument:a,replace:s,state:u,method:d=_l,action:h,onSubmit:p,relative:m,preventScrollReset:v,viewTransition:w,...x},E)=>{let k=pS(),S=mS(h,{relative:m}),R=d.toLowerCase()==="get"?"get":"post",P=typeof h=="string"&&Am.test(h),j=L=>{if(p&&p(L),L.defaultPrevented)return;L.preventDefault();let U=L.nativeEvent.submitter,H=U?.getAttribute("formmethod")||d;k(U||L.currentTarget,{fetcherKey:r,method:H,navigate:o,replace:s,state:u,relative:m,preventScrollReset:v,viewTransition:w})};return g.createElement("form",{ref:E,method:R,action:S,onSubmit:a?p:j,...x,"data-discover":!P&&t==="render"?"true":void 0})});uS.displayName="Form";function cS(t){return`${t} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Fm(t){let r=g.useContext(to);return Le(r,cS(t)),r}function dS(t,{target:r,replace:o,state:a,preventScrollReset:s,relative:u,viewTransition:d}={}){let h=ql(),p=An(),m=Ji(t,{relative:u});return g.useCallback(v=>{if($x(v,r)){v.preventDefault();let w=o!==void 0?o:Pr(p)===Pr(m);h(t,{replace:w,state:a,preventScrollReset:s,relative:u,viewTransition:d})}},[p,h,m,o,a,r,t,s,u,d])}function TP(t){nt(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let r=g.useRef(Sc(t)),o=g.useRef(!1),a=An(),s=g.useMemo(()=>Hx(a.search,o.current?null:r.current),[a.search]),u=ql(),d=g.useCallback((h,p)=>{const m=Sc(typeof h=="function"?h(new URLSearchParams(s)):h);o.current=!0,u("?"+m,p)},[u,s]);return[s,d]}var fS=0,hS=()=>`__${String(++fS)}__`;function pS(){let{router:t}=Fm("useSubmit"),{basename:r}=g.useContext(Sn),o=kx();return g.useCallback(async(a,s={})=>{let{action:u,method:d,encType:h,formData:p,body:m}=Wx(a,r);if(s.navigate===!1){let v=s.fetcherKey||hS();await t.fetch(v,o,s.action||u,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||h,flushSync:s.flushSync})}else await t.navigate(s.action||u,{preventScrollReset:s.preventScrollReset,formData:p,body:m,formMethod:s.method||d,formEncType:s.encType||h,replace:s.replace,state:s.state,fromRouteId:o,flushSync:s.flushSync,viewTransition:s.viewTransition})},[t,r,o])}function mS(t,{relative:r}={}){let{basename:o}=g.useContext(Sn),a=g.useContext(an);Le(a,"useFormAction must be used inside a RouteContext");let[s]=a.matches.slice(-1),u={...Ji(t||".",{relative:r})},d=An();if(t==null){u.search=d.search;let h=new URLSearchParams(u.search),p=h.getAll("index");if(p.some(v=>v==="")){h.delete("index"),p.filter(w=>w).forEach(w=>h.append("index",w));let v=h.toString();u.search=v?`?${v}`:""}}return(!t||t===".")&&s.route.index&&(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(u.pathname=u.pathname==="/"?o:On([o,u.pathname])),Pr(u)}function vS(t,{relative:r}={}){let o=g.useContext($c);Le(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:a}=Fm("useViewTransitionState"),s=Ji(t,{relative:r});if(!o.isTransitioning)return!1;let u=on(o.currentLocation.pathname,a)||o.currentLocation.pathname,d=on(o.nextLocation.pathname,a)||o.nextLocation.pathname;return Fl(s.pathname,d)!=null||Fl(s.pathname,u)!=null}var Zl=rm();const gS=tm(Zl);function yS(t){return g.createElement(Ox,{flushSync:Zl.flushSync,...t})}const Xe={home:{path:"/",getHref:()=>"/"},auth:{register:{path:"/auth/register",getHref:t=>`/auth/register${t?`?redirectTo=${encodeURIComponent(t)}`:""}`},login:{path:"/auth/login",getHref:t=>`/auth/login${t?`?redirectTo=${encodeURIComponent(t)}`:""}`}},app:{root:{path:"/app"},dashboard:{path:"",getHref:()=>"/app"},applications:{path:"applications",getHref:()=>"/app/applications"},savedJobs:{path:"saved-jobs",getHref:()=>"/app/saved-jobs"},jobs:{path:"jobs",getHref:()=>"/app/jobs"},companies:{path:"companies",getHref:()=>"/app/companies"},profile:{path:"profile",getHref:()=>"/app/profile"},settings:{path:"settings",getHref:()=>"/app/settings"}},jobs:{root:{path:"/jobs",getHref:()=>"/jobs"},detail:{path:"/jobs/:id"}},companies:{root:{path:"/companies",getHref:()=>"/companies"},detail:{path:"/companies/:id"}},about:{path:"/about",getHref:()=>"/about"},contact:{path:"/contact",getHref:()=>"/contact"},privacy:{path:"/privacy"},terms:{path:"/terms"}},Ep=t=>{let r;const o=new Set,a=(m,v)=>{const w=typeof m=="function"?m(r):m;if(!Object.is(w,r)){const x=r;r=v??(typeof w!="object"||w===null)?w:Object.assign({},r,w),o.forEach(E=>E(r,x))}},s=()=>r,h={setState:a,getState:s,getInitialState:()=>p,subscribe:m=>(o.add(m),()=>o.delete(m))},p=r=t(a,s,h);return h},wS=t=>t?Ep(t):Ep,xS=t=>t;function SS(t,r=xS){const o=It.useSyncExternalStore(t.subscribe,()=>r(t.getState()),()=>r(t.getInitialState()));return It.useDebugValue(o),o}const ES=t=>{const r=wS(t),o=a=>SS(r,a);return Object.assign(o,r),o},CS=t=>ES;function kS(t,r){let o;try{o=t()}catch{return}return{getItem:s=>{var u;const d=p=>p===null?null:JSON.parse(p,void 0),h=(u=o.getItem(s))!=null?u:null;return h instanceof Promise?h.then(d):d(h)},setItem:(s,u)=>o.setItem(s,JSON.stringify(u,void 0)),removeItem:s=>o.removeItem(s)}}const Ec=t=>r=>{try{const o=t(r);return o instanceof Promise?o:{then(a){return Ec(a)(o)},catch(a){return this}}}catch(o){return{then(a){return this},catch(a){return Ec(a)(o)}}}},RS=(t,r)=>(o,a,s)=>{let u={storage:kS(()=>localStorage),partialize:S=>S,version:0,merge:(S,R)=>({...R,...S}),...r},d=!1;const h=new Set,p=new Set;let m=u.storage;if(!m)return t((...S)=>{console.warn(`[zustand persist middleware] Unable to update item '${u.name}', the given storage is currently unavailable.`),o(...S)},a,s);const v=()=>{const S=u.partialize({...a()});return m.setItem(u.name,{state:S,version:u.version})},w=s.setState;s.setState=(S,R)=>{w(S,R),v()};const x=t((...S)=>{o(...S),v()},a,s);s.getInitialState=()=>x;let E;const k=()=>{var S,R;if(!m)return;d=!1,h.forEach(j=>{var L;return j((L=a())!=null?L:x)});const P=((R=u.onRehydrateStorage)==null?void 0:R.call(u,(S=a())!=null?S:x))||void 0;return Ec(m.getItem.bind(m))(u.name).then(j=>{if(j)if(typeof j.version=="number"&&j.version!==u.version){if(u.migrate){const L=u.migrate(j.state,j.version);return L instanceof Promise?L.then(U=>[!0,U]):[!0,L]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,j.state];return[!1,void 0]}).then(j=>{var L;const[U,H]=j;if(E=u.merge(H,(L=a())!=null?L:x),o(E,!0),U)return v()}).then(()=>{P?.(E,void 0),E=a(),d=!0,p.forEach(j=>j(E))}).catch(j=>{P?.(void 0,j)})};return s.persist={setOptions:S=>{u={...u,...S},S.storage&&(m=S.storage)},clearStorage:()=>{m?.removeItem(u.name)},getOptions:()=>u,rehydrate:()=>k(),hasHydrated:()=>d,onHydrate:S=>(h.add(S),()=>{h.delete(S)}),onFinishHydration:S=>(p.add(S),()=>{p.delete(S)})},u.skipHydration||k(),E||x},PS=RS,bS="work-finder-auth",Gc=CS()(PS((t,r)=>({user:null,isAuthenticated:!1,isInitializing:!0,accessToken:null,refreshToken:null,login:async(o,a)=>{try{const s={user:{id:"1",name:"John Doe",email:o,role:"user",isVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token",refreshToken:"mock-refresh-token"};t({user:s.user,isAuthenticated:!0,accessToken:s.accessToken,refreshToken:s.refreshToken,isInitializing:!1})}catch(s){throw console.error("Login failed:",s),s}},register:async o=>{try{const a={user:{id:"1",name:o.name,email:o.email,role:"user",isVerified:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token",refreshToken:"mock-refresh-token"};t({user:a.user,isAuthenticated:!0,accessToken:a.accessToken,refreshToken:a.refreshToken,isInitializing:!1})}catch(a){throw console.error("Registration failed:",a),a}},logout:async()=>{try{t({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}catch(o){console.error("Logout failed:",o),t({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}},getCurrentUser:async()=>{try{const{accessToken:o}=r();if(!o){t({isInitializing:!1});return}const a={id:"1",name:"John Doe",email:"<EMAIL>",role:"user",isVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};t({user:a,isAuthenticated:!0,isInitializing:!1})}catch(o){console.error("Get current user failed:",o),t({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}},updateUser:o=>{const{user:a}=r();a&&t({user:{...a,...o,updatedAt:new Date().toISOString()}})},setInitialized:()=>{t({isInitializing:!1})},setAuthFromLocal:o=>{t({user:o,isAuthenticated:!0,isInitializing:!1})},refreshAccessToken:async()=>{try{const{refreshToken:o}=r();if(!o)throw new Error("No refresh token available");const a={accessToken:"new-mock-access-token"};return t({accessToken:a.accessToken}),a.accessToken}catch(o){return console.error("Token refresh failed:",o),t({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null}),null}}}),{name:bS,partialize:t=>({user:t.user,isAuthenticated:t.isAuthenticated,accessToken:t.accessToken,refreshToken:t.refreshToken})})),MS=({children:t})=>{const r=An(),{isAuthenticated:o}=Gc();return o?D.jsx(D.Fragment,{children:t}):D.jsx(Lx,{to:Xe.auth.login.getHref(r.pathname),replace:!0})};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _S=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),TS=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,o,a)=>a?a.toUpperCase():o.toLowerCase()),Cp=t=>{const r=TS(t);return r.charAt(0).toUpperCase()+r.slice(1)},jm=(...t)=>t.filter((r,o,a)=>!!r&&r.trim()!==""&&a.indexOf(r)===o).join(" ").trim(),OS=t=>{for(const r in t)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var NS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DS=g.forwardRef(({color:t="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:a,className:s="",children:u,iconNode:d,...h},p)=>g.createElement("svg",{ref:p,...NS,width:r,height:r,stroke:t,strokeWidth:a?Number(o)*24/Number(r):o,className:jm("lucide",s),...!u&&!OS(h)&&{"aria-hidden":"true"},...h},[...d.map(([m,v])=>g.createElement(m,v)),...Array.isArray(u)?u:[u]]));/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const En=(t,r)=>{const o=g.forwardRef(({className:a,...s},u)=>g.createElement(DS,{ref:u,iconNode:r,className:jm(`lucide-${_S(Cp(t))}`,`lucide-${t}`,a),...s}));return o.displayName=Cp(t),o};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LS=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],AS=En("briefcase",LS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IS=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],FS=En("building-2",IS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jS=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],zS=En("check",jS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const US=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],$S=En("chevron-right",US);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HS=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],BS=En("circle",HS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VS=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],WS=En("file-text",VS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KS=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],QS=En("heart",KS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GS=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],YS=En("house",GS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const XS=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],qS=En("panel-left",XS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const JS=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ZS=En("settings",JS);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eE=[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]],kp=En("user-round",eE);function Rp(t,r){if(typeof t=="function")return t(r);t!=null&&(t.current=r)}function es(...t){return r=>{let o=!1;const a=t.map(s=>{const u=Rp(s,r);return!o&&typeof u=="function"&&(o=!0),u});if(o)return()=>{for(let s=0;s<a.length;s++){const u=a[s];typeof u=="function"?u():Rp(t[s],null)}}}}function jt(...t){return g.useCallback(es(...t),t)}function Wi(t){const r=nE(t),o=g.forwardRef((a,s)=>{const{children:u,...d}=a,h=g.Children.toArray(u),p=h.find(oE);if(p){const m=p.props.children,v=h.map(w=>w===p?g.Children.count(m)>1?g.Children.only(null):g.isValidElement(m)?m.props.children:null:w);return D.jsx(r,{...d,ref:s,children:g.isValidElement(m)?g.cloneElement(m,void 0,v):null})}return D.jsx(r,{...d,ref:s,children:u})});return o.displayName=`${t}.Slot`,o}var tE=Wi("Slot");function nE(t){const r=g.forwardRef((o,a)=>{const{children:s,...u}=o;if(g.isValidElement(s)){const d=aE(s),h=iE(u,s.props);return s.type!==g.Fragment&&(h.ref=a?es(a,d):d),g.cloneElement(s,h)}return g.Children.count(s)>1?g.Children.only(null):null});return r.displayName=`${t}.SlotClone`,r}var rE=Symbol("radix.slottable");function oE(t){return g.isValidElement(t)&&typeof t.type=="function"&&"__radixId"in t.type&&t.type.__radixId===rE}function iE(t,r){const o={...r};for(const a in r){const s=t[a],u=r[a];/^on[A-Z]/.test(a)?s&&u?o[a]=(...h)=>{const p=u(...h);return s(...h),p}:s&&(o[a]=s):a==="style"?o[a]={...s,...u}:a==="className"&&(o[a]=[s,u].filter(Boolean).join(" "))}return{...t,...o}}function aE(t){let r=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?t.ref:(r=Object.getOwnPropertyDescriptor(t,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?t.props.ref:t.props.ref||t.ref)}function zm(t){var r,o,a="";if(typeof t=="string"||typeof t=="number")a+=t;else if(typeof t=="object")if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(o=zm(t[r]))&&(a&&(a+=" "),a+=o)}else for(o in t)t[o]&&(a&&(a+=" "),a+=o);return a}function Yc(){for(var t,r,o=0,a="",s=arguments.length;o<s;o++)(t=arguments[o])&&(r=zm(t))&&(a&&(a+=" "),a+=r);return a}const Pp=t=>typeof t=="boolean"?`${t}`:t===0?"0":t,bp=Yc,lE=(t,r)=>o=>{var a;if(r?.variants==null)return bp(t,o?.class,o?.className);const{variants:s,defaultVariants:u}=r,d=Object.keys(s).map(m=>{const v=o?.[m],w=u?.[m];if(v===null)return null;const x=Pp(v)||Pp(w);return s[m][x]}),h=o&&Object.entries(o).reduce((m,v)=>{let[w,x]=v;return x===void 0||(m[w]=x),m},{}),p=r==null||(a=r.compoundVariants)===null||a===void 0?void 0:a.reduce((m,v)=>{let{class:w,className:x,...E}=v;return Object.entries(E).every(k=>{let[S,R]=k;return Array.isArray(R)?R.includes({...u,...h}[S]):{...u,...h}[S]===R})?[...m,w,x]:m},[]);return bp(t,d,p,o?.class,o?.className)},Xc="-",sE=t=>{const r=cE(t),{conflictingClassGroups:o,conflictingClassGroupModifiers:a}=t;return{getClassGroupId:d=>{const h=d.split(Xc);return h[0]===""&&h.length!==1&&h.shift(),Um(h,r)||uE(d)},getConflictingClassGroupIds:(d,h)=>{const p=o[d]||[];return h&&a[d]?[...p,...a[d]]:p}}},Um=(t,r)=>{if(t.length===0)return r.classGroupId;const o=t[0],a=r.nextPart.get(o),s=a?Um(t.slice(1),a):void 0;if(s)return s;if(r.validators.length===0)return;const u=t.join(Xc);return r.validators.find(({validator:d})=>d(u))?.classGroupId},Mp=/^\[(.+)\]$/,uE=t=>{if(Mp.test(t)){const r=Mp.exec(t)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},cE=t=>{const{theme:r,classGroups:o}=t,a={nextPart:new Map,validators:[]};for(const s in o)Cc(o[s],a,s,r);return a},Cc=(t,r,o,a)=>{t.forEach(s=>{if(typeof s=="string"){const u=s===""?r:_p(r,s);u.classGroupId=o;return}if(typeof s=="function"){if(dE(s)){Cc(s(a),r,o,a);return}r.validators.push({validator:s,classGroupId:o});return}Object.entries(s).forEach(([u,d])=>{Cc(d,_p(r,u),o,a)})})},_p=(t,r)=>{let o=t;return r.split(Xc).forEach(a=>{o.nextPart.has(a)||o.nextPart.set(a,{nextPart:new Map,validators:[]}),o=o.nextPart.get(a)}),o},dE=t=>t.isThemeGetter,fE=t=>{if(t<1)return{get:()=>{},set:()=>{}};let r=0,o=new Map,a=new Map;const s=(u,d)=>{o.set(u,d),r++,r>t&&(r=0,a=o,o=new Map)};return{get(u){let d=o.get(u);if(d!==void 0)return d;if((d=a.get(u))!==void 0)return s(u,d),d},set(u,d){o.has(u)?o.set(u,d):s(u,d)}}},kc="!",Rc=":",hE=Rc.length,pE=t=>{const{prefix:r,experimentalParseClassName:o}=t;let a=s=>{const u=[];let d=0,h=0,p=0,m;for(let k=0;k<s.length;k++){let S=s[k];if(d===0&&h===0){if(S===Rc){u.push(s.slice(p,k)),p=k+hE;continue}if(S==="/"){m=k;continue}}S==="["?d++:S==="]"?d--:S==="("?h++:S===")"&&h--}const v=u.length===0?s:s.substring(p),w=mE(v),x=w!==v,E=m&&m>p?m-p:void 0;return{modifiers:u,hasImportantModifier:x,baseClassName:w,maybePostfixModifierPosition:E}};if(r){const s=r+Rc,u=a;a=d=>d.startsWith(s)?u(d.substring(s.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:d,maybePostfixModifierPosition:void 0}}if(o){const s=a;a=u=>o({className:u,parseClassName:s})}return a},mE=t=>t.endsWith(kc)?t.substring(0,t.length-1):t.startsWith(kc)?t.substring(1):t,vE=t=>{const r=Object.fromEntries(t.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;const s=[];let u=[];return a.forEach(d=>{d[0]==="["||r[d]?(s.push(...u.sort(),d),u=[]):u.push(d)}),s.push(...u.sort()),s}},gE=t=>({cache:fE(t.cacheSize),parseClassName:pE(t),sortModifiers:vE(t),...sE(t)}),yE=/\s+/,wE=(t,r)=>{const{parseClassName:o,getClassGroupId:a,getConflictingClassGroupIds:s,sortModifiers:u}=r,d=[],h=t.trim().split(yE);let p="";for(let m=h.length-1;m>=0;m-=1){const v=h[m],{isExternal:w,modifiers:x,hasImportantModifier:E,baseClassName:k,maybePostfixModifierPosition:S}=o(v);if(w){p=v+(p.length>0?" "+p:p);continue}let R=!!S,P=a(R?k.substring(0,S):k);if(!P){if(!R){p=v+(p.length>0?" "+p:p);continue}if(P=a(k),!P){p=v+(p.length>0?" "+p:p);continue}R=!1}const j=u(x).join(":"),L=E?j+kc:j,U=L+P;if(d.includes(U))continue;d.push(U);const H=s(P,R);for(let _=0;_<H.length;++_){const K=H[_];d.push(L+K)}p=v+(p.length>0?" "+p:p)}return p};function xE(){let t=0,r,o,a="";for(;t<arguments.length;)(r=arguments[t++])&&(o=$m(r))&&(a&&(a+=" "),a+=o);return a}const $m=t=>{if(typeof t=="string")return t;let r,o="";for(let a=0;a<t.length;a++)t[a]&&(r=$m(t[a]))&&(o&&(o+=" "),o+=r);return o};function SE(t,...r){let o,a,s,u=d;function d(p){const m=r.reduce((v,w)=>w(v),t());return o=gE(m),a=o.cache.get,s=o.cache.set,u=h,h(p)}function h(p){const m=a(p);if(m)return m;const v=wE(p,o);return s(p,v),v}return function(){return u(xE.apply(null,arguments))}}const st=t=>{const r=o=>o[t]||[];return r.isThemeGetter=!0,r},Hm=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Bm=/^\((?:(\w[\w-]*):)?(.+)\)$/i,EE=/^\d+\/\d+$/,CE=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,kE=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,RE=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,PE=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,bE=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Do=t=>EE.test(t),Ne=t=>!!t&&!Number.isNaN(Number(t)),Er=t=>!!t&&Number.isInteger(Number(t)),ec=t=>t.endsWith("%")&&Ne(t.slice(0,-1)),Qn=t=>CE.test(t),ME=()=>!0,_E=t=>kE.test(t)&&!RE.test(t),Vm=()=>!1,TE=t=>PE.test(t),OE=t=>bE.test(t),NE=t=>!fe(t)&&!he(t),DE=t=>Bo(t,Qm,Vm),fe=t=>Hm.test(t),qr=t=>Bo(t,Gm,_E),tc=t=>Bo(t,jE,Ne),Tp=t=>Bo(t,Wm,Vm),LE=t=>Bo(t,Km,OE),yl=t=>Bo(t,Ym,TE),he=t=>Bm.test(t),Fi=t=>Vo(t,Gm),AE=t=>Vo(t,zE),Op=t=>Vo(t,Wm),IE=t=>Vo(t,Qm),FE=t=>Vo(t,Km),wl=t=>Vo(t,Ym,!0),Bo=(t,r,o)=>{const a=Hm.exec(t);return a?a[1]?r(a[1]):o(a[2]):!1},Vo=(t,r,o=!1)=>{const a=Bm.exec(t);return a?a[1]?r(a[1]):o:!1},Wm=t=>t==="position"||t==="percentage",Km=t=>t==="image"||t==="url",Qm=t=>t==="length"||t==="size"||t==="bg-size",Gm=t=>t==="length",jE=t=>t==="number",zE=t=>t==="family-name",Ym=t=>t==="shadow",UE=()=>{const t=st("color"),r=st("font"),o=st("text"),a=st("font-weight"),s=st("tracking"),u=st("leading"),d=st("breakpoint"),h=st("container"),p=st("spacing"),m=st("radius"),v=st("shadow"),w=st("inset-shadow"),x=st("text-shadow"),E=st("drop-shadow"),k=st("blur"),S=st("perspective"),R=st("aspect"),P=st("ease"),j=st("animate"),L=()=>["auto","avoid","all","avoid-page","page","left","right","column"],U=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],H=()=>[...U(),he,fe],_=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto","contain","none"],V=()=>[he,fe,p],te=()=>[Do,"full","auto",...V()],ve=()=>[Er,"none","subgrid",he,fe],Te=()=>["auto",{span:["full",Er,he,fe]},Er,he,fe],_e=()=>[Er,"auto",he,fe],oe=()=>["auto","min","max","fr",he,fe],ae=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Pe=()=>["start","end","center","stretch","center-safe","end-safe"],Z=()=>["auto",...V()],se=()=>[Do,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...V()],N=()=>[t,he,fe],W=()=>[...U(),Op,Tp,{position:[he,fe]}],G=()=>["no-repeat",{repeat:["","x","y","space","round"]}],T=()=>["auto","cover","contain",IE,DE,{size:[he,fe]}],B=()=>[ec,Fi,qr],ne=()=>["","none","full",m,he,fe],ue=()=>["",Ne,Fi,qr],be=()=>["solid","dashed","dotted","double"],Re=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ie=()=>[Ne,ec,Op,Tp],Oe=()=>["","none",k,he,fe],je=()=>["none",Ne,he,fe],Ae=()=>["none",Ne,he,fe],pt=()=>[Ne,he,fe],Qt=()=>[Do,"full",...V()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Qn],breakpoint:[Qn],color:[ME],container:[Qn],"drop-shadow":[Qn],ease:["in","out","in-out"],font:[NE],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Qn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Qn],shadow:[Qn],spacing:["px",Ne],text:[Qn],"text-shadow":[Qn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Do,fe,he,R]}],container:["container"],columns:[{columns:[Ne,fe,he,h]}],"break-after":[{"break-after":L()}],"break-before":[{"break-before":L()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:H()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:K()}],"overscroll-x":[{"overscroll-x":K()}],"overscroll-y":[{"overscroll-y":K()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:te()}],"inset-x":[{"inset-x":te()}],"inset-y":[{"inset-y":te()}],start:[{start:te()}],end:[{end:te()}],top:[{top:te()}],right:[{right:te()}],bottom:[{bottom:te()}],left:[{left:te()}],visibility:["visible","invisible","collapse"],z:[{z:[Er,"auto",he,fe]}],basis:[{basis:[Do,"full","auto",h,...V()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Ne,Do,"auto","initial","none",fe]}],grow:[{grow:["",Ne,he,fe]}],shrink:[{shrink:["",Ne,he,fe]}],order:[{order:[Er,"first","last","none",he,fe]}],"grid-cols":[{"grid-cols":ve()}],"col-start-end":[{col:Te()}],"col-start":[{"col-start":_e()}],"col-end":[{"col-end":_e()}],"grid-rows":[{"grid-rows":ve()}],"row-start-end":[{row:Te()}],"row-start":[{"row-start":_e()}],"row-end":[{"row-end":_e()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":oe()}],"auto-rows":[{"auto-rows":oe()}],gap:[{gap:V()}],"gap-x":[{"gap-x":V()}],"gap-y":[{"gap-y":V()}],"justify-content":[{justify:[...ae(),"normal"]}],"justify-items":[{"justify-items":[...Pe(),"normal"]}],"justify-self":[{"justify-self":["auto",...Pe()]}],"align-content":[{content:["normal",...ae()]}],"align-items":[{items:[...Pe(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Pe(),{baseline:["","last"]}]}],"place-content":[{"place-content":ae()}],"place-items":[{"place-items":[...Pe(),"baseline"]}],"place-self":[{"place-self":["auto",...Pe()]}],p:[{p:V()}],px:[{px:V()}],py:[{py:V()}],ps:[{ps:V()}],pe:[{pe:V()}],pt:[{pt:V()}],pr:[{pr:V()}],pb:[{pb:V()}],pl:[{pl:V()}],m:[{m:Z()}],mx:[{mx:Z()}],my:[{my:Z()}],ms:[{ms:Z()}],me:[{me:Z()}],mt:[{mt:Z()}],mr:[{mr:Z()}],mb:[{mb:Z()}],ml:[{ml:Z()}],"space-x":[{"space-x":V()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":V()}],"space-y-reverse":["space-y-reverse"],size:[{size:se()}],w:[{w:[h,"screen",...se()]}],"min-w":[{"min-w":[h,"screen","none",...se()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[d]},...se()]}],h:[{h:["screen","lh",...se()]}],"min-h":[{"min-h":["screen","lh","none",...se()]}],"max-h":[{"max-h":["screen","lh",...se()]}],"font-size":[{text:["base",o,Fi,qr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[a,he,tc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ec,fe]}],"font-family":[{font:[AE,fe,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,he,fe]}],"line-clamp":[{"line-clamp":[Ne,"none",he,tc]}],leading:[{leading:[u,...V()]}],"list-image":[{"list-image":["none",he,fe]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",he,fe]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:N()}],"text-color":[{text:N()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...be(),"wavy"]}],"text-decoration-thickness":[{decoration:[Ne,"from-font","auto",he,qr]}],"text-decoration-color":[{decoration:N()}],"underline-offset":[{"underline-offset":[Ne,"auto",he,fe]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:V()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",he,fe]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",he,fe]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:W()}],"bg-repeat":[{bg:G()}],"bg-size":[{bg:T()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Er,he,fe],radial:["",he,fe],conic:[Er,he,fe]},FE,LE]}],"bg-color":[{bg:N()}],"gradient-from-pos":[{from:B()}],"gradient-via-pos":[{via:B()}],"gradient-to-pos":[{to:B()}],"gradient-from":[{from:N()}],"gradient-via":[{via:N()}],"gradient-to":[{to:N()}],rounded:[{rounded:ne()}],"rounded-s":[{"rounded-s":ne()}],"rounded-e":[{"rounded-e":ne()}],"rounded-t":[{"rounded-t":ne()}],"rounded-r":[{"rounded-r":ne()}],"rounded-b":[{"rounded-b":ne()}],"rounded-l":[{"rounded-l":ne()}],"rounded-ss":[{"rounded-ss":ne()}],"rounded-se":[{"rounded-se":ne()}],"rounded-ee":[{"rounded-ee":ne()}],"rounded-es":[{"rounded-es":ne()}],"rounded-tl":[{"rounded-tl":ne()}],"rounded-tr":[{"rounded-tr":ne()}],"rounded-br":[{"rounded-br":ne()}],"rounded-bl":[{"rounded-bl":ne()}],"border-w":[{border:ue()}],"border-w-x":[{"border-x":ue()}],"border-w-y":[{"border-y":ue()}],"border-w-s":[{"border-s":ue()}],"border-w-e":[{"border-e":ue()}],"border-w-t":[{"border-t":ue()}],"border-w-r":[{"border-r":ue()}],"border-w-b":[{"border-b":ue()}],"border-w-l":[{"border-l":ue()}],"divide-x":[{"divide-x":ue()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ue()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...be(),"hidden","none"]}],"divide-style":[{divide:[...be(),"hidden","none"]}],"border-color":[{border:N()}],"border-color-x":[{"border-x":N()}],"border-color-y":[{"border-y":N()}],"border-color-s":[{"border-s":N()}],"border-color-e":[{"border-e":N()}],"border-color-t":[{"border-t":N()}],"border-color-r":[{"border-r":N()}],"border-color-b":[{"border-b":N()}],"border-color-l":[{"border-l":N()}],"divide-color":[{divide:N()}],"outline-style":[{outline:[...be(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Ne,he,fe]}],"outline-w":[{outline:["",Ne,Fi,qr]}],"outline-color":[{outline:N()}],shadow:[{shadow:["","none",v,wl,yl]}],"shadow-color":[{shadow:N()}],"inset-shadow":[{"inset-shadow":["none",w,wl,yl]}],"inset-shadow-color":[{"inset-shadow":N()}],"ring-w":[{ring:ue()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:N()}],"ring-offset-w":[{"ring-offset":[Ne,qr]}],"ring-offset-color":[{"ring-offset":N()}],"inset-ring-w":[{"inset-ring":ue()}],"inset-ring-color":[{"inset-ring":N()}],"text-shadow":[{"text-shadow":["none",x,wl,yl]}],"text-shadow-color":[{"text-shadow":N()}],opacity:[{opacity:[Ne,he,fe]}],"mix-blend":[{"mix-blend":[...Re(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Re()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Ne]}],"mask-image-linear-from-pos":[{"mask-linear-from":ie()}],"mask-image-linear-to-pos":[{"mask-linear-to":ie()}],"mask-image-linear-from-color":[{"mask-linear-from":N()}],"mask-image-linear-to-color":[{"mask-linear-to":N()}],"mask-image-t-from-pos":[{"mask-t-from":ie()}],"mask-image-t-to-pos":[{"mask-t-to":ie()}],"mask-image-t-from-color":[{"mask-t-from":N()}],"mask-image-t-to-color":[{"mask-t-to":N()}],"mask-image-r-from-pos":[{"mask-r-from":ie()}],"mask-image-r-to-pos":[{"mask-r-to":ie()}],"mask-image-r-from-color":[{"mask-r-from":N()}],"mask-image-r-to-color":[{"mask-r-to":N()}],"mask-image-b-from-pos":[{"mask-b-from":ie()}],"mask-image-b-to-pos":[{"mask-b-to":ie()}],"mask-image-b-from-color":[{"mask-b-from":N()}],"mask-image-b-to-color":[{"mask-b-to":N()}],"mask-image-l-from-pos":[{"mask-l-from":ie()}],"mask-image-l-to-pos":[{"mask-l-to":ie()}],"mask-image-l-from-color":[{"mask-l-from":N()}],"mask-image-l-to-color":[{"mask-l-to":N()}],"mask-image-x-from-pos":[{"mask-x-from":ie()}],"mask-image-x-to-pos":[{"mask-x-to":ie()}],"mask-image-x-from-color":[{"mask-x-from":N()}],"mask-image-x-to-color":[{"mask-x-to":N()}],"mask-image-y-from-pos":[{"mask-y-from":ie()}],"mask-image-y-to-pos":[{"mask-y-to":ie()}],"mask-image-y-from-color":[{"mask-y-from":N()}],"mask-image-y-to-color":[{"mask-y-to":N()}],"mask-image-radial":[{"mask-radial":[he,fe]}],"mask-image-radial-from-pos":[{"mask-radial-from":ie()}],"mask-image-radial-to-pos":[{"mask-radial-to":ie()}],"mask-image-radial-from-color":[{"mask-radial-from":N()}],"mask-image-radial-to-color":[{"mask-radial-to":N()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":U()}],"mask-image-conic-pos":[{"mask-conic":[Ne]}],"mask-image-conic-from-pos":[{"mask-conic-from":ie()}],"mask-image-conic-to-pos":[{"mask-conic-to":ie()}],"mask-image-conic-from-color":[{"mask-conic-from":N()}],"mask-image-conic-to-color":[{"mask-conic-to":N()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:W()}],"mask-repeat":[{mask:G()}],"mask-size":[{mask:T()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",he,fe]}],filter:[{filter:["","none",he,fe]}],blur:[{blur:Oe()}],brightness:[{brightness:[Ne,he,fe]}],contrast:[{contrast:[Ne,he,fe]}],"drop-shadow":[{"drop-shadow":["","none",E,wl,yl]}],"drop-shadow-color":[{"drop-shadow":N()}],grayscale:[{grayscale:["",Ne,he,fe]}],"hue-rotate":[{"hue-rotate":[Ne,he,fe]}],invert:[{invert:["",Ne,he,fe]}],saturate:[{saturate:[Ne,he,fe]}],sepia:[{sepia:["",Ne,he,fe]}],"backdrop-filter":[{"backdrop-filter":["","none",he,fe]}],"backdrop-blur":[{"backdrop-blur":Oe()}],"backdrop-brightness":[{"backdrop-brightness":[Ne,he,fe]}],"backdrop-contrast":[{"backdrop-contrast":[Ne,he,fe]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Ne,he,fe]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Ne,he,fe]}],"backdrop-invert":[{"backdrop-invert":["",Ne,he,fe]}],"backdrop-opacity":[{"backdrop-opacity":[Ne,he,fe]}],"backdrop-saturate":[{"backdrop-saturate":[Ne,he,fe]}],"backdrop-sepia":[{"backdrop-sepia":["",Ne,he,fe]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":V()}],"border-spacing-x":[{"border-spacing-x":V()}],"border-spacing-y":[{"border-spacing-y":V()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",he,fe]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Ne,"initial",he,fe]}],ease:[{ease:["linear","initial",P,he,fe]}],delay:[{delay:[Ne,he,fe]}],animate:[{animate:["none",j,he,fe]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[S,he,fe]}],"perspective-origin":[{"perspective-origin":H()}],rotate:[{rotate:je()}],"rotate-x":[{"rotate-x":je()}],"rotate-y":[{"rotate-y":je()}],"rotate-z":[{"rotate-z":je()}],scale:[{scale:Ae()}],"scale-x":[{"scale-x":Ae()}],"scale-y":[{"scale-y":Ae()}],"scale-z":[{"scale-z":Ae()}],"scale-3d":["scale-3d"],skew:[{skew:pt()}],"skew-x":[{"skew-x":pt()}],"skew-y":[{"skew-y":pt()}],transform:[{transform:[he,fe,"","none","gpu","cpu"]}],"transform-origin":[{origin:H()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Qt()}],"translate-x":[{"translate-x":Qt()}],"translate-y":[{"translate-y":Qt()}],"translate-z":[{"translate-z":Qt()}],"translate-none":["translate-none"],accent:[{accent:N()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:N()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",he,fe]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":V()}],"scroll-mx":[{"scroll-mx":V()}],"scroll-my":[{"scroll-my":V()}],"scroll-ms":[{"scroll-ms":V()}],"scroll-me":[{"scroll-me":V()}],"scroll-mt":[{"scroll-mt":V()}],"scroll-mr":[{"scroll-mr":V()}],"scroll-mb":[{"scroll-mb":V()}],"scroll-ml":[{"scroll-ml":V()}],"scroll-p":[{"scroll-p":V()}],"scroll-px":[{"scroll-px":V()}],"scroll-py":[{"scroll-py":V()}],"scroll-ps":[{"scroll-ps":V()}],"scroll-pe":[{"scroll-pe":V()}],"scroll-pt":[{"scroll-pt":V()}],"scroll-pr":[{"scroll-pr":V()}],"scroll-pb":[{"scroll-pb":V()}],"scroll-pl":[{"scroll-pl":V()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",he,fe]}],fill:[{fill:["none",...N()]}],"stroke-w":[{stroke:[Ne,Fi,qr,tc]}],stroke:[{stroke:["none",...N()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Xm=SE(UE);function $E(...t){return Xm(Yc(t))}const HE=lE("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Pc=g.forwardRef(({className:t,variant:r,size:o,asChild:a=!1,...s},u)=>{const d=a?tE:"button";return D.jsx(d,{className:$E(HE({variant:r,size:o,className:t})),ref:u,...s})});Pc.displayName="Button";function Fe(t,r,{checkForDefaultPrevented:o=!0}={}){return function(s){if(t?.(s),o===!1||!s.defaultPrevented)return r?.(s)}}function OP(t,r){const o=g.createContext(r),a=u=>{const{children:d,...h}=u,p=g.useMemo(()=>h,Object.values(h));return D.jsx(o.Provider,{value:p,children:d})};a.displayName=t+"Provider";function s(u){const d=g.useContext(o);if(d)return d;if(r!==void 0)return r;throw new Error(`\`${u}\` must be used within \`${t}\``)}return[a,s]}function Zi(t,r=[]){let o=[];function a(u,d){const h=g.createContext(d),p=o.length;o=[...o,d];const m=w=>{const{scope:x,children:E,...k}=w,S=x?.[t]?.[p]||h,R=g.useMemo(()=>k,Object.values(k));return D.jsx(S.Provider,{value:R,children:E})};m.displayName=u+"Provider";function v(w,x){const E=x?.[t]?.[p]||h,k=g.useContext(E);if(k)return k;if(d!==void 0)return d;throw new Error(`\`${w}\` must be used within \`${u}\``)}return[m,v]}const s=()=>{const u=o.map(d=>g.createContext(d));return function(h){const p=h?.[t]||u;return g.useMemo(()=>({[`__scope${t}`]:{...h,[t]:p}}),[h,p])}};return s.scopeName=t,[a,BE(s,...r)]}function BE(...t){const r=t[0];if(t.length===1)return r;const o=()=>{const a=t.map(s=>({useScope:s(),scopeName:s.scopeName}));return function(u){const d=a.reduce((h,{useScope:p,scopeName:m})=>{const w=p(u)[`__scope${m}`];return{...h,...w}},{});return g.useMemo(()=>({[`__scope${r.scopeName}`]:d}),[d])}};return o.scopeName=r.scopeName,o}var br=globalThis?.document?g.useLayoutEffect:()=>{},VE=nm[" useInsertionEffect ".trim().toString()]||br;function qm({prop:t,defaultProp:r,onChange:o=()=>{},caller:a}){const[s,u,d]=WE({defaultProp:r,onChange:o}),h=t!==void 0,p=h?t:s;{const v=g.useRef(t!==void 0);g.useEffect(()=>{const w=v.current;w!==h&&console.warn(`${a} is changing from ${w?"controlled":"uncontrolled"} to ${h?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),v.current=h},[h,a])}const m=g.useCallback(v=>{if(h){const w=KE(v)?v(t):v;w!==t&&d.current?.(w)}else u(v)},[h,t,u,d]);return[p,m]}function WE({defaultProp:t,onChange:r}){const[o,a]=g.useState(t),s=g.useRef(o),u=g.useRef(r);return VE(()=>{u.current=r},[r]),g.useEffect(()=>{s.current!==o&&(u.current?.(o),s.current=o)},[o,s]),[o,a,u]}function KE(t){return typeof t=="function"}var QE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Pt=QE.reduce((t,r)=>{const o=Wi(`Primitive.${r}`),a=g.forwardRef((s,u)=>{const{asChild:d,...h}=s,p=d?o:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),D.jsx(p,{...h,ref:u})});return a.displayName=`Primitive.${r}`,{...t,[r]:a}},{});function Jm(t,r){t&&Zl.flushSync(()=>t.dispatchEvent(r))}function Zm(t){const r=t+"CollectionProvider",[o,a]=Zi(r),[s,u]=o(r,{collectionRef:{current:null},itemMap:new Map}),d=S=>{const{scope:R,children:P}=S,j=It.useRef(null),L=It.useRef(new Map).current;return D.jsx(s,{scope:R,itemMap:L,collectionRef:j,children:P})};d.displayName=r;const h=t+"CollectionSlot",p=Wi(h),m=It.forwardRef((S,R)=>{const{scope:P,children:j}=S,L=u(h,P),U=jt(R,L.collectionRef);return D.jsx(p,{ref:U,children:j})});m.displayName=h;const v=t+"CollectionItemSlot",w="data-radix-collection-item",x=Wi(v),E=It.forwardRef((S,R)=>{const{scope:P,children:j,...L}=S,U=It.useRef(null),H=jt(R,U),_=u(v,P);return It.useEffect(()=>(_.itemMap.set(U,{ref:U,...L}),()=>void _.itemMap.delete(U))),D.jsx(x,{[w]:"",ref:H,children:j})});E.displayName=v;function k(S){const R=u(t+"CollectionConsumer",S);return It.useCallback(()=>{const j=R.collectionRef.current;if(!j)return[];const L=Array.from(j.querySelectorAll(`[${w}]`));return Array.from(R.itemMap.values()).sort((_,K)=>L.indexOf(_.ref.current)-L.indexOf(K.ref.current))},[R.collectionRef,R.itemMap])}return[{Provider:d,Slot:m,ItemSlot:E},k,a]}var GE=g.createContext(void 0);function ev(t){const r=g.useContext(GE);return t||r||"ltr"}function Gn(t){const r=g.useRef(t);return g.useEffect(()=>{r.current=t}),g.useMemo(()=>(...o)=>r.current?.(...o),[])}function YE(t,r=globalThis?.document){const o=Gn(t);g.useEffect(()=>{const a=s=>{s.key==="Escape"&&o(s)};return r.addEventListener("keydown",a,{capture:!0}),()=>r.removeEventListener("keydown",a,{capture:!0})},[o,r])}var XE="DismissableLayer",bc="dismissableLayer.update",qE="dismissableLayer.pointerDownOutside",JE="dismissableLayer.focusOutside",Np,tv=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),nv=g.forwardRef((t,r)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:s,onFocusOutside:u,onInteractOutside:d,onDismiss:h,...p}=t,m=g.useContext(tv),[v,w]=g.useState(null),x=v?.ownerDocument??globalThis?.document,[,E]=g.useState({}),k=jt(r,K=>w(K)),S=Array.from(m.layers),[R]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),P=S.indexOf(R),j=v?S.indexOf(v):-1,L=m.layersWithOutsidePointerEventsDisabled.size>0,U=j>=P,H=t1(K=>{const V=K.target,te=[...m.branches].some(ve=>ve.contains(V));!U||te||(s?.(K),d?.(K),K.defaultPrevented||h?.())},x),_=n1(K=>{const V=K.target;[...m.branches].some(ve=>ve.contains(V))||(u?.(K),d?.(K),K.defaultPrevented||h?.())},x);return YE(K=>{j===m.layers.size-1&&(a?.(K),!K.defaultPrevented&&h&&(K.preventDefault(),h()))},x),g.useEffect(()=>{if(v)return o&&(m.layersWithOutsidePointerEventsDisabled.size===0&&(Np=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(v)),m.layers.add(v),Dp(),()=>{o&&m.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=Np)}},[v,x,o,m]),g.useEffect(()=>()=>{v&&(m.layers.delete(v),m.layersWithOutsidePointerEventsDisabled.delete(v),Dp())},[v,m]),g.useEffect(()=>{const K=()=>E({});return document.addEventListener(bc,K),()=>document.removeEventListener(bc,K)},[]),D.jsx(Pt.div,{...p,ref:k,style:{pointerEvents:L?U?"auto":"none":void 0,...t.style},onFocusCapture:Fe(t.onFocusCapture,_.onFocusCapture),onBlurCapture:Fe(t.onBlurCapture,_.onBlurCapture),onPointerDownCapture:Fe(t.onPointerDownCapture,H.onPointerDownCapture)})});nv.displayName=XE;var ZE="DismissableLayerBranch",e1=g.forwardRef((t,r)=>{const o=g.useContext(tv),a=g.useRef(null),s=jt(r,a);return g.useEffect(()=>{const u=a.current;if(u)return o.branches.add(u),()=>{o.branches.delete(u)}},[o.branches]),D.jsx(Pt.div,{...t,ref:s})});e1.displayName=ZE;function t1(t,r=globalThis?.document){const o=Gn(t),a=g.useRef(!1),s=g.useRef(()=>{});return g.useEffect(()=>{const u=h=>{if(h.target&&!a.current){let p=function(){rv(qE,o,m,{discrete:!0})};const m={originalEvent:h};h.pointerType==="touch"?(r.removeEventListener("click",s.current),s.current=p,r.addEventListener("click",s.current,{once:!0})):p()}else r.removeEventListener("click",s.current);a.current=!1},d=window.setTimeout(()=>{r.addEventListener("pointerdown",u)},0);return()=>{window.clearTimeout(d),r.removeEventListener("pointerdown",u),r.removeEventListener("click",s.current)}},[r,o]),{onPointerDownCapture:()=>a.current=!0}}function n1(t,r=globalThis?.document){const o=Gn(t),a=g.useRef(!1);return g.useEffect(()=>{const s=u=>{u.target&&!a.current&&rv(JE,o,{originalEvent:u},{discrete:!1})};return r.addEventListener("focusin",s),()=>r.removeEventListener("focusin",s)},[r,o]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}function Dp(){const t=new CustomEvent(bc);document.dispatchEvent(t)}function rv(t,r,o,{discrete:a}){const s=o.originalEvent.target,u=new CustomEvent(t,{bubbles:!1,cancelable:!0,detail:o});r&&s.addEventListener(t,r,{once:!0}),a?Jm(s,u):s.dispatchEvent(u)}var nc=0;function r1(){g.useEffect(()=>{const t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",t[0]??Lp()),document.body.insertAdjacentElement("beforeend",t[1]??Lp()),nc++,()=>{nc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),nc--}},[])}function Lp(){const t=document.createElement("span");return t.setAttribute("data-radix-focus-guard",""),t.tabIndex=0,t.style.outline="none",t.style.opacity="0",t.style.position="fixed",t.style.pointerEvents="none",t}var rc="focusScope.autoFocusOnMount",oc="focusScope.autoFocusOnUnmount",Ap={bubbles:!1,cancelable:!0},o1="FocusScope",ov=g.forwardRef((t,r)=>{const{loop:o=!1,trapped:a=!1,onMountAutoFocus:s,onUnmountAutoFocus:u,...d}=t,[h,p]=g.useState(null),m=Gn(s),v=Gn(u),w=g.useRef(null),x=jt(r,S=>p(S)),E=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(a){let S=function(L){if(E.paused||!h)return;const U=L.target;h.contains(U)?w.current=U:kr(w.current,{select:!0})},R=function(L){if(E.paused||!h)return;const U=L.relatedTarget;U!==null&&(h.contains(U)||kr(w.current,{select:!0}))},P=function(L){if(document.activeElement===document.body)for(const H of L)H.removedNodes.length>0&&kr(h)};document.addEventListener("focusin",S),document.addEventListener("focusout",R);const j=new MutationObserver(P);return h&&j.observe(h,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",S),document.removeEventListener("focusout",R),j.disconnect()}}},[a,h,E.paused]),g.useEffect(()=>{if(h){Fp.add(E);const S=document.activeElement;if(!h.contains(S)){const P=new CustomEvent(rc,Ap);h.addEventListener(rc,m),h.dispatchEvent(P),P.defaultPrevented||(i1(c1(iv(h)),{select:!0}),document.activeElement===S&&kr(h))}return()=>{h.removeEventListener(rc,m),setTimeout(()=>{const P=new CustomEvent(oc,Ap);h.addEventListener(oc,v),h.dispatchEvent(P),P.defaultPrevented||kr(S??document.body,{select:!0}),h.removeEventListener(oc,v),Fp.remove(E)},0)}}},[h,m,v,E]);const k=g.useCallback(S=>{if(!o&&!a||E.paused)return;const R=S.key==="Tab"&&!S.altKey&&!S.ctrlKey&&!S.metaKey,P=document.activeElement;if(R&&P){const j=S.currentTarget,[L,U]=a1(j);L&&U?!S.shiftKey&&P===U?(S.preventDefault(),o&&kr(L,{select:!0})):S.shiftKey&&P===L&&(S.preventDefault(),o&&kr(U,{select:!0})):P===j&&S.preventDefault()}},[o,a,E.paused]);return D.jsx(Pt.div,{tabIndex:-1,...d,ref:x,onKeyDown:k})});ov.displayName=o1;function i1(t,{select:r=!1}={}){const o=document.activeElement;for(const a of t)if(kr(a,{select:r}),document.activeElement!==o)return}function a1(t){const r=iv(t),o=Ip(r,t),a=Ip(r.reverse(),t);return[o,a]}function iv(t){const r=[],o=document.createTreeWalker(t,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{const s=a.tagName==="INPUT"&&a.type==="hidden";return a.disabled||a.hidden||s?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)r.push(o.currentNode);return r}function Ip(t,r){for(const o of t)if(!l1(o,{upTo:r}))return o}function l1(t,{upTo:r}){if(getComputedStyle(t).visibility==="hidden")return!0;for(;t;){if(r!==void 0&&t===r)return!1;if(getComputedStyle(t).display==="none")return!0;t=t.parentElement}return!1}function s1(t){return t instanceof HTMLInputElement&&"select"in t}function kr(t,{select:r=!1}={}){if(t&&t.focus){const o=document.activeElement;t.focus({preventScroll:!0}),t!==o&&s1(t)&&r&&t.select()}}var Fp=u1();function u1(){let t=[];return{add(r){const o=t[0];r!==o&&o?.pause(),t=jp(t,r),t.unshift(r)},remove(r){t=jp(t,r),t[0]?.resume()}}}function jp(t,r){const o=[...t],a=o.indexOf(r);return a!==-1&&o.splice(a,1),o}function c1(t){return t.filter(r=>r.tagName!=="A")}var d1=nm[" useId ".trim().toString()]||(()=>{}),f1=0;function Mc(t){const[r,o]=g.useState(d1());return br(()=>{o(a=>a??String(f1++))},[t]),r?`radix-${r}`:""}const h1=["top","right","bottom","left"],Mr=Math.min,Wt=Math.max,Ul=Math.round,xl=Math.floor,Nn=t=>({x:t,y:t}),p1={left:"right",right:"left",bottom:"top",top:"bottom"},m1={start:"end",end:"start"};function _c(t,r,o){return Wt(t,Mr(r,o))}function Yn(t,r){return typeof t=="function"?t(r):t}function Xn(t){return t.split("-")[0]}function Wo(t){return t.split("-")[1]}function qc(t){return t==="x"?"y":"x"}function Jc(t){return t==="y"?"height":"width"}const v1=new Set(["top","bottom"]);function Tn(t){return v1.has(Xn(t))?"y":"x"}function Zc(t){return qc(Tn(t))}function g1(t,r,o){o===void 0&&(o=!1);const a=Wo(t),s=Zc(t),u=Jc(s);let d=s==="x"?a===(o?"end":"start")?"right":"left":a==="start"?"bottom":"top";return r.reference[u]>r.floating[u]&&(d=$l(d)),[d,$l(d)]}function y1(t){const r=$l(t);return[Tc(t),r,Tc(r)]}function Tc(t){return t.replace(/start|end/g,r=>m1[r])}const zp=["left","right"],Up=["right","left"],w1=["top","bottom"],x1=["bottom","top"];function S1(t,r,o){switch(t){case"top":case"bottom":return o?r?Up:zp:r?zp:Up;case"left":case"right":return r?w1:x1;default:return[]}}function E1(t,r,o,a){const s=Wo(t);let u=S1(Xn(t),o==="start",a);return s&&(u=u.map(d=>d+"-"+s),r&&(u=u.concat(u.map(Tc)))),u}function $l(t){return t.replace(/left|right|bottom|top/g,r=>p1[r])}function C1(t){return{top:0,right:0,bottom:0,left:0,...t}}function av(t){return typeof t!="number"?C1(t):{top:t,right:t,bottom:t,left:t}}function Hl(t){const{x:r,y:o,width:a,height:s}=t;return{width:a,height:s,top:o,left:r,right:r+a,bottom:o+s,x:r,y:o}}function $p(t,r,o){let{reference:a,floating:s}=t;const u=Tn(r),d=Zc(r),h=Jc(d),p=Xn(r),m=u==="y",v=a.x+a.width/2-s.width/2,w=a.y+a.height/2-s.height/2,x=a[h]/2-s[h]/2;let E;switch(p){case"top":E={x:v,y:a.y-s.height};break;case"bottom":E={x:v,y:a.y+a.height};break;case"right":E={x:a.x+a.width,y:w};break;case"left":E={x:a.x-s.width,y:w};break;default:E={x:a.x,y:a.y}}switch(Wo(r)){case"start":E[d]-=x*(o&&m?-1:1);break;case"end":E[d]+=x*(o&&m?-1:1);break}return E}const k1=async(t,r,o)=>{const{placement:a="bottom",strategy:s="absolute",middleware:u=[],platform:d}=o,h=u.filter(Boolean),p=await(d.isRTL==null?void 0:d.isRTL(r));let m=await d.getElementRects({reference:t,floating:r,strategy:s}),{x:v,y:w}=$p(m,a,p),x=a,E={},k=0;for(let S=0;S<h.length;S++){const{name:R,fn:P}=h[S],{x:j,y:L,data:U,reset:H}=await P({x:v,y:w,initialPlacement:a,placement:x,strategy:s,middlewareData:E,rects:m,platform:d,elements:{reference:t,floating:r}});v=j??v,w=L??w,E={...E,[R]:{...E[R],...U}},H&&k<=50&&(k++,typeof H=="object"&&(H.placement&&(x=H.placement),H.rects&&(m=H.rects===!0?await d.getElementRects({reference:t,floating:r,strategy:s}):H.rects),{x:v,y:w}=$p(m,x,p)),S=-1)}return{x:v,y:w,placement:x,strategy:s,middlewareData:E}};async function Ki(t,r){var o;r===void 0&&(r={});const{x:a,y:s,platform:u,rects:d,elements:h,strategy:p}=t,{boundary:m="clippingAncestors",rootBoundary:v="viewport",elementContext:w="floating",altBoundary:x=!1,padding:E=0}=Yn(r,t),k=av(E),R=h[x?w==="floating"?"reference":"floating":w],P=Hl(await u.getClippingRect({element:(o=await(u.isElement==null?void 0:u.isElement(R)))==null||o?R:R.contextElement||await(u.getDocumentElement==null?void 0:u.getDocumentElement(h.floating)),boundary:m,rootBoundary:v,strategy:p})),j=w==="floating"?{x:a,y:s,width:d.floating.width,height:d.floating.height}:d.reference,L=await(u.getOffsetParent==null?void 0:u.getOffsetParent(h.floating)),U=await(u.isElement==null?void 0:u.isElement(L))?await(u.getScale==null?void 0:u.getScale(L))||{x:1,y:1}:{x:1,y:1},H=Hl(u.convertOffsetParentRelativeRectToViewportRelativeRect?await u.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:j,offsetParent:L,strategy:p}):j);return{top:(P.top-H.top+k.top)/U.y,bottom:(H.bottom-P.bottom+k.bottom)/U.y,left:(P.left-H.left+k.left)/U.x,right:(H.right-P.right+k.right)/U.x}}const R1=t=>({name:"arrow",options:t,async fn(r){const{x:o,y:a,placement:s,rects:u,platform:d,elements:h,middlewareData:p}=r,{element:m,padding:v=0}=Yn(t,r)||{};if(m==null)return{};const w=av(v),x={x:o,y:a},E=Zc(s),k=Jc(E),S=await d.getDimensions(m),R=E==="y",P=R?"top":"left",j=R?"bottom":"right",L=R?"clientHeight":"clientWidth",U=u.reference[k]+u.reference[E]-x[E]-u.floating[k],H=x[E]-u.reference[E],_=await(d.getOffsetParent==null?void 0:d.getOffsetParent(m));let K=_?_[L]:0;(!K||!await(d.isElement==null?void 0:d.isElement(_)))&&(K=h.floating[L]||u.floating[k]);const V=U/2-H/2,te=K/2-S[k]/2-1,ve=Mr(w[P],te),Te=Mr(w[j],te),_e=ve,oe=K-S[k]-Te,ae=K/2-S[k]/2+V,Pe=_c(_e,ae,oe),Z=!p.arrow&&Wo(s)!=null&&ae!==Pe&&u.reference[k]/2-(ae<_e?ve:Te)-S[k]/2<0,se=Z?ae<_e?ae-_e:ae-oe:0;return{[E]:x[E]+se,data:{[E]:Pe,centerOffset:ae-Pe-se,...Z&&{alignmentOffset:se}},reset:Z}}}),P1=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(r){var o,a;const{placement:s,middlewareData:u,rects:d,initialPlacement:h,platform:p,elements:m}=r,{mainAxis:v=!0,crossAxis:w=!0,fallbackPlacements:x,fallbackStrategy:E="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:S=!0,...R}=Yn(t,r);if((o=u.arrow)!=null&&o.alignmentOffset)return{};const P=Xn(s),j=Tn(h),L=Xn(h)===h,U=await(p.isRTL==null?void 0:p.isRTL(m.floating)),H=x||(L||!S?[$l(h)]:y1(h)),_=k!=="none";!x&&_&&H.push(...E1(h,S,k,U));const K=[h,...H],V=await Ki(r,R),te=[];let ve=((a=u.flip)==null?void 0:a.overflows)||[];if(v&&te.push(V[P]),w){const ae=g1(s,d,U);te.push(V[ae[0]],V[ae[1]])}if(ve=[...ve,{placement:s,overflows:te}],!te.every(ae=>ae<=0)){var Te,_e;const ae=(((Te=u.flip)==null?void 0:Te.index)||0)+1,Pe=K[ae];if(Pe&&(!(w==="alignment"?j!==Tn(Pe):!1)||ve.every(N=>N.overflows[0]>0&&Tn(N.placement)===j)))return{data:{index:ae,overflows:ve},reset:{placement:Pe}};let Z=(_e=ve.filter(se=>se.overflows[0]<=0).sort((se,N)=>se.overflows[1]-N.overflows[1])[0])==null?void 0:_e.placement;if(!Z)switch(E){case"bestFit":{var oe;const se=(oe=ve.filter(N=>{if(_){const W=Tn(N.placement);return W===j||W==="y"}return!0}).map(N=>[N.placement,N.overflows.filter(W=>W>0).reduce((W,G)=>W+G,0)]).sort((N,W)=>N[1]-W[1])[0])==null?void 0:oe[0];se&&(Z=se);break}case"initialPlacement":Z=h;break}if(s!==Z)return{reset:{placement:Z}}}return{}}}};function Hp(t,r){return{top:t.top-r.height,right:t.right-r.width,bottom:t.bottom-r.height,left:t.left-r.width}}function Bp(t){return h1.some(r=>t[r]>=0)}const b1=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(r){const{rects:o}=r,{strategy:a="referenceHidden",...s}=Yn(t,r);switch(a){case"referenceHidden":{const u=await Ki(r,{...s,elementContext:"reference"}),d=Hp(u,o.reference);return{data:{referenceHiddenOffsets:d,referenceHidden:Bp(d)}}}case"escaped":{const u=await Ki(r,{...s,altBoundary:!0}),d=Hp(u,o.floating);return{data:{escapedOffsets:d,escaped:Bp(d)}}}default:return{}}}}},lv=new Set(["left","top"]);async function M1(t,r){const{placement:o,platform:a,elements:s}=t,u=await(a.isRTL==null?void 0:a.isRTL(s.floating)),d=Xn(o),h=Wo(o),p=Tn(o)==="y",m=lv.has(d)?-1:1,v=u&&p?-1:1,w=Yn(r,t);let{mainAxis:x,crossAxis:E,alignmentAxis:k}=typeof w=="number"?{mainAxis:w,crossAxis:0,alignmentAxis:null}:{mainAxis:w.mainAxis||0,crossAxis:w.crossAxis||0,alignmentAxis:w.alignmentAxis};return h&&typeof k=="number"&&(E=h==="end"?k*-1:k),p?{x:E*v,y:x*m}:{x:x*m,y:E*v}}const _1=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(r){var o,a;const{x:s,y:u,placement:d,middlewareData:h}=r,p=await M1(r,t);return d===((o=h.offset)==null?void 0:o.placement)&&(a=h.arrow)!=null&&a.alignmentOffset?{}:{x:s+p.x,y:u+p.y,data:{...p,placement:d}}}}},T1=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(r){const{x:o,y:a,placement:s}=r,{mainAxis:u=!0,crossAxis:d=!1,limiter:h={fn:R=>{let{x:P,y:j}=R;return{x:P,y:j}}},...p}=Yn(t,r),m={x:o,y:a},v=await Ki(r,p),w=Tn(Xn(s)),x=qc(w);let E=m[x],k=m[w];if(u){const R=x==="y"?"top":"left",P=x==="y"?"bottom":"right",j=E+v[R],L=E-v[P];E=_c(j,E,L)}if(d){const R=w==="y"?"top":"left",P=w==="y"?"bottom":"right",j=k+v[R],L=k-v[P];k=_c(j,k,L)}const S=h.fn({...r,[x]:E,[w]:k});return{...S,data:{x:S.x-o,y:S.y-a,enabled:{[x]:u,[w]:d}}}}}},O1=function(t){return t===void 0&&(t={}),{options:t,fn(r){const{x:o,y:a,placement:s,rects:u,middlewareData:d}=r,{offset:h=0,mainAxis:p=!0,crossAxis:m=!0}=Yn(t,r),v={x:o,y:a},w=Tn(s),x=qc(w);let E=v[x],k=v[w];const S=Yn(h,r),R=typeof S=="number"?{mainAxis:S,crossAxis:0}:{mainAxis:0,crossAxis:0,...S};if(p){const L=x==="y"?"height":"width",U=u.reference[x]-u.floating[L]+R.mainAxis,H=u.reference[x]+u.reference[L]-R.mainAxis;E<U?E=U:E>H&&(E=H)}if(m){var P,j;const L=x==="y"?"width":"height",U=lv.has(Xn(s)),H=u.reference[w]-u.floating[L]+(U&&((P=d.offset)==null?void 0:P[w])||0)+(U?0:R.crossAxis),_=u.reference[w]+u.reference[L]+(U?0:((j=d.offset)==null?void 0:j[w])||0)-(U?R.crossAxis:0);k<H?k=H:k>_&&(k=_)}return{[x]:E,[w]:k}}}},N1=function(t){return t===void 0&&(t={}),{name:"size",options:t,async fn(r){var o,a;const{placement:s,rects:u,platform:d,elements:h}=r,{apply:p=()=>{},...m}=Yn(t,r),v=await Ki(r,m),w=Xn(s),x=Wo(s),E=Tn(s)==="y",{width:k,height:S}=u.floating;let R,P;w==="top"||w==="bottom"?(R=w,P=x===(await(d.isRTL==null?void 0:d.isRTL(h.floating))?"start":"end")?"left":"right"):(P=w,R=x==="end"?"top":"bottom");const j=S-v.top-v.bottom,L=k-v.left-v.right,U=Mr(S-v[R],j),H=Mr(k-v[P],L),_=!r.middlewareData.shift;let K=U,V=H;if((o=r.middlewareData.shift)!=null&&o.enabled.x&&(V=L),(a=r.middlewareData.shift)!=null&&a.enabled.y&&(K=j),_&&!x){const ve=Wt(v.left,0),Te=Wt(v.right,0),_e=Wt(v.top,0),oe=Wt(v.bottom,0);E?V=k-2*(ve!==0||Te!==0?ve+Te:Wt(v.left,v.right)):K=S-2*(_e!==0||oe!==0?_e+oe:Wt(v.top,v.bottom))}await p({...r,availableWidth:V,availableHeight:K});const te=await d.getDimensions(h.floating);return k!==te.width||S!==te.height?{reset:{rects:!0}}:{}}}};function ts(){return typeof window<"u"}function Ko(t){return sv(t)?(t.nodeName||"").toLowerCase():"#document"}function Kt(t){var r;return(t==null||(r=t.ownerDocument)==null?void 0:r.defaultView)||window}function In(t){var r;return(r=(sv(t)?t.ownerDocument:t.document)||window.document)==null?void 0:r.documentElement}function sv(t){return ts()?t instanceof Node||t instanceof Kt(t).Node:!1}function wn(t){return ts()?t instanceof Element||t instanceof Kt(t).Element:!1}function Dn(t){return ts()?t instanceof HTMLElement||t instanceof Kt(t).HTMLElement:!1}function Vp(t){return!ts()||typeof ShadowRoot>"u"?!1:t instanceof ShadowRoot||t instanceof Kt(t).ShadowRoot}const D1=new Set(["inline","contents"]);function ea(t){const{overflow:r,overflowX:o,overflowY:a,display:s}=xn(t);return/auto|scroll|overlay|hidden|clip/.test(r+a+o)&&!D1.has(s)}const L1=new Set(["table","td","th"]);function A1(t){return L1.has(Ko(t))}const I1=[":popover-open",":modal"];function ns(t){return I1.some(r=>{try{return t.matches(r)}catch{return!1}})}const F1=["transform","translate","scale","rotate","perspective"],j1=["transform","translate","scale","rotate","perspective","filter"],z1=["paint","layout","strict","content"];function ed(t){const r=td(),o=wn(t)?xn(t):t;return F1.some(a=>o[a]?o[a]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!r&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!r&&(o.filter?o.filter!=="none":!1)||j1.some(a=>(o.willChange||"").includes(a))||z1.some(a=>(o.contain||"").includes(a))}function U1(t){let r=_r(t);for(;Dn(r)&&!$o(r);){if(ed(r))return r;if(ns(r))return null;r=_r(r)}return null}function td(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const $1=new Set(["html","body","#document"]);function $o(t){return $1.has(Ko(t))}function xn(t){return Kt(t).getComputedStyle(t)}function rs(t){return wn(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _r(t){if(Ko(t)==="html")return t;const r=t.assignedSlot||t.parentNode||Vp(t)&&t.host||In(t);return Vp(r)?r.host:r}function uv(t){const r=_r(t);return $o(r)?t.ownerDocument?t.ownerDocument.body:t.body:Dn(r)&&ea(r)?r:uv(r)}function Qi(t,r,o){var a;r===void 0&&(r=[]),o===void 0&&(o=!0);const s=uv(t),u=s===((a=t.ownerDocument)==null?void 0:a.body),d=Kt(s);if(u){const h=Oc(d);return r.concat(d,d.visualViewport||[],ea(s)?s:[],h&&o?Qi(h):[])}return r.concat(s,Qi(s,[],o))}function Oc(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function cv(t){const r=xn(t);let o=parseFloat(r.width)||0,a=parseFloat(r.height)||0;const s=Dn(t),u=s?t.offsetWidth:o,d=s?t.offsetHeight:a,h=Ul(o)!==u||Ul(a)!==d;return h&&(o=u,a=d),{width:o,height:a,$:h}}function nd(t){return wn(t)?t:t.contextElement}function zo(t){const r=nd(t);if(!Dn(r))return Nn(1);const o=r.getBoundingClientRect(),{width:a,height:s,$:u}=cv(r);let d=(u?Ul(o.width):o.width)/a,h=(u?Ul(o.height):o.height)/s;return(!d||!Number.isFinite(d))&&(d=1),(!h||!Number.isFinite(h))&&(h=1),{x:d,y:h}}const H1=Nn(0);function dv(t){const r=Kt(t);return!td()||!r.visualViewport?H1:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function B1(t,r,o){return r===void 0&&(r=!1),!o||r&&o!==Kt(t)?!1:r}function eo(t,r,o,a){r===void 0&&(r=!1),o===void 0&&(o=!1);const s=t.getBoundingClientRect(),u=nd(t);let d=Nn(1);r&&(a?wn(a)&&(d=zo(a)):d=zo(t));const h=B1(u,o,a)?dv(u):Nn(0);let p=(s.left+h.x)/d.x,m=(s.top+h.y)/d.y,v=s.width/d.x,w=s.height/d.y;if(u){const x=Kt(u),E=a&&wn(a)?Kt(a):a;let k=x,S=Oc(k);for(;S&&a&&E!==k;){const R=zo(S),P=S.getBoundingClientRect(),j=xn(S),L=P.left+(S.clientLeft+parseFloat(j.paddingLeft))*R.x,U=P.top+(S.clientTop+parseFloat(j.paddingTop))*R.y;p*=R.x,m*=R.y,v*=R.x,w*=R.y,p+=L,m+=U,k=Kt(S),S=Oc(k)}}return Hl({width:v,height:w,x:p,y:m})}function rd(t,r){const o=rs(t).scrollLeft;return r?r.left+o:eo(In(t)).left+o}function fv(t,r,o){o===void 0&&(o=!1);const a=t.getBoundingClientRect(),s=a.left+r.scrollLeft-(o?0:rd(t,a)),u=a.top+r.scrollTop;return{x:s,y:u}}function V1(t){let{elements:r,rect:o,offsetParent:a,strategy:s}=t;const u=s==="fixed",d=In(a),h=r?ns(r.floating):!1;if(a===d||h&&u)return o;let p={scrollLeft:0,scrollTop:0},m=Nn(1);const v=Nn(0),w=Dn(a);if((w||!w&&!u)&&((Ko(a)!=="body"||ea(d))&&(p=rs(a)),Dn(a))){const E=eo(a);m=zo(a),v.x=E.x+a.clientLeft,v.y=E.y+a.clientTop}const x=d&&!w&&!u?fv(d,p,!0):Nn(0);return{width:o.width*m.x,height:o.height*m.y,x:o.x*m.x-p.scrollLeft*m.x+v.x+x.x,y:o.y*m.y-p.scrollTop*m.y+v.y+x.y}}function W1(t){return Array.from(t.getClientRects())}function K1(t){const r=In(t),o=rs(t),a=t.ownerDocument.body,s=Wt(r.scrollWidth,r.clientWidth,a.scrollWidth,a.clientWidth),u=Wt(r.scrollHeight,r.clientHeight,a.scrollHeight,a.clientHeight);let d=-o.scrollLeft+rd(t);const h=-o.scrollTop;return xn(a).direction==="rtl"&&(d+=Wt(r.clientWidth,a.clientWidth)-s),{width:s,height:u,x:d,y:h}}function Q1(t,r){const o=Kt(t),a=In(t),s=o.visualViewport;let u=a.clientWidth,d=a.clientHeight,h=0,p=0;if(s){u=s.width,d=s.height;const m=td();(!m||m&&r==="fixed")&&(h=s.offsetLeft,p=s.offsetTop)}return{width:u,height:d,x:h,y:p}}const G1=new Set(["absolute","fixed"]);function Y1(t,r){const o=eo(t,!0,r==="fixed"),a=o.top+t.clientTop,s=o.left+t.clientLeft,u=Dn(t)?zo(t):Nn(1),d=t.clientWidth*u.x,h=t.clientHeight*u.y,p=s*u.x,m=a*u.y;return{width:d,height:h,x:p,y:m}}function Wp(t,r,o){let a;if(r==="viewport")a=Q1(t,o);else if(r==="document")a=K1(In(t));else if(wn(r))a=Y1(r,o);else{const s=dv(t);a={x:r.x-s.x,y:r.y-s.y,width:r.width,height:r.height}}return Hl(a)}function hv(t,r){const o=_r(t);return o===r||!wn(o)||$o(o)?!1:xn(o).position==="fixed"||hv(o,r)}function X1(t,r){const o=r.get(t);if(o)return o;let a=Qi(t,[],!1).filter(h=>wn(h)&&Ko(h)!=="body"),s=null;const u=xn(t).position==="fixed";let d=u?_r(t):t;for(;wn(d)&&!$o(d);){const h=xn(d),p=ed(d);!p&&h.position==="fixed"&&(s=null),(u?!p&&!s:!p&&h.position==="static"&&!!s&&G1.has(s.position)||ea(d)&&!p&&hv(t,d))?a=a.filter(v=>v!==d):s=h,d=_r(d)}return r.set(t,a),a}function q1(t){let{element:r,boundary:o,rootBoundary:a,strategy:s}=t;const d=[...o==="clippingAncestors"?ns(r)?[]:X1(r,this._c):[].concat(o),a],h=d[0],p=d.reduce((m,v)=>{const w=Wp(r,v,s);return m.top=Wt(w.top,m.top),m.right=Mr(w.right,m.right),m.bottom=Mr(w.bottom,m.bottom),m.left=Wt(w.left,m.left),m},Wp(r,h,s));return{width:p.right-p.left,height:p.bottom-p.top,x:p.left,y:p.top}}function J1(t){const{width:r,height:o}=cv(t);return{width:r,height:o}}function Z1(t,r,o){const a=Dn(r),s=In(r),u=o==="fixed",d=eo(t,!0,u,r);let h={scrollLeft:0,scrollTop:0};const p=Nn(0);function m(){p.x=rd(s)}if(a||!a&&!u)if((Ko(r)!=="body"||ea(s))&&(h=rs(r)),a){const E=eo(r,!0,u,r);p.x=E.x+r.clientLeft,p.y=E.y+r.clientTop}else s&&m();u&&!a&&s&&m();const v=s&&!a&&!u?fv(s,h):Nn(0),w=d.left+h.scrollLeft-p.x-v.x,x=d.top+h.scrollTop-p.y-v.y;return{x:w,y:x,width:d.width,height:d.height}}function ic(t){return xn(t).position==="static"}function Kp(t,r){if(!Dn(t)||xn(t).position==="fixed")return null;if(r)return r(t);let o=t.offsetParent;return In(t)===o&&(o=o.ownerDocument.body),o}function pv(t,r){const o=Kt(t);if(ns(t))return o;if(!Dn(t)){let s=_r(t);for(;s&&!$o(s);){if(wn(s)&&!ic(s))return s;s=_r(s)}return o}let a=Kp(t,r);for(;a&&A1(a)&&ic(a);)a=Kp(a,r);return a&&$o(a)&&ic(a)&&!ed(a)?o:a||U1(t)||o}const eC=async function(t){const r=this.getOffsetParent||pv,o=this.getDimensions,a=await o(t.floating);return{reference:Z1(t.reference,await r(t.floating),t.strategy),floating:{x:0,y:0,width:a.width,height:a.height}}};function tC(t){return xn(t).direction==="rtl"}const nC={convertOffsetParentRelativeRectToViewportRelativeRect:V1,getDocumentElement:In,getClippingRect:q1,getOffsetParent:pv,getElementRects:eC,getClientRects:W1,getDimensions:J1,getScale:zo,isElement:wn,isRTL:tC};function mv(t,r){return t.x===r.x&&t.y===r.y&&t.width===r.width&&t.height===r.height}function rC(t,r){let o=null,a;const s=In(t);function u(){var h;clearTimeout(a),(h=o)==null||h.disconnect(),o=null}function d(h,p){h===void 0&&(h=!1),p===void 0&&(p=1),u();const m=t.getBoundingClientRect(),{left:v,top:w,width:x,height:E}=m;if(h||r(),!x||!E)return;const k=xl(w),S=xl(s.clientWidth-(v+x)),R=xl(s.clientHeight-(w+E)),P=xl(v),L={rootMargin:-k+"px "+-S+"px "+-R+"px "+-P+"px",threshold:Wt(0,Mr(1,p))||1};let U=!0;function H(_){const K=_[0].intersectionRatio;if(K!==p){if(!U)return d();K?d(!1,K):a=setTimeout(()=>{d(!1,1e-7)},1e3)}K===1&&!mv(m,t.getBoundingClientRect())&&d(),U=!1}try{o=new IntersectionObserver(H,{...L,root:s.ownerDocument})}catch{o=new IntersectionObserver(H,L)}o.observe(t)}return d(!0),u}function oC(t,r,o,a){a===void 0&&(a={});const{ancestorScroll:s=!0,ancestorResize:u=!0,elementResize:d=typeof ResizeObserver=="function",layoutShift:h=typeof IntersectionObserver=="function",animationFrame:p=!1}=a,m=nd(t),v=s||u?[...m?Qi(m):[],...Qi(r)]:[];v.forEach(P=>{s&&P.addEventListener("scroll",o,{passive:!0}),u&&P.addEventListener("resize",o)});const w=m&&h?rC(m,o):null;let x=-1,E=null;d&&(E=new ResizeObserver(P=>{let[j]=P;j&&j.target===m&&E&&(E.unobserve(r),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var L;(L=E)==null||L.observe(r)})),o()}),m&&!p&&E.observe(m),E.observe(r));let k,S=p?eo(t):null;p&&R();function R(){const P=eo(t);S&&!mv(S,P)&&o(),S=P,k=requestAnimationFrame(R)}return o(),()=>{var P;v.forEach(j=>{s&&j.removeEventListener("scroll",o),u&&j.removeEventListener("resize",o)}),w?.(),(P=E)==null||P.disconnect(),E=null,p&&cancelAnimationFrame(k)}}const iC=_1,aC=T1,lC=P1,sC=N1,uC=b1,Qp=R1,cC=O1,dC=(t,r,o)=>{const a=new Map,s={platform:nC,...o},u={...s.platform,_c:a};return k1(t,r,{...s,platform:u})};var fC=typeof document<"u",hC=function(){},Ol=fC?g.useLayoutEffect:hC;function Bl(t,r){if(t===r)return!0;if(typeof t!=typeof r)return!1;if(typeof t=="function"&&t.toString()===r.toString())return!0;let o,a,s;if(t&&r&&typeof t=="object"){if(Array.isArray(t)){if(o=t.length,o!==r.length)return!1;for(a=o;a--!==0;)if(!Bl(t[a],r[a]))return!1;return!0}if(s=Object.keys(t),o=s.length,o!==Object.keys(r).length)return!1;for(a=o;a--!==0;)if(!{}.hasOwnProperty.call(r,s[a]))return!1;for(a=o;a--!==0;){const u=s[a];if(!(u==="_owner"&&t.$$typeof)&&!Bl(t[u],r[u]))return!1}return!0}return t!==t&&r!==r}function vv(t){return typeof window>"u"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function Gp(t,r){const o=vv(t);return Math.round(r*o)/o}function ac(t){const r=g.useRef(t);return Ol(()=>{r.current=t}),r}function pC(t){t===void 0&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:s,elements:{reference:u,floating:d}={},transform:h=!0,whileElementsMounted:p,open:m}=t,[v,w]=g.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[x,E]=g.useState(a);Bl(x,a)||E(a);const[k,S]=g.useState(null),[R,P]=g.useState(null),j=g.useCallback(N=>{N!==_.current&&(_.current=N,S(N))},[]),L=g.useCallback(N=>{N!==K.current&&(K.current=N,P(N))},[]),U=u||k,H=d||R,_=g.useRef(null),K=g.useRef(null),V=g.useRef(v),te=p!=null,ve=ac(p),Te=ac(s),_e=ac(m),oe=g.useCallback(()=>{if(!_.current||!K.current)return;const N={placement:r,strategy:o,middleware:x};Te.current&&(N.platform=Te.current),dC(_.current,K.current,N).then(W=>{const G={...W,isPositioned:_e.current!==!1};ae.current&&!Bl(V.current,G)&&(V.current=G,Zl.flushSync(()=>{w(G)}))})},[x,r,o,Te,_e]);Ol(()=>{m===!1&&V.current.isPositioned&&(V.current.isPositioned=!1,w(N=>({...N,isPositioned:!1})))},[m]);const ae=g.useRef(!1);Ol(()=>(ae.current=!0,()=>{ae.current=!1}),[]),Ol(()=>{if(U&&(_.current=U),H&&(K.current=H),U&&H){if(ve.current)return ve.current(U,H,oe);oe()}},[U,H,oe,ve,te]);const Pe=g.useMemo(()=>({reference:_,floating:K,setReference:j,setFloating:L}),[j,L]),Z=g.useMemo(()=>({reference:U,floating:H}),[U,H]),se=g.useMemo(()=>{const N={position:o,left:0,top:0};if(!Z.floating)return N;const W=Gp(Z.floating,v.x),G=Gp(Z.floating,v.y);return h?{...N,transform:"translate("+W+"px, "+G+"px)",...vv(Z.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:W,top:G}},[o,h,Z.floating,v.x,v.y]);return g.useMemo(()=>({...v,update:oe,refs:Pe,elements:Z,floatingStyles:se}),[v,oe,Pe,Z,se])}const mC=t=>{function r(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:t,fn(o){const{element:a,padding:s}=typeof t=="function"?t(o):t;return a&&r(a)?a.current!=null?Qp({element:a.current,padding:s}).fn(o):{}:a?Qp({element:a,padding:s}).fn(o):{}}}},vC=(t,r)=>({...iC(t),options:[t,r]}),gC=(t,r)=>({...aC(t),options:[t,r]}),yC=(t,r)=>({...cC(t),options:[t,r]}),wC=(t,r)=>({...lC(t),options:[t,r]}),xC=(t,r)=>({...sC(t),options:[t,r]}),SC=(t,r)=>({...uC(t),options:[t,r]}),EC=(t,r)=>({...mC(t),options:[t,r]});var CC="Arrow",gv=g.forwardRef((t,r)=>{const{children:o,width:a=10,height:s=5,...u}=t;return D.jsx(Pt.svg,{...u,ref:r,width:a,height:s,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:t.asChild?o:D.jsx("polygon",{points:"0,0 30,0 15,10"})})});gv.displayName=CC;var kC=gv;function RC(t){const[r,o]=g.useState(void 0);return br(()=>{if(t){o({width:t.offsetWidth,height:t.offsetHeight});const a=new ResizeObserver(s=>{if(!Array.isArray(s)||!s.length)return;const u=s[0];let d,h;if("borderBoxSize"in u){const p=u.borderBoxSize,m=Array.isArray(p)?p[0]:p;d=m.inlineSize,h=m.blockSize}else d=t.offsetWidth,h=t.offsetHeight;o({width:d,height:h})});return a.observe(t,{box:"border-box"}),()=>a.unobserve(t)}else o(void 0)},[t]),r}var od="Popper",[yv,wv]=Zi(od),[PC,xv]=yv(od),Sv=t=>{const{__scopePopper:r,children:o}=t,[a,s]=g.useState(null);return D.jsx(PC,{scope:r,anchor:a,onAnchorChange:s,children:o})};Sv.displayName=od;var Ev="PopperAnchor",Cv=g.forwardRef((t,r)=>{const{__scopePopper:o,virtualRef:a,...s}=t,u=xv(Ev,o),d=g.useRef(null),h=jt(r,d);return g.useEffect(()=>{u.onAnchorChange(a?.current||d.current)}),a?null:D.jsx(Pt.div,{...s,ref:h})});Cv.displayName=Ev;var id="PopperContent",[bC,MC]=yv(id),kv=g.forwardRef((t,r)=>{const{__scopePopper:o,side:a="bottom",sideOffset:s=0,align:u="center",alignOffset:d=0,arrowPadding:h=0,avoidCollisions:p=!0,collisionBoundary:m=[],collisionPadding:v=0,sticky:w="partial",hideWhenDetached:x=!1,updatePositionStrategy:E="optimized",onPlaced:k,...S}=t,R=xv(id,o),[P,j]=g.useState(null),L=jt(r,ie=>j(ie)),[U,H]=g.useState(null),_=RC(U),K=_?.width??0,V=_?.height??0,te=a+(u!=="center"?"-"+u:""),ve=typeof v=="number"?v:{top:0,right:0,bottom:0,left:0,...v},Te=Array.isArray(m)?m:[m],_e=Te.length>0,oe={padding:ve,boundary:Te.filter(TC),altBoundary:_e},{refs:ae,floatingStyles:Pe,placement:Z,isPositioned:se,middlewareData:N}=pC({strategy:"fixed",placement:te,whileElementsMounted:(...ie)=>oC(...ie,{animationFrame:E==="always"}),elements:{reference:R.anchor},middleware:[vC({mainAxis:s+V,alignmentAxis:d}),p&&gC({mainAxis:!0,crossAxis:!1,limiter:w==="partial"?yC():void 0,...oe}),p&&wC({...oe}),xC({...oe,apply:({elements:ie,rects:Oe,availableWidth:je,availableHeight:Ae})=>{const{width:pt,height:Qt}=Oe.reference,ln=ie.floating.style;ln.setProperty("--radix-popper-available-width",`${je}px`),ln.setProperty("--radix-popper-available-height",`${Ae}px`),ln.setProperty("--radix-popper-anchor-width",`${pt}px`),ln.setProperty("--radix-popper-anchor-height",`${Qt}px`)}}),U&&EC({element:U,padding:h}),OC({arrowWidth:K,arrowHeight:V}),x&&SC({strategy:"referenceHidden",...oe})]}),[W,G]=bv(Z),T=Gn(k);br(()=>{se&&T?.()},[se,T]);const B=N.arrow?.x,ne=N.arrow?.y,ue=N.arrow?.centerOffset!==0,[be,Re]=g.useState();return br(()=>{P&&Re(window.getComputedStyle(P).zIndex)},[P]),D.jsx("div",{ref:ae.setFloating,"data-radix-popper-content-wrapper":"",style:{...Pe,transform:se?Pe.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:be,"--radix-popper-transform-origin":[N.transformOrigin?.x,N.transformOrigin?.y].join(" "),...N.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:D.jsx(bC,{scope:o,placedSide:W,onArrowChange:H,arrowX:B,arrowY:ne,shouldHideArrow:ue,children:D.jsx(Pt.div,{"data-side":W,"data-align":G,...S,ref:L,style:{...S.style,animation:se?void 0:"none"}})})})});kv.displayName=id;var Rv="PopperArrow",_C={top:"bottom",right:"left",bottom:"top",left:"right"},Pv=g.forwardRef(function(r,o){const{__scopePopper:a,...s}=r,u=MC(Rv,a),d=_C[u.placedSide];return D.jsx("span",{ref:u.onArrowChange,style:{position:"absolute",left:u.arrowX,top:u.arrowY,[d]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[u.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[u.placedSide],visibility:u.shouldHideArrow?"hidden":void 0},children:D.jsx(kC,{...s,ref:o,style:{...s.style,display:"block"}})})});Pv.displayName=Rv;function TC(t){return t!==null}var OC=t=>({name:"transformOrigin",options:t,fn(r){const{placement:o,rects:a,middlewareData:s}=r,d=s.arrow?.centerOffset!==0,h=d?0:t.arrowWidth,p=d?0:t.arrowHeight,[m,v]=bv(o),w={start:"0%",center:"50%",end:"100%"}[v],x=(s.arrow?.x??0)+h/2,E=(s.arrow?.y??0)+p/2;let k="",S="";return m==="bottom"?(k=d?w:`${x}px`,S=`${-p}px`):m==="top"?(k=d?w:`${x}px`,S=`${a.floating.height+p}px`):m==="right"?(k=`${-p}px`,S=d?w:`${E}px`):m==="left"&&(k=`${a.floating.width+p}px`,S=d?w:`${E}px`),{data:{x:k,y:S}}}});function bv(t){const[r,o="center"]=t.split("-");return[r,o]}var NC=Sv,DC=Cv,LC=kv,AC=Pv,IC="Portal",Mv=g.forwardRef((t,r)=>{const{container:o,...a}=t,[s,u]=g.useState(!1);br(()=>u(!0),[]);const d=o||s&&globalThis?.document?.body;return d?gS.createPortal(D.jsx(Pt.div,{...a,ref:r}),d):null});Mv.displayName=IC;function FC(t,r){return g.useReducer((o,a)=>r[o][a]??o,t)}var ta=t=>{const{present:r,children:o}=t,a=jC(r),s=typeof o=="function"?o({present:a.isPresent}):g.Children.only(o),u=jt(a.ref,zC(s));return typeof o=="function"||a.isPresent?g.cloneElement(s,{ref:u}):null};ta.displayName="Presence";function jC(t){const[r,o]=g.useState(),a=g.useRef(null),s=g.useRef(t),u=g.useRef("none"),d=t?"mounted":"unmounted",[h,p]=FC(d,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return g.useEffect(()=>{const m=Sl(a.current);u.current=h==="mounted"?m:"none"},[h]),br(()=>{const m=a.current,v=s.current;if(v!==t){const x=u.current,E=Sl(m);t?p("MOUNT"):E==="none"||m?.display==="none"?p("UNMOUNT"):p(v&&x!==E?"ANIMATION_OUT":"UNMOUNT"),s.current=t}},[t,p]),br(()=>{if(r){let m;const v=r.ownerDocument.defaultView??window,w=E=>{const S=Sl(a.current).includes(E.animationName);if(E.target===r&&S&&(p("ANIMATION_END"),!s.current)){const R=r.style.animationFillMode;r.style.animationFillMode="forwards",m=v.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=R)})}},x=E=>{E.target===r&&(u.current=Sl(a.current))};return r.addEventListener("animationstart",x),r.addEventListener("animationcancel",w),r.addEventListener("animationend",w),()=>{v.clearTimeout(m),r.removeEventListener("animationstart",x),r.removeEventListener("animationcancel",w),r.removeEventListener("animationend",w)}}else p("ANIMATION_END")},[r,p]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:g.useCallback(m=>{a.current=m?getComputedStyle(m):null,o(m)},[])}}function Sl(t){return t?.animationName||"none"}function zC(t){let r=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?t.ref:(r=Object.getOwnPropertyDescriptor(t,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?t.props.ref:t.props.ref||t.ref)}var lc="rovingFocusGroup.onEntryFocus",UC={bubbles:!1,cancelable:!0},na="RovingFocusGroup",[Nc,_v,$C]=Zm(na),[HC,Tv]=Zi(na,[$C]),[BC,VC]=HC(na),Ov=g.forwardRef((t,r)=>D.jsx(Nc.Provider,{scope:t.__scopeRovingFocusGroup,children:D.jsx(Nc.Slot,{scope:t.__scopeRovingFocusGroup,children:D.jsx(WC,{...t,ref:r})})}));Ov.displayName=na;var WC=g.forwardRef((t,r)=>{const{__scopeRovingFocusGroup:o,orientation:a,loop:s=!1,dir:u,currentTabStopId:d,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:p,onEntryFocus:m,preventScrollOnEntryFocus:v=!1,...w}=t,x=g.useRef(null),E=jt(r,x),k=ev(u),[S,R]=qm({prop:d,defaultProp:h??null,onChange:p,caller:na}),[P,j]=g.useState(!1),L=Gn(m),U=_v(o),H=g.useRef(!1),[_,K]=g.useState(0);return g.useEffect(()=>{const V=x.current;if(V)return V.addEventListener(lc,L),()=>V.removeEventListener(lc,L)},[L]),D.jsx(BC,{scope:o,orientation:a,dir:k,loop:s,currentTabStopId:S,onItemFocus:g.useCallback(V=>R(V),[R]),onItemShiftTab:g.useCallback(()=>j(!0),[]),onFocusableItemAdd:g.useCallback(()=>K(V=>V+1),[]),onFocusableItemRemove:g.useCallback(()=>K(V=>V-1),[]),children:D.jsx(Pt.div,{tabIndex:P||_===0?-1:0,"data-orientation":a,...w,ref:E,style:{outline:"none",...t.style},onMouseDown:Fe(t.onMouseDown,()=>{H.current=!0}),onFocus:Fe(t.onFocus,V=>{const te=!H.current;if(V.target===V.currentTarget&&te&&!P){const ve=new CustomEvent(lc,UC);if(V.currentTarget.dispatchEvent(ve),!ve.defaultPrevented){const Te=U().filter(Z=>Z.focusable),_e=Te.find(Z=>Z.active),oe=Te.find(Z=>Z.id===S),Pe=[_e,oe,...Te].filter(Boolean).map(Z=>Z.ref.current);Lv(Pe,v)}}H.current=!1}),onBlur:Fe(t.onBlur,()=>j(!1))})})}),Nv="RovingFocusGroupItem",Dv=g.forwardRef((t,r)=>{const{__scopeRovingFocusGroup:o,focusable:a=!0,active:s=!1,tabStopId:u,children:d,...h}=t,p=Mc(),m=u||p,v=VC(Nv,o),w=v.currentTabStopId===m,x=_v(o),{onFocusableItemAdd:E,onFocusableItemRemove:k,currentTabStopId:S}=v;return g.useEffect(()=>{if(a)return E(),()=>k()},[a,E,k]),D.jsx(Nc.ItemSlot,{scope:o,id:m,focusable:a,active:s,children:D.jsx(Pt.span,{tabIndex:w?0:-1,"data-orientation":v.orientation,...h,ref:r,onMouseDown:Fe(t.onMouseDown,R=>{a?v.onItemFocus(m):R.preventDefault()}),onFocus:Fe(t.onFocus,()=>v.onItemFocus(m)),onKeyDown:Fe(t.onKeyDown,R=>{if(R.key==="Tab"&&R.shiftKey){v.onItemShiftTab();return}if(R.target!==R.currentTarget)return;const P=GC(R,v.orientation,v.dir);if(P!==void 0){if(R.metaKey||R.ctrlKey||R.altKey||R.shiftKey)return;R.preventDefault();let L=x().filter(U=>U.focusable).map(U=>U.ref.current);if(P==="last")L.reverse();else if(P==="prev"||P==="next"){P==="prev"&&L.reverse();const U=L.indexOf(R.currentTarget);L=v.loop?YC(L,U+1):L.slice(U+1)}setTimeout(()=>Lv(L))}}),children:typeof d=="function"?d({isCurrentTabStop:w,hasTabStop:S!=null}):d})})});Dv.displayName=Nv;var KC={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function QC(t,r){return r!=="rtl"?t:t==="ArrowLeft"?"ArrowRight":t==="ArrowRight"?"ArrowLeft":t}function GC(t,r,o){const a=QC(t.key,o);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(a))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(a)))return KC[a]}function Lv(t,r=!1){const o=document.activeElement;for(const a of t)if(a===o||(a.focus({preventScroll:r}),document.activeElement!==o))return}function YC(t,r){return t.map((o,a)=>t[(r+a)%t.length])}var XC=Ov,qC=Dv,JC=function(t){if(typeof document>"u")return null;var r=Array.isArray(t)?t[0]:t;return r.ownerDocument.body},Lo=new WeakMap,El=new WeakMap,Cl={},sc=0,Av=function(t){return t&&(t.host||Av(t.parentNode))},ZC=function(t,r){return r.map(function(o){if(t.contains(o))return o;var a=Av(o);return a&&t.contains(a)?a:(console.error("aria-hidden",o,"in not contained inside",t,". Doing nothing"),null)}).filter(function(o){return!!o})},ek=function(t,r,o,a){var s=ZC(r,Array.isArray(t)?t:[t]);Cl[o]||(Cl[o]=new WeakMap);var u=Cl[o],d=[],h=new Set,p=new Set(s),m=function(w){!w||h.has(w)||(h.add(w),m(w.parentNode))};s.forEach(m);var v=function(w){!w||p.has(w)||Array.prototype.forEach.call(w.children,function(x){if(h.has(x))v(x);else try{var E=x.getAttribute(a),k=E!==null&&E!=="false",S=(Lo.get(x)||0)+1,R=(u.get(x)||0)+1;Lo.set(x,S),u.set(x,R),d.push(x),S===1&&k&&El.set(x,!0),R===1&&x.setAttribute(o,"true"),k||x.setAttribute(a,"true")}catch(P){console.error("aria-hidden: cannot operate on ",x,P)}})};return v(r),h.clear(),sc++,function(){d.forEach(function(w){var x=Lo.get(w)-1,E=u.get(w)-1;Lo.set(w,x),u.set(w,E),x||(El.has(w)||w.removeAttribute(a),El.delete(w)),E||w.removeAttribute(o)}),sc--,sc||(Lo=new WeakMap,Lo=new WeakMap,El=new WeakMap,Cl={})}},tk=function(t,r,o){o===void 0&&(o="data-aria-hidden");var a=Array.from(Array.isArray(t)?t:[t]),s=JC(t);return s?(a.push.apply(a,Array.from(s.querySelectorAll("[aria-live], script"))),ek(a,s,o,"aria-hidden")):function(){return null}},_n=function(){return _n=Object.assign||function(r){for(var o,a=1,s=arguments.length;a<s;a++){o=arguments[a];for(var u in o)Object.prototype.hasOwnProperty.call(o,u)&&(r[u]=o[u])}return r},_n.apply(this,arguments)};function Iv(t,r){var o={};for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&r.indexOf(a)<0&&(o[a]=t[a]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,a=Object.getOwnPropertySymbols(t);s<a.length;s++)r.indexOf(a[s])<0&&Object.prototype.propertyIsEnumerable.call(t,a[s])&&(o[a[s]]=t[a[s]]);return o}function nk(t,r,o){if(o||arguments.length===2)for(var a=0,s=r.length,u;a<s;a++)(u||!(a in r))&&(u||(u=Array.prototype.slice.call(r,0,a)),u[a]=r[a]);return t.concat(u||Array.prototype.slice.call(r))}var Nl="right-scroll-bar-position",Dl="width-before-scroll-bar",rk="with-scroll-bars-hidden",ok="--removed-body-scroll-bar-size";function uc(t,r){return typeof t=="function"?t(r):t&&(t.current=r),t}function ik(t,r){var o=g.useState(function(){return{value:t,callback:r,facade:{get current(){return o.value},set current(a){var s=o.value;s!==a&&(o.value=a,o.callback(a,s))}}}})[0];return o.callback=r,o.facade}var ak=typeof window<"u"?g.useLayoutEffect:g.useEffect,Yp=new WeakMap;function lk(t,r){var o=ik(null,function(a){return t.forEach(function(s){return uc(s,a)})});return ak(function(){var a=Yp.get(o);if(a){var s=new Set(a),u=new Set(t),d=o.current;s.forEach(function(h){u.has(h)||uc(h,null)}),u.forEach(function(h){s.has(h)||uc(h,d)})}Yp.set(o,t)},[t]),o}function sk(t){return t}function uk(t,r){r===void 0&&(r=sk);var o=[],a=!1,s={read:function(){if(a)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:t},useMedium:function(u){var d=r(u,a);return o.push(d),function(){o=o.filter(function(h){return h!==d})}},assignSyncMedium:function(u){for(a=!0;o.length;){var d=o;o=[],d.forEach(u)}o={push:function(h){return u(h)},filter:function(){return o}}},assignMedium:function(u){a=!0;var d=[];if(o.length){var h=o;o=[],h.forEach(u),d=o}var p=function(){var v=d;d=[],v.forEach(u)},m=function(){return Promise.resolve().then(p)};m(),o={push:function(v){d.push(v),m()},filter:function(v){return d=d.filter(v),o}}}};return s}function ck(t){t===void 0&&(t={});var r=uk(null);return r.options=_n({async:!0,ssr:!1},t),r}var Fv=function(t){var r=t.sideCar,o=Iv(t,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var a=r.read();if(!a)throw new Error("Sidecar medium not found");return g.createElement(a,_n({},o))};Fv.isSideCarExport=!0;function dk(t,r){return t.useMedium(r),Fv}var jv=ck(),cc=function(){},os=g.forwardRef(function(t,r){var o=g.useRef(null),a=g.useState({onScrollCapture:cc,onWheelCapture:cc,onTouchMoveCapture:cc}),s=a[0],u=a[1],d=t.forwardProps,h=t.children,p=t.className,m=t.removeScrollBar,v=t.enabled,w=t.shards,x=t.sideCar,E=t.noRelative,k=t.noIsolation,S=t.inert,R=t.allowPinchZoom,P=t.as,j=P===void 0?"div":P,L=t.gapMode,U=Iv(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),H=x,_=lk([o,r]),K=_n(_n({},U),s);return g.createElement(g.Fragment,null,v&&g.createElement(H,{sideCar:jv,removeScrollBar:m,shards:w,noRelative:E,noIsolation:k,inert:S,setCallbacks:u,allowPinchZoom:!!R,lockRef:o,gapMode:L}),d?g.cloneElement(g.Children.only(h),_n(_n({},K),{ref:_})):g.createElement(j,_n({},K,{className:p,ref:_}),h))});os.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};os.classNames={fullWidth:Dl,zeroRight:Nl};var fk=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function hk(){if(!document)return null;var t=document.createElement("style");t.type="text/css";var r=fk();return r&&t.setAttribute("nonce",r),t}function pk(t,r){t.styleSheet?t.styleSheet.cssText=r:t.appendChild(document.createTextNode(r))}function mk(t){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(t)}var vk=function(){var t=0,r=null;return{add:function(o){t==0&&(r=hk())&&(pk(r,o),mk(r)),t++},remove:function(){t--,!t&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},gk=function(){var t=vk();return function(r,o){g.useEffect(function(){return t.add(r),function(){t.remove()}},[r&&o])}},zv=function(){var t=gk(),r=function(o){var a=o.styles,s=o.dynamic;return t(a,s),null};return r},yk={left:0,top:0,right:0,gap:0},dc=function(t){return parseInt(t||"",10)||0},wk=function(t){var r=window.getComputedStyle(document.body),o=r[t==="padding"?"paddingLeft":"marginLeft"],a=r[t==="padding"?"paddingTop":"marginTop"],s=r[t==="padding"?"paddingRight":"marginRight"];return[dc(o),dc(a),dc(s)]},xk=function(t){if(t===void 0&&(t="margin"),typeof window>"u")return yk;var r=wk(t),o=document.documentElement.clientWidth,a=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,a-o+r[2]-r[0])}},Sk=zv(),Uo="data-scroll-locked",Ek=function(t,r,o,a){var s=t.left,u=t.top,d=t.right,h=t.gap;return o===void 0&&(o="margin"),`
  .`.concat(rk,` {
   overflow: hidden `).concat(a,`;
   padding-right: `).concat(h,"px ").concat(a,`;
  }
  body[`).concat(Uo,`] {
    overflow: hidden `).concat(a,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(a,";"),o==="margin"&&`
    padding-left: `.concat(s,`px;
    padding-top: `).concat(u,`px;
    padding-right: `).concat(d,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(h,"px ").concat(a,`;
    `),o==="padding"&&"padding-right: ".concat(h,"px ").concat(a,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Nl,` {
    right: `).concat(h,"px ").concat(a,`;
  }
  
  .`).concat(Dl,` {
    margin-right: `).concat(h,"px ").concat(a,`;
  }
  
  .`).concat(Nl," .").concat(Nl,` {
    right: 0 `).concat(a,`;
  }
  
  .`).concat(Dl," .").concat(Dl,` {
    margin-right: 0 `).concat(a,`;
  }
  
  body[`).concat(Uo,`] {
    `).concat(ok,": ").concat(h,`px;
  }
`)},Xp=function(){var t=parseInt(document.body.getAttribute(Uo)||"0",10);return isFinite(t)?t:0},Ck=function(){g.useEffect(function(){return document.body.setAttribute(Uo,(Xp()+1).toString()),function(){var t=Xp()-1;t<=0?document.body.removeAttribute(Uo):document.body.setAttribute(Uo,t.toString())}},[])},kk=function(t){var r=t.noRelative,o=t.noImportant,a=t.gapMode,s=a===void 0?"margin":a;Ck();var u=g.useMemo(function(){return xk(s)},[s]);return g.createElement(Sk,{styles:Ek(u,!r,s,o?"":"!important")})},Dc=!1;if(typeof window<"u")try{var kl=Object.defineProperty({},"passive",{get:function(){return Dc=!0,!0}});window.addEventListener("test",kl,kl),window.removeEventListener("test",kl,kl)}catch{Dc=!1}var Ao=Dc?{passive:!1}:!1,Rk=function(t){return t.tagName==="TEXTAREA"},Uv=function(t,r){if(!(t instanceof Element))return!1;var o=window.getComputedStyle(t);return o[r]!=="hidden"&&!(o.overflowY===o.overflowX&&!Rk(t)&&o[r]==="visible")},Pk=function(t){return Uv(t,"overflowY")},bk=function(t){return Uv(t,"overflowX")},qp=function(t,r){var o=r.ownerDocument,a=r;do{typeof ShadowRoot<"u"&&a instanceof ShadowRoot&&(a=a.host);var s=$v(t,a);if(s){var u=Hv(t,a),d=u[1],h=u[2];if(d>h)return!0}a=a.parentNode}while(a&&a!==o.body);return!1},Mk=function(t){var r=t.scrollTop,o=t.scrollHeight,a=t.clientHeight;return[r,o,a]},_k=function(t){var r=t.scrollLeft,o=t.scrollWidth,a=t.clientWidth;return[r,o,a]},$v=function(t,r){return t==="v"?Pk(r):bk(r)},Hv=function(t,r){return t==="v"?Mk(r):_k(r)},Tk=function(t,r){return t==="h"&&r==="rtl"?-1:1},Ok=function(t,r,o,a,s){var u=Tk(t,window.getComputedStyle(r).direction),d=u*a,h=o.target,p=r.contains(h),m=!1,v=d>0,w=0,x=0;do{if(!h)break;var E=Hv(t,h),k=E[0],S=E[1],R=E[2],P=S-R-u*k;(k||P)&&$v(t,h)&&(w+=P,x+=k);var j=h.parentNode;h=j&&j.nodeType===Node.DOCUMENT_FRAGMENT_NODE?j.host:j}while(!p&&h!==document.body||p&&(r.contains(h)||r===h));return(v&&Math.abs(w)<1||!v&&Math.abs(x)<1)&&(m=!0),m},Rl=function(t){return"changedTouches"in t?[t.changedTouches[0].clientX,t.changedTouches[0].clientY]:[0,0]},Jp=function(t){return[t.deltaX,t.deltaY]},Zp=function(t){return t&&"current"in t?t.current:t},Nk=function(t,r){return t[0]===r[0]&&t[1]===r[1]},Dk=function(t){return`
  .block-interactivity-`.concat(t,` {pointer-events: none;}
  .allow-interactivity-`).concat(t,` {pointer-events: all;}
`)},Lk=0,Io=[];function Ak(t){var r=g.useRef([]),o=g.useRef([0,0]),a=g.useRef(),s=g.useState(Lk++)[0],u=g.useState(zv)[0],d=g.useRef(t);g.useEffect(function(){d.current=t},[t]),g.useEffect(function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(s));var S=nk([t.lockRef.current],(t.shards||[]).map(Zp),!0).filter(Boolean);return S.forEach(function(R){return R.classList.add("allow-interactivity-".concat(s))}),function(){document.body.classList.remove("block-interactivity-".concat(s)),S.forEach(function(R){return R.classList.remove("allow-interactivity-".concat(s))})}}},[t.inert,t.lockRef.current,t.shards]);var h=g.useCallback(function(S,R){if("touches"in S&&S.touches.length===2||S.type==="wheel"&&S.ctrlKey)return!d.current.allowPinchZoom;var P=Rl(S),j=o.current,L="deltaX"in S?S.deltaX:j[0]-P[0],U="deltaY"in S?S.deltaY:j[1]-P[1],H,_=S.target,K=Math.abs(L)>Math.abs(U)?"h":"v";if("touches"in S&&K==="h"&&_.type==="range")return!1;var V=qp(K,_);if(!V)return!0;if(V?H=K:(H=K==="v"?"h":"v",V=qp(K,_)),!V)return!1;if(!a.current&&"changedTouches"in S&&(L||U)&&(a.current=H),!H)return!0;var te=a.current||H;return Ok(te,R,S,te==="h"?L:U)},[]),p=g.useCallback(function(S){var R=S;if(!(!Io.length||Io[Io.length-1]!==u)){var P="deltaY"in R?Jp(R):Rl(R),j=r.current.filter(function(H){return H.name===R.type&&(H.target===R.target||R.target===H.shadowParent)&&Nk(H.delta,P)})[0];if(j&&j.should){R.cancelable&&R.preventDefault();return}if(!j){var L=(d.current.shards||[]).map(Zp).filter(Boolean).filter(function(H){return H.contains(R.target)}),U=L.length>0?h(R,L[0]):!d.current.noIsolation;U&&R.cancelable&&R.preventDefault()}}},[]),m=g.useCallback(function(S,R,P,j){var L={name:S,delta:R,target:P,should:j,shadowParent:Ik(P)};r.current.push(L),setTimeout(function(){r.current=r.current.filter(function(U){return U!==L})},1)},[]),v=g.useCallback(function(S){o.current=Rl(S),a.current=void 0},[]),w=g.useCallback(function(S){m(S.type,Jp(S),S.target,h(S,t.lockRef.current))},[]),x=g.useCallback(function(S){m(S.type,Rl(S),S.target,h(S,t.lockRef.current))},[]);g.useEffect(function(){return Io.push(u),t.setCallbacks({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:x}),document.addEventListener("wheel",p,Ao),document.addEventListener("touchmove",p,Ao),document.addEventListener("touchstart",v,Ao),function(){Io=Io.filter(function(S){return S!==u}),document.removeEventListener("wheel",p,Ao),document.removeEventListener("touchmove",p,Ao),document.removeEventListener("touchstart",v,Ao)}},[]);var E=t.removeScrollBar,k=t.inert;return g.createElement(g.Fragment,null,k?g.createElement(u,{styles:Dk(s)}):null,E?g.createElement(kk,{noRelative:t.noRelative,gapMode:t.gapMode}):null)}function Ik(t){for(var r=null;t!==null;)t instanceof ShadowRoot&&(r=t.host,t=t.host),t=t.parentNode;return r}const Fk=dk(jv,Ak);var Bv=g.forwardRef(function(t,r){return g.createElement(os,_n({},t,{ref:r,sideCar:Fk}))});Bv.classNames=os.classNames;var Lc=["Enter"," "],jk=["ArrowDown","PageUp","Home"],Vv=["ArrowUp","PageDown","End"],zk=[...jk,...Vv],Uk={ltr:[...Lc,"ArrowRight"],rtl:[...Lc,"ArrowLeft"]},$k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ra="Menu",[Gi,Hk,Bk]=Zm(ra),[no,Wv]=Zi(ra,[Bk,wv,Tv]),is=wv(),Kv=Tv(),[Vk,ro]=no(ra),[Wk,oa]=no(ra),Qv=t=>{const{__scopeMenu:r,open:o=!1,children:a,dir:s,onOpenChange:u,modal:d=!0}=t,h=is(r),[p,m]=g.useState(null),v=g.useRef(!1),w=Gn(u),x=ev(s);return g.useEffect(()=>{const E=()=>{v.current=!0,document.addEventListener("pointerdown",k,{capture:!0,once:!0}),document.addEventListener("pointermove",k,{capture:!0,once:!0})},k=()=>v.current=!1;return document.addEventListener("keydown",E,{capture:!0}),()=>{document.removeEventListener("keydown",E,{capture:!0}),document.removeEventListener("pointerdown",k,{capture:!0}),document.removeEventListener("pointermove",k,{capture:!0})}},[]),D.jsx(NC,{...h,children:D.jsx(Vk,{scope:r,open:o,onOpenChange:w,content:p,onContentChange:m,children:D.jsx(Wk,{scope:r,onClose:g.useCallback(()=>w(!1),[w]),isUsingKeyboardRef:v,dir:x,modal:d,children:a})})})};Qv.displayName=ra;var Kk="MenuAnchor",ad=g.forwardRef((t,r)=>{const{__scopeMenu:o,...a}=t,s=is(o);return D.jsx(DC,{...s,...a,ref:r})});ad.displayName=Kk;var ld="MenuPortal",[Qk,Gv]=no(ld,{forceMount:void 0}),Yv=t=>{const{__scopeMenu:r,forceMount:o,children:a,container:s}=t,u=ro(ld,r);return D.jsx(Qk,{scope:r,forceMount:o,children:D.jsx(ta,{present:o||u.open,children:D.jsx(Mv,{asChild:!0,container:s,children:a})})})};Yv.displayName=ld;var rn="MenuContent",[Gk,sd]=no(rn),Xv=g.forwardRef((t,r)=>{const o=Gv(rn,t.__scopeMenu),{forceMount:a=o.forceMount,...s}=t,u=ro(rn,t.__scopeMenu),d=oa(rn,t.__scopeMenu);return D.jsx(Gi.Provider,{scope:t.__scopeMenu,children:D.jsx(ta,{present:a||u.open,children:D.jsx(Gi.Slot,{scope:t.__scopeMenu,children:d.modal?D.jsx(Yk,{...s,ref:r}):D.jsx(Xk,{...s,ref:r})})})})}),Yk=g.forwardRef((t,r)=>{const o=ro(rn,t.__scopeMenu),a=g.useRef(null),s=jt(r,a);return g.useEffect(()=>{const u=a.current;if(u)return tk(u)},[]),D.jsx(ud,{...t,ref:s,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:Fe(t.onFocusOutside,u=>u.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})}),Xk=g.forwardRef((t,r)=>{const o=ro(rn,t.__scopeMenu);return D.jsx(ud,{...t,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>o.onOpenChange(!1)})}),qk=Wi("MenuContent.ScrollLock"),ud=g.forwardRef((t,r)=>{const{__scopeMenu:o,loop:a=!1,trapFocus:s,onOpenAutoFocus:u,onCloseAutoFocus:d,disableOutsidePointerEvents:h,onEntryFocus:p,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:x,onDismiss:E,disableOutsideScroll:k,...S}=t,R=ro(rn,o),P=oa(rn,o),j=is(o),L=Kv(o),U=Hk(o),[H,_]=g.useState(null),K=g.useRef(null),V=jt(r,K,R.onContentChange),te=g.useRef(0),ve=g.useRef(""),Te=g.useRef(0),_e=g.useRef(null),oe=g.useRef("right"),ae=g.useRef(0),Pe=k?Bv:g.Fragment,Z=k?{as:qk,allowPinchZoom:!0}:void 0,se=W=>{const G=ve.current+W,T=U().filter(ie=>!ie.disabled),B=document.activeElement,ne=T.find(ie=>ie.ref.current===B)?.textValue,ue=T.map(ie=>ie.textValue),be=uR(ue,G,ne),Re=T.find(ie=>ie.textValue===be)?.ref.current;(function ie(Oe){ve.current=Oe,window.clearTimeout(te.current),Oe!==""&&(te.current=window.setTimeout(()=>ie(""),1e3))})(G),Re&&setTimeout(()=>Re.focus())};g.useEffect(()=>()=>window.clearTimeout(te.current),[]),r1();const N=g.useCallback(W=>oe.current===_e.current?.side&&dR(W,_e.current?.area),[]);return D.jsx(Gk,{scope:o,searchRef:ve,onItemEnter:g.useCallback(W=>{N(W)&&W.preventDefault()},[N]),onItemLeave:g.useCallback(W=>{N(W)||(K.current?.focus(),_(null))},[N]),onTriggerLeave:g.useCallback(W=>{N(W)&&W.preventDefault()},[N]),pointerGraceTimerRef:Te,onPointerGraceIntentChange:g.useCallback(W=>{_e.current=W},[]),children:D.jsx(Pe,{...Z,children:D.jsx(ov,{asChild:!0,trapped:s,onMountAutoFocus:Fe(u,W=>{W.preventDefault(),K.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:d,children:D.jsx(nv,{asChild:!0,disableOutsidePointerEvents:h,onEscapeKeyDown:m,onPointerDownOutside:v,onFocusOutside:w,onInteractOutside:x,onDismiss:E,children:D.jsx(XC,{asChild:!0,...L,dir:P.dir,orientation:"vertical",loop:a,currentTabStopId:H,onCurrentTabStopIdChange:_,onEntryFocus:Fe(p,W=>{P.isUsingKeyboardRef.current||W.preventDefault()}),preventScrollOnEntryFocus:!0,children:D.jsx(LC,{role:"menu","aria-orientation":"vertical","data-state":fg(R.open),"data-radix-menu-content":"",dir:P.dir,...j,...S,ref:V,style:{outline:"none",...S.style},onKeyDown:Fe(S.onKeyDown,W=>{const T=W.target.closest("[data-radix-menu-content]")===W.currentTarget,B=W.ctrlKey||W.altKey||W.metaKey,ne=W.key.length===1;T&&(W.key==="Tab"&&W.preventDefault(),!B&&ne&&se(W.key));const ue=K.current;if(W.target!==ue||!zk.includes(W.key))return;W.preventDefault();const Re=U().filter(ie=>!ie.disabled).map(ie=>ie.ref.current);Vv.includes(W.key)&&Re.reverse(),lR(Re)}),onBlur:Fe(t.onBlur,W=>{W.currentTarget.contains(W.target)||(window.clearTimeout(te.current),ve.current="")}),onPointerMove:Fe(t.onPointerMove,Yi(W=>{const G=W.target,T=ae.current!==W.clientX;if(W.currentTarget.contains(G)&&T){const B=W.clientX>ae.current?"right":"left";oe.current=B,ae.current=W.clientX}}))})})})})})})});Xv.displayName=rn;var Jk="MenuGroup",cd=g.forwardRef((t,r)=>{const{__scopeMenu:o,...a}=t;return D.jsx(Pt.div,{role:"group",...a,ref:r})});cd.displayName=Jk;var Zk="MenuLabel",qv=g.forwardRef((t,r)=>{const{__scopeMenu:o,...a}=t;return D.jsx(Pt.div,{...a,ref:r})});qv.displayName=Zk;var Vl="MenuItem",em="menu.itemSelect",as=g.forwardRef((t,r)=>{const{disabled:o=!1,onSelect:a,...s}=t,u=g.useRef(null),d=oa(Vl,t.__scopeMenu),h=sd(Vl,t.__scopeMenu),p=jt(r,u),m=g.useRef(!1),v=()=>{const w=u.current;if(!o&&w){const x=new CustomEvent(em,{bubbles:!0,cancelable:!0});w.addEventListener(em,E=>a?.(E),{once:!0}),Jm(w,x),x.defaultPrevented?m.current=!1:d.onClose()}};return D.jsx(Jv,{...s,ref:p,disabled:o,onClick:Fe(t.onClick,v),onPointerDown:w=>{t.onPointerDown?.(w),m.current=!0},onPointerUp:Fe(t.onPointerUp,w=>{m.current||w.currentTarget?.click()}),onKeyDown:Fe(t.onKeyDown,w=>{const x=h.searchRef.current!=="";o||x&&w.key===" "||Lc.includes(w.key)&&(w.currentTarget.click(),w.preventDefault())})})});as.displayName=Vl;var Jv=g.forwardRef((t,r)=>{const{__scopeMenu:o,disabled:a=!1,textValue:s,...u}=t,d=sd(Vl,o),h=Kv(o),p=g.useRef(null),m=jt(r,p),[v,w]=g.useState(!1),[x,E]=g.useState("");return g.useEffect(()=>{const k=p.current;k&&E((k.textContent??"").trim())},[u.children]),D.jsx(Gi.ItemSlot,{scope:o,disabled:a,textValue:s??x,children:D.jsx(qC,{asChild:!0,...h,focusable:!a,children:D.jsx(Pt.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...u,ref:m,onPointerMove:Fe(t.onPointerMove,Yi(k=>{a?d.onItemLeave(k):(d.onItemEnter(k),k.defaultPrevented||k.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:Fe(t.onPointerLeave,Yi(k=>d.onItemLeave(k))),onFocus:Fe(t.onFocus,()=>w(!0)),onBlur:Fe(t.onBlur,()=>w(!1))})})})}),eR="MenuCheckboxItem",Zv=g.forwardRef((t,r)=>{const{checked:o=!1,onCheckedChange:a,...s}=t;return D.jsx(og,{scope:t.__scopeMenu,checked:o,children:D.jsx(as,{role:"menuitemcheckbox","aria-checked":Wl(o)?"mixed":o,...s,ref:r,"data-state":fd(o),onSelect:Fe(s.onSelect,()=>a?.(Wl(o)?!0:!o),{checkForDefaultPrevented:!1})})})});Zv.displayName=eR;var eg="MenuRadioGroup",[tR,nR]=no(eg,{value:void 0,onValueChange:()=>{}}),tg=g.forwardRef((t,r)=>{const{value:o,onValueChange:a,...s}=t,u=Gn(a);return D.jsx(tR,{scope:t.__scopeMenu,value:o,onValueChange:u,children:D.jsx(cd,{...s,ref:r})})});tg.displayName=eg;var ng="MenuRadioItem",rg=g.forwardRef((t,r)=>{const{value:o,...a}=t,s=nR(ng,t.__scopeMenu),u=o===s.value;return D.jsx(og,{scope:t.__scopeMenu,checked:u,children:D.jsx(as,{role:"menuitemradio","aria-checked":u,...a,ref:r,"data-state":fd(u),onSelect:Fe(a.onSelect,()=>s.onValueChange?.(o),{checkForDefaultPrevented:!1})})})});rg.displayName=ng;var dd="MenuItemIndicator",[og,rR]=no(dd,{checked:!1}),ig=g.forwardRef((t,r)=>{const{__scopeMenu:o,forceMount:a,...s}=t,u=rR(dd,o);return D.jsx(ta,{present:a||Wl(u.checked)||u.checked===!0,children:D.jsx(Pt.span,{...s,ref:r,"data-state":fd(u.checked)})})});ig.displayName=dd;var oR="MenuSeparator",ag=g.forwardRef((t,r)=>{const{__scopeMenu:o,...a}=t;return D.jsx(Pt.div,{role:"separator","aria-orientation":"horizontal",...a,ref:r})});ag.displayName=oR;var iR="MenuArrow",lg=g.forwardRef((t,r)=>{const{__scopeMenu:o,...a}=t,s=is(o);return D.jsx(AC,{...s,...a,ref:r})});lg.displayName=iR;var aR="MenuSub",[NP,sg]=no(aR),zi="MenuSubTrigger",ug=g.forwardRef((t,r)=>{const o=ro(zi,t.__scopeMenu),a=oa(zi,t.__scopeMenu),s=sg(zi,t.__scopeMenu),u=sd(zi,t.__scopeMenu),d=g.useRef(null),{pointerGraceTimerRef:h,onPointerGraceIntentChange:p}=u,m={__scopeMenu:t.__scopeMenu},v=g.useCallback(()=>{d.current&&window.clearTimeout(d.current),d.current=null},[]);return g.useEffect(()=>v,[v]),g.useEffect(()=>{const w=h.current;return()=>{window.clearTimeout(w),p(null)}},[h,p]),D.jsx(ad,{asChild:!0,...m,children:D.jsx(Jv,{id:s.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":s.contentId,"data-state":fg(o.open),...t,ref:es(r,s.onTriggerChange),onClick:w=>{t.onClick?.(w),!(t.disabled||w.defaultPrevented)&&(w.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:Fe(t.onPointerMove,Yi(w=>{u.onItemEnter(w),!w.defaultPrevented&&!t.disabled&&!o.open&&!d.current&&(u.onPointerGraceIntentChange(null),d.current=window.setTimeout(()=>{o.onOpenChange(!0),v()},100))})),onPointerLeave:Fe(t.onPointerLeave,Yi(w=>{v();const x=o.content?.getBoundingClientRect();if(x){const E=o.content?.dataset.side,k=E==="right",S=k?-5:5,R=x[k?"left":"right"],P=x[k?"right":"left"];u.onPointerGraceIntentChange({area:[{x:w.clientX+S,y:w.clientY},{x:R,y:x.top},{x:P,y:x.top},{x:P,y:x.bottom},{x:R,y:x.bottom}],side:E}),window.clearTimeout(h.current),h.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(w),w.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:Fe(t.onKeyDown,w=>{const x=u.searchRef.current!=="";t.disabled||x&&w.key===" "||Uk[a.dir].includes(w.key)&&(o.onOpenChange(!0),o.content?.focus(),w.preventDefault())})})})});ug.displayName=zi;var cg="MenuSubContent",dg=g.forwardRef((t,r)=>{const o=Gv(rn,t.__scopeMenu),{forceMount:a=o.forceMount,...s}=t,u=ro(rn,t.__scopeMenu),d=oa(rn,t.__scopeMenu),h=sg(cg,t.__scopeMenu),p=g.useRef(null),m=jt(r,p);return D.jsx(Gi.Provider,{scope:t.__scopeMenu,children:D.jsx(ta,{present:a||u.open,children:D.jsx(Gi.Slot,{scope:t.__scopeMenu,children:D.jsx(ud,{id:h.contentId,"aria-labelledby":h.triggerId,...s,ref:m,align:"start",side:d.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:v=>{d.isUsingKeyboardRef.current&&p.current?.focus(),v.preventDefault()},onCloseAutoFocus:v=>v.preventDefault(),onFocusOutside:Fe(t.onFocusOutside,v=>{v.target!==h.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:Fe(t.onEscapeKeyDown,v=>{d.onClose(),v.preventDefault()}),onKeyDown:Fe(t.onKeyDown,v=>{const w=v.currentTarget.contains(v.target),x=$k[d.dir].includes(v.key);w&&x&&(u.onOpenChange(!1),h.trigger?.focus(),v.preventDefault())})})})})})});dg.displayName=cg;function fg(t){return t?"open":"closed"}function Wl(t){return t==="indeterminate"}function fd(t){return Wl(t)?"indeterminate":t?"checked":"unchecked"}function lR(t){const r=document.activeElement;for(const o of t)if(o===r||(o.focus(),document.activeElement!==r))return}function sR(t,r){return t.map((o,a)=>t[(r+a)%t.length])}function uR(t,r,o){const s=r.length>1&&Array.from(r).every(m=>m===r[0])?r[0]:r,u=o?t.indexOf(o):-1;let d=sR(t,Math.max(u,0));s.length===1&&(d=d.filter(m=>m!==o));const p=d.find(m=>m.toLowerCase().startsWith(s.toLowerCase()));return p!==o?p:void 0}function cR(t,r){const{x:o,y:a}=t;let s=!1;for(let u=0,d=r.length-1;u<r.length;d=u++){const h=r[u],p=r[d],m=h.x,v=h.y,w=p.x,x=p.y;v>a!=x>a&&o<(w-m)*(a-v)/(x-v)+m&&(s=!s)}return s}function dR(t,r){if(!r)return!1;const o={x:t.clientX,y:t.clientY};return cR(o,r)}function Yi(t){return r=>r.pointerType==="mouse"?t(r):void 0}var fR=Qv,hR=ad,pR=Yv,mR=Xv,vR=cd,gR=qv,yR=as,wR=Zv,xR=tg,SR=rg,ER=ig,CR=ag,kR=lg,RR=ug,PR=dg,ls="DropdownMenu",[bR,DP]=Zi(ls,[Wv]),bt=Wv(),[MR,hg]=bR(ls),pg=t=>{const{__scopeDropdownMenu:r,children:o,dir:a,open:s,defaultOpen:u,onOpenChange:d,modal:h=!0}=t,p=bt(r),m=g.useRef(null),[v,w]=qm({prop:s,defaultProp:u??!1,onChange:d,caller:ls});return D.jsx(MR,{scope:r,triggerId:Mc(),triggerRef:m,contentId:Mc(),open:v,onOpenChange:w,onOpenToggle:g.useCallback(()=>w(x=>!x),[w]),modal:h,children:D.jsx(fR,{...p,open:v,onOpenChange:w,dir:a,modal:h,children:o})})};pg.displayName=ls;var mg="DropdownMenuTrigger",vg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,disabled:a=!1,...s}=t,u=hg(mg,o),d=bt(o);return D.jsx(hR,{asChild:!0,...d,children:D.jsx(Pt.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":a?"":void 0,disabled:a,...s,ref:es(r,u.triggerRef),onPointerDown:Fe(t.onPointerDown,h=>{!a&&h.button===0&&h.ctrlKey===!1&&(u.onOpenToggle(),u.open||h.preventDefault())}),onKeyDown:Fe(t.onKeyDown,h=>{a||(["Enter"," "].includes(h.key)&&u.onOpenToggle(),h.key==="ArrowDown"&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(h.key)&&h.preventDefault())})})})});vg.displayName=mg;var _R="DropdownMenuPortal",gg=t=>{const{__scopeDropdownMenu:r,...o}=t,a=bt(r);return D.jsx(pR,{...a,...o})};gg.displayName=_R;var yg="DropdownMenuContent",wg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=hg(yg,o),u=bt(o),d=g.useRef(!1);return D.jsx(mR,{id:s.contentId,"aria-labelledby":s.triggerId,...u,...a,ref:r,onCloseAutoFocus:Fe(t.onCloseAutoFocus,h=>{d.current||s.triggerRef.current?.focus(),d.current=!1,h.preventDefault()}),onInteractOutside:Fe(t.onInteractOutside,h=>{const p=h.detail.originalEvent,m=p.button===0&&p.ctrlKey===!0,v=p.button===2||m;(!s.modal||v)&&(d.current=!0)}),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});wg.displayName=yg;var TR="DropdownMenuGroup",OR=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(vR,{...s,...a,ref:r})});OR.displayName=TR;var NR="DropdownMenuLabel",xg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(gR,{...s,...a,ref:r})});xg.displayName=NR;var DR="DropdownMenuItem",Sg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(yR,{...s,...a,ref:r})});Sg.displayName=DR;var LR="DropdownMenuCheckboxItem",Eg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(wR,{...s,...a,ref:r})});Eg.displayName=LR;var AR="DropdownMenuRadioGroup",IR=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(xR,{...s,...a,ref:r})});IR.displayName=AR;var FR="DropdownMenuRadioItem",Cg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(SR,{...s,...a,ref:r})});Cg.displayName=FR;var jR="DropdownMenuItemIndicator",kg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(ER,{...s,...a,ref:r})});kg.displayName=jR;var zR="DropdownMenuSeparator",Rg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(CR,{...s,...a,ref:r})});Rg.displayName=zR;var UR="DropdownMenuArrow",$R=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(kR,{...s,...a,ref:r})});$R.displayName=UR;var HR="DropdownMenuSubTrigger",Pg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(RR,{...s,...a,ref:r})});Pg.displayName=HR;var BR="DropdownMenuSubContent",bg=g.forwardRef((t,r)=>{const{__scopeDropdownMenu:o,...a}=t,s=bt(o);return D.jsx(PR,{...s,...a,ref:r,style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});bg.displayName=BR;var VR=pg,WR=vg,KR=gg,Mg=wg,_g=xg,Tg=Sg,Og=Eg,Ng=Cg,Dg=kg,Lg=Rg,Ag=Pg,Ig=bg;function Ln(...t){return Xm(Yc(t))}const QR=VR,GR=WR,YR=g.forwardRef(({className:t,inset:r,children:o,...a},s)=>D.jsxs(Ag,{ref:s,className:Ln("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",t),...a,children:[o,D.jsx($S,{className:"ml-auto h-4 w-4"})]}));YR.displayName=Ag.displayName;const XR=g.forwardRef(({className:t,...r},o)=>D.jsx(Ig,{ref:o,className:Ln("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...r}));XR.displayName=Ig.displayName;const Fg=g.forwardRef(({className:t,sideOffset:r=4,...o},a)=>D.jsx(KR,{children:D.jsx(Mg,{ref:a,sideOffset:r,className:Ln("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...o})}));Fg.displayName=Mg.displayName;const Ll=g.forwardRef(({className:t,inset:r,...o},a)=>D.jsx(Tg,{ref:a,className:Ln("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",t),...o}));Ll.displayName=Tg.displayName;const qR=g.forwardRef(({className:t,children:r,checked:o,...a},s)=>D.jsxs(Og,{ref:s,className:Ln("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),checked:o,...a,children:[D.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:D.jsx(Dg,{children:D.jsx(zS,{className:"h-4 w-4"})})}),r]}));qR.displayName=Og.displayName;const JR=g.forwardRef(({className:t,children:r,...o},a)=>D.jsxs(Ng,{ref:a,className:Ln("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...o,children:[D.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:D.jsx(Dg,{children:D.jsx(BS,{className:"h-2 w-2 fill-current"})})}),r]}));JR.displayName=Ng.displayName;const ZR=g.forwardRef(({className:t,inset:r,...o},a)=>D.jsx(_g,{ref:a,className:Ln("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",t),...o}));ZR.displayName=_g.displayName;const jg=g.forwardRef(({className:t,...r},o)=>D.jsx(Lg,{ref:o,className:Ln("-mx-1 my-1 h-px bg-muted",t),...r}));jg.displayName=Lg.displayName;const eP=()=>D.jsx(Qc,{className:"flex items-center text-white",to:Xe.home.getHref(),children:D.jsxs("div",{className:"flex items-center space-x-2",children:[D.jsx("div",{className:"w-8 h-8 bg-white rounded-lg flex items-center justify-center",children:D.jsx("span",{className:"text-blue-600 font-bold text-lg",children:"W"})}),D.jsx("span",{className:"text-sm font-semibold text-white",children:"WorkFinder"})]})}),tP=()=>{const{state:t,location:r}=Rx(),[o,a]=g.useState(0);return g.useEffect(()=>{a(0)},[r?.pathname]),g.useEffect(()=>{if(t==="loading"){const s=setInterval(()=>{a(u=>{if(u===100)return clearInterval(s),100;const d=u+10;return d>100?100:d})},300);return()=>{clearInterval(s)}}},[t]),t!=="loading"?null:D.jsx("div",{className:"fixed left-0 top-0 h-1 bg-blue-500 transition-all duration-200 ease-in-out z-50",style:{width:`${o}%`}})};function nP({children:t}){const r=ql(),{logout:o}=Gc(),a=()=>{o(),r(Xe.auth.login.getHref())},s=[{name:"Dashboard",to:Xe.app.dashboard.getHref(),icon:YS},{name:"Jobs",to:Xe.app.jobs.getHref(),icon:AS},{name:"Companies",to:Xe.app.companies.getHref(),icon:FS},{name:"Saved Jobs",to:Xe.app.savedJobs.getHref(),icon:QS},{name:"Applications",to:Xe.app.applications.getHref(),icon:WS}];return D.jsxs("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[D.jsx(tP,{}),D.jsx("aside",{className:"fixed inset-y-0 left-0 z-10 hidden w-60 flex-col border-r bg-gray-900 sm:flex",children:D.jsxs("nav",{className:"flex flex-col items-center gap-4 px-2 py-4",children:[D.jsx("div",{className:"flex h-16 shrink-0 items-center px-4",children:D.jsx(eP,{})}),s.map(u=>D.jsxs(Im,{to:u.to,end:u.name==="Dashboard",className:({isActive:d})=>Ln("text-gray-300 hover:bg-gray-700 hover:text-white","group flex flex-1 w-full items-center rounded-md p-2 text-base font-medium",d&&"bg-gray-800 text-white"),children:[D.jsx(u.icon,{className:Ln("text-gray-400 group-hover:text-gray-300","mr-4 size-6 shrink-0"),"aria-hidden":"true"}),u.name]},u.name))]})}),D.jsxs("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-60",children:[D.jsxs("header",{className:"sticky top-0 z-30 flex h-14 items-center justify-between gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:justify-end sm:border-0 sm:bg-transparent sm:px-6",children:[D.jsxs(Pc,{size:"icon",variant:"outline",className:"sm:hidden",children:[D.jsx(qS,{className:"size-5"}),D.jsx("span",{className:"sr-only",children:"Toggle Menu"})]}),D.jsxs(QR,{children:[D.jsx(GR,{asChild:!0,children:D.jsxs(Pc,{variant:"outline",size:"icon",className:"overflow-hidden rounded-full",children:[D.jsx("span",{className:"sr-only",children:"Open user menu"}),D.jsx(kp,{className:"size-6 rounded-full"})]})}),D.jsxs(Fg,{align:"end",children:[D.jsxs(Ll,{onClick:()=>r(Xe.app.profile.getHref()),className:"cursor-pointer",children:[D.jsx(kp,{className:"mr-2 h-4 w-4"}),"Profile"]}),D.jsxs(Ll,{onClick:()=>r(Xe.app.settings.getHref()),className:"cursor-pointer",children:[D.jsx(ZS,{className:"mr-2 h-4 w-4"}),"Settings"]}),D.jsx(jg,{}),D.jsx(Ll,{onClick:a,className:"cursor-pointer text-red-600",children:"Sign Out"})]})]})]}),D.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:t})]})]})}function rP(){return D.jsx(nP,{children:D.jsx(Ax,{})})}function oP(){return D.jsx("div",{className:"min-h-screen flex items-center justify-center",children:D.jsxs("div",{className:"text-center",children:[D.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Something went wrong"}),D.jsx("p",{className:"text-gray-600 mb-4",children:"We're sorry, but something went wrong. Please try again later."}),D.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Reload page"})]})})}const Et=t=>r=>{const{clientLoader:o,clientAction:a,default:s,...u}=r;return{...u,loader:o?.(t),action:a?.(t),Component:s}},iP=t=>aS([{path:Xe.auth.register.path,lazy:()=>St(()=>import("./register-eb8iiTDM.js"),__vite__mapDeps([0,1])).then(Et(t))},{path:Xe.auth.login.path,lazy:()=>St(()=>import("./login-Dk40jRo6.js"),__vite__mapDeps([2,1])).then(Et(t))},{path:"/",lazy:()=>St(()=>import("./app-layout-zSFanS2y.js"),[]).then(Et(t)),children:[{path:Xe.home.path,lazy:()=>St(()=>import("./landing-DWY5xz1B.js"),__vite__mapDeps([3,4,5,6,7,8,9,10,11,12,13,14])).then(Et(t))},{path:Xe.jobs.root.path,lazy:()=>St(()=>import("./jobs-Dhvce3fG.js"),__vite__mapDeps([15,4,9,16,6,12,10,13,11,5])).then(Et(t))},{path:Xe.jobs.detail.path,lazy:()=>St(()=>import("./job-detail-BoHAZB1Q.js"),__vite__mapDeps([17,4,9,18,6,12,13,10,19,20,21,22])).then(Et(t))},{path:Xe.companies.root.path,lazy:()=>St(()=>import("./companies-DaklL1th.js"),__vite__mapDeps([23,4,9,14,7,13,6,5,16])).then(Et(t))},{path:Xe.companies.detail.path,lazy:()=>St(()=>import("./company-detail-tIb1biaf.js"),__vite__mapDeps([24,4,9,8,7,10,6,11,12,13,18,20,22])).then(Et(t))},{path:Xe.about.path,lazy:()=>St(()=>import("./about-Bl1BGm1_.js"),__vite__mapDeps([25,4,26])).then(Et(t))},{path:Xe.contact.path,lazy:()=>St(()=>import("./contact-wFkBKXle.js"),__vite__mapDeps([27,4,26])).then(Et(t))},{path:Xe.privacy.path,lazy:()=>St(()=>import("./privacy-CXKLxjwX.js"),__vite__mapDeps([28,4,26])).then(Et(t))},{path:Xe.terms.path,lazy:()=>St(()=>import("./terms-DiAt30ct.js"),__vite__mapDeps([29,4,26])).then(Et(t))},{path:Xe.app.root.path,element:D.jsx(MS,{children:D.jsx(rP,{})}),ErrorBoundary:oP,children:[{path:Xe.app.dashboard.path,lazy:()=>St(()=>import("./dashboard-DSP7rGoJ.js"),[]).then(Et(t))},{path:Xe.app.applications.path,lazy:()=>St(()=>import("./applications-DmdnGKQQ.js"),__vite__mapDeps([30,4,9,12,13,21,20,31,22])).then(Et(t))},{path:Xe.app.savedJobs.path,lazy:()=>St(()=>import("./saved-jobs-CxlyyZWr.js"),__vite__mapDeps([32,4,9,10,12,22,6,11,31,19])).then(Et(t))}]}]},{path:"*",lazy:()=>St(()=>import("./not-found-BtbYTj6t.js"),[]).then(Et(t))}]),aP=()=>{const t=l0(),r=g.useMemo(()=>iP(t),[t]);return D.jsx(yS,{router:r})};function lP(){return D.jsx(aP,{})}const sP=g.createContext(null),fc={didCatch:!1,error:null};class uP extends g.Component{constructor(r){super(r),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=fc}static getDerivedStateFromError(r){return{didCatch:!0,error:r}}resetErrorBoundary(){const{error:r}=this.state;if(r!==null){for(var o,a,s=arguments.length,u=new Array(s),d=0;d<s;d++)u[d]=arguments[d];(o=(a=this.props).onReset)===null||o===void 0||o.call(a,{args:u,reason:"imperative-api"}),this.setState(fc)}}componentDidCatch(r,o){var a,s;(a=(s=this.props).onError)===null||a===void 0||a.call(s,r,o)}componentDidUpdate(r,o){const{didCatch:a}=this.state,{resetKeys:s}=this.props;if(a&&o.error!==null&&cP(r.resetKeys,s)){var u,d;(u=(d=this.props).onReset)===null||u===void 0||u.call(d,{next:s,prev:r.resetKeys,reason:"keys"}),this.setState(fc)}}render(){const{children:r,fallbackRender:o,FallbackComponent:a,fallback:s}=this.props,{didCatch:u,error:d}=this.state;let h=r;if(u){const p={error:d,resetErrorBoundary:this.resetErrorBoundary};if(typeof o=="function")h=o(p);else if(a)h=g.createElement(a,p);else if(s!==void 0)h=s;else throw d}return g.createElement(sP.Provider,{value:{didCatch:u,error:d,resetErrorBoundary:this.resetErrorBoundary}},h)}}function cP(){let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return t.length!==r.length||t.some((o,a)=>!Object.is(o,r[a]))}var zg=(t=>(t.BASE="base",t.BODY="body",t.HEAD="head",t.HTML="html",t.LINK="link",t.META="meta",t.NOSCRIPT="noscript",t.SCRIPT="script",t.STYLE="style",t.TITLE="title",t.FRAGMENT="Symbol(react.fragment)",t))(zg||{}),hc={link:{rel:["amphtml","canonical","alternate"]},script:{type:["application/ld+json"]},meta:{charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]}};Object.values(zg);var hd={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"};Object.entries(hd).reduce((t,[r,o])=>(t[o]=r,t),{});var Xi="data-rh",dP=t=>Array.isArray(t)?t.join(""):t,fP=(t,r)=>{const o=Object.keys(t);for(let a=0;a<o.length;a+=1)if(r[o[a]]&&r[o[a]].includes(t[o[a]]))return!0;return!1},pc=(t,r)=>Array.isArray(t)?t.reduce((o,a)=>(fP(a,r)?o.priority.push(a):o.default.push(a),o),{priority:[],default:[]}):{default:t,priority:[]},hP=["noscript","script","style"],Ac=(t,r=!0)=>r===!1?String(t):String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),Ug=t=>Object.keys(t).reduce((r,o)=>{const a=typeof t[o]<"u"?`${o}="${t[o]}"`:`${o}`;return r?`${r} ${a}`:a},""),pP=(t,r,o,a)=>{const s=Ug(o),u=dP(r);return s?`<${t} ${Xi}="true" ${s}>${Ac(u,a)}</${t}>`:`<${t} ${Xi}="true">${Ac(u,a)}</${t}>`},mP=(t,r,o=!0)=>r.reduce((a,s)=>{const u=s,d=Object.keys(u).filter(m=>!(m==="innerHTML"||m==="cssText")).reduce((m,v)=>{const w=typeof u[v]>"u"?v:`${v}="${Ac(u[v],o)}"`;return m?`${m} ${w}`:w},""),h=u.innerHTML||u.cssText||"",p=hP.indexOf(t)===-1;return`${a}<${t} ${Xi}="true" ${d}${p?"/>":`>${h}</${t}>`}`},""),$g=(t,r={})=>Object.keys(t).reduce((o,a)=>{const s=hd[a];return o[s||a]=t[a],o},r),vP=(t,r,o)=>{const a={key:r,[Xi]:!0},s=$g(o,a);return[It.createElement("title",s,r)]},Al=(t,r)=>r.map((o,a)=>{const s={key:a,[Xi]:!0};return Object.keys(o).forEach(u=>{const h=hd[u]||u;if(h==="innerHTML"||h==="cssText"){const p=o.innerHTML||o.cssText;s.dangerouslySetInnerHTML={__html:p}}else s[h]=o[u]}),It.createElement(t,s)}),tn=(t,r,o=!0)=>{switch(t){case"title":return{toComponent:()=>vP(t,r.title,r.titleAttributes),toString:()=>pP(t,r.title,r.titleAttributes,o)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>$g(r),toString:()=>Ug(r)};default:return{toComponent:()=>Al(t,r),toString:()=>mP(t,r,o)}}},gP=({metaTags:t,linkTags:r,scriptTags:o,encode:a})=>{const s=pc(t,hc.meta),u=pc(r,hc.link),d=pc(o,hc.script);return{priorityMethods:{toComponent:()=>[...Al("meta",s.priority),...Al("link",u.priority),...Al("script",d.priority)],toString:()=>`${tn("meta",s.priority,a)} ${tn("link",u.priority,a)} ${tn("script",d.priority,a)}`},metaTags:s.default,linkTags:u.default,scriptTags:d.default}},yP=t=>{const{baseTag:r,bodyAttributes:o,encode:a=!0,htmlAttributes:s,noscriptTags:u,styleTags:d,title:h="",titleAttributes:p,prioritizeSeoTags:m}=t;let{linkTags:v,metaTags:w,scriptTags:x}=t,E={toComponent:()=>{},toString:()=>""};return m&&({priorityMethods:E,linkTags:v,metaTags:w,scriptTags:x}=gP(t)),{priority:E,base:tn("base",r,a),bodyAttributes:tn("bodyAttributes",o,a),htmlAttributes:tn("htmlAttributes",s,a),link:tn("link",v,a),meta:tn("meta",w,a),noscript:tn("noscript",u,a),script:tn("script",x,a),style:tn("style",d,a),title:tn("title",{title:h,titleAttributes:p},a)}},wP=yP,Pl=[],Hg=!!(typeof window<"u"&&window.document&&window.document.createElement),xP=class{instances=[];canUseDOM=Hg;context;value={setHelmet:t=>{this.context.helmet=t},helmetInstances:{get:()=>this.canUseDOM?Pl:this.instances,add:t=>{(this.canUseDOM?Pl:this.instances).push(t)},remove:t=>{const r=(this.canUseDOM?Pl:this.instances).indexOf(t);(this.canUseDOM?Pl:this.instances).splice(r,1)}}};constructor(t,r){this.context=t,this.canUseDOM=r||!1,r||(t.helmet=wP({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},SP={},EP=It.createContext(SP),CP=class Bg extends g.Component{static canUseDOM=Hg;helmetData;constructor(r){super(r),this.helmetData=new xP(this.props.context||{},Bg.canUseDOM)}render(){return It.createElement(EP.Provider,{value:this.helmetData.value},this.props.children)}};const kP=new a0({defaultOptions:{queries:{staleTime:300*1e3,gcTime:600*1e3,retry:(t,r)=>r?.response?.status>=400&&r?.response?.status<500?r?.response?.status===401&&t<1:t<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}});function RP({children:t}){const{getCurrentUser:r,isInitializing:o,setInitialized:a}=Gc();return g.useEffect(()=>{o&&(async()=>{try{localStorage.getItem("work-finder-auth")?await r():a()}catch(u){console.error("Auth initialization failed:",u),a()}})()},[r,o,a]),o?D.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:D.jsxs("div",{className:"text-center",children:[D.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),D.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):D.jsx(D.Fragment,{children:t})}function PP(){return D.jsx("div",{className:"flex h-screen w-screen items-center justify-center",children:D.jsxs("div",{className:"text-center",children:[D.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Something went wrong"}),D.jsx("p",{className:"text-gray-600 mb-4",children:"We're sorry, but something went wrong. Please try again later."}),D.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Reload page"})]})})}function bP(){return D.jsx("div",{className:"flex h-screen w-screen items-center justify-center",children:D.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}function MP({children:t}){return D.jsx(g.Suspense,{fallback:D.jsx(bP,{}),children:D.jsx(uP,{FallbackComponent:PP,children:D.jsx(CP,{children:D.jsx(s0,{client:kP,children:D.jsxs(RP,{children:[t,!1]})})})})})}Aw.createRoot(document.getElementById("root")).render(D.jsx(g.StrictMode,{children:D.jsx(MP,{children:D.jsx(lP,{})})}));export{DC as A,AS as B,$S as C,tk as D,r1 as E,WS as F,Bv as G,Wi as H,ov as I,nv as J,LC as K,Qc as L,AC as M,Lx as N,Ax as O,Pt as P,zS as Q,It as R,Tv as S,XC as T,qC as U,ta as V,Ic as W,RC as X,OP as Y,ql as a,An as b,TP as c,Pc as d,En as e,_P as f,FS as g,$E as h,lE as i,D as j,wv as k,ev as l,qm as m,NC as n,Zi as o,Xe as p,Mc as q,g as r,Zm as s,jt as t,Gc as u,br as v,Fe as w,Mv as x,Zl as y,Gn as z};
