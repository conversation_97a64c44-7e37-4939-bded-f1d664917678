import{a as S,r as m,j as e,d as r}from"./index-BOPfWTHD.js";import{m as D,C as l,a as i,D as C,o as A,q as J,s as $,t as T,u as L,v as R}from"./mock-data-CbJZR1PP.js";import{B as c}from"./badge-Bzyz8_QN.js";import{B as x}from"./bookmark-DWvq3fkz.js";import{C as g,f as p}from"./formatDistanceToNow-Bm5-CVp4.js";import{E}from"./external-link-CApOk594.js";import{M as z}from"./map-pin-BR21nmoc.js";import{D as B}from"./dollar-sign-C4uAz_6M.js";import{E as I,T as P}from"./trash-2-DiK6xcQR.js";import{S as F}from"./share-2-B0mcIQr4.js";function U(){const d=S(),[n,h]=m.useState([]),[N,b]=m.useState(!0);m.useEffect(()=>{setTimeout(()=>{const a=D.slice(0,3).map((s,t)=>({id:`saved-${t+1}`,userId:"user-1",jobId:s.id,job:s,savedAt:new Date(Date.now()-(t+1)*24*60*60*1e3).toISOString(),notes:t===0?"Interested in this role for the tech stack":void 0}));h(a),b(!1)},500)},[]);const j=a=>{d(`/jobs/${a}`)},v=a=>{d(`/jobs/${a}`)},u=a=>{h(s=>s.filter(t=>t.id!==a))},f=a=>{const s=`${window.location.origin}/jobs/${a.id}`;navigator.clipboard.writeText(s)},y=a=>{if(!a)return"Salary not specified";const{min:s,max:t}=a;return`$${(s/1e3).toFixed(0)}k - $${(t/1e3).toFixed(0)}k`},w=a=>a.location.isRemote||a.workLocation==="remote"?"Remote":`${a.location.city}, ${a.location.state}`,k=({savedJob:a})=>{const{job:s}=a;return e.jsx(l,{className:"shadow-sm border-0 hover:shadow-md transition-shadow",children:e.jsxs(i,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.companyLogo&&e.jsx("img",{src:s.companyLogo,alt:s.companyName,className:"w-12 h-12 rounded-lg object-cover border"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900 hover:text-blue-600 cursor-pointer transition-colors",onClick:()=>j(s.id),children:s.title}),e.jsx("p",{className:"text-blue-600 font-medium",children:s.companyName})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-3",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:w(s)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:y(s.salary)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(g,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:["Posted"," ",p(new Date(s.postedAt),{addSuffix:!0})]})]})]}),e.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:s.summary})]}),e.jsx("div",{className:"flex items-center gap-2 ml-4",children:e.jsxs("div",{className:"text-right text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(x,{className:"h-4 w-4 mr-1 text-blue-500"}),e.jsxs("span",{children:["Saved"," ",p(new Date(a.savedAt),{addSuffix:!0})]})]}),s.featured&&e.jsx(c,{className:"bg-yellow-100 text-yellow-800 border-yellow-200 text-xs mb-1",children:"Featured"}),s.urgent&&e.jsx(c,{className:"bg-red-100 text-red-800 border-red-200 text-xs",children:"Urgent"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"Job Type"}),e.jsx(c,{variant:"secondary",className:"text-xs capitalize",children:s.type.replace("-"," ")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"Experience"}),e.jsx(c,{variant:"secondary",className:"text-xs capitalize",children:s.experienceLevel.replace("-"," ")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"Work Style"}),e.jsx(c,{variant:"secondary",className:"text-xs capitalize",children:s.workLocation.replace("-"," ")})]})]}),s.skills&&s.skills.length>0&&e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.skills.slice(0,5).map((t,o)=>e.jsx(c,{variant:"outline",className:"text-xs",children:t},o)),s.skills.length>5&&e.jsxs(c,{variant:"outline",className:"text-xs",children:["+",s.skills.length-5," more"]})]})}),a.notes&&e.jsxs("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg",children:[e.jsx("div",{className:"text-blue-800 text-sm font-medium mb-1",children:"Your Notes"}),e.jsx("p",{className:"text-blue-700 text-sm",children:a.notes})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(r,{variant:"ghost",size:"sm",onClick:()=>j(s.id),children:[e.jsx(I,{className:"h-4 w-4 mr-1"}),"View Details"]}),e.jsxs(r,{variant:"ghost",size:"sm",onClick:()=>f(s),children:[e.jsx(F,{className:"h-4 w-4 mr-1"}),"Share"]})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(r,{size:"sm",onClick:()=>v(s.id),className:"bg-blue-600 hover:bg-blue-700",children:"Apply Now"}),e.jsxs(C,{children:[e.jsx(A,{asChild:!0,children:e.jsxs(r,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",children:[e.jsx(P,{className:"h-4 w-4 mr-1"}),"Remove"]})}),e.jsxs(J,{children:[e.jsxs($,{children:[e.jsx(T,{children:"Remove Saved Job"}),e.jsxs(L,{children:['Are you sure you want to remove "',s.title,'" at'," ",s.companyName," from your saved jobs?"]})]}),e.jsxs(R,{children:[e.jsx(r,{variant:"outline",children:"Cancel"}),e.jsx(r,{variant:"destructive",onClick:()=>u(a.id),children:"Remove"})]})]})]})]})]})]})})};return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Saved Jobs"}),e.jsx("p",{className:"text-lg text-gray-600",children:"Keep track of jobs you're interested in and apply when you're ready"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsx(l,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(x,{className:"h-8 w-8 text-blue-500"})}),e.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:n.length}),e.jsx("div",{className:"text-gray-600",children:"Saved Jobs"})]})}),e.jsx(l,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(g,{className:"h-8 w-8 text-green-500"})}),e.jsx("div",{className:"text-3xl font-bold text-green-600 mb-1",children:n.filter(a=>{const s=new Date(a.job.postedAt),t=new Date(Date.now()-10080*60*1e3);return s>t}).length}),e.jsx("div",{className:"text-gray-600",children:"Recently Posted"})]})}),e.jsx(l,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(E,{className:"h-8 w-8 text-purple-500"})}),e.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-1",children:n.filter(a=>a.job.urgent).length}),e.jsx("div",{className:"text-gray-600",children:"Urgent Hiring"})]})})]}),N?e.jsx("div",{className:"space-y-4",children:Array.from({length:3}).map((a,s)=>e.jsx(l,{className:"shadow-sm border-0",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"}),e.jsxs("div",{children:[e.jsx("div",{className:"w-48 h-6 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"w-32 h-4 bg-gray-200 rounded"})]})]}),e.jsx("div",{className:"w-24 h-6 bg-gray-200 rounded"})]}),e.jsx("div",{className:"w-full h-16 bg-gray-200 rounded mb-4"}),e.jsx("div",{className:"flex gap-2",children:Array.from({length:3}).map((t,o)=>e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded"},o))})]})})},s))}):n.length>0?e.jsx("div",{className:"space-y-4",children:n.map(a=>e.jsx(k,{savedJob:a},a.id))}):e.jsx(l,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"text-center py-12",children:[e.jsx(x,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No saved jobs yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start saving jobs you're interested in to keep track of them and apply later"}),e.jsx(r,{onClick:()=>d("/jobs"),children:"Browse Jobs"})]})}),n.length>0&&e.jsx(l,{className:"shadow-sm border-0 mt-8",children:e.jsxs(i,{className:"p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Pro Tips"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("strong",{children:"Set up job alerts"})," to get notified when similar positions are posted"]})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("strong",{children:"Apply within 48 hours"})," of a job being posted for better chances"]})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("strong",{children:"Research the company"})," before applying to tailor your application"]})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("strong",{children:"Keep your profile updated"})," to match with relevant opportunities"]})]})]})]})})]})})}const Q=()=>e.jsx(U,{});export{Q as default};
