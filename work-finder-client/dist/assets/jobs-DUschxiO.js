import{d as D,j as e,f as S,r as x,P as I,e as re,l as Ne,m as ke,n as we,o as le,q as Z,s as Ce,a as Se,c as Le}from"./index-Bxm2R2OW.js";import{l as $e,j as Re,m as q}from"./mock-data-BqVSz9O0.js";import{u as Fe,C as Me,a as J,b as U,c as K,d as z,e as _,S as Ee,I as Y}from"./select-454QdpBE.js";import{C as W,a as V,B as C}from"./badge-L1wjnFol.js";import{S as F}from"./separator-CR9qNBde.js";import{F as Pe,A as Ae,G as _e,L as De}from"./list-DXfJh1Mw.js";import{X as Ie}from"./x-Ci9JSY07.js";import{M as O}from"./map-pin-CKdZFLuR.js";import{C as ee,f as se}from"./formatDistanceToNow-DcQq3y4b.js";import{B as ae}from"./bookmark-CUB1O_PA.js";import{U as te}from"./users-CLiaPoiA.js";import{D as Te}from"./dollar-sign-12fYRf7A.js";import"./chevron-down-Bu8lQHGF.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Be=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],qe=D("chevron-right",Be);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],Ue=D("chevron-left",Je);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],ne=D("refresh-cw",Ke);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],ie=D("triangle-alert",ze);class Oe extends x.Component{constructor(r){super(r),this.state={hasError:!1}}static getDerivedStateFromError(r){return{hasError:!0,error:r}}componentDidCatch(r,l){this.setState({error:r,errorInfo:l}),this.props.onError?.(r,l)}handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})};render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-[400px] flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center max-w-md",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(ie,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),e.jsx("p",{className:"text-gray-600",children:"We're sorry, but something unexpected happened. Our team has been notified."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(S,{onClick:this.handleRetry,className:"w-full",children:[e.jsx(ne,{className:"h-4 w-4 mr-2"}),"Try Again"]}),e.jsx(S,{variant:"outline",onClick:()=>window.location.reload(),className:"w-full",children:"Reload Page"})]}),!1]})}):this.props.children}}const We=({children:s})=>e.jsx(Oe,{fallback:e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(ie,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Unable to load jobs"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"There was an error loading the job listings. Please try refreshing the page."}),e.jsxs(S,{onClick:()=>window.location.reload(),children:[e.jsx(ne,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),children:s});var Ve="Label",ce=x.forwardRef((s,r)=>e.jsx(I.label,{...s,ref:r,onMouseDown:l=>{l.target.closest("button, input, select, textarea")||(s.onMouseDown?.(l),!l.defaultPrevented&&l.detail>1&&l.preventDefault())}}));ce.displayName=Ve;var He=ce;function w({className:s,...r}){return e.jsx(He,{"data-slot":"label",className:re("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}var T="Checkbox",[Ge,js]=Ne(T),[Xe,H]=Ge(T);function Qe(s){const{__scopeCheckbox:r,checked:l,children:m,defaultChecked:o,disabled:c,form:t,name:d,onCheckedChange:f,required:v,value:y="on",internal_do_not_use_render:j}=s,[a,h]=we({prop:l,defaultProp:o??!1,onChange:f,caller:T}),[u,b]=x.useState(null),[N,g]=x.useState(null),k=x.useRef(!1),$=u?!!t||!!u.closest("form"):!0,M={checked:a,disabled:c,setChecked:h,control:u,setControl:b,name:d,form:t,value:y,hasConsumerStoppedPropagationRef:k,required:v,defaultChecked:L(o)?!1:o,isFormControl:$,bubbleInput:N,setBubbleInput:g};return e.jsx(Xe,{scope:r,...M,children:Ze(j)?j(M):m})}var oe="CheckboxTrigger",de=x.forwardRef(({__scopeCheckbox:s,onKeyDown:r,onClick:l,...m},o)=>{const{control:c,value:t,disabled:d,checked:f,required:v,setControl:y,setChecked:j,hasConsumerStoppedPropagationRef:a,isFormControl:h,bubbleInput:u}=H(oe,s),b=le(o,y),N=x.useRef(f);return x.useEffect(()=>{const g=c?.form;if(g){const k=()=>j(N.current);return g.addEventListener("reset",k),()=>g.removeEventListener("reset",k)}},[c,j]),e.jsx(I.button,{type:"button",role:"checkbox","aria-checked":L(f)?"mixed":f,"aria-required":v,"data-state":fe(f),"data-disabled":d?"":void 0,disabled:d,value:t,...m,ref:b,onKeyDown:Z(r,g=>{g.key==="Enter"&&g.preventDefault()}),onClick:Z(l,g=>{j(k=>L(k)?!0:!k),u&&h&&(a.current=g.isPropagationStopped(),a.current||g.stopPropagation())})})});de.displayName=oe;var me=x.forwardRef((s,r)=>{const{__scopeCheckbox:l,name:m,checked:o,defaultChecked:c,required:t,disabled:d,value:f,onCheckedChange:v,form:y,...j}=s;return e.jsx(Qe,{__scopeCheckbox:l,checked:o,defaultChecked:c,disabled:d,required:t,onCheckedChange:v,name:m,form:y,value:f,internal_do_not_use_render:({isFormControl:a})=>e.jsxs(e.Fragment,{children:[e.jsx(de,{...j,ref:r,__scopeCheckbox:l}),a&&e.jsx(pe,{__scopeCheckbox:l})]})})});me.displayName=T;var xe="CheckboxIndicator",he=x.forwardRef((s,r)=>{const{__scopeCheckbox:l,forceMount:m,...o}=s,c=H(xe,l);return e.jsx(ke,{present:m||L(c.checked)||c.checked===!0,children:e.jsx(I.span,{"data-state":fe(c.checked),"data-disabled":c.disabled?"":void 0,...o,ref:r,style:{pointerEvents:"none",...s.style}})})});he.displayName=xe;var ue="CheckboxBubbleInput",pe=x.forwardRef(({__scopeCheckbox:s,...r},l)=>{const{control:m,hasConsumerStoppedPropagationRef:o,checked:c,defaultChecked:t,required:d,disabled:f,name:v,value:y,form:j,bubbleInput:a,setBubbleInput:h}=H(ue,s),u=le(l,h),b=Fe(c),N=Ce(m);x.useEffect(()=>{const k=a;if(!k)return;const $=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor($,"checked").set,R=!o.current;if(b!==c&&E){const B=new Event("click",{bubbles:R});k.indeterminate=L(c),E.call(k,L(c)?!1:c),k.dispatchEvent(B)}},[a,b,c,o]);const g=x.useRef(L(c)?!1:c);return e.jsx(I.input,{type:"checkbox","aria-hidden":!0,defaultChecked:t??g.current,required:d,disabled:f,name:v,value:y,form:j,...r,tabIndex:-1,ref:u,style:{...r.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});pe.displayName=ue;function Ze(s){return typeof s=="function"}function L(s){return s==="indeterminate"}function fe(s){return L(s)?"indeterminate":s?"checked":"unchecked"}function A({className:s,...r}){return e.jsx(me,{"data-slot":"checkbox",className:re("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:e.jsx(he,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(Me,{className:"size-3.5"})})})}const Ye=({filters:s,onFiltersChange:r,onClearFilters:l,className:m=""})=>{const o=(a,h)=>{r({...s,[a]:h})},c=(a,h,u)=>{const b=s[a]||[],N=u?[...b,h]:b.filter(g=>g!==h);o(a,N.length>0?N:void 0)},t=()=>{let a=0;return s.type?.length&&a++,s.experienceLevel?.length&&a++,s.workLocation?.length&&a++,s.categories?.length&&a++,s.salaryMin&&a++,s.salaryMax&&a++,s.postedWithin&&s.postedWithin!=="all"&&a++,s.location&&a++,a},d=[{value:"full-time",label:"Full-time"},{value:"part-time",label:"Part-time"},{value:"contract",label:"Contract"},{value:"internship",label:"Internship"},{value:"freelance",label:"Freelance"}],f=[{value:"entry-level",label:"Entry Level"},{value:"mid-level",label:"Mid Level"},{value:"senior-level",label:"Senior Level"},{value:"executive",label:"Executive"}],v=[{value:"remote",label:"Remote"},{value:"hybrid",label:"Hybrid"},{value:"on-site",label:"On-site"}],y=[{value:"all",label:"Any time"},{value:"day",label:"Past 24 hours"},{value:"week",label:"Past week"},{value:"month",label:"Past month"}],j=[{min:0,max:5e4,label:"Under $50K"},{min:5e4,max:75e3,label:"$50K - $75K"},{min:75e3,max:1e5,label:"$75K - $100K"},{min:1e5,max:15e4,label:"$100K - $150K"},{min:15e4,max:2e5,label:"$150K - $200K"},{min:2e5,max:999999,label:"$200K+"}];return e.jsx(W,{className:`${m}`,children:e.jsxs(V,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Pe,{className:"h-5 w-5 mr-2 text-gray-600"}),e.jsx("h3",{className:"font-semibold text-lg",children:"Filters"}),t()>0&&e.jsx(C,{variant:"secondary",className:"ml-2",children:t()})]}),t()>0&&e.jsxs(S,{variant:"ghost",size:"sm",onClick:l,className:"text-gray-500 hover:text-gray-700",children:[e.jsx(Ie,{className:"h-4 w-4 mr-1"}),"Clear All"]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Location"}),e.jsxs(J,{value:s.location||"",onValueChange:a=>o("location",a||void 0),children:[e.jsx(U,{children:e.jsx(K,{placeholder:"Any location"})}),e.jsxs(z,{children:[e.jsx(_,{value:"",children:"Any location"}),e.jsx(_,{value:"remote",children:"Remote"}),$e.map(a=>e.jsx(_,{value:a,children:a},a))]})]})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Job Type"}),e.jsx("div",{className:"space-y-2",children:d.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`type-${a.value}`,checked:s.type?.includes(a.value)||!1,onCheckedChange:h=>c("type",a.value,h)}),e.jsx(w,{htmlFor:`type-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Experience Level"}),e.jsx("div",{className:"space-y-2",children:f.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`experience-${a.value}`,checked:s.experienceLevel?.includes(a.value)||!1,onCheckedChange:h=>c("experienceLevel",a.value,h)}),e.jsx(w,{htmlFor:`experience-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Work Arrangement"}),e.jsx("div",{className:"space-y-2",children:v.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`work-${a.value}`,checked:s.workLocation?.includes(a.value)||!1,onCheckedChange:h=>c("workLocation",a.value,h)}),e.jsx(w,{htmlFor:`work-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Categories"}),e.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:Re.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`category-${a}`,checked:s.categories?.includes(a)||!1,onCheckedChange:h=>c("categories",a,h)}),e.jsx(w,{htmlFor:`category-${a}`,className:"text-sm font-normal cursor-pointer",children:a})]},a))})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Salary Range"}),e.jsx("div",{className:"space-y-2",children:j.map((a,h)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{id:`salary-${h}`,checked:s.salaryMin===a.min&&s.salaryMax===a.max,onCheckedChange:u=>{u?(o("salaryMin",a.min),o("salaryMax",a.max)):(o("salaryMin",void 0),o("salaryMax",void 0))}}),e.jsx(w,{htmlFor:`salary-${h}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},h))})]}),e.jsx(F,{}),e.jsxs("div",{children:[e.jsx(w,{className:"text-sm font-medium mb-3 block",children:"Date Posted"}),e.jsxs(J,{value:s.postedWithin||"all",onValueChange:a=>o("postedWithin",a==="all"?void 0:a),children:[e.jsx(U,{children:e.jsx(K,{})}),e.jsx(z,{children:y.map(a=>e.jsx(_,{value:a.value,children:a.label},a.value))})]})]})]})]})})},es=({job:s,viewMode:r,onSaveJob:l,onViewJob:m})=>{const o=d=>{if(!d)return"Salary not specified";const{min:f,max:v}=d;return`$${(f/1e3).toFixed(0)}k - $${(v/1e3).toFixed(0)}k`},c=d=>d.location.isRemote?"Remote":`${d.location.city}, ${d.location.state}`,t=r==="list";return e.jsx(W,{className:`group hover:shadow-lg transition-all duration-300 border-0 shadow-md hover:-translate-y-1 bg-white cursor-pointer ${t?"mb-4":""}`,onClick:()=>m(s.id),children:e.jsxs(V,{className:`p-6 ${t?"flex items-center space-x-6":""}`,children:[e.jsxs("div",{className:`${t?"flex-shrink-0":"flex items-start justify-between mb-4"}`,children:[e.jsxs("div",{className:`flex items-center ${t?"space-x-4":"space-x-3"}`,children:[s.companyLogo&&e.jsx("img",{src:s.companyLogo,alt:s.companyName,className:`${t?"w-16 h-16":"w-12 h-12"} rounded-lg object-cover border`}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-semibold ${t?"text-xl":"text-lg"} text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1`,children:s.title}),e.jsx("p",{className:`text-gray-600 ${t?"text-base":"text-sm"}`,children:s.companyName}),t&&e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500 mt-1",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:c(s)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(ee,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:se(new Date(s.postedAt),{addSuffix:!0})})]})]})]})]}),!t&&e.jsx("button",{onClick:d=>{d.stopPropagation(),l?.(s.id)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(ae,{className:"h-4 w-4 text-gray-400 hover:text-blue-600"})})]}),e.jsx("div",{className:`${t?"flex-1":"space-y-3 mb-4"}`,children:t?e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Salary"}),e.jsx("div",{className:"font-medium",children:o(s.salary)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Type"}),e.jsx(C,{variant:"secondary",className:"text-xs",children:s.type})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Experience"}),e.jsx(C,{variant:"secondary",className:"text-xs",children:s.experienceLevel})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Applicants"}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(te,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:s.applicationsCount})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(O,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:c(s)}),s.workLocation!=="on-site"&&e.jsx(C,{variant:"secondary",className:"ml-2 text-xs",children:s.workLocation})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(Te,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:o(s.salary)})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(ee,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:se(new Date(s.postedAt),{addSuffix:!0})})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(te,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s.applicationsCount," applicants"]})]})]})}),!t&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:s.summary}),s.skills&&s.skills.length>0&&e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.skills.slice(0,3).map((d,f)=>e.jsx(C,{variant:"outline",className:"text-xs",children:d},f)),s.skills.length>3&&e.jsxs(C,{variant:"outline",className:"text-xs",children:["+",s.skills.length-3," more"]})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(C,{variant:"secondary",className:"text-xs",children:s.type}),e.jsx(C,{variant:"secondary",className:"text-xs",children:s.experienceLevel}),s.featured&&e.jsx(C,{className:"text-xs bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),s.urgent&&e.jsx(C,{className:"text-xs bg-red-100 text-red-800 border-red-200",children:"Urgent"})]})})]}),t&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:d=>{d.stopPropagation(),l?.(s.id)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(ae,{className:"h-5 w-5 text-gray-400 hover:text-blue-600"})}),e.jsxs("div",{className:"flex flex-col gap-1",children:[s.featured&&e.jsx(C,{className:"text-xs bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),s.urgent&&e.jsx(C,{className:"text-xs bg-red-100 text-red-800 border-red-200",children:"Urgent"})]})]})]})})},ss=({viewMode:s})=>{const r=s==="list";return e.jsx(W,{className:`border-0 shadow-md bg-white ${r?"mb-4":""}`,children:e.jsxs(V,{className:`p-6 ${r?"flex items-center space-x-6":""}`,children:[e.jsxs("div",{className:`${r?"flex-shrink-0":"flex items-start justify-between mb-4"}`,children:[e.jsxs("div",{className:`flex items-center ${r?"space-x-4":"space-x-3"}`,children:[e.jsx("div",{className:`${r?"w-16 h-16":"w-12 h-12"} bg-gray-200 rounded-lg animate-pulse`}),e.jsxs("div",{children:[e.jsx("div",{className:`${r?"w-48 h-6":"w-32 h-5"} bg-gray-200 rounded animate-pulse mb-2`}),e.jsx("div",{className:`${r?"w-32 h-5":"w-24 h-4"} bg-gray-200 rounded animate-pulse`})]})]}),!r&&e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"})]}),r?e.jsx("div",{className:"flex-1 grid grid-cols-2 lg:grid-cols-4 gap-4",children:Array.from({length:4}).map((l,m)=>e.jsxs("div",{children:[e.jsx("div",{className:"w-16 h-4 bg-gray-200 rounded animate-pulse mb-1"}),e.jsx("div",{className:"w-20 h-5 bg-gray-200 rounded animate-pulse"})]},m))}):e.jsx("div",{className:"space-y-3 mb-4",children:Array.from({length:4}).map((l,m)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-200 rounded animate-pulse mr-2"}),e.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse"})]},m))}),!r&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse mb-4"}),e.jsx("div",{className:"flex gap-2 mb-4",children:Array.from({length:3}).map((l,m)=>e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"},m))})]}),r&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"}),e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"})]})]})})},as=({jobs:s,totalJobs:r,currentPage:l,totalPages:m,isLoading:o=!1,viewMode:c,sortBy:t,onViewModeChange:d,onSortChange:f,onPageChange:v,onSaveJob:y})=>{const j=Se(),a=u=>{j(`/jobs/${u}`)},h=[{value:"relevance",label:"Most Relevant"},{value:"date",label:"Most Recent"},{value:"salary",label:"Highest Salary"},{value:"company",label:"Company A-Z"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[r.toLocaleString()," Jobs Found"]}),e.jsxs("p",{className:"text-gray-600",children:["Showing ",(l-1)*20+1," -"," ",Math.min(l*20,r)," of"," ",r.toLocaleString()," results"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ae,{className:"h-4 w-4 text-gray-500"}),e.jsxs(J,{value:t,onValueChange:f,children:[e.jsx(U,{className:"w-40",children:e.jsx(K,{})}),e.jsx(z,{children:h.map(u=>e.jsx(_,{value:u.value,children:u.label},u.value))})]})]}),e.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[e.jsx(S,{variant:c==="grid"?"default":"ghost",size:"sm",onClick:()=>d("grid"),className:"px-3",children:e.jsx(_e,{className:"h-4 w-4"})}),e.jsx(S,{variant:c==="list"?"default":"ghost",size:"sm",onClick:()=>d("list"),className:"px-3",children:e.jsx(De,{className:"h-4 w-4"})})]})]})]}),e.jsx("div",{className:c==="grid"?"grid grid-cols-1 lg:grid-cols-2 gap-6":"space-y-0",children:o?Array.from({length:10}).map((u,b)=>e.jsx(ss,{viewMode:c},b)):s.map(u=>e.jsx(es,{job:u,viewMode:c,onSaveJob:y,onViewJob:a},u.id))}),m>1&&e.jsxs("div",{className:"flex items-center justify-center gap-2 mt-8",children:[e.jsx(S,{variant:"outline",onClick:()=>v(l-1),disabled:l<=1,className:"px-3",children:e.jsx(Ue,{className:"h-4 w-4"})}),Array.from({length:Math.min(5,m)},(u,b)=>{const N=Math.max(1,Math.min(m-4,l-2))+b;return e.jsx(S,{variant:l===N?"default":"outline",onClick:()=>v(N),className:"px-3",children:N},N)}),e.jsx(S,{variant:"outline",onClick:()=>v(l+1),disabled:l>=m,className:"px-3",children:e.jsx(qe,{className:"h-4 w-4"})})]}),!o&&s.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No jobs found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters to see more results."})]})]})};function ts(){const[s,r]=x.useTransition(),l=x.useCallback(t=>{r(()=>{t()})},[r]),m=x.useCallback(t=>{r(()=>{t()})},[r]),o=x.useCallback(t=>{r(()=>{t()})},[r]),c=x.useCallback(t=>{r(()=>{t()})},[r]);return{isPending:s,startTransition:r,startNonUrgentUpdate:l,startFilterUpdate:m,startSortUpdate:o,startPaginationUpdate:c}}function rs(){const[s,r]=Le(),[l,m]=x.useState(s.get("q")||""),[o,c]=x.useState(s.get("location")||""),[t,d]=x.useState({query:s.get("q")||void 0,location:s.get("location")||void 0,categories:s.get("category")?[s.get("category")]:void 0,page:1,limit:20,sortBy:"relevance",sortOrder:"desc"}),[f,v]=x.useState("grid"),[y,j]=x.useState("relevance"),[a]=x.useState(!1),{isPending:h,startFilterUpdate:u,startSortUpdate:b,startPaginationUpdate:N}=ts(),[g,k]=x.useState(q),[$,M]=x.useState(q.length),[E,R]=x.useState(1),B=Math.ceil($/20);x.useEffect(()=>{let n=[...q];if(t.query){const i=t.query.toLowerCase();n=n.filter(p=>p.title.toLowerCase().includes(i)||p.companyName.toLowerCase().includes(i)||p.description.toLowerCase().includes(i)||p.skills.some(P=>P.toLowerCase().includes(i)))}if(t.location&&(n=n.filter(i=>t.location==="remote"?i.workLocation==="remote"||i.location.isRemote:`${i.location.city}, ${i.location.state}`===t.location)),t.type?.length&&(n=n.filter(i=>t.type.includes(i.type))),t.experienceLevel?.length&&(n=n.filter(i=>t.experienceLevel.includes(i.experienceLevel))),t.workLocation?.length&&(n=n.filter(i=>t.workLocation.includes(i.workLocation))),t.categories?.length&&(n=n.filter(i=>i.categories.some(p=>t.categories.includes(p)))),t.salaryMin&&t.salaryMax&&(n=n.filter(i=>i.salary?i.salary.min>=t.salaryMin&&i.salary.max<=t.salaryMax:!1)),t.postedWithin){const i=new Date,p=new Date;switch(t.postedWithin){case"day":p.setDate(i.getDate()-1);break;case"week":p.setDate(i.getDate()-7);break;case"month":p.setMonth(i.getMonth()-1);break}n=n.filter(P=>new Date(P.postedAt)>=p)}switch(y){case"date":n.sort((i,p)=>new Date(p.postedAt).getTime()-new Date(i.postedAt).getTime());break;case"salary":n.sort((i,p)=>{const P=i.salary?.max||0;return(p.salary?.max||0)-P});break;case"company":n.sort((i,p)=>i.companyName.localeCompare(p.companyName));break;default:n.sort((i,p)=>i.featured&&!p.featured?-1:!i.featured&&p.featured?1:i.applicationsCount-p.applicationsCount)}k(n),M(n.length)},[t,y]);const G=()=>{const n={...t,query:l||void 0,location:o||void 0,page:1};d(n),R(1);const i=new URLSearchParams;l&&i.set("q",l),o&&i.set("location",o),r(i)},ve=n=>{u(()=>{d(n),R(1)})},ge=()=>{u(()=>{const n={query:t.query,location:t.location,page:1,limit:20,sortBy:"relevance",sortOrder:"desc"};d(n),R(1)})},je=n=>{console.log("Save job:",n)},X=n=>{n.key==="Enter"&&G()},Q=(E-1)*20,ye=Q+20,be=g.slice(Q,ye);return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Find Jobs"}),e.jsx("p",{className:"text-lg text-gray-600",children:"Discover your next career opportunity from thousands of job listings"})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-8",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(Ee,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),e.jsx(Y,{placeholder:"Job title, keywords, or company",value:l,onChange:n=>m(n.target.value),onKeyPress:X,className:"pl-10 h-12"})]}),e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(O,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400 z-10"}),e.jsx(Y,{placeholder:"Location or 'Remote'",value:o,onChange:n=>c(n.target.value),onKeyPress:X,className:"pl-10 h-12"})]}),e.jsx(S,{onClick:G,className:"h-12 px-8 font-semibold",children:"Search Jobs"})]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[e.jsx("div",{className:"lg:w-80 flex-shrink-0",children:e.jsx(Ye,{filters:t,onFiltersChange:ve,onClearFilters:ge,className:"sticky top-4"})}),e.jsx("div",{className:"flex-1",children:e.jsx(We,{children:e.jsx(as,{jobs:be,totalJobs:$,currentPage:E,totalPages:B,isLoading:a||h,viewMode:f,sortBy:y,onViewModeChange:v,onSortChange:n=>b(()=>j(n)),onPageChange:n=>N(()=>R(n)),onSaveJob:je})})})]})]})})}const ys=()=>e.jsx(rs,{});export{ys as default};
