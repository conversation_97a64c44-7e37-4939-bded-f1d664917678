import{e as Le,r as n,j as a,h as C,P as N,i as Yo,k as Ye,l as Je,m as de,n as Jo,o as ue,q as oe,s as Xo,t as D,v as O,A as Qo,w as T,x as Xe,y as Qe,z as et,D as tt,E as ot,G as nt,H as at,I as rt,J as st,K as en,M as tn,Q as it,S as ct,T as on,U as nn,V as pe,W as an,X as rn,Y as sn}from"./index-BOPfWTHD.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]],lt=Le("chevron-down",cn);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ln=[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]],dn=Le("chevron-up",ln);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const un=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],pn=Le("x",un),fn=n.forwardRef(({className:e,type:o,...t},r)=>a.jsx("input",{type:o,className:C("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));fn.displayName="Input";var mn="Label",dt=n.forwardRef((e,o)=>a.jsx(N.label,{...e,ref:o,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));dt.displayName=mn;var ut=dt;const gn=Yo("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),hn=n.forwardRef(({className:e,...o},t)=>a.jsx(ut,{ref:t,className:C(gn(),e),...o}));hn.displayName=ut.displayName;function qe(e,[o,t]){return Math.min(t,Math.max(o,e))}function pt(e){const o=n.useRef({value:e,previous:e});return n.useMemo(()=>(o.current.value!==e&&(o.current.previous=o.current.value,o.current.value=e),o.current.previous),[e])}var ft=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),vn="VisuallyHidden",yn=n.forwardRef((e,o)=>a.jsx(N.span,{...e,ref:o,style:{...ft,...e.style}}));yn.displayName=vn;var xn=[" ","Enter","ArrowUp","ArrowDown"],bn=[" ","Enter"],ne="Select",[ve,ye,wn]=Xo(ne),[ie,Xa]=ue(ne,[wn,Ye]),xe=Ye(),[Sn,Y]=ie(ne),[Cn,Nn]=ie(ne),mt=e=>{const{__scopeSelect:o,children:t,open:r,defaultOpen:i,onOpenChange:s,value:c,defaultValue:l,onValueChange:d,dir:u,name:m,autoComplete:p,disabled:h,required:b,form:w}=e,f=xe(o),[v,x]=n.useState(null),[g,S]=n.useState(null),[M,_]=n.useState(!1),X=Je(u),[k,B]=de({prop:r,defaultProp:i??!1,onChange:s,caller:ne}),[z,Q]=de({prop:c,defaultProp:l,onChange:d,caller:ne}),U=n.useRef(null),V=v?w||!!v.closest("form"):!0,[G,H]=n.useState(new Set),W=Array.from(G).map(P=>P.props.value).join(";");return a.jsx(Jo,{...f,children:a.jsxs(Sn,{required:b,scope:o,trigger:v,onTriggerChange:x,valueNode:g,onValueNodeChange:S,valueNodeHasChildren:M,onValueNodeHasChildrenChange:_,contentId:oe(),value:z,onValueChange:Q,open:k,onOpenChange:B,dir:X,triggerPointerDownPosRef:U,disabled:h,children:[a.jsx(ve.Provider,{scope:o,children:a.jsx(Cn,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(P=>{H($=>new Set($).add(P))},[]),onNativeOptionRemove:n.useCallback(P=>{H($=>{const q=new Set($);return q.delete(P),q})},[]),children:t})}),V?a.jsxs(Ft,{"aria-hidden":!0,required:b,tabIndex:-1,name:m,autoComplete:p,value:z,onChange:P=>Q(P.target.value),disabled:h,form:w,children:[z===void 0?a.jsx("option",{value:""}):null,Array.from(G)]},W):null]})})};mt.displayName=ne;var gt="SelectTrigger",ht=n.forwardRef((e,o)=>{const{__scopeSelect:t,disabled:r=!1,...i}=e,s=xe(t),c=Y(gt,t),l=c.disabled||r,d=D(o,c.onTriggerChange),u=ye(t),m=n.useRef("touch"),[p,h,b]=$t(f=>{const v=u().filter(S=>!S.disabled),x=v.find(S=>S.value===c.value),g=Ut(v,f,x);g!==void 0&&c.onValueChange(g.value)}),w=f=>{l||(c.onOpenChange(!0),b()),f&&(c.triggerPointerDownPosRef.current={x:Math.round(f.pageX),y:Math.round(f.pageY)})};return a.jsx(Qo,{asChild:!0,...s,children:a.jsx(N.button,{type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Bt(c.value)?"":void 0,...i,ref:d,onClick:T(i.onClick,f=>{f.currentTarget.focus(),m.current!=="mouse"&&w(f)}),onPointerDown:T(i.onPointerDown,f=>{m.current=f.pointerType;const v=f.target;v.hasPointerCapture(f.pointerId)&&v.releasePointerCapture(f.pointerId),f.button===0&&f.ctrlKey===!1&&f.pointerType==="mouse"&&(w(f),f.preventDefault())}),onKeyDown:T(i.onKeyDown,f=>{const v=p.current!=="";!(f.ctrlKey||f.altKey||f.metaKey)&&f.key.length===1&&h(f.key),!(v&&f.key===" ")&&xn.includes(f.key)&&(w(),f.preventDefault())})})})});ht.displayName=gt;var vt="SelectValue",yt=n.forwardRef((e,o)=>{const{__scopeSelect:t,className:r,style:i,children:s,placeholder:c="",...l}=e,d=Y(vt,t),{onValueNodeHasChildrenChange:u}=d,m=s!==void 0,p=D(o,d.onValueNodeChange);return O(()=>{u(m)},[u,m]),a.jsx(N.span,{...l,ref:p,style:{pointerEvents:"none"},children:Bt(d.value)?a.jsx(a.Fragment,{children:c}):s})});yt.displayName=vt;var Tn="SelectIcon",xt=n.forwardRef((e,o)=>{const{__scopeSelect:t,children:r,...i}=e;return a.jsx(N.span,{"aria-hidden":!0,...i,ref:o,children:r||"▼"})});xt.displayName=Tn;var Rn="SelectPortal",bt=e=>a.jsx(Xe,{asChild:!0,...e});bt.displayName=Rn;var ae="SelectContent",wt=n.forwardRef((e,o)=>{const t=Y(ae,e.__scopeSelect),[r,i]=n.useState();if(O(()=>{i(new DocumentFragment)},[]),!t.open){const s=r;return s?Qe.createPortal(a.jsx(St,{scope:e.__scopeSelect,children:a.jsx(ve.Slot,{scope:e.__scopeSelect,children:a.jsx("div",{children:e.children})})}),s):null}return a.jsx(Ct,{...e,ref:o})});wt.displayName=ae;var L=10,[St,J]=ie(ae),In="SelectContentImpl",An=at("SelectContent.RemoveScroll"),Ct=n.forwardRef((e,o)=>{const{__scopeSelect:t,position:r="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:s,onPointerDownOutside:c,side:l,sideOffset:d,align:u,alignOffset:m,arrowPadding:p,collisionBoundary:h,collisionPadding:b,sticky:w,hideWhenDetached:f,avoidCollisions:v,...x}=e,g=Y(ae,t),[S,M]=n.useState(null),[_,X]=n.useState(null),k=D(o,y=>M(y)),[B,z]=n.useState(null),[Q,U]=n.useState(null),V=ye(t),[G,H]=n.useState(!1),W=n.useRef(!1);n.useEffect(()=>{if(S)return tt(S)},[S]),ot();const P=n.useCallback(y=>{const[A,...j]=V().map(E=>E.ref.current),[R]=j.slice(-1),I=document.activeElement;for(const E of y)if(E===I||(E?.scrollIntoView({block:"nearest"}),E===A&&_&&(_.scrollTop=0),E===R&&_&&(_.scrollTop=_.scrollHeight),E?.focus(),document.activeElement!==I))return},[V,_]),$=n.useCallback(()=>P([B,S]),[P,B,S]);n.useEffect(()=>{G&&$()},[G,$]);const{onOpenChange:q,triggerPointerDownPosRef:Z}=g;n.useEffect(()=>{if(S){let y={x:0,y:0};const A=R=>{y={x:Math.abs(Math.round(R.pageX)-(Z.current?.x??0)),y:Math.abs(Math.round(R.pageY)-(Z.current?.y??0))}},j=R=>{y.x<=10&&y.y<=10?R.preventDefault():S.contains(R.target)||q(!1),document.removeEventListener("pointermove",A),Z.current=null};return Z.current!==null&&(document.addEventListener("pointermove",A),document.addEventListener("pointerup",j,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",A),document.removeEventListener("pointerup",j,{capture:!0})}}},[S,q,Z]),n.useEffect(()=>{const y=()=>q(!1);return window.addEventListener("blur",y),window.addEventListener("resize",y),()=>{window.removeEventListener("blur",y),window.removeEventListener("resize",y)}},[q]);const[Ce,fe]=$t(y=>{const A=V().filter(I=>!I.disabled),j=A.find(I=>I.ref.current===document.activeElement),R=Ut(A,y,j);R&&setTimeout(()=>R.ref.current.focus())}),Ne=n.useCallback((y,A,j)=>{const R=!W.current&&!j;(g.value!==void 0&&g.value===A||R)&&(z(y),R&&(W.current=!0))},[g.value]),Te=n.useCallback(()=>S?.focus(),[S]),se=n.useCallback((y,A,j)=>{const R=!W.current&&!j;(g.value!==void 0&&g.value===A||R)&&U(y)},[g.value]),me=r==="popper"?De:Nt,ce=me===De?{side:l,sideOffset:d,align:u,alignOffset:m,arrowPadding:p,collisionBoundary:h,collisionPadding:b,sticky:w,hideWhenDetached:f,avoidCollisions:v}:{};return a.jsx(St,{scope:t,content:S,viewport:_,onViewportChange:X,itemRefCallback:Ne,selectedItem:B,onItemLeave:Te,itemTextRefCallback:se,focusSelectedItem:$,selectedItemText:Q,position:r,isPositioned:G,searchRef:Ce,children:a.jsx(nt,{as:An,allowPinchZoom:!0,children:a.jsx(rt,{asChild:!0,trapped:g.open,onMountAutoFocus:y=>{y.preventDefault()},onUnmountAutoFocus:T(i,y=>{g.trigger?.focus({preventScroll:!0}),y.preventDefault()}),children:a.jsx(st,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:y=>y.preventDefault(),onDismiss:()=>g.onOpenChange(!1),children:a.jsx(me,{role:"listbox",id:g.contentId,"data-state":g.open?"open":"closed",dir:g.dir,onContextMenu:y=>y.preventDefault(),...x,...ce,onPlaced:()=>H(!0),ref:k,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:T(x.onKeyDown,y=>{const A=y.ctrlKey||y.altKey||y.metaKey;if(y.key==="Tab"&&y.preventDefault(),!A&&y.key.length===1&&fe(y.key),["ArrowUp","ArrowDown","Home","End"].includes(y.key)){let R=V().filter(I=>!I.disabled).map(I=>I.ref.current);if(["ArrowUp","End"].includes(y.key)&&(R=R.slice().reverse()),["ArrowUp","ArrowDown"].includes(y.key)){const I=y.target,E=R.indexOf(I);R=R.slice(E+1)}setTimeout(()=>P(R)),y.preventDefault()}})})})})})})});Ct.displayName=In;var En="SelectItemAlignedPosition",Nt=n.forwardRef((e,o)=>{const{__scopeSelect:t,onPlaced:r,...i}=e,s=Y(ae,t),c=J(ae,t),[l,d]=n.useState(null),[u,m]=n.useState(null),p=D(o,k=>m(k)),h=ye(t),b=n.useRef(!1),w=n.useRef(!0),{viewport:f,selectedItem:v,selectedItemText:x,focusSelectedItem:g}=c,S=n.useCallback(()=>{if(s.trigger&&s.valueNode&&l&&u&&f&&v&&x){const k=s.trigger.getBoundingClientRect(),B=u.getBoundingClientRect(),z=s.valueNode.getBoundingClientRect(),Q=x.getBoundingClientRect();if(s.dir!=="rtl"){const I=Q.left-B.left,E=z.left-I,ee=k.left-E,te=k.width+ee,Re=Math.max(te,B.width),Ie=window.innerWidth-L,Ae=qe(E,[L,Math.max(L,Ie-Re)]);l.style.minWidth=te+"px",l.style.left=Ae+"px"}else{const I=B.right-Q.right,E=window.innerWidth-z.right-I,ee=window.innerWidth-k.right-E,te=k.width+ee,Re=Math.max(te,B.width),Ie=window.innerWidth-L,Ae=qe(E,[L,Math.max(L,Ie-Re)]);l.style.minWidth=te+"px",l.style.right=Ae+"px"}const U=h(),V=window.innerHeight-L*2,G=f.scrollHeight,H=window.getComputedStyle(u),W=parseInt(H.borderTopWidth,10),P=parseInt(H.paddingTop,10),$=parseInt(H.borderBottomWidth,10),q=parseInt(H.paddingBottom,10),Z=W+P+G+q+$,Ce=Math.min(v.offsetHeight*5,Z),fe=window.getComputedStyle(f),Ne=parseInt(fe.paddingTop,10),Te=parseInt(fe.paddingBottom,10),se=k.top+k.height/2-L,me=V-se,ce=v.offsetHeight/2,y=v.offsetTop+ce,A=W+P+y,j=Z-A;if(A<=se){const I=U.length>0&&v===U[U.length-1].ref.current;l.style.bottom="0px";const E=u.clientHeight-f.offsetTop-f.offsetHeight,ee=Math.max(me,ce+(I?Te:0)+E+$),te=A+ee;l.style.height=te+"px"}else{const I=U.length>0&&v===U[0].ref.current;l.style.top="0px";const ee=Math.max(se,W+f.offsetTop+(I?Ne:0)+ce)+j;l.style.height=ee+"px",f.scrollTop=A-se+f.offsetTop}l.style.margin=`${L}px 0`,l.style.minHeight=Ce+"px",l.style.maxHeight=V+"px",r?.(),requestAnimationFrame(()=>b.current=!0)}},[h,s.trigger,s.valueNode,l,u,f,v,x,s.dir,r]);O(()=>S(),[S]);const[M,_]=n.useState();O(()=>{u&&_(window.getComputedStyle(u).zIndex)},[u]);const X=n.useCallback(k=>{k&&w.current===!0&&(S(),g?.(),w.current=!1)},[S,g]);return a.jsx(Dn,{scope:t,contentWrapper:l,shouldExpandOnScrollRef:b,onScrollButtonChange:X,children:a.jsx("div",{ref:d,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:M},children:a.jsx(N.div,{...i,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});Nt.displayName=En;var kn="SelectPopperPosition",De=n.forwardRef((e,o)=>{const{__scopeSelect:t,align:r="start",collisionPadding:i=L,...s}=e,c=xe(t);return a.jsx(en,{...c,...s,ref:o,align:r,collisionPadding:i,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});De.displayName=kn;var[Dn,Oe]=ie(ae,{}),_e="SelectViewport",Tt=n.forwardRef((e,o)=>{const{__scopeSelect:t,nonce:r,...i}=e,s=J(_e,t),c=Oe(_e,t),l=D(o,s.onViewportChange),d=n.useRef(0);return a.jsxs(a.Fragment,{children:[a.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),a.jsx(ve.Slot,{scope:t,children:a.jsx(N.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:T(i.onScroll,u=>{const m=u.currentTarget,{contentWrapper:p,shouldExpandOnScrollRef:h}=c;if(h?.current&&p){const b=Math.abs(d.current-m.scrollTop);if(b>0){const w=window.innerHeight-L*2,f=parseFloat(p.style.minHeight),v=parseFloat(p.style.height),x=Math.max(f,v);if(x<w){const g=x+b,S=Math.min(w,g),M=g-S;p.style.height=S+"px",p.style.bottom==="0px"&&(m.scrollTop=M>0?M:0,p.style.justifyContent="flex-end")}}}d.current=m.scrollTop})})})]})});Tt.displayName=_e;var Rt="SelectGroup",[_n,Pn]=ie(Rt),jn=n.forwardRef((e,o)=>{const{__scopeSelect:t,...r}=e,i=oe();return a.jsx(_n,{scope:t,id:i,children:a.jsx(N.div,{role:"group","aria-labelledby":i,...r,ref:o})})});jn.displayName=Rt;var It="SelectLabel",At=n.forwardRef((e,o)=>{const{__scopeSelect:t,...r}=e,i=Pn(It,t);return a.jsx(N.div,{id:i.id,...r,ref:o})});At.displayName=It;var ge="SelectItem",[Mn,Et]=ie(ge),kt=n.forwardRef((e,o)=>{const{__scopeSelect:t,value:r,disabled:i=!1,textValue:s,...c}=e,l=Y(ge,t),d=J(ge,t),u=l.value===r,[m,p]=n.useState(s??""),[h,b]=n.useState(!1),w=D(o,g=>d.itemRefCallback?.(g,r,i)),f=oe(),v=n.useRef("touch"),x=()=>{i||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return a.jsx(Mn,{scope:t,value:r,disabled:i,textId:f,isSelected:u,onItemTextChange:n.useCallback(g=>{p(S=>S||(g?.textContent??"").trim())},[]),children:a.jsx(ve.ItemSlot,{scope:t,value:r,disabled:i,textValue:m,children:a.jsx(N.div,{role:"option","aria-labelledby":f,"data-highlighted":h?"":void 0,"aria-selected":u&&h,"data-state":u?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...c,ref:w,onFocus:T(c.onFocus,()=>b(!0)),onBlur:T(c.onBlur,()=>b(!1)),onClick:T(c.onClick,()=>{v.current!=="mouse"&&x()}),onPointerUp:T(c.onPointerUp,()=>{v.current==="mouse"&&x()}),onPointerDown:T(c.onPointerDown,g=>{v.current=g.pointerType}),onPointerMove:T(c.onPointerMove,g=>{v.current=g.pointerType,i?d.onItemLeave?.():v.current==="mouse"&&g.currentTarget.focus({preventScroll:!0})}),onPointerLeave:T(c.onPointerLeave,g=>{g.currentTarget===document.activeElement&&d.onItemLeave?.()}),onKeyDown:T(c.onKeyDown,g=>{d.searchRef?.current!==""&&g.key===" "||(bn.includes(g.key)&&x(),g.key===" "&&g.preventDefault())})})})})});kt.displayName=ge;var le="SelectItemText",Dt=n.forwardRef((e,o)=>{const{__scopeSelect:t,className:r,style:i,...s}=e,c=Y(le,t),l=J(le,t),d=Et(le,t),u=Nn(le,t),[m,p]=n.useState(null),h=D(o,x=>p(x),d.onItemTextChange,x=>l.itemTextRefCallback?.(x,d.value,d.disabled)),b=m?.textContent,w=n.useMemo(()=>a.jsx("option",{value:d.value,disabled:d.disabled,children:b},d.value),[d.disabled,d.value,b]),{onNativeOptionAdd:f,onNativeOptionRemove:v}=u;return O(()=>(f(w),()=>v(w)),[f,v,w]),a.jsxs(a.Fragment,{children:[a.jsx(N.span,{id:d.textId,...s,ref:h}),d.isSelected&&c.valueNode&&!c.valueNodeHasChildren?Qe.createPortal(s.children,c.valueNode):null]})});Dt.displayName=le;var _t="SelectItemIndicator",Pt=n.forwardRef((e,o)=>{const{__scopeSelect:t,...r}=e;return Et(_t,t).isSelected?a.jsx(N.span,{"aria-hidden":!0,...r,ref:o}):null});Pt.displayName=_t;var Pe="SelectScrollUpButton",jt=n.forwardRef((e,o)=>{const t=J(Pe,e.__scopeSelect),r=Oe(Pe,e.__scopeSelect),[i,s]=n.useState(!1),c=D(o,r.onScrollButtonChange);return O(()=>{if(t.viewport&&t.isPositioned){let l=function(){const u=d.scrollTop>0;s(u)};const d=t.viewport;return l(),d.addEventListener("scroll",l),()=>d.removeEventListener("scroll",l)}},[t.viewport,t.isPositioned]),i?a.jsx(Lt,{...e,ref:c,onAutoScroll:()=>{const{viewport:l,selectedItem:d}=t;l&&d&&(l.scrollTop=l.scrollTop-d.offsetHeight)}}):null});jt.displayName=Pe;var je="SelectScrollDownButton",Mt=n.forwardRef((e,o)=>{const t=J(je,e.__scopeSelect),r=Oe(je,e.__scopeSelect),[i,s]=n.useState(!1),c=D(o,r.onScrollButtonChange);return O(()=>{if(t.viewport&&t.isPositioned){let l=function(){const u=d.scrollHeight-d.clientHeight,m=Math.ceil(d.scrollTop)<u;s(m)};const d=t.viewport;return l(),d.addEventListener("scroll",l),()=>d.removeEventListener("scroll",l)}},[t.viewport,t.isPositioned]),i?a.jsx(Lt,{...e,ref:c,onAutoScroll:()=>{const{viewport:l,selectedItem:d}=t;l&&d&&(l.scrollTop=l.scrollTop+d.offsetHeight)}}):null});Mt.displayName=je;var Lt=n.forwardRef((e,o)=>{const{__scopeSelect:t,onAutoScroll:r,...i}=e,s=J("SelectScrollButton",t),c=n.useRef(null),l=ye(t),d=n.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),O(()=>{l().find(m=>m.ref.current===document.activeElement)?.ref.current?.scrollIntoView({block:"nearest"})},[l]),a.jsx(N.div,{"aria-hidden":!0,...i,ref:o,style:{flexShrink:0,...i.style},onPointerDown:T(i.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(r,50))}),onPointerMove:T(i.onPointerMove,()=>{s.onItemLeave?.(),c.current===null&&(c.current=window.setInterval(r,50))}),onPointerLeave:T(i.onPointerLeave,()=>{d()})})}),Ln="SelectSeparator",Ot=n.forwardRef((e,o)=>{const{__scopeSelect:t,...r}=e;return a.jsx(N.div,{"aria-hidden":!0,...r,ref:o})});Ot.displayName=Ln;var Me="SelectArrow",On=n.forwardRef((e,o)=>{const{__scopeSelect:t,...r}=e,i=xe(t),s=Y(Me,t),c=J(Me,t);return s.open&&c.position==="popper"?a.jsx(tn,{...i,...r,ref:o}):null});On.displayName=Me;var Fn="SelectBubbleInput",Ft=n.forwardRef(({__scopeSelect:e,value:o,...t},r)=>{const i=n.useRef(null),s=D(r,i),c=pt(o);return n.useEffect(()=>{const l=i.current;if(!l)return;const d=window.HTMLSelectElement.prototype,m=Object.getOwnPropertyDescriptor(d,"value").set;if(c!==o&&m){const p=new Event("change",{bubbles:!0});m.call(l,o),l.dispatchEvent(p)}},[c,o]),a.jsx(N.select,{...t,style:{...ft,...t.style},ref:s,defaultValue:o})});Ft.displayName=Fn;function Bt(e){return e===""||e===void 0}function $t(e){const o=et(e),t=n.useRef(""),r=n.useRef(0),i=n.useCallback(c=>{const l=t.current+c;o(l),function d(u){t.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>d(""),1e3))}(l)},[o]),s=n.useCallback(()=>{t.current="",window.clearTimeout(r.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(r.current),[]),[t,i,s]}function Ut(e,o,t){const i=o.length>1&&Array.from(o).every(u=>u===o[0])?o[0]:o,s=t?e.indexOf(t):-1;let c=Bn(e,Math.max(s,0));i.length===1&&(c=c.filter(u=>u!==t));const d=c.find(u=>u.textValue.toLowerCase().startsWith(i.toLowerCase()));return d!==t?d:void 0}function Bn(e,o){return e.map((t,r)=>e[(o+r)%e.length])}var $n=mt,Vt=ht,Un=yt,Vn=xt,Hn=bt,Ht=wt,Wn=Tt,Wt=At,qt=kt,qn=Dt,zn=Pt,zt=jt,Gt=Mt,Zt=Ot;const Qa=$n,er=Un,Gn=n.forwardRef(({className:e,children:o,...t},r)=>a.jsxs(Vt,{ref:r,className:C("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[o,a.jsx(Vn,{asChild:!0,children:a.jsx(lt,{className:"h-4 w-4 opacity-50"})})]}));Gn.displayName=Vt.displayName;const Kt=n.forwardRef(({className:e,...o},t)=>a.jsx(zt,{ref:t,className:C("flex cursor-default items-center justify-center py-1",e),...o,children:a.jsx(dn,{className:"h-4 w-4"})}));Kt.displayName=zt.displayName;const Yt=n.forwardRef(({className:e,...o},t)=>a.jsx(Gt,{ref:t,className:C("flex cursor-default items-center justify-center py-1",e),...o,children:a.jsx(lt,{className:"h-4 w-4"})}));Yt.displayName=Gt.displayName;const Zn=n.forwardRef(({className:e,children:o,position:t="popper",...r},i)=>a.jsx(Hn,{children:a.jsxs(Ht,{ref:i,className:C("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[a.jsx(Kt,{}),a.jsx(Wn,{className:C("p-1",t==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:o}),a.jsx(Yt,{})]})}));Zn.displayName=Ht.displayName;const Kn=n.forwardRef(({className:e,...o},t)=>a.jsx(Wt,{ref:t,className:C("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...o}));Kn.displayName=Wt.displayName;const Yn=n.forwardRef(({className:e,children:o,...t},r)=>a.jsxs(qt,{ref:r,className:C("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(zn,{children:a.jsx(it,{className:"h-4 w-4"})})}),a.jsx(qn,{children:o})]}));Yn.displayName=qt.displayName;const Jn=n.forwardRef(({className:e,...o},t)=>a.jsx(Zt,{ref:t,className:C("-mx-1 my-1 h-px bg-muted",e),...o}));Jn.displayName=Zt.displayName;const Xn=n.forwardRef(({className:e,...o},t)=>a.jsx("div",{ref:t,className:C("rounded-lg border bg-card text-card-foreground shadow-sm",e),...o}));Xn.displayName="Card";const Qn=n.forwardRef(({className:e,...o},t)=>a.jsx("div",{ref:t,className:C("flex flex-col space-y-1.5 p-6",e),...o}));Qn.displayName="CardHeader";const ea=n.forwardRef(({className:e,...o},t)=>a.jsx("h3",{ref:t,className:C("text-2xl font-semibold leading-none tracking-tight",e),...o}));ea.displayName="CardTitle";const ta=n.forwardRef(({className:e,...o},t)=>a.jsx("p",{ref:t,className:C("text-sm text-muted-foreground",e),...o}));ta.displayName="CardDescription";const oa=n.forwardRef(({className:e,...o},t)=>a.jsx("div",{ref:t,className:C("p-6 pt-0",e),...o}));oa.displayName="CardContent";const na=n.forwardRef(({className:e,...o},t)=>a.jsx("div",{ref:t,className:C("flex items-center p-6 pt-0",e),...o}));na.displayName="CardFooter";var be="Tabs",[aa,tr]=ue(be,[ct]),Jt=ct(),[ra,Fe]=aa(be),Xt=n.forwardRef((e,o)=>{const{__scopeTabs:t,value:r,onValueChange:i,defaultValue:s,orientation:c="horizontal",dir:l,activationMode:d="automatic",...u}=e,m=Je(l),[p,h]=de({prop:r,onChange:i,defaultProp:s??"",caller:be});return a.jsx(ra,{scope:t,baseId:oe(),value:p,onValueChange:h,orientation:c,dir:m,activationMode:d,children:a.jsx(N.div,{dir:m,"data-orientation":c,...u,ref:o})})});Xt.displayName=be;var Qt="TabsList",eo=n.forwardRef((e,o)=>{const{__scopeTabs:t,loop:r=!0,...i}=e,s=Fe(Qt,t),c=Jt(t);return a.jsx(on,{asChild:!0,...c,orientation:s.orientation,dir:s.dir,loop:r,children:a.jsx(N.div,{role:"tablist","aria-orientation":s.orientation,...i,ref:o})})});eo.displayName=Qt;var to="TabsTrigger",oo=n.forwardRef((e,o)=>{const{__scopeTabs:t,value:r,disabled:i=!1,...s}=e,c=Fe(to,t),l=Jt(t),d=ro(c.baseId,r),u=so(c.baseId,r),m=r===c.value;return a.jsx(nn,{asChild:!0,...l,focusable:!i,active:m,children:a.jsx(N.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...s,ref:o,onMouseDown:T(e.onMouseDown,p=>{!i&&p.button===0&&p.ctrlKey===!1?c.onValueChange(r):p.preventDefault()}),onKeyDown:T(e.onKeyDown,p=>{[" ","Enter"].includes(p.key)&&c.onValueChange(r)}),onFocus:T(e.onFocus,()=>{const p=c.activationMode!=="manual";!m&&!i&&p&&c.onValueChange(r)})})})});oo.displayName=to;var no="TabsContent",ao=n.forwardRef((e,o)=>{const{__scopeTabs:t,value:r,forceMount:i,children:s,...c}=e,l=Fe(no,t),d=ro(l.baseId,r),u=so(l.baseId,r),m=r===l.value,p=n.useRef(m);return n.useEffect(()=>{const h=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(h)},[]),a.jsx(pe,{present:i||m,children:({present:h})=>a.jsx(N.div,{"data-state":m?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!h,id:u,tabIndex:0,...c,ref:o,style:{...e.style,animationDuration:p.current?"0s":void 0},children:h&&s})})});ao.displayName=no;function ro(e,o){return`${e}-trigger-${o}`}function so(e,o){return`${e}-content-${o}`}var sa=Xt,io=eo,co=oo,lo=ao;const or=sa,ia=n.forwardRef(({className:e,...o},t)=>a.jsx(io,{ref:t,className:C("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...o}));ia.displayName=io.displayName;const ca=n.forwardRef(({className:e,...o},t)=>a.jsx(co,{ref:t,className:C("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...o}));ca.displayName=co.displayName;const la=n.forwardRef(({className:e,...o},t)=>a.jsx(lo,{ref:t,className:C("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...o}));la.displayName=lo.displayName;var Ee={exports:{}},ke={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ze;function da(){if(ze)return ke;ze=1;var e=an();function o(p,h){return p===h&&(p!==0||1/p===1/h)||p!==p&&h!==h}var t=typeof Object.is=="function"?Object.is:o,r=e.useState,i=e.useEffect,s=e.useLayoutEffect,c=e.useDebugValue;function l(p,h){var b=h(),w=r({inst:{value:b,getSnapshot:h}}),f=w[0].inst,v=w[1];return s(function(){f.value=b,f.getSnapshot=h,d(f)&&v({inst:f})},[p,b,h]),i(function(){return d(f)&&v({inst:f}),p(function(){d(f)&&v({inst:f})})},[p]),c(b),b}function d(p){var h=p.getSnapshot;p=p.value;try{var b=h();return!t(p,b)}catch{return!0}}function u(p,h){return h()}var m=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?u:l;return ke.useSyncExternalStore=e.useSyncExternalStore!==void 0?e.useSyncExternalStore:m,ke}var Ge;function ua(){return Ge||(Ge=1,Ee.exports=da()),Ee.exports}var pa=ua();function fa(){return pa.useSyncExternalStore(ma,()=>!0,()=>!1)}function ma(){return()=>{}}var Be="Avatar",[ga,nr]=ue(Be),[ha,uo]=ga(Be),po=n.forwardRef((e,o)=>{const{__scopeAvatar:t,...r}=e,[i,s]=n.useState("idle");return a.jsx(ha,{scope:t,imageLoadingStatus:i,onImageLoadingStatusChange:s,children:a.jsx(N.span,{...r,ref:o})})});po.displayName=Be;var fo="AvatarImage",mo=n.forwardRef((e,o)=>{const{__scopeAvatar:t,src:r,onLoadingStatusChange:i=()=>{},...s}=e,c=uo(fo,t),l=va(r,s),d=et(u=>{i(u),c.onImageLoadingStatusChange(u)});return O(()=>{l!=="idle"&&d(l)},[l,d]),l==="loaded"?a.jsx(N.img,{...s,ref:o,src:r}):null});mo.displayName=fo;var go="AvatarFallback",ho=n.forwardRef((e,o)=>{const{__scopeAvatar:t,delayMs:r,...i}=e,s=uo(go,t),[c,l]=n.useState(r===void 0);return n.useEffect(()=>{if(r!==void 0){const d=window.setTimeout(()=>l(!0),r);return()=>window.clearTimeout(d)}},[r]),c&&s.imageLoadingStatus!=="loaded"?a.jsx(N.span,{...i,ref:o}):null});ho.displayName=go;function Ze(e,o){return e?o?(e.src!==o&&(e.src=o),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}function va(e,{referrerPolicy:o,crossOrigin:t}){const r=fa(),i=n.useRef(null),s=r?(i.current||(i.current=new window.Image),i.current):null,[c,l]=n.useState(()=>Ze(s,e));return O(()=>{l(Ze(s,e))},[s,e]),O(()=>{const d=p=>()=>{l(p)};if(!s)return;const u=d("loaded"),m=d("error");return s.addEventListener("load",u),s.addEventListener("error",m),o&&(s.referrerPolicy=o),typeof t=="string"&&(s.crossOrigin=t),()=>{s.removeEventListener("load",u),s.removeEventListener("error",m)}},[s,t,o]),c}var vo=po,yo=mo,xo=ho;const ya=n.forwardRef(({className:e,...o},t)=>a.jsx(vo,{ref:t,className:C("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...o}));ya.displayName=vo.displayName;const xa=n.forwardRef(({className:e,...o},t)=>a.jsx(yo,{ref:t,className:C("aspect-square h-full w-full",e),...o}));xa.displayName=yo.displayName;const ba=n.forwardRef(({className:e,...o},t)=>a.jsx(xo,{ref:t,className:C("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...o}));ba.displayName=xo.displayName;var we="Checkbox",[wa,ar]=ue(we),[Sa,$e]=wa(we);function Ca(e){const{__scopeCheckbox:o,checked:t,children:r,defaultChecked:i,disabled:s,form:c,name:l,onCheckedChange:d,required:u,value:m="on",internal_do_not_use_render:p}=e,[h,b]=de({prop:t,defaultProp:i??!1,onChange:d,caller:we}),[w,f]=n.useState(null),[v,x]=n.useState(null),g=n.useRef(!1),S=w?!!c||!!w.closest("form"):!0,M={checked:h,disabled:s,setChecked:b,control:w,setControl:f,name:l,form:c,value:m,hasConsumerStoppedPropagationRef:g,required:u,defaultChecked:K(i)?!1:i,isFormControl:S,bubbleInput:v,setBubbleInput:x};return a.jsx(Sa,{scope:o,...M,children:Na(p)?p(M):r})}var bo="CheckboxTrigger",wo=n.forwardRef(({__scopeCheckbox:e,onKeyDown:o,onClick:t,...r},i)=>{const{control:s,value:c,disabled:l,checked:d,required:u,setControl:m,setChecked:p,hasConsumerStoppedPropagationRef:h,isFormControl:b,bubbleInput:w}=$e(bo,e),f=D(i,m),v=n.useRef(d);return n.useEffect(()=>{const x=s?.form;if(x){const g=()=>p(v.current);return x.addEventListener("reset",g),()=>x.removeEventListener("reset",g)}},[s,p]),a.jsx(N.button,{type:"button",role:"checkbox","aria-checked":K(d)?"mixed":d,"aria-required":u,"data-state":Ro(d),"data-disabled":l?"":void 0,disabled:l,value:c,...r,ref:f,onKeyDown:T(o,x=>{x.key==="Enter"&&x.preventDefault()}),onClick:T(t,x=>{p(g=>K(g)?!0:!g),w&&b&&(h.current=x.isPropagationStopped(),h.current||x.stopPropagation())})})});wo.displayName=bo;var Ue=n.forwardRef((e,o)=>{const{__scopeCheckbox:t,name:r,checked:i,defaultChecked:s,required:c,disabled:l,value:d,onCheckedChange:u,form:m,...p}=e;return a.jsx(Ca,{__scopeCheckbox:t,checked:i,defaultChecked:s,disabled:l,required:c,onCheckedChange:u,name:r,form:m,value:d,internal_do_not_use_render:({isFormControl:h})=>a.jsxs(a.Fragment,{children:[a.jsx(wo,{...p,ref:o,__scopeCheckbox:t}),h&&a.jsx(To,{__scopeCheckbox:t})]})})});Ue.displayName=we;var So="CheckboxIndicator",Co=n.forwardRef((e,o)=>{const{__scopeCheckbox:t,forceMount:r,...i}=e,s=$e(So,t);return a.jsx(pe,{present:r||K(s.checked)||s.checked===!0,children:a.jsx(N.span,{"data-state":Ro(s.checked),"data-disabled":s.disabled?"":void 0,...i,ref:o,style:{pointerEvents:"none",...e.style}})})});Co.displayName=So;var No="CheckboxBubbleInput",To=n.forwardRef(({__scopeCheckbox:e,...o},t)=>{const{control:r,hasConsumerStoppedPropagationRef:i,checked:s,defaultChecked:c,required:l,disabled:d,name:u,value:m,form:p,bubbleInput:h,setBubbleInput:b}=$e(No,e),w=D(t,b),f=pt(s),v=rn(r);n.useEffect(()=>{const g=h;if(!g)return;const S=window.HTMLInputElement.prototype,_=Object.getOwnPropertyDescriptor(S,"checked").set,X=!i.current;if(f!==s&&_){const k=new Event("click",{bubbles:X});g.indeterminate=K(s),_.call(g,K(s)?!1:s),g.dispatchEvent(k)}},[h,f,s,i]);const x=n.useRef(K(s)?!1:s);return a.jsx(N.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??x.current,required:l,disabled:d,name:u,value:m,form:p,...o,tabIndex:-1,ref:w,style:{...o.style,...v,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});To.displayName=No;function Na(e){return typeof e=="function"}function K(e){return e==="indeterminate"}function Ro(e){return K(e)?"indeterminate":e?"checked":"unchecked"}const Ta=n.forwardRef(({className:e,...o},t)=>a.jsx(Ue,{ref:t,className:C("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...o,children:a.jsx(Co,{className:C("flex items-center justify-center text-current"),children:a.jsx(it,{className:"h-4 w-4"})})}));Ta.displayName=Ue.displayName;var Ra="Separator",Ke="horizontal",Ia=["horizontal","vertical"],Io=n.forwardRef((e,o)=>{const{decorative:t,orientation:r=Ke,...i}=e,s=Aa(r)?r:Ke,l=t?{role:"none"}:{"aria-orientation":s==="vertical"?s:void 0,role:"separator"};return a.jsx(N.div,{"data-orientation":s,...l,...i,ref:o})});Io.displayName=Ra;function Aa(e){return Ia.includes(e)}var Ao=Io;const Ea=n.forwardRef(({className:e,orientation:o="horizontal",decorative:t=!0,...r},i)=>a.jsx(Ao,{ref:i,decorative:t,orientation:o,className:C("shrink-0 bg-border",o==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",e),...r}));Ea.displayName=Ao.displayName;var Se="Dialog",[Eo,rr]=ue(Se),[ka,F]=Eo(Se),ko=e=>{const{__scopeDialog:o,children:t,open:r,defaultOpen:i,onOpenChange:s,modal:c=!0}=e,l=n.useRef(null),d=n.useRef(null),[u,m]=de({prop:r,defaultProp:i??!1,onChange:s,caller:Se});return a.jsx(ka,{scope:o,triggerRef:l,contentRef:d,contentId:oe(),titleId:oe(),descriptionId:oe(),open:u,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(p=>!p),[m]),modal:c,children:t})};ko.displayName=Se;var Do="DialogTrigger",_o=n.forwardRef((e,o)=>{const{__scopeDialog:t,...r}=e,i=F(Do,t),s=D(o,i.triggerRef);return a.jsx(N.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":We(i.open),...r,ref:s,onClick:T(e.onClick,i.onOpenToggle)})});_o.displayName=Do;var Ve="DialogPortal",[Da,Po]=Eo(Ve,{forceMount:void 0}),jo=e=>{const{__scopeDialog:o,forceMount:t,children:r,container:i}=e,s=F(Ve,o);return a.jsx(Da,{scope:o,forceMount:t,children:n.Children.map(r,c=>a.jsx(pe,{present:t||s.open,children:a.jsx(Xe,{asChild:!0,container:i,children:c})}))})};jo.displayName=Ve;var he="DialogOverlay",Mo=n.forwardRef((e,o)=>{const t=Po(he,e.__scopeDialog),{forceMount:r=t.forceMount,...i}=e,s=F(he,e.__scopeDialog);return s.modal?a.jsx(pe,{present:r||s.open,children:a.jsx(Pa,{...i,ref:o})}):null});Mo.displayName=he;var _a=at("DialogOverlay.RemoveScroll"),Pa=n.forwardRef((e,o)=>{const{__scopeDialog:t,...r}=e,i=F(he,t);return a.jsx(nt,{as:_a,allowPinchZoom:!0,shards:[i.contentRef],children:a.jsx(N.div,{"data-state":We(i.open),...r,ref:o,style:{pointerEvents:"auto",...r.style}})})}),re="DialogContent",Lo=n.forwardRef((e,o)=>{const t=Po(re,e.__scopeDialog),{forceMount:r=t.forceMount,...i}=e,s=F(re,e.__scopeDialog);return a.jsx(pe,{present:r||s.open,children:s.modal?a.jsx(ja,{...i,ref:o}):a.jsx(Ma,{...i,ref:o})})});Lo.displayName=re;var ja=n.forwardRef((e,o)=>{const t=F(re,e.__scopeDialog),r=n.useRef(null),i=D(o,t.contentRef,r);return n.useEffect(()=>{const s=r.current;if(s)return tt(s)},[]),a.jsx(Oo,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:T(e.onCloseAutoFocus,s=>{s.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:T(e.onPointerDownOutside,s=>{const c=s.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&s.preventDefault()}),onFocusOutside:T(e.onFocusOutside,s=>s.preventDefault())})}),Ma=n.forwardRef((e,o)=>{const t=F(re,e.__scopeDialog),r=n.useRef(!1),i=n.useRef(!1);return a.jsx(Oo,{...e,ref:o,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||t.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(i.current=!0));const c=s.target;t.triggerRef.current?.contains(c)&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&i.current&&s.preventDefault()}})}),Oo=n.forwardRef((e,o)=>{const{__scopeDialog:t,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:s,...c}=e,l=F(re,t),d=n.useRef(null),u=D(o,d);return ot(),a.jsxs(a.Fragment,{children:[a.jsx(rt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:s,children:a.jsx(st,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":We(l.open),...c,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),a.jsxs(a.Fragment,{children:[a.jsx(La,{titleId:l.titleId}),a.jsx(Fa,{contentRef:d,descriptionId:l.descriptionId})]})]})}),He="DialogTitle",Fo=n.forwardRef((e,o)=>{const{__scopeDialog:t,...r}=e,i=F(He,t);return a.jsx(N.h2,{id:i.titleId,...r,ref:o})});Fo.displayName=He;var Bo="DialogDescription",$o=n.forwardRef((e,o)=>{const{__scopeDialog:t,...r}=e,i=F(Bo,t);return a.jsx(N.p,{id:i.descriptionId,...r,ref:o})});$o.displayName=Bo;var Uo="DialogClose",Vo=n.forwardRef((e,o)=>{const{__scopeDialog:t,...r}=e,i=F(Uo,t);return a.jsx(N.button,{type:"button",...r,ref:o,onClick:T(e.onClick,()=>i.onOpenChange(!1))})});Vo.displayName=Uo;function We(e){return e?"open":"closed"}var Ho="DialogTitleWarning",[sr,Wo]=sn(Ho,{contentName:re,titleName:He,docsSlug:"dialog"}),La=({titleId:e})=>{const o=Wo(Ho),t=`\`${o.contentName}\` requires a \`${o.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${o.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${o.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},Oa="DialogDescriptionWarning",Fa=({contentRef:e,descriptionId:o})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Wo(Oa).contentName}}.`;return n.useEffect(()=>{const i=e.current?.getAttribute("aria-describedby");o&&i&&(document.getElementById(o)||console.warn(r))},[r,e,o]),null},Ba=ko,$a=_o,Ua=jo,qo=Mo,zo=Lo,Go=Fo,Zo=$o,Va=Vo;const ir=Ba,cr=$a,Ha=Ua,Ko=n.forwardRef(({className:e,...o},t)=>a.jsx(qo,{ref:t,className:C("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...o}));Ko.displayName=qo.displayName;const Wa=n.forwardRef(({className:e,children:o,...t},r)=>a.jsxs(Ha,{children:[a.jsx(Ko,{}),a.jsxs(zo,{ref:r,className:C("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[o,a.jsxs(Va,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(pn,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));Wa.displayName=zo.displayName;const qa=({className:e,...o})=>a.jsx("div",{className:C("flex flex-col space-y-1.5 text-center sm:text-left",e),...o});qa.displayName="DialogHeader";const za=({className:e,...o})=>a.jsx("div",{className:C("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...o});za.displayName="DialogFooter";const Ga=n.forwardRef(({className:e,...o},t)=>a.jsx(Go,{ref:t,className:C("text-lg font-semibold leading-none tracking-tight",e),...o}));Ga.displayName=Go.displayName;const Za=n.forwardRef(({className:e,...o},t)=>a.jsx(Zo,{ref:t,className:C("text-sm text-muted-foreground",e),...o}));Za.displayName=Zo.displayName;const Ka=[{id:"1",name:"TechFlow Innovations",slug:"techflow-innovations",description:"A leading technology company specializing in AI and machine learning solutions for enterprise clients.",mission:"To democratize artificial intelligence and make it accessible to businesses of all sizes.",vision:"To be the global leader in AI-driven business transformation.",values:["Innovation","Integrity","Collaboration","Excellence"],logo:"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=200&fit=crop&crop=center",coverImage:"https://images.unsplash.com/photo-1497366216548-37526070297c?w=1200&h=400&fit=crop",size:"medium",type:"private",foundedYear:2018,industry:"Technology",specialties:["Artificial Intelligence","Machine Learning","Cloud Computing","Data Analytics"],locations:[{id:"1",name:"San Francisco HQ",address:"123 Innovation Drive",city:"San Francisco",state:"CA",country:"USA",postalCode:"94105",isHeadquarters:!0}],socialLinks:{website:"https://techflow.com",linkedin:"https://linkedin.com/company/techflow",twitter:"https://twitter.com/techflow"},benefits:[{id:"1",name:"Health Insurance",description:"Comprehensive health, dental, and vision insurance",category:"health"},{id:"2",name:"Remote Work",description:"Flexible remote work options",category:"perks"}],stats:{totalEmployees:250,totalJobs:15,averageRating:4.2,totalReviews:87,responseRate:85,avgResponseTime:3,hiringGrowth:15},isVerified:!0,isSponsored:!1,createdAt:"2023-01-15T00:00:00Z",updatedAt:"2024-01-15T00:00:00Z"},{id:"2",name:"DataCorp Solutions",slug:"datacorp-solutions",description:"Enterprise data management and analytics company helping businesses make data-driven decisions.",mission:"To transform how businesses understand and utilize their data.",values:["Data-Driven","Customer-Centric","Innovation","Transparency"],logo:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=200&h=200&fit=crop&crop=center",coverImage:"https://images.unsplash.com/photo-**********-e076c223a692?w=1200&h=400&fit=crop",size:"large",type:"public",foundedYear:2015,industry:"Data & Analytics",specialties:["Big Data","Business Intelligence","Data Warehousing","Analytics"],locations:[{id:"2",name:"New York Office",address:"456 Data Street",city:"New York",state:"NY",country:"USA",postalCode:"10001",isHeadquarters:!0}],socialLinks:{website:"https://datacorp.com",linkedin:"https://linkedin.com/company/datacorp"},benefits:[{id:"3",name:"Professional Development",description:"$3000 annual learning budget",category:"development"}],stats:{totalEmployees:500,totalJobs:8,averageRating:4,totalReviews:156,responseRate:78,avgResponseTime:5,hiringGrowth:8},isVerified:!0,isSponsored:!0,createdAt:"2023-02-20T00:00:00Z",updatedAt:"2024-01-20T00:00:00Z"},{id:"3",name:"GreenTech Dynamics",slug:"greentech-dynamics",description:"Sustainable technology solutions for a cleaner future. Specializing in renewable energy software.",mission:"To accelerate the transition to sustainable energy through innovative technology.",values:["Sustainability","Innovation","Impact","Collaboration"],logo:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop&crop=center",coverImage:"https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=1200&h=400&fit=crop",size:"startup",type:"private",foundedYear:2020,industry:"CleanTech",specialties:["Renewable Energy","IoT","Sustainability Software","Smart Grid"],locations:[{id:"3",name:"Austin HQ",address:"789 Green Way",city:"Austin",state:"TX",country:"USA",postalCode:"73301",isHeadquarters:!0}],socialLinks:{website:"https://greentechdynamics.com",linkedin:"https://linkedin.com/company/greentech-dynamics",twitter:"https://twitter.com/greentechd"},benefits:[{id:"4",name:"Equity Package",description:"Competitive equity compensation",category:"financial"}],stats:{totalEmployees:85,totalJobs:12,averageRating:4.5,totalReviews:23,responseRate:92,avgResponseTime:2,hiringGrowth:45},isVerified:!0,isSponsored:!1,createdAt:"2023-03-10T00:00:00Z",updatedAt:"2024-01-10T00:00:00Z"}],Ya=[{id:"1",title:"Senior Full Stack Developer",description:`We are looking for an experienced Full Stack Developer to join our growing team. You will be responsible for developing and maintaining web applications using modern technologies like React, Node.js, and cloud platforms.

Key Responsibilities:
• Develop and maintain web applications using React and Node.js
• Collaborate with cross-functional teams to define and implement features
• Write clean, maintainable, and efficient code
• Participate in code reviews and technical discussions
• Ensure applications are scalable and performant

What we offer:
• Competitive salary and equity package
• Comprehensive health benefits
• Flexible work arrangements
• Professional development opportunities
• Modern tech stack and tools`,summary:"Join our team as a Senior Full Stack Developer working with React, Node.js, and modern cloud technologies.",companyId:"1",companyName:"TechFlow Innovations",companyLogo:"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=200&fit=crop&crop=center",type:"full-time",experienceLevel:"senior-level",workLocation:"hybrid",location:{city:"San Francisco",state:"CA",country:"USA",isRemote:!1},salary:{min:14e4,max:18e4,currency:"USD",period:"yearly"},requirements:[{id:"1",type:"skill",name:"React",level:"advanced",required:!0},{id:"2",type:"skill",name:"Node.js",level:"advanced",required:!0},{id:"3",type:"experience",name:"5+ years experience",level:"advanced",required:!0}],benefits:[{id:"1",name:"Health Insurance",description:"Full health, dental, vision"},{id:"2",name:"Remote Work",description:"Flexible remote options"}],skills:["React","Node.js","TypeScript","AWS","Docker","PostgreSQL"],categories:["Engineering","Full Stack"],postedAt:"2024-01-20T00:00:00Z",updatedAt:"2024-01-20T00:00:00Z",isActive:!0,applicationsCount:47,viewsCount:234,featured:!0,urgent:!1,applicationDeadline:"2024-02-20T00:00:00Z"},{id:"2",title:"Data Analyst",description:`Join our data team as a Data Analyst where you'll help drive business decisions through data insights and analytics.

Responsibilities:
• Analyze large datasets to identify trends and patterns
• Create dashboards and reports for stakeholders
• Collaborate with product teams to define metrics
• Perform statistical analysis and A/B testing
• Present findings to leadership team

Requirements:
• Bachelor's degree in Statistics, Mathematics, or related field
• 3+ years of experience in data analysis
• Proficiency in SQL, Python, or R
• Experience with visualization tools like Tableau or Power BI
• Strong communication skills`,summary:"Drive business decisions through data analysis and insights using SQL, Python, and visualization tools.",companyId:"2",companyName:"DataCorp Solutions",companyLogo:"https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=200&h=200&fit=crop&crop=center",type:"full-time",experienceLevel:"mid-level",workLocation:"remote",location:{city:"New York",state:"NY",country:"USA",isRemote:!0},salary:{min:85e3,max:115e3,currency:"USD",period:"yearly"},requirements:[{id:"4",type:"skill",name:"SQL",level:"advanced",required:!0},{id:"5",type:"skill",name:"Python",level:"intermediate",required:!0}],benefits:[{id:"3",name:"Learning Budget",description:"$3000 annual budget"}],skills:["SQL","Python","Tableau","Statistics","Excel"],categories:["Data","Analytics"],postedAt:"2024-01-18T00:00:00Z",updatedAt:"2024-01-18T00:00:00Z",isActive:!0,applicationsCount:23,viewsCount:156,featured:!1,urgent:!1},{id:"3",title:"Frontend Developer - React",description:`We're seeking a talented Frontend Developer to join our team building the next generation of clean energy software solutions.

What you'll do:
• Build responsive web applications using React and TypeScript
• Collaborate with designers to implement pixel-perfect UIs
• Optimize applications for performance and accessibility
• Work with backend teams to integrate APIs
• Participate in agile development process

What we're looking for:
• 3+ years of frontend development experience
• Strong proficiency in React and modern JavaScript
• Experience with TypeScript, CSS-in-JS, and testing frameworks
• Understanding of web performance optimization
• Passion for clean code and user experience`,summary:"Build responsive React applications for clean energy solutions with a passionate team.",companyId:"3",companyName:"GreenTech Dynamics",companyLogo:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop&crop=center",type:"full-time",experienceLevel:"mid-level",workLocation:"hybrid",location:{city:"Austin",state:"TX",country:"USA",isRemote:!1},salary:{min:95e3,max:125e3,currency:"USD",period:"yearly"},requirements:[{id:"6",type:"skill",name:"React",level:"advanced",required:!0},{id:"7",type:"skill",name:"TypeScript",level:"intermediate",required:!0}],benefits:[{id:"4",name:"Equity",description:"Competitive equity package"}],skills:["React","TypeScript","CSS","JavaScript","Testing"],categories:["Engineering","Frontend"],postedAt:"2024-01-15T00:00:00Z",updatedAt:"2024-01-15T00:00:00Z",isActive:!0,applicationsCount:31,viewsCount:189,featured:!1,urgent:!0},{id:"4",title:"Product Manager",description:`Lead product strategy and development for our AI-powered enterprise solutions.

Responsibilities:
• Define product roadmap and strategy
• Collaborate with engineering, design, and sales teams
• Conduct market research and competitive analysis
• Manage product launches and go-to-market strategy
• Analyze product metrics and user feedback

Requirements:
• 5+ years of product management experience
• Experience with B2B SaaS products
• Strong analytical and communication skills
• Technical background preferred
• MBA or equivalent experience`,summary:"Lead product strategy for AI-powered enterprise solutions with cross-functional collaboration.",companyId:"1",companyName:"TechFlow Innovations",companyLogo:"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=200&fit=crop&crop=center",type:"full-time",experienceLevel:"senior-level",workLocation:"on-site",location:{city:"San Francisco",state:"CA",country:"USA",isRemote:!1},salary:{min:15e4,max:2e5,currency:"USD",period:"yearly"},requirements:[{id:"8",type:"experience",name:"5+ years PM experience",level:"advanced",required:!0}],benefits:[{id:"5",name:"Stock Options",description:"Competitive equity package"}],skills:["Product Management","Strategy","Analytics","Agile","Leadership"],categories:["Product","Management"],postedAt:"2024-01-22T00:00:00Z",updatedAt:"2024-01-22T00:00:00Z",isActive:!0,applicationsCount:18,viewsCount:98,featured:!0,urgent:!1},{id:"5",title:"UX/UI Designer",description:`Join our design team to create beautiful and intuitive user experiences for our sustainability platform.

What you'll do:
• Design user interfaces for web and mobile applications
• Conduct user research and usability testing
• Create wireframes, prototypes, and design systems
• Collaborate with product and engineering teams
• Ensure designs are accessible and inclusive

What we're looking for:
• 4+ years of UX/UI design experience
• Proficiency in Figma, Sketch, or similar tools
• Strong portfolio demonstrating design thinking
• Experience with design systems and prototyping
• Understanding of accessibility principles`,summary:"Create beautiful and intuitive user experiences for sustainability platform applications.",companyId:"3",companyName:"GreenTech Dynamics",companyLogo:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop&crop=center",type:"full-time",experienceLevel:"mid-level",workLocation:"remote",location:{city:"Austin",state:"TX",country:"USA",isRemote:!0},salary:{min:9e4,max:12e4,currency:"USD",period:"yearly"},requirements:[{id:"9",type:"skill",name:"Figma",level:"advanced",required:!0},{id:"10",type:"experience",name:"4+ years UX/UI",level:"advanced",required:!0}],benefits:[{id:"6",name:"Creative Time",description:"20% time for creative projects"}],skills:["Figma","User Research","Prototyping","Design Systems","Accessibility"],categories:["Design","UX/UI"],postedAt:"2024-01-25T00:00:00Z",updatedAt:"2024-01-25T00:00:00Z",isActive:!0,applicationsCount:29,viewsCount:167,featured:!1,urgent:!1}],lr=[{id:"app-1",userId:"user-1",jobId:"1",job:{id:"1",title:"Senior Full Stack Developer",companyName:"TechFlow Innovations",companyLogo:"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=200&h=200&fit=crop&crop=center",location:"San Francisco, CA",type:"Full-time",salary:{min:14e4,max:18e4,currency:"USD"}},status:"interview_scheduled",appliedAt:"2024-01-22T00:00:00Z",updatedAt:"2024-01-25T00:00:00Z",coverLetter:"I am excited to apply for the Senior Full Stack Developer position...",documents:[{id:"doc-1",name:"John_Doe_Resume.pdf",type:"resume",url:"/documents/resume.pdf",size:245760,uploadedAt:"2024-01-22T00:00:00Z"}],notes:[],interviews:[{id:"int-1",type:"video",title:"Technical Interview",scheduledAt:"2024-02-01T15:00:00Z",duration:60,meetingUrl:"https://zoom.us/j/*********",interviewers:[{id:"int-1",name:"Sarah Johnson",title:"Engineering Manager",email:"<EMAIL>"}],status:"scheduled"}],timeline:[{id:"tl-1",status:"submitted",timestamp:"2024-01-22T00:00:00Z",description:"Application submitted",performer:{id:"user-1",name:"John Doe",role:"applicant"}},{id:"tl-2",status:"under_review",timestamp:"2024-01-23T10:00:00Z",description:"Application under review",performer:{id:"hr-1",name:"HR Team",role:"system"}},{id:"tl-3",status:"interview_scheduled",timestamp:"2024-01-25T14:00:00Z",description:"Technical interview scheduled",performer:{id:"rec-1",name:"Sarah Johnson",role:"recruiter"}}],isUrgent:!1,priority:"high",tags:["frontend","full-stack"],customFields:{}},{id:"app-2",userId:"user-1",jobId:"3",job:{id:"3",title:"Frontend Developer - React",companyName:"GreenTech Dynamics",companyLogo:"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=200&h=200&fit=crop&crop=center",location:"Austin, TX",type:"Full-time",salary:{min:95e3,max:125e3,currency:"USD"}},status:"under_review",appliedAt:"2024-01-20T00:00:00Z",updatedAt:"2024-01-21T00:00:00Z",coverLetter:"I am passionate about clean technology and would love to contribute...",documents:[{id:"doc-2",name:"John_Doe_Resume.pdf",type:"resume",url:"/documents/resume.pdf",size:245760,uploadedAt:"2024-01-20T00:00:00Z"}],notes:[],interviews:[],timeline:[{id:"tl-4",status:"submitted",timestamp:"2024-01-20T00:00:00Z",description:"Application submitted",performer:{id:"user-1",name:"John Doe",role:"applicant"}},{id:"tl-5",status:"under_review",timestamp:"2024-01-21T09:00:00Z",description:"Application under review",performer:{id:"hr-2",name:"HR Team",role:"system"}}],isUrgent:!1,priority:"medium",tags:["frontend","react"],customFields:{}}],dr=["Engineering","Design","Product","Marketing","Sales","Data","Operations","Finance","HR","Customer Success","DevOps","QA","Research","Legal","Consulting"],ur=["San Francisco, CA","New York, NY","Los Angeles, CA","Chicago, IL","Austin, TX","Seattle, WA","Boston, MA","Denver, CO","Atlanta, GA","Miami, FL","Remote"],pr=Ka.slice(0,6),fr=Ya.slice(0,6),mr=["Remote Software Engineer","Product Manager","Data Scientist","UX Designer","Frontend Developer","Full Stack Developer","DevOps Engineer","Marketing Manager"];export{ya as A,ba as B,Xn as C,ir as D,lr as E,fn as I,hn as L,Qa as S,or as T,pn as X,oa as a,Gn as b,er as c,Zn as d,Yn as e,pr as f,Ea as g,Ta as h,Ka as i,dr as j,Qn as k,ur as l,Ya as m,ea as n,cr as o,mr as p,Wa as q,fr as r,qa as s,Ga as t,Za as u,za as v,ia as w,ca as x,la as y,xa as z};
