import{a as D,r as x,j as e,F as j,d as r}from"./index-BOPfWTHD.js";import{E as k,C as a,a as i,T as S,w as I,x as o,y as h,D as R,o as L,q as E,s as V,t as O,u as W,v as z}from"./mock-data-CbJZR1PP.js";import{B as u}from"./badge-Bzyz8_QN.js";import{C as v,f as B}from"./formatDistanceToNow-Bm5-CVp4.js";import{U as F}from"./users-DpHMEXct.js";import{C as $}from"./circle-check-big-CBdbYUSk.js";import{C as J}from"./calendar-jrt4hqPn.js";import{E as P,T as U}from"./trash-2-DiK6xcQR.js";import{E as H}from"./external-link-CApOk594.js";function q(){const g=D(),[l,b]=x.useState([]),[f,w]=x.useState("all"),[N,y]=x.useState(!0);x.useEffect(()=>{setTimeout(()=>{b(k),y(!1)},500)},[]);const p=s=>{switch(s){case"submitted":case"under_review":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"interview_scheduled":case"interview_completed":return"bg-blue-100 text-blue-800 border-blue-200";case"offer_pending":case"offer_accepted":return"bg-green-100 text-green-800 border-green-200";case"rejected":case"withdrawn":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},_=s=>{switch(s){case"submitted":return"Submitted";case"under_review":return"Under Review";case"screening":return"Screening";case"interview_scheduled":return"Interview Scheduled";case"interview_completed":return"Interview Completed";case"reference_check":return"Reference Check";case"offer_pending":return"Offer Pending";case"offer_accepted":return"Offer Accepted";case"offer_declined":return"Offer Declined";case"rejected":return"Rejected";case"withdrawn":return"Withdrawn";default:return"Draft"}},c=l.filter(s=>{switch(f){case"in-review":return["submitted","under_review","screening"].includes(s.status);case"interviews":return["interview_scheduled","interview_completed"].includes(s.status);case"offers":return["offer_pending","offer_accepted","offer_declined"].includes(s.status);default:return!0}}),t={total:l.length,inReview:l.filter(s=>["submitted","under_review","screening"].includes(s.status)).length,interviews:l.filter(s=>["interview_scheduled","interview_completed"].includes(s.status)).length,offers:l.filter(s=>["offer_pending","offer_accepted"].includes(s.status)).length},C=s=>{console.log("View application:",s)},A=s=>{g(`/jobs/${s}`)},T=s=>{console.log("Withdraw application:",s)},d=({application:s})=>e.jsx(a,{className:"shadow-sm border-0 hover:shadow-md transition-shadow",children:e.jsxs(i,{className:"p-6",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.job.companyLogo&&e.jsx("img",{src:s.job.companyLogo,alt:s.job.companyName,className:"w-10 h-10 rounded-lg object-cover border"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.job.title}),e.jsx("p",{className:"text-blue-600 font-medium",children:s.job.companyName})]})]}),e.jsx("p",{className:"text-gray-600 mb-2",children:s.job.location}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(v,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:["Applied"," ",B(new Date(s.appliedAt),{addSuffix:!0})]})]}),s.job.salary&&e.jsxs("div",{className:"text-green-600 font-medium",children:["$",(s.job.salary.min/1e3).toFixed(0),"k - $",(s.job.salary.max/1e3).toFixed(0),"k"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx(u,{className:`${p(s.status)} mb-2`,children:_(s.status)}),s.priority==="high"&&e.jsx(u,{variant:"outline",className:"block text-xs",children:"High Priority"})]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[e.jsx(j,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:"Application Progress"})]}),e.jsx("div",{className:"flex items-center space-x-2",children:s.timeline.slice(-3).map((n,m)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${m===s.timeline.length-1?"bg-blue-500":"bg-gray-300"}`}),m<2&&e.jsx("div",{className:"w-8 h-px bg-gray-300 mx-1"})]},n.id))})]}),s.interviews.length>0&&e.jsxs("div",{className:"mb-4 p-3 bg-blue-50 rounded-lg",children:[e.jsxs("div",{className:"flex items-center text-blue-800 text-sm mb-1",children:[e.jsx(J,{className:"h-4 w-4 mr-1"}),e.jsx("span",{className:"font-medium",children:"Upcoming Interview"})]}),e.jsxs("p",{className:"text-blue-700 text-sm",children:[s.interviews[0].title," -"," ",new Date(s.interviews[0].scheduledAt).toLocaleDateString()," ","at"," ",new Date(s.interviews[0].scheduledAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]}),s.tags.length>0&&e.jsx("div",{className:"mb-4",children:e.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map((n,m)=>e.jsx(u,{variant:"outline",className:"text-xs",children:n},m))})}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex space-x-3",children:[e.jsxs(r,{variant:"ghost",size:"sm",onClick:()=>C(s.id),children:[e.jsx(P,{className:"h-4 w-4 mr-1"}),"View Details"]}),e.jsxs(r,{variant:"ghost",size:"sm",onClick:()=>A(s.jobId),children:[e.jsx(H,{className:"h-4 w-4 mr-1"}),"View Job"]})]}),e.jsxs("div",{className:"flex space-x-2",children:[s.status==="interview_scheduled"&&e.jsx(r,{size:"sm",className:"bg-green-600 hover:bg-green-700",children:"Join Interview"}),s.status==="offer_pending"&&e.jsx(r,{size:"sm",className:"bg-blue-600 hover:bg-blue-700",children:"View Offer"}),e.jsxs(R,{children:[e.jsx(L,{asChild:!0,children:e.jsxs(r,{variant:"ghost",size:"sm",className:"text-red-600 hover:text-red-700",children:[e.jsx(U,{className:"h-4 w-4 mr-1"}),"Withdraw"]})}),e.jsxs(E,{children:[e.jsxs(V,{children:[e.jsx(O,{children:"Withdraw Application"}),e.jsxs(W,{children:["Are you sure you want to withdraw your application for"," ",s.job.title," at ",s.job.companyName,"? This action cannot be undone."]})]}),e.jsxs(z,{children:[e.jsx(r,{variant:"outline",children:"Cancel"}),e.jsx(r,{variant:"destructive",onClick:()=>T(s.id),children:"Withdraw Application"})]})]})]})]})]})]})});return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"My Applications"}),e.jsx("p",{className:"text-lg text-gray-600",children:"Track your job applications and their status"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[e.jsx(a,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(j,{className:"h-8 w-8 text-blue-500"})}),e.jsx("div",{className:"text-3xl font-bold text-blue-600 mb-1",children:t.total}),e.jsx("div",{className:"text-gray-600",children:"Total Applications"})]})}),e.jsx(a,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(v,{className:"h-8 w-8 text-yellow-500"})}),e.jsx("div",{className:"text-3xl font-bold text-yellow-600 mb-1",children:t.inReview}),e.jsx("div",{className:"text-gray-600",children:"In Review"})]})}),e.jsx(a,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx(F,{className:"h-8 w-8 text-purple-500"})}),e.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-1",children:t.interviews}),e.jsx("div",{className:"text-gray-600",children:"Interviews"})]})}),e.jsx(a,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"p-6 text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx($,{className:"h-8 w-8 text-green-500"})}),e.jsx("div",{className:"text-3xl font-bold text-green-600 mb-1",children:t.offers}),e.jsx("div",{className:"text-gray-600",children:"Offers"})]})})]}),e.jsxs(S,{value:f,onValueChange:w,className:"mb-6",children:[e.jsxs(I,{className:"grid w-full grid-cols-4",children:[e.jsxs(o,{value:"all",children:["All Applications (",t.total,")"]}),e.jsxs(o,{value:"in-review",children:["In Review (",t.inReview,")"]}),e.jsxs(o,{value:"interviews",children:["Interviews (",t.interviews,")"]}),e.jsxs(o,{value:"offers",children:["Offers (",t.offers,")"]})]}),e.jsx(h,{value:"all",className:"mt-6",children:N?e.jsx("div",{className:"space-y-4",children:Array.from({length:3}).map((s,n)=>e.jsx(a,{className:"shadow-sm border-0",children:e.jsx(i,{className:"p-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsxs("div",{className:"flex justify-between items-start mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-gray-200 rounded-lg"}),e.jsxs("div",{children:[e.jsx("div",{className:"w-48 h-6 bg-gray-200 rounded mb-2"}),e.jsx("div",{className:"w-32 h-4 bg-gray-200 rounded"})]})]}),e.jsx("div",{className:"w-24 h-6 bg-gray-200 rounded"})]}),e.jsx("div",{className:"w-full h-16 bg-gray-200 rounded"})]})})},n))}):c.length>0?e.jsx("div",{className:"space-y-4",children:c.map(s=>e.jsx(d,{application:s},s.id))}):e.jsx(a,{className:"shadow-sm border-0",children:e.jsxs(i,{className:"text-center py-12",children:[e.jsx(j,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No applications yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Start applying to jobs to see your applications here"}),e.jsx(r,{onClick:()=>g("/jobs"),children:"Browse Jobs"})]})})}),e.jsx(h,{value:"in-review",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:c.map(s=>e.jsx(d,{application:s},s.id))})}),e.jsx(h,{value:"interviews",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:c.map(s=>e.jsx(d,{application:s},s.id))})}),e.jsx(h,{value:"offers",className:"mt-6",children:e.jsx("div",{className:"space-y-4",children:c.map(s=>e.jsx(d,{application:s},s.id))})})]})]})})}const te=()=>e.jsx(q,{});export{te as default};
