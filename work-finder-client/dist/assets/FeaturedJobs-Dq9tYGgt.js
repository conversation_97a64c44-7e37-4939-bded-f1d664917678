import{a as p,j as e,f as d}from"./index-Bxm2R2OW.js";import"./mock-data-BqVSz9O0.js";import{C as x,a as o,B as r}from"./badge-L1wjnFol.js";import{A as h}from"./trending-up-CiYyj7aR.js";import{B as g}from"./bookmark-CUB1O_PA.js";import{M as N}from"./map-pin-CKdZFLuR.js";import{D as u}from"./dollar-sign-12fYRf7A.js";import{C as f,f as j}from"./formatDistanceToNow-DcQq3y4b.js";import{U as v}from"./users-CLiaPoiA.js";const y=({job:s,onSaveJob:t,onViewJob:n})=>{const i=a=>{if(!a)return"Salary not specified";const{min:l,max:m}=a;return`$${(l/1e3).toFixed(0)}k - $${(m/1e3).toFixed(0)}k`},c=a=>a.location.isRemote?"Remote":`${a.location.city}, ${a.location.state}`;return e.jsx(x,{className:"group hover:shadow-lg transition-all duration-300 border-0 shadow-md hover:-translate-y-1 bg-white",children:e.jsxs(o,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[s.companyLogo&&e.jsx("img",{src:s.companyLogo,alt:s.companyName,className:"w-12 h-12 rounded-lg object-cover border"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-lg text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1",children:s.title}),e.jsx("p",{className:"text-gray-600 text-sm",children:s.companyName})]})]}),e.jsx("button",{onClick:a=>{a.stopPropagation(),t?.(s.id)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(g,{className:"h-4 w-4 text-gray-400 hover:text-blue-600"})})]}),e.jsxs("div",{className:"space-y-3 mb-4",children:[e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(N,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:c(s)}),s.workLocation!=="on-site"&&e.jsx(r,{variant:"secondary",className:"ml-2 text-xs",children:s.workLocation})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(u,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:i(s.salary)})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(f,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:j(new Date(s.postedAt),{addSuffix:!0})})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(v,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s.applicationsCount," applicants"]})]})]}),e.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:s.summary}),s.skills&&s.skills.length>0&&e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.skills.slice(0,3).map((a,l)=>e.jsx(r,{variant:"outline",className:"text-xs",children:a},l)),s.skills.length>3&&e.jsxs(r,{variant:"outline",className:"text-xs",children:["+",s.skills.length-3," more"]})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(r,{variant:"secondary",className:"text-xs",children:s.type}),e.jsx(r,{variant:"secondary",className:"text-xs",children:s.experienceLevel}),s.featured&&e.jsx(r,{className:"text-xs bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),s.urgent&&e.jsx(r,{className:"text-xs bg-red-100 text-red-800 border-red-200",children:"Urgent"})]})}),e.jsxs(d,{onClick:()=>n(s.id),className:"w-full mt-4 group-hover:bg-blue-600 transition-colors",variant:"outline",children:["View Details",e.jsx(h,{className:"ml-2 h-4 w-4"})]})]})})},w=()=>e.jsx(x,{className:"border-0 shadow-md bg-white",children:e.jsxs(o,{className:"p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded-lg animate-pulse"}),e.jsxs("div",{children:[e.jsx("div",{className:"w-32 h-5 bg-gray-200 rounded animate-pulse mb-2"}),e.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse"})]})]}),e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"})]}),e.jsx("div",{className:"space-y-3 mb-4",children:Array.from({length:4}).map((s,t)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-200 rounded animate-pulse mr-2"}),e.jsx("div",{className:"w-20 h-4 bg-gray-200 rounded animate-pulse"})]},t))}),e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse mb-4"}),e.jsx("div",{className:"flex gap-2 mb-4",children:Array.from({length:3}).map((s,t)=>e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"},t))}),e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse"})]})}),B=({jobs:s,onSaveJob:t,isLoading:n=!1})=>{const i=p(),c=l=>{i(`/jobs/${l}`)},a=()=>{i("/jobs")};return e.jsx("section",{className:"py-16 px-4 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:["Featured ",e.jsx("span",{className:"text-blue-600",children:"Jobs"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Discover hand-picked opportunities from top companies actively hiring talented professionals"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12",children:n?Array.from({length:6}).map((l,m)=>e.jsx(w,{},m)):s.map(l=>e.jsx(y,{job:l,onSaveJob:t,onViewJob:c},l.id))}),e.jsx("div",{className:"text-center",children:e.jsxs(d,{onClick:a,size:"lg",className:"px-8 py-3 text-base font-semibold",children:["View All Jobs",e.jsx(h,{className:"ml-2 h-5 w-5"})]})})]})})};export{B as F};
