import{r as d,u as p,a as b,j as e,L as l,p as o}from"./index-Bxm2R2OW.js";import{u as j,a as y,o as w,b as N,s as a,A as v}from"./schemas-B68yf-xN.js";const C=w({name:a().min(2,"Name must be at least 2 characters"),email:a().email("Please enter a valid email address"),password:a().min(8,"Password must be at least 8 characters"),confirmPassword:a(),agreeToTerms:N().refine(r=>r===!0,{message:"You must agree to the terms and conditions"})}).refine(r=>r.password===r.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});function P(){const[r,i]=d.useState(!1),[n,c]=d.useState(null),{register:m}=p(),x=b(),{register:t,handleSubmit:u,formState:{errors:s}}=j({resolver:y(C)}),h=async g=>{try{i(!0),c(null),await m(g),x("/")}catch(f){c(f.message||"Registration failed. Please try again.")}finally{i(!1)}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center text-sm",children:[e.jsx("span",{className:"text-gray-600",children:"Already have an account? "}),e.jsx(l,{to:o.auth.login.getHref(),className:"font-semibold text-blue-600 hover:text-blue-700 transition-colors",children:"Sign in here"})]}),e.jsxs("form",{className:"space-y-6",onSubmit:u(h),children:[n&&e.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:e.jsxs("div",{className:"flex",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Registration Error"}),e.jsx("div",{className:"mt-2 text-sm text-red-700",children:e.jsx("p",{children:n})})]})]})}),e.jsxs("div",{className:"space-y-5",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),e.jsx("input",{...t("name"),type:"text",autoComplete:"name",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:"Enter your full name"}),s.name&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:s.name.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email address"}),e.jsx("input",{...t("email"),type:"email",autoComplete:"email",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:"Enter your email address"}),s.email&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:s.email.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),e.jsx("input",{...t("password"),type:"password",autoComplete:"new-password",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:"Create a strong password"}),s.password&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:s.password.message})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),e.jsx("input",{...t("confirmPassword"),type:"password",autoComplete:"new-password",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:"Confirm your password"}),s.confirmPassword&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:s.confirmPassword.message})]})]}),e.jsxs("div",{className:"flex items-start",children:[e.jsx("div",{className:"flex items-center h-5",children:e.jsx("input",{...t("agreeToTerms"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})}),e.jsxs("div",{className:"ml-3 text-sm",children:[e.jsxs("label",{htmlFor:"agreeToTerms",className:"text-gray-700",children:["I agree to the"," ",e.jsx(l,{to:o.terms.getHref(),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Terms and Conditions"})," ","and"," ",e.jsx(l,{to:o.privacy.getHref(),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Privacy Policy"})]}),s.agreeToTerms&&e.jsx("p",{className:"mt-2 text-sm text-red-600",children:s.agreeToTerms.message})]})]}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:r,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:r?e.jsxs("div",{className:"flex items-center",children:[e.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[e.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),e.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Creating account..."]}):"Create Account"})}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 flex items-center",children:e.jsx("div",{className:"w-full border-t border-gray-300"})}),e.jsx("div",{className:"relative flex justify-center text-sm",children:e.jsx("span",{className:"px-4 bg-white text-gray-500 font-medium",children:"Or sign up with"})})]}),e.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[e.jsxs("button",{type:"button",className:"w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[e.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[e.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),e.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),e.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),e.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),e.jsx("span",{className:"ml-2",children:"Google"})]}),e.jsxs("button",{type:"button",className:"w-full inline-flex justify-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors",children:[e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})}),e.jsx("span",{className:"ml-2",children:"Twitter"})]})]})]})]})]})}const z=()=>e.jsx(v,{title:"Create your account",subtitle:"Join thousands of job seekers and find your dream career",children:e.jsx(P,{})});export{z as default};
