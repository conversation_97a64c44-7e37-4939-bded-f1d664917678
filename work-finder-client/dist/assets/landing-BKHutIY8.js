import{r as l,j as e,B as b,f as v,a as y}from"./index-Bxm2R2OW.js";import{S as m,I as S,a as h,b as x,c as g,d as u,e as i}from"./select-454QdpBE.js";import{M as N}from"./map-pin-CKdZFLuR.js";import{F as w}from"./FeaturedJobs-Dq9tYGgt.js";import{F as C}from"./FeaturedCompanies-CEj0OwDU.js";import{r as P,f as H}from"./mock-data-BqVSz9O0.js";import"./chevron-down-Bu8lQHGF.js";import"./badge-L1wjnFol.js";import"./trending-up-CiYyj7aR.js";import"./bookmark-CUB1O_PA.js";import"./dollar-sign-12fYRf7A.js";import"./formatDistanceToNow-DcQq3y4b.js";import"./users-CLiaPoiA.js";const F=["Ho Chi Minh City","Hanoi","Da Nang","Can Tho","Hai Phong","Nha Trang","Hue","Vung Tau"],J=["Technology","Marketing","Sales","Design","Finance","Human Resources","Operations","Customer Service","Engineering","Product Management"],E=({onSearch:o,className:n=""})=>{const[a,r]=l.useState(""),[t,p]=l.useState(""),[c,j]=l.useState(""),d=s=>{s.preventDefault(),o({query:a||void 0,location:t||void 0,category:c||void 0})},f=s=>{s.key==="Enter"&&d(s)};return e.jsxs("form",{onSubmit:d,className:`space-y-4 ${n}`,children:[e.jsxs("div",{className:"relative",children:[e.jsx(m,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),e.jsx(S,{type:"text",placeholder:"Job title, keywords, or company",value:a,onChange:s=>r(s.target.value),onKeyPress:f,className:"pl-12 h-14 text-base border-gray-200 focus:border-[#1967d2] focus:ring-[#1967d2] rounded-lg"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(N,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10"}),e.jsxs(h,{value:t,onValueChange:p,children:[e.jsx(x,{className:"pl-12 h-14 border-gray-200 focus:border-[#1967d2] focus:ring-[#1967d2] rounded-lg",children:e.jsx(g,{placeholder:"Select location"})}),e.jsxs(u,{children:[e.jsx(i,{value:"remote",children:"Remote"}),F.map(s=>e.jsx(i,{value:s,children:s},s))]})]})]}),e.jsxs("div",{className:"relative",children:[e.jsx(b,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 z-10"}),e.jsxs(h,{value:c,onValueChange:j,children:[e.jsx(x,{className:"pl-12 h-14 border-gray-200 focus:border-[#1967d2] focus:ring-[#1967d2] rounded-lg",children:e.jsx(g,{placeholder:"Select category"})}),e.jsx(u,{children:J.map(s=>e.jsx(i,{value:s,children:s},s))})]})]})]}),e.jsxs(v,{type:"submit",size:"lg",className:"w-full h-14 text-base font-semibold bg-[#1967d2] hover:bg-[#1557b8] text-white rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl",children:[e.jsx(m,{className:"mr-2 h-5 w-5"}),"Search Jobs"]})]})},L="/assets/hero_right-CVdcvmGH.png",T=({onSearch:o})=>{const n=y(),a=r=>{const t=new URLSearchParams;r.query&&t.set("q",r.query),r.location&&t.set("location",r.location),n(`/jobs?${t.toString()}`),o?.(r)};return e.jsxs("section",{className:"relative min-h-screen overflow-hidden bg-gradient-to-br from-[#f8faff] to-[#f0f4ff]",children:[e.jsx("div",{className:"absolute inset-0",children:e.jsx("div",{className:"absolute -left-[675px] -top-[1202px] w-[3546.63px] h-[3368.71px] opacity-20",style:{background:"linear-gradient(135deg, rgba(25, 103, 210, 0.1) 0%, rgba(25, 103, 210, 0.05) 50%, rgba(25, 103, 210, 0.02) 100%)",clipPath:"polygon(20% 0%, 100% 0%, 80% 100%, 0% 100%)",transform:"rotate(-15deg)"}})}),e.jsx("div",{className:"container mx-auto max-w-7xl relative z-10 px-4 pt-28 md:pt-32 pb-20",children:e.jsxs("div",{className:"grid lg:grid-cols-2 gap-12 items-center min-h-[600px]",children:[e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsxs("h1",{className:"text-5xl font-medium text-[#202124] leading-tight",children:["There Are ",e.jsx("span",{className:"text-[#1967d2]",children:"93,178"})," ","Postings ",e.jsx("span",{className:"block",children:"Here For you!"})]}),e.jsx("p",{className:"text-[#696969] text-lg",children:"Find Jobs, Employment & Career Opportunities"})]}),e.jsx(E,{onSearch:a}),e.jsx("div",{className:"space-y-3",children:e.jsxs("p",{className:"text-[#202124] text-base",children:[e.jsx("span",{className:"font-medium",children:"Popular Searches : "}),e.jsx("span",{className:"text-[#696969] font-normal",children:"Designer, Developer, Web, IOS, PHP, Senior, Engineer"})]})})]}),e.jsx("div",{className:"relative flex justify-center items-center",children:e.jsx("div",{className:"w-full max-w-lg relative z-10",children:e.jsx("img",{src:L,alt:"Professional team collaboration",className:"w-full h-auto object-contain"})})})]})})]})};function k(){const o=a=>{console.log("Search:",a)},n=a=>{console.log("Save job:",a)};return e.jsxs("main",{className:"min-h-screen",children:[e.jsx(T,{onSearch:o}),e.jsx(w,{jobs:P,onSaveJob:n,isLoading:!1}),e.jsx(C,{companies:H,isLoading:!1})]})}const U=()=>e.jsx(k,{});export{U as default};
