import{e as T,j as e,d as j,r as p,a as ne,C as ie,c as ce}from"./index-BOPfWTHD.js";import{C as P,a as K,L as g,S as D,b as J,c as R,d as E,l as oe,e as $,g as k,h as S,j as de,X as me,m as A,I}from"./mock-data-CbJZR1PP.js";import{B as v}from"./badge-Bzyz8_QN.js";import{F as xe,A as he,G as ue,L as pe}from"./list-B10iYFZg.js";import{M as U}from"./map-pin-BR21nmoc.js";import{C as z,f as O}from"./formatDistanceToNow-Bm5-CVp4.js";import{B as H}from"./bookmark-DWvq3fkz.js";import{U as G}from"./users-DpHMEXct.js";import{D as ge}from"./dollar-sign-C4uAz_6M.js";import{S as ve}from"./search-C4Jn7o1m.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]],ye=T("chevron-left",je);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]],Q=T("refresh-cw",fe);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]],X=T("triangle-alert",Ne);class be extends p.Component{constructor(n){super(n),this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,i){this.setState({error:n,errorInfo:i}),this.props.onError?.(n,i)}handleRetry=()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})};render(){return this.state.hasError?this.props.fallback?this.props.fallback:e.jsx("div",{className:"min-h-[400px] flex items-center justify-center p-8",children:e.jsxs("div",{className:"text-center max-w-md",children:[e.jsxs("div",{className:"mb-6",children:[e.jsx(X,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),e.jsx("p",{className:"text-gray-600",children:"We're sorry, but something unexpected happened. Our team has been notified."})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs(j,{onClick:this.handleRetry,className:"w-full",children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),"Try Again"]}),e.jsx(j,{variant:"outline",onClick:()=>window.location.reload(),className:"w-full",children:"Reload Page"})]}),!1]})}):this.props.children}}const we=({children:s})=>e.jsx(be,{fallback:e.jsxs("div",{className:"p-8 text-center",children:[e.jsx(X,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Unable to load jobs"}),e.jsx("p",{className:"text-gray-600 mb-4",children:"There was an error loading the job listings. Please try refreshing the page."}),e.jsxs(j,{onClick:()=>window.location.reload(),children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),children:s}),ke=({filters:s,onFiltersChange:n,onClearFilters:i,className:x=""})=>{const d=(a,m)=>{n({...s,[a]:m})},h=(a,m,u)=>{const b=s[a]||[],N=u?[...b,m]:b.filter(M=>M!==m);d(a,N.length>0?N:void 0)},t=()=>{let a=0;return s.type?.length&&a++,s.experienceLevel?.length&&a++,s.workLocation?.length&&a++,s.categories?.length&&a++,s.salaryMin&&a++,s.salaryMax&&a++,s.postedWithin&&s.postedWithin!=="all"&&a++,s.location&&a++,a},c=[{value:"full-time",label:"Full-time"},{value:"part-time",label:"Part-time"},{value:"contract",label:"Contract"},{value:"internship",label:"Internship"},{value:"freelance",label:"Freelance"}],y=[{value:"entry-level",label:"Entry Level"},{value:"mid-level",label:"Mid Level"},{value:"senior-level",label:"Senior Level"},{value:"executive",label:"Executive"}],f=[{value:"remote",label:"Remote"},{value:"hybrid",label:"Hybrid"},{value:"on-site",label:"On-site"}],w=[{value:"all",label:"Any time"},{value:"day",label:"Past 24 hours"},{value:"week",label:"Past week"},{value:"month",label:"Past month"}],C=[{min:0,max:5e4,label:"Under $50K"},{min:5e4,max:75e3,label:"$50K - $75K"},{min:75e3,max:1e5,label:"$75K - $100K"},{min:1e5,max:15e4,label:"$100K - $150K"},{min:15e4,max:2e5,label:"$150K - $200K"},{min:2e5,max:999999,label:"$200K+"}];return e.jsx(P,{className:`${x}`,children:e.jsxs(K,{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(xe,{className:"h-5 w-5 mr-2 text-gray-600"}),e.jsx("h3",{className:"font-semibold text-lg",children:"Filters"}),t()>0&&e.jsx(v,{variant:"secondary",className:"ml-2",children:t()})]}),t()>0&&e.jsxs(j,{variant:"ghost",size:"sm",onClick:i,className:"text-gray-500 hover:text-gray-700",children:[e.jsx(me,{className:"h-4 w-4 mr-1"}),"Clear All"]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Location"}),e.jsxs(D,{value:s.location||"",onValueChange:a=>d("location",a||void 0),children:[e.jsx(J,{children:e.jsx(R,{placeholder:"Any location"})}),e.jsxs(E,{children:[e.jsx($,{value:"",children:"Any location"}),e.jsx($,{value:"remote",children:"Remote"}),oe.map(a=>e.jsx($,{value:a,children:a},a))]})]})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Job Type"}),e.jsx("div",{className:"space-y-2",children:c.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:`type-${a.value}`,checked:s.type?.includes(a.value)||!1,onCheckedChange:m=>h("type",a.value,m)}),e.jsx(g,{htmlFor:`type-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Experience Level"}),e.jsx("div",{className:"space-y-2",children:y.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:`experience-${a.value}`,checked:s.experienceLevel?.includes(a.value)||!1,onCheckedChange:m=>h("experienceLevel",a.value,m)}),e.jsx(g,{htmlFor:`experience-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Work Arrangement"}),e.jsx("div",{className:"space-y-2",children:f.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:`work-${a.value}`,checked:s.workLocation?.includes(a.value)||!1,onCheckedChange:m=>h("workLocation",a.value,m)}),e.jsx(g,{htmlFor:`work-${a.value}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},a.value))})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Categories"}),e.jsx("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:de.map(a=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:`category-${a}`,checked:s.categories?.includes(a)||!1,onCheckedChange:m=>h("categories",a,m)}),e.jsx(g,{htmlFor:`category-${a}`,className:"text-sm font-normal cursor-pointer",children:a})]},a))})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Salary Range"}),e.jsx("div",{className:"space-y-2",children:C.map((a,m)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(S,{id:`salary-${m}`,checked:s.salaryMin===a.min&&s.salaryMax===a.max,onCheckedChange:u=>{u?(d("salaryMin",a.min),d("salaryMax",a.max)):(d("salaryMin",void 0),d("salaryMax",void 0))}}),e.jsx(g,{htmlFor:`salary-${m}`,className:"text-sm font-normal cursor-pointer",children:a.label})]},m))})]}),e.jsx(k,{}),e.jsxs("div",{children:[e.jsx(g,{className:"text-sm font-medium mb-3 block",children:"Date Posted"}),e.jsxs(D,{value:s.postedWithin||"all",onValueChange:a=>d("postedWithin",a==="all"?void 0:a),children:[e.jsx(J,{children:e.jsx(R,{})}),e.jsx(E,{children:w.map(a=>e.jsx($,{value:a.value,children:a.label},a.value))})]})]})]})]})})},Ce=({job:s,viewMode:n,onSaveJob:i,onViewJob:x})=>{const d=c=>{if(!c)return"Salary not specified";const{min:y,max:f}=c;return`$${(y/1e3).toFixed(0)}k - $${(f/1e3).toFixed(0)}k`},h=c=>c.location.isRemote?"Remote":`${c.location.city}, ${c.location.state}`,t=n==="list";return e.jsx(P,{className:`group hover:shadow-lg transition-all duration-300 border-0 shadow-md hover:-translate-y-1 bg-white cursor-pointer ${t?"mb-4":""}`,onClick:()=>x(s.id),children:e.jsxs(K,{className:`p-6 ${t?"flex items-center space-x-6":""}`,children:[e.jsxs("div",{className:`${t?"flex-shrink-0":"flex items-start justify-between mb-4"}`,children:[e.jsxs("div",{className:`flex items-center ${t?"space-x-4":"space-x-3"}`,children:[s.companyLogo&&e.jsx("img",{src:s.companyLogo,alt:s.companyName,className:`${t?"w-16 h-16":"w-12 h-12"} rounded-lg object-cover border`}),e.jsxs("div",{children:[e.jsx("h3",{className:`font-semibold ${t?"text-xl":"text-lg"} text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1`,children:s.title}),e.jsx("p",{className:`text-gray-600 ${t?"text-base":"text-sm"}`,children:s.companyName}),t&&e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500 mt-1",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(U,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:h(s)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(z,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:O(new Date(s.postedAt),{addSuffix:!0})})]})]})]})]}),!t&&e.jsx("button",{onClick:c=>{c.stopPropagation(),i?.(s.id)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(H,{className:"h-4 w-4 text-gray-400 hover:text-blue-600"})})]}),e.jsx("div",{className:`${t?"flex-1":"space-y-3 mb-4"}`,children:t?e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Salary"}),e.jsx("div",{className:"font-medium",children:d(s.salary)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Type"}),e.jsx(v,{variant:"secondary",className:"text-xs",children:s.type})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Experience"}),e.jsx(v,{variant:"secondary",className:"text-xs",children:s.experienceLevel})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Applicants"}),e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(G,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:s.applicationsCount})]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(U,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:h(s)}),s.workLocation!=="on-site"&&e.jsx(v,{variant:"secondary",className:"ml-2 text-xs",children:s.workLocation})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(ge,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:d(s.salary)})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(z,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:O(new Date(s.postedAt),{addSuffix:!0})})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(G,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s.applicationsCount," applicants"]})]})]})}),!t&&e.jsxs(e.Fragment,{children:[e.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:s.summary}),s.skills&&s.skills.length>0&&e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.skills.slice(0,3).map((c,y)=>e.jsx(v,{variant:"outline",className:"text-xs",children:c},y)),s.skills.length>3&&e.jsxs(v,{variant:"outline",className:"text-xs",children:["+",s.skills.length-3," more"]})]})}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex gap-2",children:[e.jsx(v,{variant:"secondary",className:"text-xs",children:s.type}),e.jsx(v,{variant:"secondary",className:"text-xs",children:s.experienceLevel}),s.featured&&e.jsx(v,{className:"text-xs bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),s.urgent&&e.jsx(v,{className:"text-xs bg-red-100 text-red-800 border-red-200",children:"Urgent"})]})})]}),t&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("button",{onClick:c=>{c.stopPropagation(),i?.(s.id)},className:"p-2 hover:bg-gray-100 rounded-full transition-colors",children:e.jsx(H,{className:"h-5 w-5 text-gray-400 hover:text-blue-600"})}),e.jsxs("div",{className:"flex flex-col gap-1",children:[s.featured&&e.jsx(v,{className:"text-xs bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),s.urgent&&e.jsx(v,{className:"text-xs bg-red-100 text-red-800 border-red-200",children:"Urgent"})]})]})]})})},Le=({viewMode:s})=>{const n=s==="list";return e.jsx(P,{className:`border-0 shadow-md bg-white ${n?"mb-4":""}`,children:e.jsxs(K,{className:`p-6 ${n?"flex items-center space-x-6":""}`,children:[e.jsxs("div",{className:`${n?"flex-shrink-0":"flex items-start justify-between mb-4"}`,children:[e.jsxs("div",{className:`flex items-center ${n?"space-x-4":"space-x-3"}`,children:[e.jsx("div",{className:`${n?"w-16 h-16":"w-12 h-12"} bg-gray-200 rounded-lg animate-pulse`}),e.jsxs("div",{children:[e.jsx("div",{className:`${n?"w-48 h-6":"w-32 h-5"} bg-gray-200 rounded animate-pulse mb-2`}),e.jsx("div",{className:`${n?"w-32 h-5":"w-24 h-4"} bg-gray-200 rounded animate-pulse`})]})]}),!n&&e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"})]}),n?e.jsx("div",{className:"flex-1 grid grid-cols-2 lg:grid-cols-4 gap-4",children:Array.from({length:4}).map((i,x)=>e.jsxs("div",{children:[e.jsx("div",{className:"w-16 h-4 bg-gray-200 rounded animate-pulse mb-1"}),e.jsx("div",{className:"w-20 h-5 bg-gray-200 rounded animate-pulse"})]},x))}):e.jsx("div",{className:"space-y-3 mb-4",children:Array.from({length:4}).map((i,x)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-200 rounded animate-pulse mr-2"}),e.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse"})]},x))}),!n&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse mb-4"}),e.jsx("div",{className:"flex gap-2 mb-4",children:Array.from({length:3}).map((i,x)=>e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"},x))})]}),n&&e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-full animate-pulse"}),e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"})]})]})})},Se=({jobs:s,totalJobs:n,currentPage:i,totalPages:x,isLoading:d=!1,viewMode:h,sortBy:t,onViewModeChange:c,onSortChange:y,onPageChange:f,onSaveJob:w})=>{const C=ne(),a=u=>{C(`/jobs/${u}`)},m=[{value:"relevance",label:"Most Relevant"},{value:"date",label:"Most Recent"},{value:"salary",label:"Highest Salary"},{value:"company",label:"Company A-Z"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[n.toLocaleString()," Jobs Found"]}),e.jsxs("p",{className:"text-gray-600",children:["Showing ",(i-1)*20+1," -"," ",Math.min(i*20,n)," of"," ",n.toLocaleString()," results"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(he,{className:"h-4 w-4 text-gray-500"}),e.jsxs(D,{value:t,onValueChange:y,children:[e.jsx(J,{className:"w-40",children:e.jsx(R,{})}),e.jsx(E,{children:m.map(u=>e.jsx($,{value:u.value,children:u.label},u.value))})]})]}),e.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[e.jsx(j,{variant:h==="grid"?"default":"ghost",size:"sm",onClick:()=>c("grid"),className:"px-3",children:e.jsx(ue,{className:"h-4 w-4"})}),e.jsx(j,{variant:h==="list"?"default":"ghost",size:"sm",onClick:()=>c("list"),className:"px-3",children:e.jsx(pe,{className:"h-4 w-4"})})]})]})]}),e.jsx("div",{className:h==="grid"?"grid grid-cols-1 lg:grid-cols-2 gap-6":"space-y-0",children:d?Array.from({length:10}).map((u,b)=>e.jsx(Le,{viewMode:h},b)):s.map(u=>e.jsx(Ce,{job:u,viewMode:h,onSaveJob:w,onViewJob:a},u.id))}),x>1&&e.jsxs("div",{className:"flex items-center justify-center gap-2 mt-8",children:[e.jsx(j,{variant:"outline",onClick:()=>f(i-1),disabled:i<=1,className:"px-3",children:e.jsx(ye,{className:"h-4 w-4"})}),Array.from({length:Math.min(5,x)},(u,b)=>{const N=Math.max(1,Math.min(x-4,i-2))+b;return e.jsx(j,{variant:i===N?"default":"outline",onClick:()=>f(N),className:"px-3",children:N},N)}),e.jsx(j,{variant:"outline",onClick:()=>f(i+1),disabled:i>=x,className:"px-3",children:e.jsx(ie,{className:"h-4 w-4"})})]}),!d&&s.length===0&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No jobs found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters to see more results."})]})]})};function $e(){const[s,n]=p.useTransition(),i=p.useCallback(t=>{n(()=>{t()})},[n]),x=p.useCallback(t=>{n(()=>{t()})},[n]),d=p.useCallback(t=>{n(()=>{t()})},[n]),h=p.useCallback(t=>{n(()=>{t()})},[n]);return{isPending:s,startTransition:n,startNonUrgentUpdate:i,startFilterUpdate:x,startSortUpdate:d,startPaginationUpdate:h}}function Fe(){const[s,n]=ce(),[i,x]=p.useState(s.get("q")||""),[d,h]=p.useState(s.get("location")||""),[t,c]=p.useState({query:s.get("q")||void 0,location:s.get("location")||void 0,categories:s.get("category")?[s.get("category")]:void 0,page:1,limit:20,sortBy:"relevance",sortOrder:"desc"}),[y,f]=p.useState("grid"),[w,C]=p.useState("relevance"),[a]=p.useState(!1),{isPending:m,startFilterUpdate:u,startSortUpdate:b,startPaginationUpdate:N}=$e(),[M,Z]=p.useState(A),[q,Y]=p.useState(A.length),[B,F]=p.useState(1),ee=Math.ceil(q/20);p.useEffect(()=>{let l=[...A];if(t.query){const r=t.query.toLowerCase();l=l.filter(o=>o.title.toLowerCase().includes(r)||o.companyName.toLowerCase().includes(r)||o.description.toLowerCase().includes(r)||o.skills.some(L=>L.toLowerCase().includes(r)))}if(t.location&&(l=l.filter(r=>t.location==="remote"?r.workLocation==="remote"||r.location.isRemote:`${r.location.city}, ${r.location.state}`===t.location)),t.type?.length&&(l=l.filter(r=>t.type.includes(r.type))),t.experienceLevel?.length&&(l=l.filter(r=>t.experienceLevel.includes(r.experienceLevel))),t.workLocation?.length&&(l=l.filter(r=>t.workLocation.includes(r.workLocation))),t.categories?.length&&(l=l.filter(r=>r.categories.some(o=>t.categories.includes(o)))),t.salaryMin&&t.salaryMax&&(l=l.filter(r=>r.salary?r.salary.min>=t.salaryMin&&r.salary.max<=t.salaryMax:!1)),t.postedWithin){const r=new Date,o=new Date;switch(t.postedWithin){case"day":o.setDate(r.getDate()-1);break;case"week":o.setDate(r.getDate()-7);break;case"month":o.setMonth(r.getMonth()-1);break}l=l.filter(L=>new Date(L.postedAt)>=o)}switch(w){case"date":l.sort((r,o)=>new Date(o.postedAt).getTime()-new Date(r.postedAt).getTime());break;case"salary":l.sort((r,o)=>{const L=r.salary?.max||0;return(o.salary?.max||0)-L});break;case"company":l.sort((r,o)=>r.companyName.localeCompare(o.companyName));break;default:l.sort((r,o)=>r.featured&&!o.featured?-1:!r.featured&&o.featured?1:r.applicationsCount-o.applicationsCount)}Z(l),Y(l.length)},[t,w]);const _=()=>{const l={...t,query:i||void 0,location:d||void 0,page:1};c(l),F(1);const r=new URLSearchParams;i&&r.set("q",i),d&&r.set("location",d),n(r)},se=l=>{u(()=>{c(l),F(1)})},ae=()=>{u(()=>{const l={query:t.query,location:t.location,page:1,limit:20,sortBy:"relevance",sortOrder:"desc"};c(l),F(1)})},te=l=>{console.log("Save job:",l)},W=l=>{l.key==="Enter"&&_()},V=(B-1)*20,le=V+20,re=M.slice(V,le);return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Find Jobs"}),e.jsx("p",{className:"text-lg text-gray-600",children:"Discover your next career opportunity from thousands of job listings"})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-6 mb-8",children:e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(ve,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),e.jsx(I,{placeholder:"Job title, keywords, or company",value:i,onChange:l=>x(l.target.value),onKeyPress:W,className:"pl-10 h-12"})]}),e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(U,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400 z-10"}),e.jsx(I,{placeholder:"Location or 'Remote'",value:d,onChange:l=>h(l.target.value),onKeyPress:W,className:"pl-10 h-12"})]}),e.jsx(j,{onClick:_,className:"h-12 px-8 font-semibold",children:"Search Jobs"})]})}),e.jsxs("div",{className:"flex flex-col lg:flex-row gap-8",children:[e.jsx("div",{className:"lg:w-80 flex-shrink-0",children:e.jsx(ke,{filters:t,onFiltersChange:se,onClearFilters:ae,className:"sticky top-4"})}),e.jsx("div",{className:"flex-1",children:e.jsx(we,{children:e.jsx(Se,{jobs:re,totalJobs:q,currentPage:B,totalPages:ee,isLoading:a||m,viewMode:y,sortBy:w,onViewModeChange:f,onSortChange:l=>b(()=>C(l)),onPageChange:l=>N(()=>F(l)),onSaveJob:te})})})]})]})})}const Be=()=>e.jsx(Fe,{});export{Be as default};
