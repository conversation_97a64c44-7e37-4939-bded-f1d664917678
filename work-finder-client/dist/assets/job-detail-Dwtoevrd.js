import{d as E,t as F,a as I,r as n,j as e,f as l,v as U}from"./index-Bxm2R2OW.js";import{m as f,a as H}from"./mock-data-BqVSz9O0.js";import{C as c,a as d,B as r,b as x,c as h}from"./badge-L1wjnFol.js";import{S as q}from"./separator-CR9qNBde.js";import{D as M,a as V,b as Y,c as _,d as W,e as O,f as G}from"./dialog-DsGkwAMW.js";import{A as K}from"./arrow-left--yQ9esMO.js";import{M as Q}from"./map-pin-CKdZFLuR.js";import{C as X,f as v}from"./formatDistanceToNow-DcQq3y4b.js";import{U as Z}from"./users-CLiaPoiA.js";import{B as ee}from"./bookmark-CUB1O_PA.js";import{S as se}from"./share-2-DKwncnMM.js";import{C as ae}from"./calendar-BnlTM3Qh.js";import{C as u}from"./circle-check-big-BkizUoxm.js";import{E as ie}from"./external-link-5JJn2v5u.js";import"./x-Ci9JSY07.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]],te=E("circle-alert",le);function re(){const{id:g}=F(),o=I(),[a,w]=n.useState(null),[t,k]=n.useState(null),[C,S]=n.useState(!0),[p,D]=n.useState(!1),[m,y]=n.useState(!1),[A,L]=n.useState(!1);n.useEffect(()=>{setTimeout(()=>{const s=f.find(i=>i.id===g);if(s){w(s);const i=H.find(j=>j.id===s.companyId);k(i||null)}S(!1)},500)},[g]);const $=()=>{D(!p)},B=()=>{navigator.clipboard.writeText(window.location.href)},T=()=>{y(!0),setTimeout(()=>{y(!1),L(!0)},2e3)},N=()=>{t&&o(`/companies/${t.id}`)},z=s=>{if(!s)return"Salary not disclosed";const{min:i,max:j,period:b}=s,R=b==="yearly"?"/year":b==="monthly"?"/month":"/hour";return`$${(i/1e3).toFixed(0)}k - $${(j/1e3).toFixed(0)}k ${R}`},J=s=>s.location.isRemote||s.workLocation==="remote"?"Remote":`${s.location.city}, ${s.location.state}, ${s.location.country}`,P=s=>{switch(s){case"remote":return"bg-green-100 text-green-800 border-green-200";case"hybrid":return"bg-blue-100 text-blue-800 border-blue-200";case"on-site":return"bg-gray-100 text-gray-800 border-gray-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}};return C?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsx("div",{className:"container mx-auto px-4 max-w-6xl",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded w-32 mb-6"}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-8 mb-6",children:e.jsx("div",{className:"flex justify-between items-start mb-6",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-lg"}),e.jsxs("div",{children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded w-64 mb-2"}),e.jsx("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-40"})]})]})})})]})})}):a?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"container mx-auto px-4 max-w-6xl",children:[e.jsxs(l,{variant:"ghost",onClick:()=>o(-1),className:"mb-6 p-0 h-auto",children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Back to Jobs"]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsx(c,{className:"shadow-sm border-0",children:e.jsxs(d,{className:"p-8",children:[e.jsxs("div",{className:"flex justify-between items-start mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[a.companyLogo&&e.jsx("img",{src:a.companyLogo,alt:a.companyName,className:"w-16 h-16 rounded-lg object-cover border"}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:a.title}),e.jsx("button",{onClick:N,className:"text-xl text-blue-600 hover:text-blue-700 font-medium mb-2 transition-colors",children:a.companyName}),e.jsxs("div",{className:"flex items-center gap-4 text-gray-600",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Q,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:J(a)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(X,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:v(new Date(a.postedAt),{addSuffix:!0})})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Z,{className:"h-4 w-4 mr-1"}),e.jsxs("span",{children:[a.applicationsCount," applicants"]})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(l,{variant:"outline",size:"sm",onClick:$,className:p?"text-blue-600 border-blue-600":"",children:[e.jsx(ee,{className:`h-4 w-4 mr-2 ${p?"fill-current":""}`}),p?"Saved":"Save"]}),e.jsxs(l,{variant:"outline",size:"sm",onClick:B,children:[e.jsx(se,{className:"h-4 w-4 mr-2"}),"Share"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Salary"}),e.jsx("div",{className:"font-semibold text-green-600",children:z(a.salary)})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Job Type"}),e.jsx(r,{variant:"secondary",className:"capitalize",children:a.type.replace("-"," ")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Experience"}),e.jsx(r,{variant:"secondary",className:"capitalize",children:a.experienceLevel.replace("-"," ")})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm text-gray-500 mb-1",children:"Work Style"}),e.jsx(r,{className:`capitalize ${P(a.workLocation)}`,children:a.workLocation.replace("-"," ")})]})]}),e.jsxs("div",{className:"flex gap-2 mb-6",children:[a.featured&&e.jsx(r,{className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Featured"}),a.urgent&&e.jsx(r,{className:"bg-red-100 text-red-800 border-red-200",children:"Urgent Hiring"}),a.applicationDeadline&&e.jsxs(r,{variant:"outline",children:[e.jsx(ae,{className:"h-3 w-3 mr-1"}),"Deadline: ",new Date(a.applicationDeadline).toLocaleDateString()]})]}),a.skills&&a.skills.length>0&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Required Skills"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:a.skills.map((s,i)=>e.jsx(r,{variant:"outline",className:"text-sm",children:s},i))})]})]})}),e.jsxs(c,{className:"shadow-sm border-0",children:[e.jsx(x,{children:e.jsx(h,{children:"Job Description"})}),e.jsx(d,{children:e.jsx("div",{className:"prose max-w-none",children:e.jsx("div",{className:"whitespace-pre-line text-gray-700 leading-relaxed",children:a.description})})})]}),a.requirements&&a.requirements.length>0&&e.jsxs(c,{className:"shadow-sm border-0",children:[e.jsx(x,{children:e.jsx(h,{children:"Requirements"})}),e.jsx(d,{children:e.jsx("div",{className:"space-y-3",children:a.requirements.map(s=>e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(u,{className:`h-5 w-5 mt-0.5 flex-shrink-0 ${s.required?"text-red-500":"text-green-500"}`}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-gray-900",children:[s.name,s.required&&e.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),e.jsxs("div",{className:"text-sm text-gray-600 capitalize",children:[s.type," - ",s.level," level"]})]})]},s.id))})})]}),a.benefits&&a.benefits.length>0&&e.jsxs(c,{className:"shadow-sm border-0",children:[e.jsx(x,{children:e.jsx(h,{children:"Benefits & Perks"})}),e.jsx(d,{children:e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:a.benefits.map(s=>e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(u,{className:"h-5 w-5 mt-0.5 text-green-500 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:s.name}),s.description&&e.jsx("div",{className:"text-sm text-gray-600",children:s.description})]})]},s.id))})})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(c,{className:"shadow-sm border-0 sticky top-4",children:e.jsx(d,{className:"p-6",children:A?e.jsxs("div",{className:"text-center",children:[e.jsx(u,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Application Submitted!"}),e.jsxs("p",{className:"text-sm text-gray-600 mb-4",children:["Your application has been sent to ",a.companyName,". They typically respond within 3-5 business days."]}),e.jsx(l,{variant:"outline",className:"w-full",onClick:()=>o("/applications"),children:"View Application Status"})]}):e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Ready to Apply?"}),a.applicationUrl?e.jsxs(l,{className:"w-full mb-3",onClick:()=>window.open(a.applicationUrl,"_blank"),disabled:m,children:[e.jsx(ie,{className:"h-4 w-4 mr-2"}),"Apply on Company Site"]}):e.jsxs(M,{children:[e.jsx(V,{asChild:!0,children:e.jsx(l,{className:"w-full mb-3",disabled:m,children:m?"Submitting...":"Apply Now"})}),e.jsxs(Y,{children:[e.jsxs(_,{children:[e.jsxs(W,{children:["Apply for ",a.title]}),e.jsxs(O,{children:["Submit your application for this position at ",a.companyName,"."]})]}),e.jsx("div",{className:"py-4",children:e.jsx("p",{className:"text-sm text-gray-600",children:"Your profile and resume will be submitted to the employer. Make sure your profile is complete for the best chance of success."})}),e.jsxs(G,{children:[e.jsx(l,{variant:"outline",children:"Cancel"}),e.jsx(l,{onClick:T,disabled:m,children:m?"Submitting...":"Submit Application"})]})]})]}),e.jsx("p",{className:"text-xs text-gray-500 text-center",children:"By applying, you agree to our Terms of Service and Privacy Policy"})]})})}),t&&e.jsxs(c,{className:"shadow-sm border-0",children:[e.jsx(x,{children:e.jsxs(h,{className:"flex items-center gap-2",children:[e.jsx(U,{className:"h-5 w-5"}),"About ",t.name]})}),e.jsx(d,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-gray-700 line-clamp-3",children:t.description}),e.jsx(q,{}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-500",children:"Industry"}),e.jsx("span",{className:"font-medium",children:t.industry})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-500",children:"Company Size"}),e.jsx("span",{className:"font-medium capitalize",children:t.size})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-500",children:"Founded"}),e.jsx("span",{className:"font-medium",children:t.foundedYear})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-500",children:"Open Positions"}),e.jsx("span",{className:"font-medium",children:t.stats.totalJobs})]})]}),e.jsx(l,{variant:"outline",className:"w-full",onClick:N,children:"View Company Profile"})]})})]}),e.jsxs(c,{className:"shadow-sm border-0",children:[e.jsx(x,{children:e.jsx(h,{children:"Similar Jobs"})}),e.jsx(d,{children:e.jsx("div",{className:"space-y-4",children:f.filter(s=>s.id!==a.id&&(s.categories.some(i=>a.categories.includes(i))||s.skills.some(i=>a.skills.includes(i)))).slice(0,3).map(s=>e.jsxs("div",{className:"border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors",onClick:()=>o(`/jobs/${s.id}`),children:[e.jsx("h4",{className:"font-medium text-gray-900 mb-1 line-clamp-1",children:s.title}),e.jsx("p",{className:"text-sm text-blue-600 mb-2",children:s.companyName}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-xs text-gray-500",children:v(new Date(s.postedAt),{addSuffix:!0})}),e.jsx(r,{variant:"outline",className:"text-xs",children:s.type})]})]},s.id))})})]})]})]})]})}):e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsx("div",{className:"container mx-auto px-4 max-w-6xl",children:e.jsxs("div",{className:"text-center py-16",children:[e.jsx(te,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Job Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"The job you're looking for doesn't exist or has been removed."}),e.jsx(l,{onClick:()=>o("/jobs"),children:"Browse All Jobs"})]})})})}const ve=()=>e.jsx(re,{});export{ve as default};
