import{r as f,u as q,a as L,b as C,j as s,L as y}from"./index-BOPfWTHD.js";import{o as t,b as i,s as a,n,_ as r,d as o,e as v,l as m,f as j,u as A,a as E,A as g,c as F}from"./routes-CUJ3oYE0.js";const d=a().min(1,"Email is required").email("Please enter a valid email address"),u=a().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),w=a().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Name can only contain letters and spaces"),R=t({email:d,password:a().min(1,"Password is required"),rememberMe:i().default(!1)});t({firstName:w,lastName:w,email:d,password:u,confirmPassword:a().min(1,"Please confirm your password"),phoneNumber:a().optional().refine(e=>!e||/^\+?[1-9]\d{1,14}$/.test(e),"Please enter a valid phone number"),termsAccepted:i().refine(e=>e===!0,"You must accept the terms and conditions"),marketingEmails:i().optional().default(!1)}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});t({email:d});t({token:a().min(1,"Reset token is required"),password:u,confirmPassword:a().min(1,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});t({currentPassword:a().min(1,"Current password is required"),newPassword:u,confirmNewPassword:a().min(1,"Please confirm your new password")}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"New passwords do not match",path:["confirmNewPassword"]}).refine(e=>e.currentPassword!==e.newPassword,{message:"New password must be different from current password",path:["newPassword"]});t({query:a().optional(),location:a().optional(),category:a().optional(),jobType:r(["full-time","part-time","contract","freelance","internship"]).optional(),experienceLevel:r(["entry","mid","senior","lead","executive"]).optional(),workLocation:r(["on-site","remote","hybrid"]).optional(),salaryMin:n().min(0,"Minimum salary must be positive").optional(),salaryMax:n().min(0,"Maximum salary must be positive").optional(),postedWithin:r(["day","week","month"]).optional(),sortBy:r(["relevance","date","salary","company"]).optional().default("relevance"),page:n().min(1).optional().default(1),limit:n().min(1).max(100).optional().default(20)}).refine(e=>!e.salaryMin||!e.salaryMax||e.salaryMin<=e.salaryMax,{message:"Minimum salary cannot be greater than maximum salary",path:["salaryMax"]});t({jobId:a().min(1,"Job ID is required"),coverLetter:a().min(100,"Cover letter must be at least 100 characters").max(2e3,"Cover letter must be less than 2000 characters"),resume:v(File).refine(e=>e.size<=5*1024*1024,"Resume file must be less than 5MB").refine(e=>["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.type),"Resume must be a PDF or Word document"),portfolioUrl:a().url("Please enter a valid URL").optional().or(m("")),availableStartDate:o().min(new Date,"Start date cannot be in the past"),expectedSalary:n().min(0,"Expected salary must be positive").optional(),additionalInfo:a().max(1e3,"Additional information must be less than 1000 characters").optional(),agreeToTerms:i().refine(e=>e===!0,"You must agree to the terms and conditions")});t({name:a().min(1,"Alert name is required").max(100,"Alert name must be less than 100 characters"),keywords:a().min(1,"Keywords are required").max(200,"Keywords must be less than 200 characters"),location:a().optional(),jobType:r(["full-time","part-time","contract","freelance","internship"]).optional(),experienceLevel:r(["entry","mid","senior","lead","executive"]).optional(),workLocation:r(["on-site","remote","hybrid"]).optional(),salaryMin:n().min(0,"Minimum salary must be positive").optional(),frequency:r(["daily","weekly","monthly"]).default("weekly"),isActive:i().default(!0)}).refine(e=>!e.salaryMin||e.salaryMin>0,{message:"Minimum salary must be greater than 0",path:["salaryMin"]});t({firstName:a().min(2,"First name must be at least 2 characters").max(50,"First name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"First name can only contain letters and spaces"),lastName:a().min(2,"Last name must be at least 2 characters").max(50,"Last name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Last name can only contain letters and spaces"),email:a().min(1,"Email is required").email("Please enter a valid email address"),phoneNumber:a().optional().refine(e=>!e||/^\+?[1-9]\d{1,14}$/.test(e),"Please enter a valid phone number"),bio:a().max(500,"Bio must be less than 500 characters").optional(),location:t({city:a().min(1,"City is required"),state:a().min(1,"State is required"),country:a().min(1,"Country is required")}),website:a().url("Please enter a valid URL").optional().or(m("")),linkedinUrl:a().url("Please enter a valid LinkedIn URL").optional().or(m("")),githubUrl:a().url("Please enter a valid GitHub URL").optional().or(m("")),avatar:v(File).refine(e=>e.size<=2*1024*1024,"Avatar file must be less than 2MB").refine(e=>["image/jpeg","image/png","image/webp"].includes(e.type),"Avatar must be a JPEG, PNG, or WebP image").optional()});t({title:a().min(1,"Job title is required").max(100,"Job title must be less than 100 characters"),company:a().min(1,"Company name is required").max(100,"Company name must be less than 100 characters"),location:a().min(1,"Location is required").max(100,"Location must be less than 100 characters"),startDate:o({message:"Start date is required"}),endDate:o().optional(),isCurrentJob:i().default(!1),description:a().min(50,"Description must be at least 50 characters").max(1e3,"Description must be less than 1000 characters"),skills:j(a()).min(1,"At least one skill is required").max(10,"Maximum 10 skills allowed")}).refine(e=>e.isCurrentJob||e.endDate,{message:"End date is required unless this is your current job",path:["endDate"]}).refine(e=>!e.endDate||e.startDate<=e.endDate,{message:"End date cannot be before start date",path:["endDate"]});t({institution:a().min(1,"Institution name is required").max(100,"Institution name must be less than 100 characters"),degree:a().min(1,"Degree is required").max(100,"Degree must be less than 100 characters"),fieldOfStudy:a().min(1,"Field of study is required").max(100,"Field of study must be less than 100 characters"),startDate:o({message:"Start date is required"}),endDate:o().optional(),isCurrentlyStudying:i().default(!1),gpa:n().min(0,"GPA cannot be negative").max(4,"GPA cannot exceed 4.0").optional(),description:a().max(500,"Description must be less than 500 characters").optional()}).refine(e=>e.isCurrentlyStudying||e.endDate,{message:"End date is required unless you are currently studying",path:["endDate"]}).refine(e=>!e.endDate||e.startDate<=e.endDate,{message:"End date cannot be before start date",path:["endDate"]});t({skills:j(t({name:a().min(1,"Skill name is required"),level:r(["beginner","intermediate","advanced","expert"]),yearsOfExperience:n().min(0,"Years of experience cannot be negative").max(50,"Years of experience seems too high").optional()})).min(1,"At least one skill is required").max(20,"Maximum 20 skills allowed")});function z(){const[e,p]=f.useState(!1),[x,h]=f.useState(null),{login:N}=q(),P=L(),M=C().state?.from||"/",{register:c,handleSubmit:S,formState:{errors:l}}=A({resolver:E(R),defaultValues:{email:"",password:"",rememberMe:!1}}),D=async b=>{try{p(!0),h(null),await N(b.email,b.password),P(M,{replace:!0})}catch(k){h(k.message||"Login failed. Please try again.")}finally{p(!1)}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:s.jsxs("div",{className:"max-w-md w-full space-y-8",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx("span",{className:"text-white font-bold text-2xl",children:"W"})}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Welcome back"}),s.jsxs("p",{className:"text-gray-600",children:["Don't have an account?"," ",s.jsx(y,{to:g.REGISTER,className:"font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"Sign up for free"})]})]}),s.jsxs("form",{className:"mt-8 space-y-6",onSubmit:S(D),children:[x&&s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:s.jsx("div",{className:"flex",children:s.jsxs("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-red-800",children:"Authentication Error"}),s.jsx("div",{className:"mt-2 text-sm text-red-700",children:s.jsx("p",{children:x})})]})})}),s.jsxs("div",{className:"space-y-5",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email address"}),s.jsx("input",{...c("email"),type:"email",autoComplete:"email",className:"appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm transition-colors",placeholder:"Enter your email"}),l.email&&s.jsx("p",{className:"mt-1 text-sm text-red-600",children:l.email.message})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),s.jsx("input",{...c("password"),type:"password",autoComplete:"current-password",className:"appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm transition-colors",placeholder:"Enter your password"}),l.password&&s.jsx("p",{className:"mt-1 text-sm text-red-600",children:l.password.message})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("input",{...c("rememberMe"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),s.jsx("label",{htmlFor:"rememberMe",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),s.jsx("div",{className:"text-sm",children:s.jsx(y,{to:g.FORGOT_PASSWORD,className:"font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"Forgot your password?"})})]}),s.jsx("div",{children:s.jsx("button",{type:"submit",disabled:e,className:"group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:e?s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})}),s.jsxs("div",{className:"mt-6",children:[s.jsxs("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-gray-50 text-gray-500",children:"Or continue with"})})]}),s.jsxs("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[s.jsxs("button",{type:"button",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[s.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[s.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),s.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),s.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),s.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),s.jsx("span",{className:"ml-2",children:"Google"})]}),s.jsxs("button",{type:"button",className:"w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors",children:[s.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"})}),s.jsx("span",{className:"ml-2",children:"Twitter"})]})]})]})]})]})})}const I=()=>s.jsx(F,{title:"Log in to your account",children:s.jsx(z,{})});export{I as default};
