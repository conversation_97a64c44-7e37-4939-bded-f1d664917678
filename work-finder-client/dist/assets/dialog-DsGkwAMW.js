import{r as i,n as B,j as o,l as H,z as h,o as R,P as x,q as p,m as _,E as q,J as K,M as V,N as J,K as Q,Q as U,T as X,a0 as Y,e as g}from"./index-Bxm2R2OW.js";import{X as Z}from"./x-Ci9JSY07.js";var D="Dialog",[E,Re]=H(D),[ee,d]=E(D),b=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:s,onOpenChange:a,modal:c=!0}=e,l=i.useRef(null),f=i.useRef(null),[v,C]=B({prop:r,defaultProp:s??!1,onChange:a,caller:D});return o.jsx(ee,{scope:t,triggerRef:l,contentRef:f,contentId:h(),titleId:h(),descriptionId:h(),open:v,onOpenChange:C,onOpenToggle:i.useCallback(()=>C(z=>!z),[C]),modal:c,children:n})};b.displayName=D;var P="DialogTrigger",O=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=d(P,n),a=R(t,s.triggerRef);return o.jsx(x.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":j(s.open),...r,ref:a,onClick:p(e.onClick,s.onOpenToggle)})});O.displayName=P;var N="DialogPortal",[te,I]=E(N,{forceMount:void 0}),T=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:s}=e,a=d(N,t);return o.jsx(te,{scope:t,forceMount:n,children:i.Children.map(r,c=>o.jsx(_,{present:n||a.open,children:o.jsx(q,{asChild:!0,container:s,children:c})}))})};T.displayName=N;var m="DialogOverlay",A=i.forwardRef((e,t)=>{const n=I(m,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,a=d(m,e.__scopeDialog);return a.modal?o.jsx(_,{present:r||a.open,children:o.jsx(ae,{...s,ref:t})}):null});A.displayName=m;var oe=J("DialogOverlay.RemoveScroll"),ae=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=d(m,n);return o.jsx(V,{as:oe,allowPinchZoom:!0,shards:[s.contentRef],children:o.jsx(x.div,{"data-state":j(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),u="DialogContent",w=i.forwardRef((e,t)=>{const n=I(u,e.__scopeDialog),{forceMount:r=n.forceMount,...s}=e,a=d(u,e.__scopeDialog);return o.jsx(_,{present:r||a.open,children:a.modal?o.jsx(ne,{...s,ref:t}):o.jsx(re,{...s,ref:t})})});w.displayName=u;var ne=i.forwardRef((e,t)=>{const n=d(u,e.__scopeDialog),r=i.useRef(null),s=R(t,n.contentRef,r);return i.useEffect(()=>{const a=r.current;if(a)return K(a)},[]),o.jsx(M,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:p(e.onCloseAutoFocus,a=>{a.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:p(e.onPointerDownOutside,a=>{const c=a.detail.originalEvent,l=c.button===0&&c.ctrlKey===!0;(c.button===2||l)&&a.preventDefault()}),onFocusOutside:p(e.onFocusOutside,a=>a.preventDefault())})}),re=i.forwardRef((e,t)=>{const n=d(u,e.__scopeDialog),r=i.useRef(!1),s=i.useRef(!1);return o.jsx(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{e.onCloseAutoFocus?.(a),a.defaultPrevented||(r.current||n.triggerRef.current?.focus(),a.preventDefault()),r.current=!1,s.current=!1},onInteractOutside:a=>{e.onInteractOutside?.(a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(s.current=!0));const c=a.target;n.triggerRef.current?.contains(c)&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&s.current&&a.preventDefault()}})}),M=i.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:s,onCloseAutoFocus:a,...c}=e,l=d(u,n),f=i.useRef(null),v=R(t,f);return Q(),o.jsxs(o.Fragment,{children:[o.jsx(U,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:s,onUnmountAutoFocus:a,children:o.jsx(X,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":j(l.open),...c,ref:v,onDismiss:()=>l.onOpenChange(!1)})}),o.jsxs(o.Fragment,{children:[o.jsx(se,{titleId:l.titleId}),o.jsx(le,{contentRef:f,descriptionId:l.descriptionId})]})]})}),y="DialogTitle",F=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=d(y,n);return o.jsx(x.h2,{id:s.titleId,...r,ref:t})});F.displayName=y;var S="DialogDescription",$=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=d(S,n);return o.jsx(x.p,{id:s.descriptionId,...r,ref:t})});$.displayName=S;var k="DialogClose",W=i.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,s=d(k,n);return o.jsx(x.button,{type:"button",...r,ref:t,onClick:p(e.onClick,()=>s.onOpenChange(!1))})});W.displayName=k;function j(e){return e?"open":"closed"}var G="DialogTitleWarning",[_e,L]=Y(G,{contentName:u,titleName:y,docsSlug:"dialog"}),se=({titleId:e})=>{const t=L(G),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},ie="DialogDescriptionWarning",le=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${L(ie).contentName}}.`;return i.useEffect(()=>{const s=e.current?.getAttribute("aria-describedby");t&&s&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},ce=b,de=O,ue=T,ge=A,fe=w,pe=F,xe=$,me=W;function Ne({...e}){return o.jsx(ce,{"data-slot":"dialog",...e})}function ye({...e}){return o.jsx(de,{"data-slot":"dialog-trigger",...e})}function De({...e}){return o.jsx(ue,{"data-slot":"dialog-portal",...e})}function ve({className:e,...t}){return o.jsx(ge,{"data-slot":"dialog-overlay",className:g("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function je({className:e,children:t,showCloseButton:n=!0,...r}){return o.jsxs(De,{"data-slot":"dialog-portal",children:[o.jsx(ve,{}),o.jsxs(fe,{"data-slot":"dialog-content",className:g("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,n&&o.jsxs(me,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[o.jsx(Z,{}),o.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function Ee({className:e,...t}){return o.jsx("div",{"data-slot":"dialog-header",className:g("flex flex-col gap-2 text-center sm:text-left",e),...t})}function be({className:e,...t}){return o.jsx("div",{"data-slot":"dialog-footer",className:g("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function Pe({className:e,...t}){return o.jsx(pe,{"data-slot":"dialog-title",className:g("text-lg leading-none font-semibold",e),...t})}function Oe({className:e,...t}){return o.jsx(xe,{"data-slot":"dialog-description",className:g("text-muted-foreground text-sm",e),...t})}export{Ne as D,ye as a,je as b,Ee as c,Pe as d,Oe as e,be as f};
