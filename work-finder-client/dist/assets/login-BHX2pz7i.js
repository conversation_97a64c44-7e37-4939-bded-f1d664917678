import{r as g,u as A,a as S,b as C,j as s,L as y,p as E}from"./index-Bxm2R2OW.js";import{o as t,b as o,s as a,n,_ as i,d as l,c as v,l as c,e as j,u as F,a as z,A as R}from"./schemas-B68yf-xN.js";import{u as N}from"./useTranslation-DLY10C-4.js";const u=a().min(1,"Email is required").email("Please enter a valid email address"),p=a().min(8,"Password must be at least 8 characters").regex(/[A-Z]/,"Password must contain at least one uppercase letter").regex(/[a-z]/,"Password must contain at least one lowercase letter").regex(/[0-9]/,"Password must contain at least one number").regex(/[^A-Za-z0-9]/,"Password must contain at least one special character"),w=a().min(2,"Name must be at least 2 characters").max(50,"Name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Name can only contain letters and spaces"),I=t({email:u,password:a().min(1,"Password is required"),rememberMe:o().default(!1)});t({firstName:w,lastName:w,email:u,password:p,confirmPassword:a().min(1,"Please confirm your password"),phoneNumber:a().optional().refine(e=>!e||/^\+?[1-9]\d{1,14}$/.test(e),"Please enter a valid phone number"),termsAccepted:o().refine(e=>e===!0,"You must accept the terms and conditions"),marketingEmails:o().optional().default(!1)}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});t({email:u});t({token:a().min(1,"Reset token is required"),password:p,confirmPassword:a().min(1,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords do not match",path:["confirmPassword"]});t({currentPassword:a().min(1,"Current password is required"),newPassword:p,confirmNewPassword:a().min(1,"Please confirm your new password")}).refine(e=>e.newPassword===e.confirmNewPassword,{message:"New passwords do not match",path:["confirmNewPassword"]}).refine(e=>e.currentPassword!==e.newPassword,{message:"New password must be different from current password",path:["newPassword"]});t({query:a().optional(),location:a().optional(),category:a().optional(),jobType:i(["full-time","part-time","contract","freelance","internship"]).optional(),experienceLevel:i(["entry","mid","senior","lead","executive"]).optional(),workLocation:i(["on-site","remote","hybrid"]).optional(),salaryMin:n().min(0,"Minimum salary must be positive").optional(),salaryMax:n().min(0,"Maximum salary must be positive").optional(),postedWithin:i(["day","week","month"]).optional(),sortBy:i(["relevance","date","salary","company"]).optional().default("relevance"),page:n().min(1).optional().default(1),limit:n().min(1).max(100).optional().default(20)}).refine(e=>!e.salaryMin||!e.salaryMax||e.salaryMin<=e.salaryMax,{message:"Minimum salary cannot be greater than maximum salary",path:["salaryMax"]});t({jobId:a().min(1,"Job ID is required"),coverLetter:a().min(100,"Cover letter must be at least 100 characters").max(2e3,"Cover letter must be less than 2000 characters"),resume:v(File).refine(e=>e.size<=5*1024*1024,"Resume file must be less than 5MB").refine(e=>["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document"].includes(e.type),"Resume must be a PDF or Word document"),portfolioUrl:a().url("Please enter a valid URL").optional().or(c("")),availableStartDate:l().min(new Date,"Start date cannot be in the past"),expectedSalary:n().min(0,"Expected salary must be positive").optional(),additionalInfo:a().max(1e3,"Additional information must be less than 1000 characters").optional(),agreeToTerms:o().refine(e=>e===!0,"You must agree to the terms and conditions")});t({name:a().min(1,"Alert name is required").max(100,"Alert name must be less than 100 characters"),keywords:a().min(1,"Keywords are required").max(200,"Keywords must be less than 200 characters"),location:a().optional(),jobType:i(["full-time","part-time","contract","freelance","internship"]).optional(),experienceLevel:i(["entry","mid","senior","lead","executive"]).optional(),workLocation:i(["on-site","remote","hybrid"]).optional(),salaryMin:n().min(0,"Minimum salary must be positive").optional(),frequency:i(["daily","weekly","monthly"]).default("weekly"),isActive:o().default(!0)}).refine(e=>!e.salaryMin||e.salaryMin>0,{message:"Minimum salary must be greater than 0",path:["salaryMin"]});t({firstName:a().min(2,"First name must be at least 2 characters").max(50,"First name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"First name can only contain letters and spaces"),lastName:a().min(2,"Last name must be at least 2 characters").max(50,"Last name must be less than 50 characters").regex(/^[a-zA-Z\s]+$/,"Last name can only contain letters and spaces"),email:a().min(1,"Email is required").email("Please enter a valid email address"),phoneNumber:a().optional().refine(e=>!e||/^\+?[1-9]\d{1,14}$/.test(e),"Please enter a valid phone number"),bio:a().max(500,"Bio must be less than 500 characters").optional(),location:t({city:a().min(1,"City is required"),state:a().min(1,"State is required"),country:a().min(1,"Country is required")}),website:a().url("Please enter a valid URL").optional().or(c("")),linkedinUrl:a().url("Please enter a valid LinkedIn URL").optional().or(c("")),githubUrl:a().url("Please enter a valid GitHub URL").optional().or(c("")),avatar:v(File).refine(e=>e.size<=2*1024*1024,"Avatar file must be less than 2MB").refine(e=>["image/jpeg","image/png","image/webp"].includes(e.type),"Avatar must be a JPEG, PNG, or WebP image").optional()});t({title:a().min(1,"Job title is required").max(100,"Job title must be less than 100 characters"),company:a().min(1,"Company name is required").max(100,"Company name must be less than 100 characters"),location:a().min(1,"Location is required").max(100,"Location must be less than 100 characters"),startDate:l({message:"Start date is required"}),endDate:l().optional(),isCurrentJob:o().default(!1),description:a().min(50,"Description must be at least 50 characters").max(1e3,"Description must be less than 1000 characters"),skills:j(a()).min(1,"At least one skill is required").max(10,"Maximum 10 skills allowed")}).refine(e=>e.isCurrentJob||e.endDate,{message:"End date is required unless this is your current job",path:["endDate"]}).refine(e=>!e.endDate||e.startDate<=e.endDate,{message:"End date cannot be before start date",path:["endDate"]});t({institution:a().min(1,"Institution name is required").max(100,"Institution name must be less than 100 characters"),degree:a().min(1,"Degree is required").max(100,"Degree must be less than 100 characters"),fieldOfStudy:a().min(1,"Field of study is required").max(100,"Field of study must be less than 100 characters"),startDate:l({message:"Start date is required"}),endDate:l().optional(),isCurrentlyStudying:o().default(!1),gpa:n().min(0,"GPA cannot be negative").max(4,"GPA cannot exceed 4.0").optional(),description:a().max(500,"Description must be less than 500 characters").optional()}).refine(e=>e.isCurrentlyStudying||e.endDate,{message:"End date is required unless you are currently studying",path:["endDate"]}).refine(e=>!e.endDate||e.startDate<=e.endDate,{message:"End date cannot be before start date",path:["endDate"]});t({skills:j(t({name:a().min(1,"Skill name is required"),level:i(["beginner","intermediate","advanced","expert"]),yearsOfExperience:n().min(0,"Years of experience cannot be negative").max(50,"Years of experience seems too high").optional()})).min(1,"At least one skill is required").max(20,"Maximum 20 skills allowed")});function U(){const[e,h]=g.useState(!1),[x,b]=g.useState(null),{t:r}=N(),{login:P}=A(),k=S(),M=C().state?.from||"/",{register:d,handleSubmit:L,formState:{errors:m}}=F({resolver:z(I),defaultValues:{email:"",password:"",rememberMe:!1}}),q=async f=>{try{h(!0),b(null),await P(f.email,f.password),k(M,{replace:!0})}catch(D){b(D.message||r("auth.login.loginFailed"))}finally{h(!1)}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"text-center text-sm",children:[s.jsxs("span",{className:"text-gray-600",children:[r("auth.login.noAccount")," "]}),s.jsx(y,{to:E.auth.register.getHref(),className:"font-semibold text-blue-600 hover:text-blue-700 transition-colors",children:r("auth.login.signUp")})]}),s.jsxs("form",{className:"space-y-6",onSubmit:L(q),children:[x&&s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:s.jsxs("div",{className:"flex",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),s.jsxs("div",{className:"ml-3",children:[s.jsx("h3",{className:"text-sm font-medium text-red-800",children:r("auth.login.authenticationError")}),s.jsx("div",{className:"mt-2 text-sm text-red-700",children:s.jsx("p",{children:x})})]})]})}),s.jsxs("div",{className:"space-y-5",children:[s.jsxs("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:r("auth.login.email")}),s.jsx("input",{...d("email"),type:"email",autoComplete:"email",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:r("auth.login.emailPlaceholder")}),m.email&&s.jsx("p",{className:"mt-2 text-sm text-red-600",children:m.email.message})]}),s.jsxs("div",{children:[s.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:r("auth.login.password")}),s.jsx("input",{...d("password"),type:"password",autoComplete:"current-password",className:"block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors sm:text-sm",placeholder:r("auth.login.passwordPlaceholder")}),m.password&&s.jsx("p",{className:"mt-2 text-sm text-red-600",children:m.password.message})]})]}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("input",{...d("rememberMe"),type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),s.jsx("label",{htmlFor:"rememberMe",className:"ml-3 block text-sm text-gray-700",children:r("auth.login.rememberMe")})]}),s.jsx("div",{className:"text-sm",children:s.jsx(y,{to:"#",className:"font-medium text-blue-600 hover:text-blue-700 transition-colors",children:r("auth.login.forgotPassword")})})]}),s.jsx("div",{children:s.jsx("button",{type:"submit",disabled:e,className:"w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:e?s.jsxs("div",{className:"flex items-center",children:[s.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),r("auth.login.signingIn")]}):r("auth.login.signIn")})})]})]})}const Z=()=>{const{t:e}=N();return s.jsx(R,{title:e("auth.login.title"),subtitle:e("auth.login.subtitle"),children:s.jsx(U,{})})};export{Z as default};
