import{u as on,c as sn,a as un,r as an,p as xe,j as k,L as Te,R as Y}from"./index-Bxm2R2OW.js";const D2=({children:e,title:t,subtitle:r})=>{const{isAuthenticated:n}=on(),[o]=sn(),s=o.get("redirectTo"),i=un();return an.useEffect(()=>{n&&i(s||xe.app.dashboard.getHref(),{replace:!0})},[n,i,s]),k.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex flex-col lg:flex-row",children:[k.jsxs("div",{className:"hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden",children:[k.jsxs("div",{className:"absolute inset-0 opacity-10",children:[k.jsx("div",{className:"absolute top-20 left-20 w-72 h-72 bg-white rounded-full blur-3xl"}),k.jsx("div",{className:"absolute bottom-20 right-20 w-96 h-96 bg-white rounded-full blur-3xl"})]}),k.jsx("div",{className:"relative z-10 flex flex-col justify-center px-12 xl:px-16 2xl:px-20 text-white",children:k.jsxs("div",{className:"max-w-lg",children:[k.jsx("div",{className:"mb-12",children:k.jsx(Te,{to:xe.home.getHref(),className:"inline-flex items-center group transition-transform duration-200 hover:scale-105","aria-label":"WorkFinder - Home",children:k.jsxs("svg",{width:"195",height:"48",viewBox:"0 0 195 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-10 w-auto",children:[k.jsx("path",{d:"M34.3358 2.61818C34.9056 3.10635 35.1577 3.74331 35.4544 4.36506C35.5112 4.48196 35.5679 4.59886 35.6264 4.71931C35.8132 5.10493 35.9984 5.49107 36.1834 5.87727C36.3126 6.14573 36.4419 6.41418 36.5712 6.68262C36.8417 7.24482 37.1114 7.8073 37.3805 8.36996C37.7258 9.09162 38.0739 9.81233 38.4227 10.5328C38.6903 11.0867 38.9562 11.6411 39.2215 12.1957C39.3492 12.4617 39.4774 12.7275 39.6063 12.9932C39.786 13.3641 39.9633 13.7357 40.1399 14.1077C40.1937 14.2176 40.2476 14.3274 40.3031 14.4407C40.5539 14.9752 40.707 15.3099 40.4671 15.8545C40.1168 16.1455 40.1168 16.1455 39.4893 16.1864C38.6757 16.1693 37.9614 15.9927 37.1934 15.7818C36.054 15.4653 36.054 15.4653 34.8613 15.4182C34.7678 15.5697 34.6743 15.7212 34.578 15.8773C30.153 22.978 23.6096 28.3944 14.2911 30.8668C8.29726 32.3926 8.29726 32.3926 6.31478 31.6102C4.72118 30.8982 4.72118 30.8982 4.20437 30.2545C4.18248 29.9091 4.18248 29.9091 4.37956 29.5273C5.24309 28.9806 6.2524 28.5838 7.21532 28.1727C8.52798 27.6029 9.79051 26.9938 11.0365 26.3273C11.1426 26.2708 11.2488 26.2144 11.3581 26.1562C13.6578 24.9288 15.7085 23.5206 17.6934 21.9636C17.8454 21.8453 17.9974 21.727 18.154 21.6051C20.7927 19.4896 24.2125 15.9118 24.7007 12.8C24.5356 12.7848 24.3706 12.7696 24.2005 12.754C23.3319 12.6506 22.5059 12.4383 21.6679 12.2273C21.5092 12.1887 21.3506 12.1502 21.1872 12.1105C21.0357 12.0727 20.8843 12.035 20.7283 11.996C20.5226 11.9449 20.5226 11.9449 20.3127 11.8927C19.9708 11.7818 19.9708 11.7818 19.6204 11.4909C19.5342 11.15 19.5342 11.15 19.6204 10.7636C19.9613 10.5052 20.2657 10.3141 20.6496 10.1091C20.8761 9.98182 21.1023 9.85435 21.3285 9.72671C21.4495 9.65937 21.5705 9.59203 21.6952 9.52266C22.3505 9.15217 22.9876 8.76091 23.6277 8.37273C24.6184 7.77619 25.6166 7.19058 26.6277 6.61818C28.6158 5.49212 30.5921 4.34046 32.4738 3.09375C33.1341 2.66645 33.4982 2.53125 34.3358 2.61818Z",fill:"white"}),k.jsx("path",{d:"M34.6861 19.3455C36.0778 19.7049 37.4016 20.1727 38.7372 20.6545C38.9559 20.733 39.1747 20.8114 39.3935 20.8898C39.8073 21.0381 40.2209 21.1869 40.6342 21.3361C41.0551 21.4869 41.4774 21.633 41.9015 21.7773C42.4975 22.0027 42.8477 22.2784 43.2701 22.6909C43.4778 23.2084 43.4769 23.6822 43.4808 24.221C43.4818 24.3281 43.4828 24.4352 43.4839 24.5455C43.4855 24.7721 43.4867 24.9986 43.4873 25.2252C43.489 25.57 43.4945 25.9146 43.5 26.2594C43.5011 26.4799 43.5021 26.7005 43.5027 26.921C43.506 27.0745 43.506 27.0745 43.5093 27.231C43.5056 27.911 43.3613 28.3727 42.9197 28.9455C42.6047 29.1852 42.6047 29.1852 42.2429 29.3716C42.103 29.4437 41.9631 29.5159 41.8189 29.5902C41.0046 29.9803 40.1802 30.3529 39.3484 30.7166C38.9135 30.9067 38.4796 31.0984 38.0457 31.29C37.825 31.3875 37.6042 31.4848 37.3834 31.5821C36.4074 32.0124 35.4364 32.4502 34.4671 32.8909C33.493 33.3337 32.5183 33.7755 31.5404 34.2125C29.8151 34.9838 28.0974 35.763 26.3978 36.5727C26.2452 36.6452 26.0926 36.7176 25.9354 36.7922C25.3181 37.0855 24.7022 37.3798 24.0931 37.6847C23.9287 37.7647 23.7643 37.8448 23.5949 37.9273C23.4655 37.9925 23.3361 38.0578 23.2028 38.125C22.3038 38.3964 21.5936 38.3261 20.727 38.046C20.5062 37.9513 20.2871 37.854 20.0693 37.7545C19.9493 37.7022 19.8292 37.6499 19.7055 37.596C19.3254 37.4297 18.9474 37.2603 18.5693 37.0909C18.3273 36.9848 18.085 36.8789 17.8426 36.7733C17.3511 36.5589 16.8605 36.3431 16.3707 36.1261C15.821 35.8829 15.2703 35.6414 14.7187 35.4011C14.5721 35.3373 14.4255 35.2734 14.2745 35.2076C13.9942 35.0856 13.7138 34.9636 13.4334 34.8418C12.5098 34.4393 11.5959 34.0237 10.6861 33.6C10.6861 33.456 10.6861 33.312 10.6861 33.1636C10.8111 33.1345 10.936 33.1053 11.0647 33.0753C20.9596 30.749 28.6197 26.6925 34.5109 19.6364C34.5688 19.5404 34.6266 19.4444 34.6861 19.3455Z",fill:"white"}),k.jsx("path",{d:"M42.9197 30.9818C43.2022 31.1414 43.2022 31.1414 43.4453 31.4182C43.5452 32.0528 43.528 32.6903 43.5234 33.3297C43.5219 33.6429 43.5269 33.9558 43.5325 34.269C43.5328 34.4697 43.5327 34.6704 43.5322 34.871C43.5325 35.0526 43.5328 35.2341 43.5331 35.4212C43.4281 36.0263 43.2112 36.3311 42.7445 36.8C42.3696 37.0316 42.3696 37.0316 41.9624 37.2057C41.8098 37.2726 41.6572 37.3395 41.5 37.4084C41.3362 37.4776 41.1724 37.5469 41.0037 37.6182C40.652 37.7721 40.3005 37.9262 39.9491 38.0807C39.7713 38.1583 39.5934 38.2358 39.4102 38.3158C38.6364 38.6578 37.8762 39.0183 37.1168 39.3818C36.9686 39.4525 36.8204 39.5231 36.6678 39.5959C36.353 39.746 36.0383 39.8962 35.7237 40.0465C34.7509 40.5109 33.777 40.9737 32.8029 41.4364C32.6997 41.4854 32.5965 41.5345 32.4901 41.585C30.8004 42.388 29.1042 43.1809 27.4 43.9625C26.4515 44.3999 25.5441 44.8588 24.646 45.3636C23.5907 45.902 22.7631 46.0578 21.5475 45.8182C20.5587 45.5074 19.6602 45.0906 18.7445 44.6546C18.5823 44.5781 18.4201 44.5016 18.253 44.4228C17.2021 43.9263 16.1558 43.4234 15.1112 42.9179C13.9819 42.3717 12.8493 41.8303 11.7153 41.2909C11.5633 41.2184 11.5633 41.2184 11.4081 41.1445C9.7579 40.3586 8.09112 39.6003 6.40264 38.8724C4.62593 38.0921 4.62593 38.0921 4.20439 37.5273C4.0873 36.8401 4.09831 36.1654 4.1168 35.4727C4.11634 35.2854 4.11589 35.0981 4.11543 34.9051C4.13268 33.5372 4.13268 33.5372 4.44321 33.1792C4.72993 33.0182 4.72993 33.0182 5.19526 33.0244C5.94779 33.2033 6.58559 33.4796 7.27008 33.7909C7.41479 33.8555 7.5595 33.92 7.70859 33.9865C8.17648 34.1957 8.64307 34.4068 9.1095 34.6182C9.41856 34.7576 9.72764 34.897 10.0367 35.0364C10.6773 35.3257 11.3173 35.616 11.9569 35.9068C13.1737 36.4598 14.3941 37.007 15.6144 37.5546C16.4761 37.9417 17.3363 38.3308 18.1937 38.7244C18.355 38.7985 18.5163 38.8725 18.6825 38.9488C18.9864 39.0886 19.2901 39.2286 19.5935 39.3691C19.7282 39.431 19.8628 39.493 20.0016 39.5568C20.1189 39.6111 20.2361 39.6653 20.357 39.7212C21.2137 40.0844 22.0111 40.1303 22.9489 40C23.877 39.6904 24.7199 39.2608 25.5766 38.8364C25.9302 38.6634 26.2837 38.4905 26.6373 38.3176C27.3416 37.9724 28.045 37.6258 28.7474 37.278C30.2177 36.5501 31.6985 35.838 33.1829 35.13C34.3002 34.5969 35.4137 34.059 36.5245 33.5166C37.9273 32.8327 39.3327 32.1531 40.7628 31.5091C40.8813 31.4545 40.9999 31.3998 41.1221 31.3435C41.7371 31.0717 42.2197 30.8739 42.9197 30.9818Z",fill:"white"}),k.jsx("path",{d:"M66.1 26.475L69.5 16.5H74.525L66.95 35.025L61.3 24.475L55.675 35.025L48.1 16.5H53.1L56.55 26.475L61.3 15.625L66.1 26.475ZM73.4338 28.25C73.4338 27.0333 73.7171 25.975 74.2838 25.075C74.8505 24.175 75.6338 23.475 76.6338 22.975C77.6338 22.475 78.7588 22.225 80.0088 22.225C81.2588 22.225 82.3755 22.475 83.3588 22.975C84.3588 23.475 85.1421 24.175 85.7088 25.075C86.2921 25.975 86.5838 27.0333 86.5838 28.25C86.5838 29.45 86.2921 30.5 85.7088 31.4C85.1421 32.3 84.3588 33 83.3588 33.5C82.3755 34 81.2588 34.25 80.0088 34.25C78.7588 34.25 77.6338 34 76.6338 33.5C75.6338 33 74.8505 32.3 74.2838 31.4C73.7171 30.5 73.4338 29.45 73.4338 28.25ZM77.3088 28.25C77.3088 28.85 77.4338 29.3667 77.6838 29.8C77.9338 30.2167 78.2588 30.5417 78.6588 30.775C79.0588 31.0083 79.5088 31.125 80.0088 31.125C80.4921 31.125 80.9338 31.0083 81.3338 30.775C81.7505 30.5417 82.0755 30.2167 82.3088 29.8C82.5588 29.3667 82.6838 28.85 82.6838 28.25C82.6838 27.65 82.5588 27.1333 82.3088 26.7C82.0755 26.2667 81.7505 25.9333 81.3338 25.7C80.9338 25.4667 80.4921 25.35 80.0088 25.35C79.5088 25.35 79.0588 25.4667 78.6588 25.7C78.2588 25.9333 77.9338 26.2667 77.6838 26.7C77.4338 27.1333 77.3088 27.65 77.3088 28.25ZM92.5799 22.5V34H88.8549V22.5H92.5799ZM95.9049 26.4C95.7049 26.2 95.4882 26.0417 95.2549 25.925C95.0382 25.8083 94.7632 25.75 94.4299 25.75C94.0632 25.75 93.7382 25.85 93.4549 26.05C93.1715 26.25 92.9549 26.5417 92.8049 26.925C92.6549 27.3083 92.5799 27.775 92.5799 28.325L91.6799 27C91.6799 26.0833 91.8549 25.2667 92.2049 24.55C92.5715 23.8333 93.0465 23.2667 93.6299 22.85C94.2132 22.4333 94.8216 22.225 95.4549 22.225C95.8715 22.225 96.2715 22.3083 96.6549 22.475C97.0382 22.6417 97.3299 22.875 97.5299 23.175L95.9049 26.4ZM99.1082 14.5H102.733V34H99.1082V14.5ZM106.908 22.5H111.433L106.508 27.25L111.933 34H107.483L101.983 27.25L106.908 22.5Z",fill:"white"}),k.jsx("path",{d:"M116.737 20.125V16.5H124.562V20.125H116.737ZM116.737 26.975V23.575H124.312V26.975H116.737ZM113.937 16.5H117.987V34H113.937V16.5ZM127.155 18.175C127.155 17.575 127.372 17.0917 127.805 16.725C128.239 16.3417 128.755 16.15 129.355 16.15C129.972 16.15 130.489 16.3417 130.905 16.725C131.339 17.0917 131.555 17.575 131.555 18.175C131.555 18.775 131.339 19.2667 130.905 19.65C130.489 20.0333 129.972 20.225 129.355 20.225C128.755 20.225 128.239 20.0333 127.805 19.65C127.372 19.2667 127.155 18.775 127.155 18.175ZM127.53 22.5H131.18V34H127.53V22.5ZM141.764 27C141.764 26.3167 141.631 25.7917 141.364 25.425C141.114 25.0583 140.689 24.875 140.089 24.875C139.689 24.875 139.339 24.9583 139.039 25.125C138.739 25.2917 138.506 25.5333 138.339 25.85C138.173 26.1667 138.089 26.55 138.089 27V34H134.289V22.5H138.089V24.225C138.423 23.5917 138.873 23.1 139.439 22.75C140.023 22.4 140.739 22.225 141.589 22.225C143.023 22.225 144.064 22.6083 144.714 23.375C145.364 24.1417 145.689 25.2167 145.689 26.6V34H141.764V27ZM157.394 14.5H161.169V34H157.394V14.5ZM147.994 28.25C147.994 26.95 148.253 25.8583 148.769 24.975C149.303 24.0917 149.986 23.4167 150.819 22.95C151.653 22.4833 152.536 22.25 153.469 22.25C154.436 22.25 155.286 22.4917 156.019 22.975C156.769 23.4583 157.361 24.15 157.794 25.05C158.228 25.9333 158.444 27 158.444 28.25C158.444 29.4833 158.228 30.55 157.794 31.45C157.361 32.35 156.769 33.0417 156.019 33.525C155.286 34.0083 154.436 34.25 153.469 34.25C152.536 34.25 151.653 34.0167 150.819 33.55C149.986 33.0833 149.303 32.4083 148.769 31.525C148.253 30.625 147.994 29.5333 147.994 28.25ZM151.969 28.25C151.969 28.85 152.094 29.375 152.344 29.825C152.594 30.2583 152.928 30.6 153.344 30.85C153.761 31.0833 154.219 31.2 154.719 31.2C155.153 31.2 155.569 31.0833 155.969 30.85C156.386 30.6167 156.728 30.2833 156.994 29.85C157.261 29.4 157.394 28.8667 157.394 28.25C157.394 27.6333 157.261 27.1083 156.994 26.675C156.728 26.225 156.386 25.8833 155.969 25.65C155.569 25.4167 155.153 25.3 154.719 25.3C154.219 25.3 153.761 25.425 153.344 25.675C152.928 25.9083 152.594 26.25 152.344 26.7C152.094 27.1333 151.969 27.65 151.969 28.25ZM169.949 34.25C168.616 34.25 167.457 34.0083 166.474 33.525C165.507 33.025 164.757 32.325 164.224 31.425C163.691 30.525 163.424 29.4667 163.424 28.25C163.424 27.0167 163.682 25.95 164.199 25.05C164.716 24.15 165.457 23.4583 166.424 22.975C167.407 22.4917 168.566 22.25 169.899 22.25C171.232 22.25 172.357 22.4833 173.274 22.95C174.191 23.4 174.891 24.0583 175.374 24.925C175.857 25.7917 176.099 26.8417 176.099 28.075C176.099 28.2583 176.099 28.4417 176.099 28.625C176.099 28.7917 176.082 28.9333 176.049 29.05H165.549V26.95H172.649L171.749 28.1C171.832 27.9833 171.899 27.8417 171.949 27.675C172.016 27.4917 172.049 27.3417 172.049 27.225C172.049 26.725 171.957 26.2917 171.774 25.925C171.591 25.5583 171.332 25.275 170.999 25.075C170.666 24.875 170.274 24.775 169.824 24.775C169.257 24.775 168.782 24.9 168.399 25.15C168.016 25.4 167.724 25.7833 167.524 26.3C167.341 26.8 167.241 27.4333 167.224 28.2C167.224 28.9 167.324 29.5 167.524 30C167.724 30.5 168.024 30.8833 168.424 31.15C168.824 31.4 169.316 31.525 169.899 31.525C170.566 31.525 171.132 31.4 171.599 31.15C172.082 30.9 172.466 30.525 172.749 30.025L176.124 30.85C175.524 31.9833 174.707 32.8333 173.674 33.4C172.657 33.9667 171.416 34.25 169.949 34.25ZM182.155 22.5V34H178.43V22.5H182.155ZM185.48 26.4C185.28 26.2 185.063 26.0417 184.83 25.925C184.613 25.8083 184.338 25.75 184.005 25.75C183.638 25.75 183.313 25.85 183.03 26.05C182.747 26.25 182.53 26.5417 182.38 26.925C182.23 27.3083 182.155 27.775 182.155 28.325L181.255 27C181.255 26.0833 181.43 25.2667 181.78 24.55C182.147 23.8333 182.622 23.2667 183.205 22.85C183.788 22.4333 184.397 22.225 185.03 22.225C185.447 22.225 185.847 22.3083 186.23 22.475C186.613 22.6417 186.905 22.875 187.105 23.175L185.48 26.4Z",fill:"white"})]})})}),k.jsx("h1",{className:"text-4xl xl:text-5xl font-bold leading-tight mb-6",children:"Find Your Perfect Career Opportunity"}),k.jsx("p",{className:"text-xl text-blue-100 leading-relaxed mb-8",children:"Connect with thousands of employers and discover jobs that match your skills, interests, and career goals."}),k.jsxs("div",{className:"space-y-4",children:[k.jsxs("div",{className:"flex items-center space-x-3",children:[k.jsx("div",{className:"w-2 h-2 bg-white rounded-full"}),k.jsx("span",{className:"text-blue-100",children:"Access to premium job listings"})]}),k.jsxs("div",{className:"flex items-center space-x-3",children:[k.jsx("div",{className:"w-2 h-2 bg-white rounded-full"}),k.jsx("span",{className:"text-blue-100",children:"AI-powered job matching"})]}),k.jsxs("div",{className:"flex items-center space-x-3",children:[k.jsx("div",{className:"w-2 h-2 bg-white rounded-full"}),k.jsx("span",{className:"text-blue-100",children:"Direct communication with recruiters"})]})]})]})})]}),k.jsxs("div",{className:"flex-1 flex flex-col justify-center px-4 py-12 sm:px-6 lg:px-8 xl:px-12 2xl:px-16",children:[k.jsx("div",{className:"lg:hidden text-center mb-8",children:k.jsx(Te,{to:xe.home.getHref(),className:"inline-flex items-center group transition-transform duration-200 hover:scale-105","aria-label":"WorkFinder - Home",children:k.jsxs("svg",{width:"195",height:"48",viewBox:"0 0 195 48",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"h-10 w-auto",children:[k.jsx("path",{d:"M34.3358 2.61818C34.9056 3.10635 35.1577 3.74331 35.4544 4.36506C35.5112 4.48196 35.5679 4.59886 35.6264 4.71931C35.8132 5.10493 35.9984 5.49107 36.1834 5.87727C36.3126 6.14573 36.4419 6.41418 36.5712 6.68262C36.8417 7.24482 37.1114 7.8073 37.3805 8.36996C37.7258 9.09162 38.0739 9.81233 38.4227 10.5328C38.6903 11.0867 38.9562 11.6411 39.2215 12.1957C39.3492 12.4617 39.4774 12.7275 39.6063 12.9932C39.786 13.3641 39.9633 13.7357 40.1399 14.1077C40.1937 14.2176 40.2476 14.3274 40.3031 14.4407C40.5539 14.9752 40.707 15.3099 40.4671 15.8545C40.1168 16.1455 40.1168 16.1455 39.4893 16.1864C38.6757 16.1693 37.9614 15.9927 37.1934 15.7818C36.054 15.4653 36.054 15.4653 34.8613 15.4182C34.7678 15.5697 34.6743 15.7212 34.578 15.8773C30.153 22.978 23.6096 28.3944 14.2911 30.8668C8.29726 32.3926 8.29726 32.3926 6.31478 31.6102C4.72118 30.8982 4.72118 30.8982 4.20437 30.2545C4.18248 29.9091 4.18248 29.9091 4.37956 29.5273C5.24309 28.9806 6.2524 28.5838 7.21532 28.1727C8.52798 27.6029 9.79051 26.9938 11.0365 26.3273C11.1426 26.2708 11.2488 26.2144 11.3581 26.1562C13.6578 24.9288 15.7085 23.5206 17.6934 21.9636C17.8454 21.8453 17.9974 21.727 18.154 21.6051C20.7927 19.4896 24.2125 15.9118 24.7007 12.8C24.5356 12.7848 24.3706 12.7696 24.2005 12.754C23.3319 12.6506 22.5059 12.4383 21.6679 12.2273C21.5092 12.1887 21.3506 12.1502 21.1872 12.1105C21.0357 12.0727 20.8843 12.035 20.7283 11.996C20.5226 11.9449 20.5226 11.9449 20.3127 11.8927C19.9708 11.7818 19.9708 11.7818 19.6204 11.4909C19.5342 11.15 19.5342 11.15 19.6204 10.7636C19.9613 10.5052 20.2657 10.3141 20.6496 10.1091C20.8761 9.98182 21.1023 9.85435 21.3285 9.72671C21.4495 9.65937 21.5705 9.59203 21.6952 9.52266C22.3505 9.15217 22.9876 8.76091 23.6277 8.37273C24.6184 7.77619 25.6166 7.19058 26.6277 6.61818C28.6158 5.49212 30.5921 4.34046 32.4738 3.09375C33.1341 2.66645 33.4982 2.53125 34.3358 2.61818Z",fill:"#1967D2"}),k.jsx("path",{d:"M34.6861 19.3455C36.0778 19.7049 37.4016 20.1727 38.7372 20.6545C38.9559 20.733 39.1747 20.8114 39.3935 20.8898C39.8073 21.0381 40.2209 21.1869 40.6342 21.3361C41.0551 21.4869 41.4774 21.633 41.9015 21.7773C42.4975 22.0027 42.8477 22.2784 43.2701 22.6909C43.4778 23.2084 43.4769 23.6822 43.4808 24.221C43.4818 24.3281 43.4828 24.4352 43.4839 24.5455C43.4855 24.7721 43.4867 24.9986 43.4873 25.2252C43.489 25.57 43.4945 25.9146 43.5 26.2594C43.5011 26.4799 43.5021 26.7005 43.5027 26.921C43.506 27.0745 43.506 27.0745 43.5093 27.231C43.5056 27.911 43.3613 28.3727 42.9197 28.9455C42.6047 29.1852 42.6047 29.1852 42.2429 29.3716C42.103 29.4437 41.9631 29.5159 41.8189 29.5902C41.0046 29.9803 40.1802 30.3529 39.3484 30.7166C38.9135 30.9067 38.4796 31.0984 38.0457 31.29C37.825 31.3875 37.6042 31.4848 37.3834 31.5821C36.4074 32.0124 35.4364 32.4502 34.4671 32.8909C33.493 33.3337 32.5183 33.7755 31.5404 34.2125C29.8151 34.9838 28.0974 35.763 26.3978 36.5727C26.2452 36.6452 26.0926 36.7176 25.9354 36.7922C25.3181 37.0855 24.7022 37.3798 24.0931 37.6847C23.9287 37.7647 23.7643 37.8448 23.5949 37.9273C23.4655 37.9925 23.3361 38.0578 23.2028 38.125C22.3038 38.3964 21.5936 38.3261 20.727 38.046C20.5062 37.9513 20.2871 37.854 20.0693 37.7545C19.9493 37.7022 19.8292 37.6499 19.7055 37.596C19.3254 37.4297 18.9474 37.2603 18.5693 37.0909C18.3273 36.9848 18.085 36.8789 17.8426 36.7733C17.3511 36.5589 16.8605 36.3431 16.3707 36.1261C15.821 35.8829 15.2703 35.6414 14.7187 35.4011C14.5721 35.3373 14.4255 35.2734 14.2745 35.2076C13.9942 35.0856 13.7138 34.9636 13.4334 34.8418C12.5098 34.4393 11.5959 34.0237 10.6861 33.6C10.6861 33.456 10.6861 33.312 10.6861 33.1636C10.8111 33.1345 10.936 33.1053 11.0647 33.0753C20.9596 30.749 28.6197 26.6925 34.5109 19.6364C34.5688 19.5404 34.6266 19.4444 34.6861 19.3455Z",fill:"#F47C1A"}),k.jsx("path",{d:"M42.9197 30.9818C43.2022 31.1414 43.2022 31.1414 43.4453 31.4182C43.5452 32.0528 43.528 32.6903 43.5234 33.3297C43.5219 33.6429 43.5269 33.9558 43.5325 34.269C43.5328 34.4697 43.5327 34.6704 43.5322 34.871C43.5325 35.0526 43.5328 35.2341 43.5331 35.4212C43.4281 36.0263 43.2112 36.3311 42.7445 36.8C42.3696 37.0316 42.3696 37.0316 41.9624 37.2057C41.8098 37.2726 41.6572 37.3395 41.5 37.4084C41.3362 37.4776 41.1724 37.5469 41.0037 37.6182C40.652 37.7721 40.3005 37.9262 39.9491 38.0807C39.7713 38.1583 39.5934 38.2358 39.4102 38.3158C38.6364 38.6578 37.8762 39.0183 37.1168 39.3818C36.9686 39.4525 36.8204 39.5231 36.6678 39.5959C36.353 39.746 36.0383 39.8962 35.7237 40.0465C34.7509 40.5109 33.777 40.9737 32.8029 41.4364C32.6997 41.4854 32.5965 41.5345 32.4901 41.585C30.8004 42.388 29.1042 43.1809 27.4 43.9625C26.4515 44.3999 25.5441 44.8588 24.646 45.3636C23.5907 45.902 22.7631 46.0578 21.5475 45.8182C20.5587 45.5074 19.6602 45.0906 18.7445 44.6546C18.5823 44.5781 18.4201 44.5016 18.253 44.4228C17.2021 43.9263 16.1558 43.4234 15.1112 42.9179C13.9819 42.3717 12.8493 41.8303 11.7153 41.2909C11.5633 41.2184 11.5633 41.2184 11.4081 41.1445C9.7579 40.3586 8.09112 39.6003 6.40264 38.8724C4.62593 38.0921 4.62593 38.0921 4.20439 37.5273C4.0873 36.8401 4.09831 36.1654 4.1168 35.4727C4.11634 35.2854 4.11589 35.0981 4.11543 34.9051C4.13268 33.5372 4.13268 33.5372 4.44321 33.1792C4.72993 33.0182 4.72993 33.0182 5.19526 33.0244C5.94779 33.2033 6.58559 33.4796 7.27008 33.7909C7.41479 33.8555 7.5595 33.92 7.70859 33.9865C8.17648 34.1957 8.64307 34.4068 9.1095 34.6182C9.41856 34.7576 9.72764 34.897 10.0367 35.0364C10.6773 35.3257 11.3173 35.616 11.9569 35.9068C13.1737 36.4598 14.3941 37.007 15.6144 37.5546C16.4761 37.9417 17.3363 38.3308 18.1937 38.7244C18.355 38.7985 18.5163 38.8725 18.6825 38.9488C18.9864 39.0886 19.2901 39.2286 19.5935 39.3691C19.7282 39.431 19.8628 39.493 20.0016 39.5568C20.1189 39.6111 20.2361 39.6653 20.357 39.7212C21.2137 40.0844 22.0111 40.1303 22.9489 40C23.877 39.6904 24.7199 39.2608 25.5766 38.8364C25.9302 38.6634 26.2837 38.4905 26.6373 38.3176C27.3416 37.9724 28.045 37.6258 28.7474 37.278C30.2177 36.5501 31.6985 35.838 33.1829 35.13C34.3002 34.5969 35.4137 34.059 36.5245 33.5166C37.9273 32.8327 39.3327 32.1531 40.7628 31.5091C40.8813 31.4545 40.9999 31.3998 41.1221 31.3435C41.7371 31.0717 42.2197 30.8739 42.9197 30.9818Z",fill:"#3C3F48"}),k.jsx("path",{d:"M66.1 26.475L69.5 16.5H74.525L66.95 35.025L61.3 24.475L55.675 35.025L48.1 16.5H53.1L56.55 26.475L61.3 15.625L66.1 26.475ZM73.4338 28.25C73.4338 27.0333 73.7171 25.975 74.2838 25.075C74.8505 24.175 75.6338 23.475 76.6338 22.975C77.6338 22.475 78.7588 22.225 80.0088 22.225C81.2588 22.225 82.3755 22.475 83.3588 22.975C84.3588 23.475 85.1421 24.175 85.7088 25.075C86.2921 25.975 86.5838 27.0333 86.5838 28.25C86.5838 29.45 86.2921 30.5 85.7088 31.4C85.1421 32.3 84.3588 33 83.3588 33.5C82.3755 34 81.2588 34.25 80.0088 34.25C78.7588 34.25 77.6338 34 76.6338 33.5C75.6338 33 74.8505 32.3 74.2838 31.4C73.7171 30.5 73.4338 29.45 73.4338 28.25ZM77.3088 28.25C77.3088 28.85 77.4338 29.3667 77.6838 29.8C77.9338 30.2167 78.2588 30.5417 78.6588 30.775C79.0588 31.0083 79.5088 31.125 80.0088 31.125C80.4921 31.125 80.9338 31.0083 81.3338 30.775C81.7505 30.5417 82.0755 30.2167 82.3088 29.8C82.5588 29.3667 82.6838 28.85 82.6838 28.25C82.6838 27.65 82.5588 27.1333 82.3088 26.7C82.0755 26.2667 81.7505 25.9333 81.3338 25.7C80.9338 25.4667 80.4921 25.35 80.0088 25.35C79.5088 25.35 79.0588 25.4667 78.6588 25.7C78.2588 25.9333 77.9338 26.2667 77.6838 26.7C77.4338 27.1333 77.3088 27.65 77.3088 28.25ZM92.5799 22.5V34H88.8549V22.5H92.5799ZM95.9049 26.4C95.7049 26.2 95.4882 26.0417 95.2549 25.925C95.0382 25.8083 94.7632 25.75 94.4299 25.75C94.0632 25.75 93.7382 25.85 93.4549 26.05C93.1715 26.25 92.9549 26.5417 92.8049 26.925C92.6549 27.3083 92.5799 27.775 92.5799 28.325L91.6799 27C91.6799 26.0833 91.8549 25.2667 92.2049 24.55C92.5715 23.8333 93.0465 23.2667 93.6299 22.85C94.2132 22.4333 94.8216 22.225 95.4549 22.225C95.8715 22.225 96.2715 22.3083 96.6549 22.475C97.0382 22.6417 97.3299 22.875 97.5299 23.175L95.9049 26.4ZM99.1082 14.5H102.733V34H99.1082V14.5ZM106.908 22.5H111.433L106.508 27.25L111.933 34H107.483L101.983 27.25L106.908 22.5Z",fill:"#1967D2"}),k.jsx("path",{d:"M116.737 20.125V16.5H124.562V20.125H116.737ZM116.737 26.975V23.575H124.312V26.975H116.737ZM113.937 16.5H117.987V34H113.937V16.5ZM127.155 18.175C127.155 17.575 127.372 17.0917 127.805 16.725C128.239 16.3417 128.755 16.15 129.355 16.15C129.972 16.15 130.489 16.3417 130.905 16.725C131.339 17.0917 131.555 17.575 131.555 18.175C131.555 18.775 131.339 19.2667 130.905 19.65C130.489 20.0333 129.972 20.225 129.355 20.225C128.755 20.225 128.239 20.0333 127.805 19.65C127.372 19.2667 127.155 18.775 127.155 18.175ZM127.53 22.5H131.18V34H127.53V22.5ZM141.764 27C141.764 26.3167 141.631 25.7917 141.364 25.425C141.114 25.0583 140.689 24.875 140.089 24.875C139.689 24.875 139.339 24.9583 139.039 25.125C138.739 25.2917 138.506 25.5333 138.339 25.85C138.173 26.1667 138.089 26.55 138.089 27V34H134.289V22.5H138.089V24.225C138.423 23.5917 138.873 23.1 139.439 22.75C140.023 22.4 140.739 22.225 141.589 22.225C143.023 22.225 144.064 22.6083 144.714 23.375C145.364 24.1417 145.689 25.2167 145.689 26.6V34H141.764V27ZM157.394 14.5H161.169V34H157.394V14.5ZM147.994 28.25C147.994 26.95 148.253 25.8583 148.769 24.975C149.303 24.0917 149.986 23.4167 150.819 22.95C151.653 22.4833 152.536 22.25 153.469 22.25C154.436 22.25 155.286 22.4917 156.019 22.975C156.769 23.4583 157.361 24.15 157.794 25.05C158.228 25.9333 158.444 27 158.444 28.25C158.444 29.4833 158.228 30.55 157.794 31.45C157.361 32.35 156.769 33.0417 156.019 33.525C155.286 34.0083 154.436 34.25 153.469 34.25C152.536 34.25 151.653 34.0167 150.819 33.55C149.986 33.0833 149.303 32.4083 148.769 31.525C148.253 30.625 147.994 29.5333 147.994 28.25ZM151.969 28.25C151.969 28.85 152.094 29.375 152.344 29.825C152.594 30.2583 152.928 30.6 153.344 30.85C153.761 31.0833 154.219 31.2 154.719 31.2C155.153 31.2 155.569 31.0833 155.969 30.85C156.386 30.6167 156.728 30.2833 156.994 29.85C157.261 29.4 157.394 28.8667 157.394 28.25C157.394 27.6333 157.261 27.1083 156.994 26.675C156.728 26.225 156.386 25.8833 155.969 25.65C155.569 25.4167 155.153 25.3 154.719 25.3C154.219 25.3 153.761 25.425 153.344 25.675C152.928 25.9083 152.594 26.25 152.344 26.7C152.094 27.1333 151.969 27.65 151.969 28.25ZM169.949 34.25C168.616 34.25 167.457 34.0083 166.474 33.525C165.507 33.025 164.757 32.325 164.224 31.425C163.691 30.525 163.424 29.4667 163.424 28.25C163.424 27.0167 163.682 25.95 164.199 25.05C164.716 24.15 165.457 23.4583 166.424 22.975C167.407 22.4917 168.566 22.25 169.899 22.25C171.232 22.25 172.357 22.4833 173.274 22.95C174.191 23.4 174.891 24.0583 175.374 24.925C175.857 25.7917 176.099 26.8417 176.099 28.075C176.099 28.2583 176.099 28.4417 176.099 28.625C176.099 28.7917 176.082 28.9333 176.049 29.05H165.549V26.95H172.649L171.749 28.1C171.832 27.9833 171.899 27.8417 171.949 27.675C172.016 27.4917 172.049 27.3417 172.049 27.225C172.049 26.725 171.957 26.2917 171.774 25.925C171.591 25.5583 171.332 25.275 170.999 25.075C170.666 24.875 170.274 24.775 169.824 24.775C169.257 24.775 168.782 24.9 168.399 25.15C168.016 25.4 167.724 25.7833 167.524 26.3C167.341 26.8 167.241 27.4333 167.224 28.2C167.224 28.9 167.324 29.5 167.524 30C167.724 30.5 168.024 30.8833 168.424 31.15C168.824 31.4 169.316 31.525 169.899 31.525C170.566 31.525 171.132 31.4 171.599 31.15C172.082 30.9 172.466 30.525 172.749 30.025L176.124 30.85C175.524 31.9833 174.707 32.8333 173.674 33.4C172.657 33.9667 171.416 34.25 169.949 34.25ZM182.155 22.5V34H178.43V22.5H182.155ZM185.48 26.4C185.28 26.2 185.063 26.0417 184.83 25.925C184.613 25.8083 184.338 25.75 184.005 25.75C183.638 25.75 183.313 25.85 183.03 26.05C182.747 26.25 182.53 26.5417 182.38 26.925C182.23 27.3083 182.155 27.775 182.155 28.325L181.255 27C181.255 26.0833 181.43 25.2667 181.78 24.55C182.147 23.8333 182.622 23.2667 183.205 22.85C183.788 22.4333 184.397 22.225 185.03 22.225C185.447 22.225 185.847 22.3083 186.23 22.475C186.613 22.6417 186.905 22.875 187.105 23.175L185.48 26.4Z",fill:"#4A5568"})]})})}),k.jsxs("div",{className:"w-full max-w-md mx-auto",children:[k.jsxs("div",{className:"text-center mb-8",children:[k.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:t}),r&&k.jsx("p",{className:"text-gray-600 leading-relaxed",children:r})]}),k.jsx("div",{className:"bg-white rounded-2xl border border-gray-200 shadow-xl p-8",children:e}),k.jsx("div",{className:"text-center mt-8 text-sm text-gray-500",children:k.jsxs("p",{children:["By continuing, you agree to our"," ",k.jsx(Te,{to:xe.terms.getHref(),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Terms of Service"})," ","and"," ",k.jsx(Te,{to:xe.privacy.getHref(),className:"text-blue-600 hover:text-blue-700 font-medium",children:"Privacy Policy"})]})})]})]})]})};var Ne=e=>e.type==="checkbox",me=e=>e instanceof Date,q=e=>e==null;const vr=e=>typeof e=="object";var R=e=>!q(e)&&!Array.isArray(e)&&vr(e)&&!me(e),cn=e=>R(e)&&e.target?Ne(e.target)?e.target.checked:e.target.value:e,ln=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,dn=(e,t)=>e.has(ln(t)),fn=e=>{const t=e.constructor&&e.constructor.prototype;return R(t)&&t.hasOwnProperty("isPrototypeOf")},ft=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function B(e){let t;const r=Array.isArray(e),n=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(ft&&(e instanceof Blob||n))&&(r||R(e)))if(t=r?[]:{},!r&&!fn(e))t=e;else for(const o in e)e.hasOwnProperty(o)&&(t[o]=B(e[o]));else return e;return t}var Ge=e=>/^\w*$/.test(e),H=e=>e===void 0,ht=e=>Array.isArray(e)?e.filter(Boolean):[],mt=e=>ht(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{if(!t||!R(e))return r;const n=(Ge(t)?[t]:mt(t)).reduce((o,s)=>q(o)?o:o[s],e);return H(n)||n===e?H(e[t])?r:e[t]:n},ie=e=>typeof e=="boolean",I=(e,t,r)=>{let n=-1;const o=Ge(t)?[t]:mt(t),s=o.length,i=s-1;for(;++n<s;){const c=o[n];let v=r;if(n!==i){const b=e[c];v=R(b)||Array.isArray(b)?b:isNaN(+o[n+1])?{}:[]}if(c==="__proto__"||c==="constructor"||c==="prototype")return;e[c]=v,e=e[c]}};const Ft={BLUR:"blur",FOCUS_OUT:"focusout"},te={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ae={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},hn=Y.createContext(null);hn.displayName="HookFormContext";var mn=(e,t,r,n=!0)=>{const o={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(o,s,{get:()=>{const i=s;return t._proxyFormState[i]!==te.all&&(t._proxyFormState[i]=!n||te.all),e[i]}});return o};const pn=typeof window<"u"?Y.useLayoutEffect:Y.useEffect;var ue=e=>typeof e=="string",gn=(e,t,r,n,o)=>ue(e)?(n&&t.watch.add(e),p(r,e,o)):Array.isArray(e)?e.map(s=>(n&&t.watch.add(s),p(r,s))):(n&&(t.watchAll=!0),r),st=e=>q(e)||!vr(e);function fe(e,t,r=new WeakSet){if(st(e)||st(t))return e===t;if(me(e)&&me(t))return e.getTime()===t.getTime();const n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const s of n){const i=e[s];if(!o.includes(s))return!1;if(s!=="ref"){const c=t[s];if(me(i)&&me(c)||R(i)&&R(c)||Array.isArray(i)&&Array.isArray(c)?!fe(i,c,r):i!==c)return!1}}return!0}var pt=(e,t,r,n,o)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:o||!0}}:{},$e=e=>Array.isArray(e)?e:[e],Dt=()=>{let e=[];return{get observers(){return e},next:o=>{for(const s of e)s.next&&s.next(o)},subscribe:o=>(e.push(o),{unsubscribe:()=>{e=e.filter(s=>s!==o)}}),unsubscribe:()=>{e=[]}}},K=e=>R(e)&&!Object.keys(e).length,gt=e=>e.type==="file",re=e=>typeof e=="function",He=e=>{if(!ft)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},_r=e=>e.type==="select-multiple",vt=e=>e.type==="radio",vn=e=>vt(e)||Ne(e),ot=e=>He(e)&&e.isConnected;function _n(e,t){const r=t.slice(0,-1).length;let n=0;for(;n<r;)e=H(e)?n++:e[t[n++]];return e}function Cn(e){for(const t in e)if(e.hasOwnProperty(t)&&!H(e[t]))return!1;return!0}function U(e,t){const r=Array.isArray(t)?t:Ge(t)?[t]:mt(t),n=r.length===1?e:_n(e,r),o=r.length-1,s=r[o];return n&&delete n[s],o!==0&&(R(n)&&K(n)||Array.isArray(n)&&Cn(n))&&U(e,r.slice(0,-1)),e}var Cr=e=>{for(const t in e)if(re(e[t]))return!0;return!1};function Be(e,t={}){const r=Array.isArray(e);if(R(e)||r)for(const n in e)Array.isArray(e[n])||R(e[n])&&!Cr(e[n])?(t[n]=Array.isArray(e[n])?[]:{},Be(e[n],t[n])):q(e[n])||(t[n]=!0);return t}function yr(e,t,r){const n=Array.isArray(e);if(R(e)||n)for(const o in e)Array.isArray(e[o])||R(e[o])&&!Cr(e[o])?H(t)||st(r[o])?r[o]=Array.isArray(e[o])?Be(e[o],[]):{...Be(e[o])}:yr(e[o],q(t)?{}:t[o],r[o]):r[o]=!fe(e[o],t[o]);return r}var Ze=(e,t)=>yr(e,t,Be(t));const St={value:!1,isValid:!1},Tt={value:!0,isValid:!0};var br=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!H(e[0].attributes.value)?H(e[0].value)||e[0].value===""?Tt:{value:e[0].value,isValid:!0}:Tt:St}return St},wr=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>H(e)?e:t?e===""?NaN:e&&+e:r&&ue(e)?new Date(e):n?n(e):e;const Ot={isValid:!1,value:null};var kr=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,Ot):Ot;function Pt(e){const t=e.ref;return gt(t)?t.files:vt(t)?kr(e.refs).value:_r(t)?[...t.selectedOptions].map(({value:r})=>r):Ne(t)?br(e.refs).value:wr(H(t.value)?e.ref.value:t.value,e)}var yn=(e,t,r,n)=>{const o={};for(const s of e){const i=p(t,s);i&&I(o,s,i._f)}return{criteriaMode:r,names:[...e],fields:o,shouldUseNativeValidation:n}},We=e=>e instanceof RegExp,ze=e=>H(e)?e:We(e)?e.source:R(e)?We(e.value)?e.value.source:e.value:e,jt=e=>({isOnSubmit:!e||e===te.onSubmit,isOnBlur:e===te.onBlur,isOnChange:e===te.onChange,isOnAll:e===te.all,isOnTouch:e===te.onTouched});const Lt="AsyncFunction";var bn=e=>!!e&&!!e.validate&&!!(re(e.validate)&&e.validate.constructor.name===Lt||R(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Lt)),wn=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Rt=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(n=>e.startsWith(n)&&/^\.\w+/.test(e.slice(n.length))));const Ae=(e,t,r,n)=>{for(const o of r||Object.keys(e)){const s=p(e,o);if(s){const{_f:i,...c}=s;if(i){if(i.refs&&i.refs[0]&&t(i.refs[0],o)&&!n)return!0;if(i.ref&&t(i.ref,i.name)&&!n)return!0;if(Ae(c,t))break}else if(R(c)&&Ae(c,t))break}}};function Mt(e,t,r){const n=p(e,r);if(n||Ge(r))return{error:n,name:r};const o=r.split(".");for(;o.length;){const s=o.join("."),i=p(t,s),c=p(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(c&&c.type)return{name:s,error:c};if(c&&c.root&&c.root.type)return{name:`${s}.root`,error:c.root};o.pop()}return{name:r}}var kn=(e,t,r,n)=>{r(e);const{name:o,...s}=e;return K(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(i=>t[i]===(!n||te.all))},xn=(e,t,r)=>!e||!t||e===t||$e(e).some(n=>n&&(r?n===t:n.startsWith(t)||t.startsWith(n))),Zn=(e,t,r,n,o)=>o.isOnAll?!1:!r&&o.isOnTouch?!(t||e):(r?n.isOnBlur:o.isOnBlur)?!e:(r?n.isOnChange:o.isOnChange)?e:!0,zn=(e,t)=>!ht(p(e,t)).length&&U(e,t),$n=(e,t,r)=>{const n=$e(p(e,r));return I(n,"root",t[r]),I(e,r,n),e},Re=e=>ue(e);function Ut(e,t,r="validate"){if(Re(e)||Array.isArray(e)&&e.every(Re)||ie(e)&&!e)return{type:r,message:Re(e)?e:"",ref:t}}var ye=e=>R(e)&&!We(e)?e:{value:e,message:""},Ht=async(e,t,r,n,o,s)=>{const{ref:i,refs:c,required:v,maxLength:b,minLength:w,min:C,max:g,pattern:z,validate:M,name:A,valueAsNumber:X,mount:ce}=e._f,x=p(r,A);if(!ce||t.has(A))return{};const E=c?c[0]:i,D=Z=>{o&&E.reportValidity&&(E.setCustomValidity(ie(Z)?"":Z||""),E.reportValidity())},P={},le=vt(i),ne=Ne(i),Xe=le||ne,ee=(X||gt(i))&&H(i.value)&&H(x)||He(i)&&i.value===""||x===""||Array.isArray(x)&&!x.length,he=pt.bind(null,A,n,P),oe=(Z,V,L,W=ae.maxLength,G=ae.minLength)=>{const se=Z?V:L;P[A]={type:Z?W:G,message:se,ref:i,...he(Z?W:G,se)}};if(s?!Array.isArray(x)||!x.length:v&&(!Xe&&(ee||q(x))||ie(x)&&!x||ne&&!br(c).isValid||le&&!kr(c).isValid)){const{value:Z,message:V}=Re(v)?{value:!!v,message:v}:ye(v);if(Z&&(P[A]={type:ae.required,message:V,ref:E,...he(ae.required,V)},!n))return D(V),P}if(!ee&&(!q(C)||!q(g))){let Z,V;const L=ye(g),W=ye(C);if(!q(x)&&!isNaN(x)){const G=i.valueAsNumber||x&&+x;q(L.value)||(Z=G>L.value),q(W.value)||(V=G<W.value)}else{const G=i.valueAsDate||new Date(x),se=De=>new Date(new Date().toDateString()+" "+De),ke=i.type=="time",Ce=i.type=="week";ue(L.value)&&x&&(Z=ke?se(x)>se(L.value):Ce?x>L.value:G>new Date(L.value)),ue(W.value)&&x&&(V=ke?se(x)<se(W.value):Ce?x<W.value:G<new Date(W.value))}if((Z||V)&&(oe(!!Z,L.message,W.message,ae.max,ae.min),!n))return D(P[A].message),P}if((b||w)&&!ee&&(ue(x)||s&&Array.isArray(x))){const Z=ye(b),V=ye(w),L=!q(Z.value)&&x.length>+Z.value,W=!q(V.value)&&x.length<+V.value;if((L||W)&&(oe(L,Z.message,V.message),!n))return D(P[A].message),P}if(z&&!ee&&ue(x)){const{value:Z,message:V}=ye(z);if(We(Z)&&!x.match(Z)&&(P[A]={type:ae.pattern,message:V,ref:i,...he(ae.pattern,V)},!n))return D(V),P}if(M){if(re(M)){const Z=await M(x,r),V=Ut(Z,E);if(V&&(P[A]={...V,...he(ae.validate,V.message)},!n))return D(V.message),P}else if(R(M)){let Z={};for(const V in M){if(!K(Z)&&!n)break;const L=Ut(await M[V](x,r),E,V);L&&(Z={...L,...he(V,L.message)},D(L.message),n&&(P[A]=Z))}if(!K(Z)&&(P[A]={ref:E,...Z},!n))return P}}return D(!0),P};const An={mode:te.onSubmit,reValidateMode:te.onChange,shouldFocusError:!0};function En(e={}){let t={...An,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:re(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},n={},o=R(t.defaultValues)||R(t.values)?B(t.defaultValues||t.values)||{}:{},s=t.shouldUnregister?{}:B(o),i={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},v,b=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let C={...w};const g={array:Dt(),state:Dt()},z=t.criteriaMode===te.all,M=u=>a=>{clearTimeout(b),b=setTimeout(u,a)},A=async u=>{if(!t.disabled&&(w.isValid||C.isValid||u)){const a=t.resolver?K((await ne()).errors):await ee(n,!0);a!==r.isValid&&g.state.next({isValid:a})}},X=(u,a)=>{!t.disabled&&(w.isValidating||w.validatingFields||C.isValidating||C.validatingFields)&&((u||Array.from(c.mount)).forEach(l=>{l&&(a?I(r.validatingFields,l,a):U(r.validatingFields,l))}),g.state.next({validatingFields:r.validatingFields,isValidating:!K(r.validatingFields)}))},ce=(u,a=[],l,m,h=!0,f=!0)=>{if(m&&l&&!t.disabled){if(i.action=!0,f&&Array.isArray(p(n,u))){const y=l(p(n,u),m.argA,m.argB);h&&I(n,u,y)}if(f&&Array.isArray(p(r.errors,u))){const y=l(p(r.errors,u),m.argA,m.argB);h&&I(r.errors,u,y),zn(r.errors,u)}if((w.touchedFields||C.touchedFields)&&f&&Array.isArray(p(r.touchedFields,u))){const y=l(p(r.touchedFields,u),m.argA,m.argB);h&&I(r.touchedFields,u,y)}(w.dirtyFields||C.dirtyFields)&&(r.dirtyFields=Ze(o,s)),g.state.next({name:u,isDirty:oe(u,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else I(s,u,a)},x=(u,a)=>{I(r.errors,u,a),g.state.next({errors:r.errors})},E=u=>{r.errors=u,g.state.next({errors:r.errors,isValid:!1})},D=(u,a,l,m)=>{const h=p(n,u);if(h){const f=p(s,u,H(l)?p(o,u):l);H(f)||m&&m.defaultChecked||a?I(s,u,a?f:Pt(h._f)):L(u,f),i.mount&&A()}},P=(u,a,l,m,h)=>{let f=!1,y=!1;const $={name:u};if(!t.disabled){if(!l||m){(w.isDirty||C.isDirty)&&(y=r.isDirty,r.isDirty=$.isDirty=oe(),f=y!==$.isDirty);const N=fe(p(o,u),a);y=!!p(r.dirtyFields,u),N?U(r.dirtyFields,u):I(r.dirtyFields,u,!0),$.dirtyFields=r.dirtyFields,f=f||(w.dirtyFields||C.dirtyFields)&&y!==!N}if(l){const N=p(r.touchedFields,u);N||(I(r.touchedFields,u,l),$.touchedFields=r.touchedFields,f=f||(w.touchedFields||C.touchedFields)&&N!==l)}f&&h&&g.state.next($)}return f?$:{}},le=(u,a,l,m)=>{const h=p(r.errors,u),f=(w.isValid||C.isValid)&&ie(a)&&r.isValid!==a;if(t.delayError&&l?(v=M(()=>x(u,l)),v(t.delayError)):(clearTimeout(b),v=null,l?I(r.errors,u,l):U(r.errors,u)),(l?!fe(h,l):h)||!K(m)||f){const y={...m,...f&&ie(a)?{isValid:a}:{},errors:r.errors,name:u};r={...r,...y},g.state.next(y)}},ne=async u=>{X(u,!0);const a=await t.resolver(s,t.context,yn(u||c.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return X(u),a},Xe=async u=>{const{errors:a}=await ne(u);if(u)for(const l of u){const m=p(a,l);m?I(r.errors,l,m):U(r.errors,l)}else r.errors=a;return a},ee=async(u,a,l={valid:!0})=>{for(const m in u){const h=u[m];if(h){const{_f:f,...y}=h;if(f){const $=c.array.has(f.name),N=h._f&&bn(h._f);N&&w.validatingFields&&X([m],!0);const Q=await Ht(h,c.disabled,s,z,t.shouldUseNativeValidation&&!a,$);if(N&&w.validatingFields&&X([m]),Q[f.name]&&(l.valid=!1,a))break;!a&&(p(Q,f.name)?$?$n(r.errors,Q,f.name):I(r.errors,f.name,Q[f.name]):U(r.errors,f.name))}!K(y)&&await ee(y,a,l)}}return l.valid},he=()=>{for(const u of c.unMount){const a=p(n,u);a&&(a._f.refs?a._f.refs.every(l=>!ot(l)):!ot(a._f.ref))&&Qe(u)}c.unMount=new Set},oe=(u,a)=>!t.disabled&&(u&&a&&I(s,u,a),!fe(De(),o)),Z=(u,a,l)=>gn(u,c,{...i.mount?s:H(a)?o:ue(u)?{[u]:a}:a},l,a),V=u=>ht(p(i.mount?s:o,u,t.shouldUnregister?p(o,u,[]):[])),L=(u,a,l={})=>{const m=p(n,u);let h=a;if(m){const f=m._f;f&&(!f.disabled&&I(s,u,wr(a,f)),h=He(f.ref)&&q(a)?"":a,_r(f.ref)?[...f.ref.options].forEach(y=>y.selected=h.includes(y.value)):f.refs?Ne(f.ref)?f.refs.forEach(y=>{(!y.defaultChecked||!y.disabled)&&(Array.isArray(h)?y.checked=!!h.find($=>$===y.value):y.checked=h===y.value||!!h)}):f.refs.forEach(y=>y.checked=y.value===h):gt(f.ref)?f.ref.value="":(f.ref.value=h,f.ref.type||g.state.next({name:u,values:B(s)})))}(l.shouldDirty||l.shouldTouch)&&P(u,h,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&Ce(u)},W=(u,a,l)=>{for(const m in a){if(!a.hasOwnProperty(m))return;const h=a[m],f=u+"."+m,y=p(n,f);(c.array.has(u)||R(h)||y&&!y._f)&&!me(h)?W(f,h,l):L(f,h,l)}},G=(u,a,l={})=>{const m=p(n,u),h=c.array.has(u),f=B(a);I(s,u,f),h?(g.array.next({name:u,values:B(s)}),(w.isDirty||w.dirtyFields||C.isDirty||C.dirtyFields)&&l.shouldDirty&&g.state.next({name:u,dirtyFields:Ze(o,s),isDirty:oe(u,f)})):m&&!m._f&&!q(f)?W(u,f,l):L(u,f,l),Rt(u,c)&&g.state.next({...r,name:u}),g.state.next({name:i.mount?u:void 0,values:B(s)})},se=async u=>{i.mount=!0;const a=u.target;let l=a.name,m=!0;const h=p(n,l),f=N=>{m=Number.isNaN(N)||me(N)&&isNaN(N.getTime())||fe(N,p(s,l,N))},y=jt(t.mode),$=jt(t.reValidateMode);if(h){let N,Q;const Se=a.type?Pt(h._f):cn(u),de=u.type===Ft.BLUR||u.type===Ft.FOCUS_OUT,tn=!wn(h._f)&&!t.resolver&&!p(r.errors,l)&&!h._f.deps||Zn(de,p(r.touchedFields,l),r.isSubmitted,$,y),rt=Rt(l,c,de);I(s,l,Se),de?(h._f.onBlur&&h._f.onBlur(u),v&&v(0)):h._f.onChange&&h._f.onChange(u);const nt=P(l,Se,de),rn=!K(nt)||rt;if(!de&&g.state.next({name:l,type:u.type,values:B(s)}),tn)return(w.isValid||C.isValid)&&(t.mode==="onBlur"?de&&A():de||A()),rn&&g.state.next({name:l,...rt?{}:nt});if(!de&&rt&&g.state.next({...r}),t.resolver){const{errors:It}=await ne([l]);if(f(Se),m){const nn=Mt(r.errors,n,l),Nt=Mt(It,n,nn.name||l);N=Nt.error,l=Nt.name,Q=K(It)}}else X([l],!0),N=(await Ht(h,c.disabled,s,z,t.shouldUseNativeValidation))[l],X([l]),f(Se),m&&(N?Q=!1:(w.isValid||C.isValid)&&(Q=await ee(n,!0)));m&&(h._f.deps&&Ce(h._f.deps),le(l,Q,N,nt))}},ke=(u,a)=>{if(p(r.errors,a)&&u.focus)return u.focus(),1},Ce=async(u,a={})=>{let l,m;const h=$e(u);if(t.resolver){const f=await Xe(H(u)?u:h);l=K(f),m=u?!h.some(y=>p(f,y)):l}else u?(m=(await Promise.all(h.map(async f=>{const y=p(n,f);return await ee(y&&y._f?{[f]:y}:y)}))).every(Boolean),!(!m&&!r.isValid)&&A()):m=l=await ee(n);return g.state.next({...!ue(u)||(w.isValid||C.isValid)&&l!==r.isValid?{}:{name:u},...t.resolver||!u?{isValid:l}:{},errors:r.errors}),a.shouldFocus&&!m&&Ae(n,ke,u?h:c.mount),m},De=u=>{const a={...i.mount?s:o};return H(u)?a:ue(u)?p(a,u):u.map(l=>p(a,l))},kt=(u,a)=>({invalid:!!p((a||r).errors,u),isDirty:!!p((a||r).dirtyFields,u),error:p((a||r).errors,u),isValidating:!!p(r.validatingFields,u),isTouched:!!p((a||r).touchedFields,u)}),Gr=u=>{u&&$e(u).forEach(a=>U(r.errors,a)),g.state.next({errors:u?r.errors:{}})},xt=(u,a,l)=>{const m=(p(n,u,{_f:{}})._f||{}).ref,h=p(r.errors,u)||{},{ref:f,message:y,type:$,...N}=h;I(r.errors,u,{...N,...a,ref:m}),g.state.next({name:u,errors:r.errors,isValid:!1}),l&&l.shouldFocus&&m&&m.focus&&m.focus()},Kr=(u,a)=>re(u)?g.state.subscribe({next:l=>"values"in l&&u(Z(void 0,a),l)}):Z(u,a,!0),Zt=u=>g.state.subscribe({next:a=>{xn(u.name,a.name,u.exact)&&kn(a,u.formState||w,en,u.reRenderRoot)&&u.callback({values:{...s},...r,...a,defaultValues:o})}}).unsubscribe,Jr=u=>(i.mount=!0,C={...C,...u.formState},Zt({...u,formState:C})),Qe=(u,a={})=>{for(const l of u?$e(u):c.mount)c.mount.delete(l),c.array.delete(l),a.keepValue||(U(n,l),U(s,l)),!a.keepError&&U(r.errors,l),!a.keepDirty&&U(r.dirtyFields,l),!a.keepTouched&&U(r.touchedFields,l),!a.keepIsValidating&&U(r.validatingFields,l),!t.shouldUnregister&&!a.keepDefaultValue&&U(o,l);g.state.next({values:B(s)}),g.state.next({...r,...a.keepDirty?{isDirty:oe()}:{}}),!a.keepIsValid&&A()},zt=({disabled:u,name:a})=>{(ie(u)&&i.mount||u||c.disabled.has(a))&&(u?c.disabled.add(a):c.disabled.delete(a))},et=(u,a={})=>{let l=p(n,u);const m=ie(a.disabled)||ie(t.disabled);return I(n,u,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:u}},name:u,mount:!0,...a}}),c.mount.add(u),l?zt({disabled:ie(a.disabled)?a.disabled:t.disabled,name:u}):D(u,!0,a.value),{...m?{disabled:a.disabled||t.disabled}:{},...t.progressive?{required:!!a.required,min:ze(a.min),max:ze(a.max),minLength:ze(a.minLength),maxLength:ze(a.maxLength),pattern:ze(a.pattern)}:{},name:u,onChange:se,onBlur:se,ref:h=>{if(h){et(u,a),l=p(n,u);const f=H(h.value)&&h.querySelectorAll&&h.querySelectorAll("input,select,textarea")[0]||h,y=vn(f),$=l._f.refs||[];if(y?$.find(N=>N===f):f===l._f.ref)return;I(n,u,{_f:{...l._f,...y?{refs:[...$.filter(ot),f,...Array.isArray(p(o,u))?[{}]:[]],ref:{type:f.type,name:u}}:{ref:f}}}),D(u,!1,void 0,f)}else l=p(n,u,{}),l._f&&(l._f.mount=!1),(t.shouldUnregister||a.shouldUnregister)&&!(dn(c.array,u)&&i.action)&&c.unMount.add(u)}}},tt=()=>t.shouldFocusError&&Ae(n,ke,c.mount),Yr=u=>{ie(u)&&(g.state.next({disabled:u}),Ae(n,(a,l)=>{const m=p(n,l);m&&(a.disabled=m._f.disabled||u,Array.isArray(m._f.refs)&&m._f.refs.forEach(h=>{h.disabled=m._f.disabled||u}))},0,!1))},$t=(u,a)=>async l=>{let m;l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist());let h=B(s);if(g.state.next({isSubmitting:!0}),t.resolver){const{errors:f,values:y}=await ne();r.errors=f,h=B(y)}else await ee(n);if(c.disabled.size)for(const f of c.disabled)U(h,f);if(U(r.errors,"root"),K(r.errors)){g.state.next({errors:{}});try{await u(h,l)}catch(f){m=f}}else a&&await a({...r.errors},l),tt(),setTimeout(tt);if(g.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:K(r.errors)&&!m,submitCount:r.submitCount+1,errors:r.errors}),m)throw m},Xr=(u,a={})=>{p(n,u)&&(H(a.defaultValue)?G(u,B(p(o,u))):(G(u,a.defaultValue),I(o,u,B(a.defaultValue))),a.keepTouched||U(r.touchedFields,u),a.keepDirty||(U(r.dirtyFields,u),r.isDirty=a.defaultValue?oe(u,B(p(o,u))):oe()),a.keepError||(U(r.errors,u),w.isValid&&A()),g.state.next({...r}))},At=(u,a={})=>{const l=u?B(u):o,m=B(l),h=K(u),f=h?o:m;if(a.keepDefaultValues||(o=l),!a.keepValues){if(a.keepDirtyValues){const y=new Set([...c.mount,...Object.keys(Ze(o,s))]);for(const $ of Array.from(y))p(r.dirtyFields,$)?I(f,$,p(s,$)):G($,p(f,$))}else{if(ft&&H(u))for(const y of c.mount){const $=p(n,y);if($&&$._f){const N=Array.isArray($._f.refs)?$._f.refs[0]:$._f.ref;if(He(N)){const Q=N.closest("form");if(Q){Q.reset();break}}}}if(a.keepFieldsRef)for(const y of c.mount)G(y,p(f,y));else n={}}s=t.shouldUnregister?a.keepDefaultValues?B(o):{}:B(f),g.array.next({values:{...f}}),g.state.next({values:{...f}})}c={mount:a.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},i.mount=!w.isValid||!!a.keepIsValid||!!a.keepDirtyValues,i.watch=!!t.shouldUnregister,g.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:h?!1:a.keepDirty?r.isDirty:!!(a.keepDefaultValues&&!fe(u,o)),isSubmitted:a.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:h?{}:a.keepDirtyValues?a.keepDefaultValues&&s?Ze(o,s):r.dirtyFields:a.keepDefaultValues&&u?Ze(o,u):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:a.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},Et=(u,a)=>At(re(u)?u(s):u,a),Qr=(u,a={})=>{const l=p(n,u),m=l&&l._f;if(m){const h=m.refs?m.refs[0]:m.ref;h.focus&&(h.focus(),a.shouldSelect&&re(h.select)&&h.select())}},en=u=>{r={...r,...u}},Vt={control:{register:et,unregister:Qe,getFieldState:kt,handleSubmit:$t,setError:xt,_subscribe:Zt,_runSchema:ne,_focusError:tt,_getWatch:Z,_getDirty:oe,_setValid:A,_setFieldArray:ce,_setDisabledField:zt,_setErrors:E,_getFieldArray:V,_reset:At,_resetDefaultValues:()=>re(t.defaultValues)&&t.defaultValues().then(u=>{Et(u,t.resetOptions),g.state.next({isLoading:!1})}),_removeUnmounted:he,_disableForm:Yr,_subjects:g,_proxyFormState:w,get _fields(){return n},get _formValues(){return s},get _state(){return i},set _state(u){i=u},get _defaultValues(){return o},get _names(){return c},set _names(u){c=u},get _formState(){return r},get _options(){return t},set _options(u){t={...t,...u}}},subscribe:Jr,trigger:Ce,register:et,handleSubmit:$t,watch:Kr,setValue:G,getValues:De,reset:Et,resetField:Xr,clearErrors:Gr,unregister:Qe,setError:xt,setFocus:Qr,getFieldState:kt};return{...Vt,formControl:Vt}}function S2(e={}){const t=Y.useRef(void 0),r=Y.useRef(void 0),[n,o]=Y.useState({isDirty:!1,isValidating:!1,isLoading:re(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:re(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!re(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:i,...c}=En(e);t.current={...c,formState:n}}const s=t.current.control;return s._options=e,pn(()=>{const i=s._subscribe({formState:s._proxyFormState,callback:()=>o({...s._formState}),reRenderRoot:!0});return o(c=>({...c,isReady:!0})),s._formState.isReady=!0,i},[s]),Y.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),Y.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),Y.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),Y.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),Y.useEffect(()=>{if(s._proxyFormState.isDirty){const i=s._getDirty();i!==n.isDirty&&s._subjects.state.next({isDirty:i})}},[s,n.isDirty]),Y.useEffect(()=>{e.values&&!fe(e.values,r.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),r.current=e.values,o(i=>({...i}))):s._resetDefaultValues()},[s,e.values]),Y.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=mn(n,s),t.current}const Bt=(e,t,r)=>{if(e&&"reportValidity"in e){const n=p(r,t);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},it=(e,t)=>{for(const r in t.fields){const n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?Bt(n.ref,r,e):n&&n.refs&&n.refs.forEach(o=>Bt(o,r,e))}},Wt=(e,t)=>{t.shouldUseNativeValidation&&it(e,t);const r={};for(const n in e){const o=p(t.fields,n),s=Object.assign(e[n]||{},{ref:o&&o.ref});if(Vn(t.names||Object.keys(e),n)){const i=Object.assign({},p(r,n));I(i,"root",s),I(r,n,i)}else I(r,n,s)}return r},Vn=(e,t)=>{const r=qt(t);return e.some(n=>qt(n).match(`^${r}\\.\\d+`))};function qt(e){return e.replace(/\]|\[/g,"")}function d(e,t,r){function n(c,v){var b;Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(b=c._zod).traits??(b.traits=new Set),c._zod.traits.add(e),t(c,v);for(const w in i.prototype)w in c||Object.defineProperty(c,w,{value:i.prototype[w].bind(c)});c._zod.constr=i,c._zod.def=v}const o=r?.Parent??Object;class s extends o{}Object.defineProperty(s,"name",{value:e});function i(c){var v;const b=r?.Parent?new s:this;n(b,c),(v=b._zod).deferred??(v.deferred=[]);for(const w of b._zod.deferred)w();return b}return Object.defineProperty(i,"init",{value:n}),Object.defineProperty(i,Symbol.hasInstance,{value:c=>r?.Parent&&c instanceof r.Parent?!0:c?._zod?.traits?.has(e)}),Object.defineProperty(i,"name",{value:e}),i}class Ve extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const xr={};function pe(e){return xr}function In(e){const t=Object.values(e).filter(n=>typeof n=="number");return Object.entries(e).filter(([n,o])=>t.indexOf(+n)===-1).map(([n,o])=>o)}function ut(e,t){return typeof t=="bigint"?t.toString():t}function Zr(e){return{get value(){{const t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function _t(e){return e==null}function Ct(e){const t=e.startsWith("^")?1:0,r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function Nn(e,t){const r=(e.toString().split(".")[1]||"").length,n=t.toString();let o=(n.split(".")[1]||"").length;if(o===0&&/\d?e-\d?/.test(n)){const v=n.match(/\d?e-(\d?)/);v?.[1]&&(o=Number.parseInt(v[1]))}const s=r>o?r:o,i=Number.parseInt(e.toFixed(s).replace(".","")),c=Number.parseInt(t.toFixed(s).replace(".",""));return i%c/10**s}const Gt=Symbol("evaluating");function F(e,t,r){let n;Object.defineProperty(e,t,{get(){if(n!==Gt)return n===void 0&&(n=Gt,n=r()),n},set(o){Object.defineProperty(e,t,{value:o})},configurable:!0})}function ve(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function we(...e){const t={};for(const r of e){const n=Object.getOwnPropertyDescriptors(r);Object.assign(t,n)}return Object.defineProperties({},t)}function Kt(e){return JSON.stringify(e)}const zr="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function at(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const Fn=Zr(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const e=Function;return new e(""),!0}catch{return!1}});function ct(e){if(at(e)===!1)return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(at(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}const Dn=new Set(["string","number","symbol"]);function be(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function _e(e,t,r){const n=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(n._zod.parent=e),n}function _(e){const t=e;if(!t)return{};if(typeof t=="string")return{error:()=>t};if(t?.message!==void 0){if(t?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function Sn(e){return Object.keys(e).filter(t=>e[t]._zod.optin==="optional"&&e[t]._zod.optout==="optional")}const Tn={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function On(e,t){const r=e._zod.def,n=we(e._zod.def,{get shape(){const o={};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&(o[s]=r.shape[s])}return ve(this,"shape",o),o},checks:[]});return _e(e,n)}function Pn(e,t){const r=e._zod.def,n=we(e._zod.def,{get shape(){const o={...e._zod.def.shape};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&delete o[s]}return ve(this,"shape",o),o},checks:[]});return _e(e,n)}function jn(e,t){if(!ct(t))throw new Error("Invalid input to extend: expected a plain object");const r=we(e._zod.def,{get shape(){const n={...e._zod.def.shape,...t};return ve(this,"shape",n),n},checks:[]});return _e(e,r)}function Ln(e,t){const r=we(e._zod.def,{get shape(){const n={...e._zod.def.shape,...t._zod.def.shape};return ve(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return _e(e,r)}function Rn(e,t,r){const n=we(t._zod.def,{get shape(){const o=t._zod.def.shape,s={...o};if(r)for(const i in r){if(!(i in o))throw new Error(`Unrecognized key: "${i}"`);r[i]&&(s[i]=e?new e({type:"optional",innerType:o[i]}):o[i])}else for(const i in o)s[i]=e?new e({type:"optional",innerType:o[i]}):o[i];return ve(this,"shape",s),s},checks:[]});return _e(t,n)}function Mn(e,t,r){const n=we(t._zod.def,{get shape(){const o=t._zod.def.shape,s={...o};if(r)for(const i in r){if(!(i in s))throw new Error(`Unrecognized key: "${i}"`);r[i]&&(s[i]=new e({type:"nonoptional",innerType:o[i]}))}else for(const i in o)s[i]=new e({type:"nonoptional",innerType:o[i]});return ve(this,"shape",s),s},checks:[]});return _e(t,n)}function Ee(e,t=0){for(let r=t;r<e.issues.length;r++)if(e.issues[r]?.continue!==!0)return!0;return!1}function $r(e,t){return t.map(r=>{var n;return(n=r).path??(n.path=[]),r.path.unshift(e),r})}function Oe(e){return typeof e=="string"?e:e?.message}function ge(e,t,r){const n={...e,path:e.path??[]};if(!e.message){const o=Oe(e.inst?._zod.def?.error?.(e))??Oe(t?.error?.(e))??Oe(r.customError?.(e))??Oe(r.localeError?.(e))??"Invalid input";n.message=o}return delete n.inst,delete n.continue,t?.reportInput||delete n.input,n}function yt(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function Ie(...e){const[t,r,n]=e;return typeof t=="string"?{message:t,code:"custom",input:r,inst:n}:{...t}}const Ar=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,ut,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},bt=d("$ZodError",Ar),Ke=d("$ZodError",Ar,{Parent:Error});function Un(e,t=r=>r.message){const r={},n=[];for(const o of e.issues)o.path.length>0?(r[o.path[0]]=r[o.path[0]]||[],r[o.path[0]].push(t(o))):n.push(t(o));return{formErrors:n,fieldErrors:r}}function Hn(e,t){const r=t||function(s){return s.message},n={_errors:[]},o=s=>{for(const i of s.issues)if(i.code==="invalid_union"&&i.errors.length)i.errors.map(c=>o({issues:c}));else if(i.code==="invalid_key")o({issues:i.issues});else if(i.code==="invalid_element")o({issues:i.issues});else if(i.path.length===0)n._errors.push(r(i));else{let c=n,v=0;for(;v<i.path.length;){const b=i.path[v];v===i.path.length-1?(c[b]=c[b]||{_errors:[]},c[b]._errors.push(r(i))):c[b]=c[b]||{_errors:[]},c=c[b],v++}}};return o(e),n}const Er=e=>(t,r,n,o)=>{const s=n?Object.assign(n,{async:!1}):{async:!1},i=t._zod.run({value:r,issues:[]},s);if(i instanceof Promise)throw new Ve;if(i.issues.length){const c=new(o?.Err??e)(i.issues.map(v=>ge(v,s,pe())));throw zr(c,o?.callee),c}return i.value},Bn=Er(Ke),Vr=e=>async(t,r,n,o)=>{const s=n?Object.assign(n,{async:!0}):{async:!0};let i=t._zod.run({value:r,issues:[]},s);if(i instanceof Promise&&(i=await i),i.issues.length){const c=new(o?.Err??e)(i.issues.map(v=>ge(v,s,pe())));throw zr(c,o?.callee),c}return i.value},Wn=Vr(Ke),Ir=e=>(t,r,n)=>{const o=n?{...n,async:!1}:{async:!1},s=t._zod.run({value:r,issues:[]},o);if(s instanceof Promise)throw new Ve;return s.issues.length?{success:!1,error:new(e??bt)(s.issues.map(i=>ge(i,o,pe())))}:{success:!0,data:s.value}},qn=Ir(Ke),Nr=e=>async(t,r,n)=>{const o=n?Object.assign(n,{async:!0}):{async:!0};let s=t._zod.run({value:r,issues:[]},o);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new e(s.issues.map(i=>ge(i,o,pe())))}:{success:!0,data:s.value}},Gn=Nr(Ke),Kn=/^[cC][^\s-]{8,}$/,Jn=/^[0-9a-z]+$/,Yn=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Xn=/^[0-9a-vA-V]{20}$/,Qn=/^[A-Za-z0-9]{27}$/,eo=/^[a-zA-Z0-9_-]{21}$/,to=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,ro=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Jt=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,no=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,oo="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function so(){return new RegExp(oo,"u")}const io=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,uo=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,ao=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,co=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,lo=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Fr=/^[A-Za-z0-9_-]*$/,fo=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,ho=/^\+(?:[0-9]){6,14}[0-9]$/,Dr="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",mo=new RegExp(`^${Dr}$`);function Sr(e){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${t}`:e.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function po(e){return new RegExp(`^${Sr(e)}$`)}function go(e){const t=Sr({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");const n=`${t}(?:${r.join("|")})`;return new RegExp(`^${Dr}T(?:${n})$`)}const vo=e=>{const t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},_o=/^\d+$/,Co=/^-?\d+(?:\.\d+)?/i,yo=/true|false/i,bo=/^[^A-Z]*$/,wo=/^[^a-z]*$/,J=d("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),Tr={number:"number",bigint:"bigint",object:"date"},Or=d("$ZodCheckLessThan",(e,t)=>{J.init(e,t);const r=Tr[typeof t.value];e._zod.onattach.push(n=>{const o=n._zod.bag,s=(t.inclusive?o.maximum:o.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<s&&(t.inclusive?o.maximum=t.value:o.exclusiveMaximum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value<=t.value:n.value<t.value)||n.issues.push({origin:r,code:"too_big",maximum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),Pr=d("$ZodCheckGreaterThan",(e,t)=>{J.init(e,t);const r=Tr[typeof t.value];e._zod.onattach.push(n=>{const o=n._zod.bag,s=(t.inclusive?o.minimum:o.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>s&&(t.inclusive?o.minimum=t.value:o.exclusiveMinimum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value>=t.value:n.value>t.value)||n.issues.push({origin:r,code:"too_small",minimum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),ko=d("$ZodCheckMultipleOf",(e,t)=>{J.init(e,t),e._zod.onattach.push(r=>{var n;(n=r._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%t.value===BigInt(0):Nn(r.value,t.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),xo=d("$ZodCheckNumberFormat",(e,t)=>{J.init(e,t),t.format=t.format||"float64";const r=t.format?.includes("int"),n=r?"int":"number",[o,s]=Tn[t.format];e._zod.onattach.push(i=>{const c=i._zod.bag;c.format=t.format,c.minimum=o,c.maximum=s,r&&(c.pattern=_o)}),e._zod.check=i=>{const c=i.value;if(r){if(!Number.isInteger(c)){i.issues.push({expected:n,format:t.format,code:"invalid_type",continue:!1,input:c,inst:e});return}if(!Number.isSafeInteger(c)){c>0?i.issues.push({input:c,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort}):i.issues.push({input:c,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort});return}}c<o&&i.issues.push({origin:"number",input:c,code:"too_small",minimum:o,inclusive:!0,inst:e,continue:!t.abort}),c>s&&i.issues.push({origin:"number",input:c,code:"too_big",maximum:s,inst:e})}}),Zo=d("$ZodCheckMaxLength",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<o&&(n._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{const o=n.value;if(o.length<=t.maximum)return;const i=yt(o);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:o,inst:e,continue:!t.abort})}}),zo=d("$ZodCheckMinLength",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>o&&(n._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{const o=n.value;if(o.length>=t.minimum)return;const i=yt(o);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:o,inst:e,continue:!t.abort})}}),$o=d("$ZodCheckLengthEquals",(e,t)=>{var r;J.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag;o.minimum=t.length,o.maximum=t.length,o.length=t.length}),e._zod.check=n=>{const o=n.value,s=o.length;if(s===t.length)return;const i=yt(o),c=s>t.length;n.issues.push({origin:i,...c?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),Je=d("$ZodCheckStringFormat",(e,t)=>{var r,n;J.init(e,t),e._zod.onattach.push(o=>{const s=o._zod.bag;s.format=t.format,t.pattern&&(s.patterns??(s.patterns=new Set),s.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=o=>{t.pattern.lastIndex=0,!t.pattern.test(o.value)&&o.issues.push({origin:"string",code:"invalid_format",format:t.format,input:o.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(n=e._zod).check??(n.check=()=>{})}),Ao=d("$ZodCheckRegex",(e,t)=>{Je.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,!t.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),Eo=d("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=bo),Je.init(e,t)}),Vo=d("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=wo),Je.init(e,t)}),Io=d("$ZodCheckIncludes",(e,t)=>{J.init(e,t);const r=be(t.includes),n=new RegExp(typeof t.position=="number"?`^.{${t.position}}${r}`:r);t.pattern=n,e._zod.onattach.push(o=>{const s=o._zod.bag;s.patterns??(s.patterns=new Set),s.patterns.add(n)}),e._zod.check=o=>{o.value.includes(t.includes,t.position)||o.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:o.value,inst:e,continue:!t.abort})}}),No=d("$ZodCheckStartsWith",(e,t)=>{J.init(e,t);const r=new RegExp(`^${be(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const o=n._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(r)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),Fo=d("$ZodCheckEndsWith",(e,t)=>{J.init(e,t);const r=new RegExp(`.*${be(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const o=n._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(r)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),Do=d("$ZodCheckOverwrite",(e,t)=>{J.init(e,t),e._zod.check=r=>{r.value=t.tx(r.value)}});class So{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const n=t.split(`
`).filter(i=>i),o=Math.min(...n.map(i=>i.length-i.trimStart().length)),s=n.map(i=>i.slice(o)).map(i=>" ".repeat(this.indent*2)+i);for(const i of s)this.content.push(i)}compile(){const t=Function,r=this?.args,o=[...(this?.content??[""]).map(s=>`  ${s}`)];return new t(...r,o.join(`
`))}}const To={major:4,minor:0,patch:14},S=d("$ZodType",(e,t)=>{var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=To;const n=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&n.unshift(e);for(const o of n)for(const s of o._zod.onattach)s(e);if(n.length===0)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{const o=(s,i,c)=>{let v=Ee(s),b;for(const w of i){if(w._zod.def.when){if(!w._zod.def.when(s))continue}else if(v)continue;const C=s.issues.length,g=w._zod.check(s);if(g instanceof Promise&&c?.async===!1)throw new Ve;if(b||g instanceof Promise)b=(b??Promise.resolve()).then(async()=>{await g,s.issues.length!==C&&(v||(v=Ee(s,C)))});else{if(s.issues.length===C)continue;v||(v=Ee(s,C))}}return b?b.then(()=>s):s};e._zod.run=(s,i)=>{const c=e._zod.parse(s,i);if(c instanceof Promise){if(i.async===!1)throw new Ve;return c.then(v=>o(v,n,i))}return o(c,n,i)}}e["~standard"]={validate:o=>{try{const s=qn(e,o);return s.success?{value:s.data}:{issues:s.error?.issues}}catch{return Gn(e,o).then(i=>i.success?{value:i.data}:{issues:i.error?.issues})}},vendor:"zod",version:1}}),wt=d("$ZodString",(e,t)=>{S.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??vo(e._zod.bag),e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=String(r.value)}catch{}return typeof r.value=="string"||r.issues.push({expected:"string",code:"invalid_type",input:r.value,inst:e}),r}}),T=d("$ZodStringFormat",(e,t)=>{Je.init(e,t),wt.init(e,t)}),Oo=d("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=ro),T.init(e,t)}),Po=d("$ZodUUID",(e,t)=>{if(t.version){const n={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(n===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=Jt(n))}else t.pattern??(t.pattern=Jt());T.init(e,t)}),jo=d("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=no),T.init(e,t)}),Lo=d("$ZodURL",(e,t)=>{T.init(e,t),e._zod.check=r=>{try{const n=r.value.trim(),o=new URL(n);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(o.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:fo.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),t.normalize?r.value=o.href:r.value=n;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),Ro=d("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=so()),T.init(e,t)}),Mo=d("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=eo),T.init(e,t)}),Uo=d("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=Kn),T.init(e,t)}),Ho=d("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=Jn),T.init(e,t)}),Bo=d("$ZodULID",(e,t)=>{t.pattern??(t.pattern=Yn),T.init(e,t)}),Wo=d("$ZodXID",(e,t)=>{t.pattern??(t.pattern=Xn),T.init(e,t)}),qo=d("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=Qn),T.init(e,t)}),Go=d("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=go(t)),T.init(e,t)}),Ko=d("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=mo),T.init(e,t)}),Jo=d("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=po(t)),T.init(e,t)}),Yo=d("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=to),T.init(e,t)}),Xo=d("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=io),T.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv4"})}),Qo=d("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=uo),T.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),es=d("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=ao),T.init(e,t)}),ts=d("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=co),T.init(e,t),e._zod.check=r=>{const[n,o]=r.value.split("/");try{if(!o)throw new Error;const s=Number(o);if(`${s}`!==o)throw new Error;if(s<0||s>128)throw new Error;new URL(`http://[${n}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function jr(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const rs=d("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=lo),T.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{jr(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}});function ns(e){if(!Fr.test(e))return!1;const t=e.replace(/[-_]/g,n=>n==="-"?"+":"/"),r=t.padEnd(Math.ceil(t.length/4)*4,"=");return jr(r)}const os=d("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=Fr),T.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{ns(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),ss=d("$ZodE164",(e,t)=>{t.pattern??(t.pattern=ho),T.init(e,t)});function is(e,t=null){try{const r=e.split(".");if(r.length!==3)return!1;const[n]=r;if(!n)return!1;const o=JSON.parse(atob(n));return!("typ"in o&&o?.typ!=="JWT"||!o.alg||t&&(!("alg"in o)||o.alg!==t))}catch{return!1}}const us=d("$ZodJWT",(e,t)=>{T.init(e,t),e._zod.check=r=>{is(r.value,t.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),Lr=d("$ZodNumber",(e,t)=>{S.init(e,t),e._zod.pattern=e._zod.bag.pattern??Co,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=Number(r.value)}catch{}const o=r.value;if(typeof o=="number"&&!Number.isNaN(o)&&Number.isFinite(o))return r;const s=typeof o=="number"?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:o,inst:e,...s?{received:s}:{}}),r}}),as=d("$ZodNumber",(e,t)=>{xo.init(e,t),Lr.init(e,t)}),cs=d("$ZodBoolean",(e,t)=>{S.init(e,t),e._zod.pattern=yo,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=!!r.value}catch{}const o=r.value;return typeof o=="boolean"||r.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:e}),r}}),ls=d("$ZodUnknown",(e,t)=>{S.init(e,t),e._zod.parse=r=>r}),ds=d("$ZodNever",(e,t)=>{S.init(e,t),e._zod.parse=(r,n)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:e}),r)}),fs=d("$ZodDate",(e,t)=>{S.init(e,t),e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=new Date(r.value)}catch{}const o=r.value,s=o instanceof Date;return s&&!Number.isNaN(o.getTime())||r.issues.push({expected:"date",code:"invalid_type",input:o,...s?{received:"Invalid Date"}:{},inst:e}),r}});function Yt(e,t,r){e.issues.length&&t.issues.push(...$r(r,e.issues)),t.value[r]=e.value}const hs=d("$ZodArray",(e,t)=>{S.init(e,t),e._zod.parse=(r,n)=>{const o=r.value;if(!Array.isArray(o))return r.issues.push({expected:"array",code:"invalid_type",input:o,inst:e}),r;r.value=Array(o.length);const s=[];for(let i=0;i<o.length;i++){const c=o[i],v=t.element._zod.run({value:c,issues:[]},n);v instanceof Promise?s.push(v.then(b=>Yt(b,r,i))):Yt(v,r,i)}return s.length?Promise.all(s).then(()=>r):r}});function Pe(e,t,r,n){e.issues.length&&t.issues.push(...$r(r,e.issues)),e.value===void 0?r in n&&(t.value[r]=void 0):t.value[r]=e.value}const ms=d("$ZodObject",(e,t)=>{S.init(e,t);const r=Zr(()=>{const C=Object.keys(t.shape);for(const z of C)if(!(t.shape[z]instanceof S))throw new Error(`Invalid element at key "${z}": expected a Zod schema`);const g=Sn(t.shape);return{shape:t.shape,keys:C,keySet:new Set(C),numKeys:C.length,optionalKeys:new Set(g)}});F(e._zod,"propValues",()=>{const C=t.shape,g={};for(const z in C){const M=C[z]._zod;if(M.values){g[z]??(g[z]=new Set);for(const A of M.values)g[z].add(A)}}return g});const n=C=>{const g=new So(["shape","payload","ctx"]),z=r.value,M=x=>{const E=Kt(x);return`shape[${E}]._zod.run({ value: input[${E}], issues: [] }, ctx)`};g.write("const input = payload.value;");const A=Object.create(null);let X=0;for(const x of z.keys)A[x]=`key_${X++}`;g.write("const newResult = {}");for(const x of z.keys){const E=A[x],D=Kt(x);g.write(`const ${E} = ${M(x)};`),g.write(`
        if (${E}.issues.length) {
          payload.issues = payload.issues.concat(${E}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${D}, ...iss.path] : [${D}]
          })));
        }
        
        if (${E}.value === undefined) {
          if (${D} in input) {
            newResult[${D}] = undefined;
          }
        } else {
          newResult[${D}] = ${E}.value;
        }
      `)}g.write("payload.value = newResult;"),g.write("return payload;");const ce=g.compile();return(x,E)=>ce(C,x,E)};let o;const s=at,i=!xr.jitless,v=i&&Fn.value,b=t.catchall;let w;e._zod.parse=(C,g)=>{w??(w=r.value);const z=C.value;if(!s(z))return C.issues.push({expected:"object",code:"invalid_type",input:z,inst:e}),C;const M=[];if(i&&v&&g?.async===!1&&g.jitless!==!0)o||(o=n(t.shape)),C=o(C,g);else{C.value={};const E=w.shape;for(const D of w.keys){const le=E[D]._zod.run({value:z[D],issues:[]},g);le instanceof Promise?M.push(le.then(ne=>Pe(ne,C,D,z))):Pe(le,C,D,z)}}if(!b)return M.length?Promise.all(M).then(()=>C):C;const A=[],X=w.keySet,ce=b._zod,x=ce.def.type;for(const E of Object.keys(z)){if(X.has(E))continue;if(x==="never"){A.push(E);continue}const D=ce.run({value:z[E],issues:[]},g);D instanceof Promise?M.push(D.then(P=>Pe(P,C,E,z))):Pe(D,C,E,z)}return A.length&&C.issues.push({code:"unrecognized_keys",keys:A,input:z,inst:e}),M.length?Promise.all(M).then(()=>C):C}});function Xt(e,t,r,n){for(const s of e)if(s.issues.length===0)return t.value=s.value,t;const o=e.filter(s=>!Ee(s));return o.length===1?(t.value=o[0].value,o[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(s=>s.issues.map(i=>ge(i,n,pe())))}),t)}const ps=d("$ZodUnion",(e,t)=>{S.init(e,t),F(e._zod,"optin",()=>t.options.some(o=>o._zod.optin==="optional")?"optional":void 0),F(e._zod,"optout",()=>t.options.some(o=>o._zod.optout==="optional")?"optional":void 0),F(e._zod,"values",()=>{if(t.options.every(o=>o._zod.values))return new Set(t.options.flatMap(o=>Array.from(o._zod.values)))}),F(e._zod,"pattern",()=>{if(t.options.every(o=>o._zod.pattern)){const o=t.options.map(s=>s._zod.pattern);return new RegExp(`^(${o.map(s=>Ct(s.source)).join("|")})$`)}});const r=t.options.length===1,n=t.options[0]._zod.run;e._zod.parse=(o,s)=>{if(r)return n(o,s);let i=!1;const c=[];for(const v of t.options){const b=v._zod.run({value:o.value,issues:[]},s);if(b instanceof Promise)c.push(b),i=!0;else{if(b.issues.length===0)return b;c.push(b)}}return i?Promise.all(c).then(v=>Xt(v,o,e,s)):Xt(c,o,e,s)}}),gs=d("$ZodIntersection",(e,t)=>{S.init(e,t),e._zod.parse=(r,n)=>{const o=r.value,s=t.left._zod.run({value:o,issues:[]},n),i=t.right._zod.run({value:o,issues:[]},n);return s instanceof Promise||i instanceof Promise?Promise.all([s,i]).then(([v,b])=>Qt(r,v,b)):Qt(r,s,i)}});function lt(e,t){if(e===t)return{valid:!0,data:e};if(e instanceof Date&&t instanceof Date&&+e==+t)return{valid:!0,data:e};if(ct(e)&&ct(t)){const r=Object.keys(t),n=Object.keys(e).filter(s=>r.indexOf(s)!==-1),o={...e,...t};for(const s of n){const i=lt(e[s],t[s]);if(!i.valid)return{valid:!1,mergeErrorPath:[s,...i.mergeErrorPath]};o[s]=i.data}return{valid:!0,data:o}}if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let n=0;n<e.length;n++){const o=e[n],s=t[n],i=lt(o,s);if(!i.valid)return{valid:!1,mergeErrorPath:[n,...i.mergeErrorPath]};r.push(i.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function Qt(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),Ee(e))return e;const n=lt(t.value,r.value);if(!n.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(n.mergeErrorPath)}`);return e.value=n.data,e}const vs=d("$ZodEnum",(e,t)=>{S.init(e,t);const r=In(t.entries),n=new Set(r);e._zod.values=n,e._zod.pattern=new RegExp(`^(${r.filter(o=>Dn.has(typeof o)).map(o=>typeof o=="string"?be(o):o.toString()).join("|")})$`),e._zod.parse=(o,s)=>{const i=o.value;return n.has(i)||o.issues.push({code:"invalid_value",values:r,input:i,inst:e}),o}}),_s=d("$ZodLiteral",(e,t)=>{if(S.init(e,t),t.values.length===0)throw new Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=new RegExp(`^(${t.values.map(r=>typeof r=="string"?be(r):r?be(r.toString()):String(r)).join("|")})$`),e._zod.parse=(r,n)=>{const o=r.value;return e._zod.values.has(o)||r.issues.push({code:"invalid_value",values:t.values,input:o,inst:e}),r}}),Cs=d("$ZodTransform",(e,t)=>{S.init(e,t),e._zod.parse=(r,n)=>{const o=t.transform(r.value,r);if(n.async)return(o instanceof Promise?o:Promise.resolve(o)).then(i=>(r.value=i,r));if(o instanceof Promise)throw new Ve;return r.value=o,r}});function er(e,t){return e.issues.length&&t===void 0?{issues:[],value:void 0}:e}const ys=d("$ZodOptional",(e,t)=>{S.init(e,t),e._zod.optin="optional",e._zod.optout="optional",F(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),F(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${Ct(r.source)})?$`):void 0}),e._zod.parse=(r,n)=>{if(t.innerType._zod.optin==="optional"){const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>er(s,r.value)):er(o,r.value)}return r.value===void 0?r:t.innerType._zod.run(r,n)}}),bs=d("$ZodNullable",(e,t)=>{S.init(e,t),F(e._zod,"optin",()=>t.innerType._zod.optin),F(e._zod,"optout",()=>t.innerType._zod.optout),F(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${Ct(r.source)}|null)$`):void 0}),F(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(r,n)=>r.value===null?r:t.innerType._zod.run(r,n)}),ws=d("$ZodDefault",(e,t)=>{S.init(e,t),e._zod.optin="optional",F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{if(r.value===void 0)return r.value=t.defaultValue,r;const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>tr(s,t)):tr(o,t)}});function tr(e,t){return e.value===void 0&&(e.value=t.defaultValue),e}const ks=d("$ZodPrefault",(e,t)=>{S.init(e,t),e._zod.optin="optional",F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>(r.value===void 0&&(r.value=t.defaultValue),t.innerType._zod.run(r,n))}),xs=d("$ZodNonOptional",(e,t)=>{S.init(e,t),F(e._zod,"values",()=>{const r=t.innerType._zod.values;return r?new Set([...r].filter(n=>n!==void 0)):void 0}),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>rr(s,e)):rr(o,e)}});function rr(e,t){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}const Zs=d("$ZodCatch",(e,t)=>{S.init(e,t),F(e._zod,"optin",()=>t.innerType._zod.optin),F(e._zod,"optout",()=>t.innerType._zod.optout),F(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>(r.value=s.value,s.issues.length&&(r.value=t.catchValue({...r,error:{issues:s.issues.map(i=>ge(i,n,pe()))},input:r.value}),r.issues=[]),r)):(r.value=o.value,o.issues.length&&(r.value=t.catchValue({...r,error:{issues:o.issues.map(s=>ge(s,n,pe()))},input:r.value}),r.issues=[]),r)}}),zs=d("$ZodPipe",(e,t)=>{S.init(e,t),F(e._zod,"values",()=>t.in._zod.values),F(e._zod,"optin",()=>t.in._zod.optin),F(e._zod,"optout",()=>t.out._zod.optout),F(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(r,n)=>{const o=t.in._zod.run(r,n);return o instanceof Promise?o.then(s=>nr(s,t,n)):nr(o,t,n)}});function nr(e,t,r){return e.issues.length?e:t.out._zod.run({value:e.value,issues:e.issues},r)}const $s=d("$ZodReadonly",(e,t)=>{S.init(e,t),F(e._zod,"propValues",()=>t.innerType._zod.propValues),F(e._zod,"values",()=>t.innerType._zod.values),F(e._zod,"optin",()=>t.innerType._zod.optin),F(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(or):or(o)}});function or(e){return e.value=Object.freeze(e.value),e}const As=d("$ZodCustom",(e,t)=>{J.init(e,t),S.init(e,t),e._zod.parse=(r,n)=>r,e._zod.check=r=>{const n=r.value,o=t.fn(n);if(o instanceof Promise)return o.then(s=>sr(s,r,n,e));sr(o,r,n,e)}});function sr(e,t,r,n){if(!e){const o={code:"custom",input:r,inst:n,path:[...n._zod.def.path??[]],continue:!n._zod.def.abort};n._zod.def.params&&(o.params=n._zod.def.params),t.issues.push(Ie(o))}}class Es{constructor(){this._map=new Map,this._idmap=new Map}add(t,...r){const n=r[0];if(this._map.set(t,n),n&&typeof n=="object"&&"id"in n){if(this._idmap.has(n.id))throw new Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,t)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(t){const r=this._map.get(t);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(t),this}get(t){const r=t._zod.parent;if(r){const n={...this.get(r)??{}};delete n.id;const o={...n,...this._map.get(t)};return Object.keys(o).length?o:void 0}return this._map.get(t)}has(t){return this._map.has(t)}}function Vs(){return new Es}const je=Vs();function Is(e,t){return new e({type:"string",..._(t)})}function Ns(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,..._(t)})}function ir(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,..._(t)})}function Fs(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,..._(t)})}function Ds(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",..._(t)})}function Ss(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",..._(t)})}function Ts(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",..._(t)})}function Os(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,..._(t)})}function Ps(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,..._(t)})}function js(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,..._(t)})}function Ls(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,..._(t)})}function Rs(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,..._(t)})}function Ms(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,..._(t)})}function Us(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,..._(t)})}function Hs(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,..._(t)})}function Bs(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,..._(t)})}function Ws(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,..._(t)})}function qs(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,..._(t)})}function Gs(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,..._(t)})}function Ks(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,..._(t)})}function Js(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,..._(t)})}function Ys(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,..._(t)})}function Xs(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,..._(t)})}function Qs(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,..._(t)})}function ei(e,t){return new e({type:"string",format:"date",check:"string_format",..._(t)})}function ti(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,..._(t)})}function ri(e,t){return new e({type:"string",format:"duration",check:"string_format",..._(t)})}function ni(e,t){return new e({type:"number",checks:[],..._(t)})}function oi(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",..._(t)})}function si(e,t){return new e({type:"boolean",..._(t)})}function ii(e){return new e({type:"unknown"})}function ui(e,t){return new e({type:"never",..._(t)})}function ai(e,t){return new e({type:"date",..._(t)})}function ur(e,t){return new Or({check:"less_than",..._(t),value:e,inclusive:!1})}function Me(e,t){return new Or({check:"less_than",..._(t),value:e,inclusive:!0})}function ar(e,t){return new Pr({check:"greater_than",..._(t),value:e,inclusive:!1})}function Ue(e,t){return new Pr({check:"greater_than",..._(t),value:e,inclusive:!0})}function cr(e,t){return new ko({check:"multiple_of",..._(t),value:e})}function Rr(e,t){return new Zo({check:"max_length",..._(t),maximum:e})}function qe(e,t){return new zo({check:"min_length",..._(t),minimum:e})}function Mr(e,t){return new $o({check:"length_equals",..._(t),length:e})}function ci(e,t){return new Ao({check:"string_format",format:"regex",..._(t),pattern:e})}function li(e){return new Eo({check:"string_format",format:"lowercase",..._(e)})}function di(e){return new Vo({check:"string_format",format:"uppercase",..._(e)})}function fi(e,t){return new Io({check:"string_format",format:"includes",..._(t),includes:e})}function hi(e,t){return new No({check:"string_format",format:"starts_with",..._(t),prefix:e})}function mi(e,t){return new Fo({check:"string_format",format:"ends_with",..._(t),suffix:e})}function Fe(e){return new Do({check:"overwrite",tx:e})}function pi(e){return Fe(t=>t.normalize(e))}function gi(){return Fe(e=>e.trim())}function vi(){return Fe(e=>e.toLowerCase())}function _i(){return Fe(e=>e.toUpperCase())}function Ci(e,t,r){return new e({type:"array",element:t,..._(r)})}function yi(e,t,r){return new e({type:"custom",check:"custom",fn:t,..._(r)})}function bi(e){const t=wi(r=>(r.addIssue=n=>{if(typeof n=="string")r.issues.push(Ie(n,r.value,t._zod.def));else{const o=n;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=r.value),o.inst??(o.inst=t),o.continue??(o.continue=!t._zod.def.abort),r.issues.push(Ie(o))}},e(r.value,r)));return t}function wi(e,t){const r=new J({check:"custom",..._(t)});return r._zod.check=e,r}function lr(e,t){try{var r=e()}catch(n){return t(n)}return r&&r.then?r.then(void 0,t):r}function ki(e,t){for(var r={};e.length;){var n=e[0],o=n.code,s=n.message,i=n.path.join(".");if(!r[i])if("unionErrors"in n){var c=n.unionErrors[0].errors[0];r[i]={message:c.message,type:c.code}}else r[i]={message:s,type:o};if("unionErrors"in n&&n.unionErrors.forEach(function(w){return w.errors.forEach(function(C){return e.push(C)})}),t){var v=r[i].types,b=v&&v[n.code];r[i]=pt(i,t,r,o,b?[].concat(b,n.message):n.message)}e.shift()}return r}function xi(e,t){for(var r={};e.length;){var n=e[0],o=n.code,s=n.message,i=n.path.join(".");if(!r[i])if(n.code==="invalid_union"&&n.errors.length>0){var c=n.errors[0][0];r[i]={message:c.message,type:c.code}}else r[i]={message:s,type:o};if(n.code==="invalid_union"&&n.errors.forEach(function(w){return w.forEach(function(C){return e.push(C)})}),t){var v=r[i].types,b=v&&v[n.code];r[i]=pt(i,t,r,o,b?[].concat(b,n.message):n.message)}e.shift()}return r}function T2(e,t,r){if(r===void 0&&(r={}),function(n){return"_def"in n&&typeof n._def=="object"&&"typeName"in n._def}(e))return function(n,o,s){try{return Promise.resolve(lr(function(){return Promise.resolve(e[r.mode==="sync"?"parse":"parseAsync"](n,t)).then(function(i){return s.shouldUseNativeValidation&&it({},s),{errors:{},values:r.raw?Object.assign({},n):i}})},function(i){if(function(c){return Array.isArray(c?.issues)}(i))return{values:{},errors:Wt(ki(i.errors,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw i}))}catch(i){return Promise.reject(i)}};if(function(n){return"_zod"in n&&typeof n._zod=="object"}(e))return function(n,o,s){try{return Promise.resolve(lr(function(){return Promise.resolve((r.mode==="sync"?Bn:Wn)(e,n,t)).then(function(i){return s.shouldUseNativeValidation&&it({},s),{errors:{},values:r.raw?Object.assign({},n):i}})},function(i){if(function(c){return c instanceof bt}(i))return{values:{},errors:Wt(xi(i.issues,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw i}))}catch(i){return Promise.reject(i)}};throw new Error("Invalid input: not a Zod schema")}const Zi=d("ZodISODateTime",(e,t)=>{Go.init(e,t),O.init(e,t)});function zi(e){return Qs(Zi,e)}const $i=d("ZodISODate",(e,t)=>{Ko.init(e,t),O.init(e,t)});function Ai(e){return ei($i,e)}const Ei=d("ZodISOTime",(e,t)=>{Jo.init(e,t),O.init(e,t)});function Vi(e){return ti(Ei,e)}const Ii=d("ZodISODuration",(e,t)=>{Yo.init(e,t),O.init(e,t)});function Ni(e){return ri(Ii,e)}const Fi=(e,t)=>{bt.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:r=>Hn(e,r)},flatten:{value:r=>Un(e,r)},addIssue:{value:r=>{e.issues.push(r),e.message=JSON.stringify(e.issues,ut,2)}},addIssues:{value:r=>{e.issues.push(...r),e.message=JSON.stringify(e.issues,ut,2)}},isEmpty:{get(){return e.issues.length===0}}})},Ye=d("ZodError",Fi,{Parent:Error}),Di=Er(Ye),Si=Vr(Ye),Ti=Ir(Ye),Oi=Nr(Ye),j=d("ZodType",(e,t)=>(S.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(n=>typeof n=="function"?{_zod:{check:n,def:{check:"custom"},onattach:[]}}:n)]}),e.clone=(r,n)=>_e(e,r,n),e.brand=()=>e,e.register=(r,n)=>(r.add(e,n),e),e.parse=(r,n)=>Di(e,r,n,{callee:e.parse}),e.safeParse=(r,n)=>Ti(e,r,n),e.parseAsync=async(r,n)=>Si(e,r,n,{callee:e.parseAsync}),e.safeParseAsync=async(r,n)=>Oi(e,r,n),e.spa=e.safeParseAsync,e.refine=(r,n)=>e.check(V2(r,n)),e.superRefine=r=>e.check(I2(r)),e.overwrite=r=>e.check(Fe(r)),e.optional=()=>mr(e),e.nullable=()=>pr(e),e.nullish=()=>mr(pr(e)),e.nonoptional=r=>x2(e,r),e.array=()=>c2(e),e.or=r=>f2([e,r]),e.and=r=>m2(e,r),e.transform=r=>gr(e,_2(r)),e.default=r=>b2(e,r),e.prefault=r=>k2(e,r),e.catch=r=>z2(e,r),e.pipe=r=>gr(e,r),e.readonly=()=>E2(e),e.describe=r=>{const n=e.clone();return je.add(n,{description:r}),n},Object.defineProperty(e,"description",{get(){return je.get(e)?.description},configurable:!0}),e.meta=(...r)=>{if(r.length===0)return je.get(e);const n=e.clone();return je.add(n,r[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),Ur=d("_ZodString",(e,t)=>{wt.init(e,t),j.init(e,t);const r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...n)=>e.check(ci(...n)),e.includes=(...n)=>e.check(fi(...n)),e.startsWith=(...n)=>e.check(hi(...n)),e.endsWith=(...n)=>e.check(mi(...n)),e.min=(...n)=>e.check(qe(...n)),e.max=(...n)=>e.check(Rr(...n)),e.length=(...n)=>e.check(Mr(...n)),e.nonempty=(...n)=>e.check(qe(1,...n)),e.lowercase=n=>e.check(li(n)),e.uppercase=n=>e.check(di(n)),e.trim=()=>e.check(gi()),e.normalize=(...n)=>e.check(pi(...n)),e.toLowerCase=()=>e.check(vi()),e.toUpperCase=()=>e.check(_i())}),Pi=d("ZodString",(e,t)=>{wt.init(e,t),Ur.init(e,t),e.email=r=>e.check(Ns(ji,r)),e.url=r=>e.check(Os(Li,r)),e.jwt=r=>e.check(Xs(t2,r)),e.emoji=r=>e.check(Ps(Ri,r)),e.guid=r=>e.check(ir(dr,r)),e.uuid=r=>e.check(Fs(Le,r)),e.uuidv4=r=>e.check(Ds(Le,r)),e.uuidv6=r=>e.check(Ss(Le,r)),e.uuidv7=r=>e.check(Ts(Le,r)),e.nanoid=r=>e.check(js(Mi,r)),e.guid=r=>e.check(ir(dr,r)),e.cuid=r=>e.check(Ls(Ui,r)),e.cuid2=r=>e.check(Rs(Hi,r)),e.ulid=r=>e.check(Ms(Bi,r)),e.base64=r=>e.check(Ks(Xi,r)),e.base64url=r=>e.check(Js(Qi,r)),e.xid=r=>e.check(Us(Wi,r)),e.ksuid=r=>e.check(Hs(qi,r)),e.ipv4=r=>e.check(Bs(Gi,r)),e.ipv6=r=>e.check(Ws(Ki,r)),e.cidrv4=r=>e.check(qs(Ji,r)),e.cidrv6=r=>e.check(Gs(Yi,r)),e.e164=r=>e.check(Ys(e2,r)),e.datetime=r=>e.check(zi(r)),e.date=r=>e.check(Ai(r)),e.time=r=>e.check(Vi(r)),e.duration=r=>e.check(Ni(r))});function O2(e){return Is(Pi,e)}const O=d("ZodStringFormat",(e,t)=>{T.init(e,t),Ur.init(e,t)}),ji=d("ZodEmail",(e,t)=>{jo.init(e,t),O.init(e,t)}),dr=d("ZodGUID",(e,t)=>{Oo.init(e,t),O.init(e,t)}),Le=d("ZodUUID",(e,t)=>{Po.init(e,t),O.init(e,t)}),Li=d("ZodURL",(e,t)=>{Lo.init(e,t),O.init(e,t)}),Ri=d("ZodEmoji",(e,t)=>{Ro.init(e,t),O.init(e,t)}),Mi=d("ZodNanoID",(e,t)=>{Mo.init(e,t),O.init(e,t)}),Ui=d("ZodCUID",(e,t)=>{Uo.init(e,t),O.init(e,t)}),Hi=d("ZodCUID2",(e,t)=>{Ho.init(e,t),O.init(e,t)}),Bi=d("ZodULID",(e,t)=>{Bo.init(e,t),O.init(e,t)}),Wi=d("ZodXID",(e,t)=>{Wo.init(e,t),O.init(e,t)}),qi=d("ZodKSUID",(e,t)=>{qo.init(e,t),O.init(e,t)}),Gi=d("ZodIPv4",(e,t)=>{Xo.init(e,t),O.init(e,t)}),Ki=d("ZodIPv6",(e,t)=>{Qo.init(e,t),O.init(e,t)}),Ji=d("ZodCIDRv4",(e,t)=>{es.init(e,t),O.init(e,t)}),Yi=d("ZodCIDRv6",(e,t)=>{ts.init(e,t),O.init(e,t)}),Xi=d("ZodBase64",(e,t)=>{rs.init(e,t),O.init(e,t)}),Qi=d("ZodBase64URL",(e,t)=>{os.init(e,t),O.init(e,t)}),e2=d("ZodE164",(e,t)=>{ss.init(e,t),O.init(e,t)}),t2=d("ZodJWT",(e,t)=>{us.init(e,t),O.init(e,t)}),Hr=d("ZodNumber",(e,t)=>{Lr.init(e,t),j.init(e,t),e.gt=(n,o)=>e.check(ar(n,o)),e.gte=(n,o)=>e.check(Ue(n,o)),e.min=(n,o)=>e.check(Ue(n,o)),e.lt=(n,o)=>e.check(ur(n,o)),e.lte=(n,o)=>e.check(Me(n,o)),e.max=(n,o)=>e.check(Me(n,o)),e.int=n=>e.check(fr(n)),e.safe=n=>e.check(fr(n)),e.positive=n=>e.check(ar(0,n)),e.nonnegative=n=>e.check(Ue(0,n)),e.negative=n=>e.check(ur(0,n)),e.nonpositive=n=>e.check(Me(0,n)),e.multipleOf=(n,o)=>e.check(cr(n,o)),e.step=(n,o)=>e.check(cr(n,o)),e.finite=()=>e;const r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function P2(e){return ni(Hr,e)}const r2=d("ZodNumberFormat",(e,t)=>{as.init(e,t),Hr.init(e,t)});function fr(e){return oi(r2,e)}const n2=d("ZodBoolean",(e,t)=>{cs.init(e,t),j.init(e,t)});function j2(e){return si(n2,e)}const o2=d("ZodUnknown",(e,t)=>{ls.init(e,t),j.init(e,t)});function hr(){return ii(o2)}const s2=d("ZodNever",(e,t)=>{ds.init(e,t),j.init(e,t)});function i2(e){return ui(s2,e)}const u2=d("ZodDate",(e,t)=>{fs.init(e,t),j.init(e,t),e.min=(n,o)=>e.check(Ue(n,o)),e.max=(n,o)=>e.check(Me(n,o));const r=e._zod.bag;e.minDate=r.minimum?new Date(r.minimum):null,e.maxDate=r.maximum?new Date(r.maximum):null});function L2(e){return ai(u2,e)}const a2=d("ZodArray",(e,t)=>{hs.init(e,t),j.init(e,t),e.element=t.element,e.min=(r,n)=>e.check(qe(r,n)),e.nonempty=r=>e.check(qe(1,r)),e.max=(r,n)=>e.check(Rr(r,n)),e.length=(r,n)=>e.check(Mr(r,n)),e.unwrap=()=>e.element});function c2(e,t){return Ci(a2,e,t)}const l2=d("ZodObject",(e,t)=>{ms.init(e,t),j.init(e,t),F(e,"shape",()=>t.shape),e.keyof=()=>p2(Object.keys(e._zod.def.shape)),e.catchall=r=>e.clone({...e._zod.def,catchall:r}),e.passthrough=()=>e.clone({...e._zod.def,catchall:hr()}),e.loose=()=>e.clone({...e._zod.def,catchall:hr()}),e.strict=()=>e.clone({...e._zod.def,catchall:i2()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=r=>jn(e,r),e.merge=r=>Ln(e,r),e.pick=r=>On(e,r),e.omit=r=>Pn(e,r),e.partial=(...r)=>Rn(Br,e,r[0]),e.required=(...r)=>Mn(Wr,e,r[0])});function R2(e,t){const r={type:"object",get shape(){return ve(this,"shape",{...e}),this.shape},..._(t)};return new l2(r)}const d2=d("ZodUnion",(e,t)=>{ps.init(e,t),j.init(e,t),e.options=t.options});function f2(e,t){return new d2({type:"union",options:e,..._(t)})}const h2=d("ZodIntersection",(e,t)=>{gs.init(e,t),j.init(e,t)});function m2(e,t){return new h2({type:"intersection",left:e,right:t})}const dt=d("ZodEnum",(e,t)=>{vs.init(e,t),j.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);const r=new Set(Object.keys(t.entries));e.extract=(n,o)=>{const s={};for(const i of n)if(r.has(i))s[i]=t.entries[i];else throw new Error(`Key ${i} not found in enum`);return new dt({...t,checks:[],..._(o),entries:s})},e.exclude=(n,o)=>{const s={...t.entries};for(const i of n)if(r.has(i))delete s[i];else throw new Error(`Key ${i} not found in enum`);return new dt({...t,checks:[],..._(o),entries:s})}});function p2(e,t){const r=Array.isArray(e)?Object.fromEntries(e.map(n=>[n,n])):e;return new dt({type:"enum",entries:r,..._(t)})}const g2=d("ZodLiteral",(e,t)=>{_s.init(e,t),j.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function M2(e,t){return new g2({type:"literal",values:Array.isArray(e)?e:[e],..._(t)})}const v2=d("ZodTransform",(e,t)=>{Cs.init(e,t),j.init(e,t),e._zod.parse=(r,n)=>{r.addIssue=s=>{if(typeof s=="string")r.issues.push(Ie(s,r.value,t));else{const i=s;i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=r.value),i.inst??(i.inst=e),r.issues.push(Ie(i))}};const o=t.transform(r.value,r);return o instanceof Promise?o.then(s=>(r.value=s,r)):(r.value=o,r)}});function _2(e){return new v2({type:"transform",transform:e})}const Br=d("ZodOptional",(e,t)=>{ys.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType});function mr(e){return new Br({type:"optional",innerType:e})}const C2=d("ZodNullable",(e,t)=>{bs.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType});function pr(e){return new C2({type:"nullable",innerType:e})}const y2=d("ZodDefault",(e,t)=>{ws.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function b2(e,t){return new y2({type:"default",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const w2=d("ZodPrefault",(e,t)=>{ks.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType});function k2(e,t){return new w2({type:"prefault",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const Wr=d("ZodNonOptional",(e,t)=>{xs.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType});function x2(e,t){return new Wr({type:"nonoptional",innerType:e,..._(t)})}const Z2=d("ZodCatch",(e,t)=>{Zs.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function z2(e,t){return new Z2({type:"catch",innerType:e,catchValue:typeof t=="function"?t:()=>t})}const $2=d("ZodPipe",(e,t)=>{zs.init(e,t),j.init(e,t),e.in=t.in,e.out=t.out});function gr(e,t){return new $2({type:"pipe",in:e,out:t})}const A2=d("ZodReadonly",(e,t)=>{$s.init(e,t),j.init(e,t),e.unwrap=()=>e._zod.def.innerType});function E2(e){return new A2({type:"readonly",innerType:e})}const qr=d("ZodCustom",(e,t)=>{As.init(e,t),j.init(e,t)});function V2(e,t={}){return yi(qr,e,t)}function I2(e){return bi(e)}function U2(e,t={error:`Input not instance of ${e.name}`}){const r=new qr({type:"custom",check:"custom",fn:n=>n instanceof e,abort:!0,..._(t)});return r._zod.bag.Class=e,r}export{D2 as A,p2 as _,T2 as a,j2 as b,U2 as c,L2 as d,c2 as e,M2 as l,P2 as n,R2 as o,O2 as s,S2 as u};
