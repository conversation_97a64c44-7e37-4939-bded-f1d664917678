import{d as F}from"./index-Bxm2R2OW.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x=[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]],pt=F("clock",x),M=43200,W=1440,S=Symbol.for("constructDateFrom");function v(t,n){return typeof t=="function"?t(n):t&&typeof t=="object"&&S in t?t[S](n):t instanceof Date?new t.constructor(n):new Date(n)}function f(t,n){return v(t,t)}let T={};function C(){return T}function k(t){const n=f(t),e=new Date(Date.UTC(n.getFullYear(),n.getMonth(),n.getDate(),n.getHours(),n.getMinutes(),n.getSeconds(),n.getMilliseconds()));return e.setUTCFullYear(n.getFullYear()),+t-+e}function P(t,...n){const e=v.bind(null,t||n.find(o=>typeof o=="object"));return n.map(e)}function w(t,n){const e=+f(t)-+f(n);return e<0?-1:e>0?1:e}function X(t){return v(t,Date.now())}function A(t,n,e){const[o,a]=P(e?.in,t,n),s=o.getFullYear()-a.getFullYear(),i=o.getMonth()-a.getMonth();return s*12+i}function O(t){return n=>{const o=(t?Math[t]:Math.trunc)(n);return o===0?0:o}}function _(t,n){return+f(t)-+f(n)}function j(t,n){const e=f(t);return e.setHours(23,59,59,999),e}function Y(t,n){const e=f(t),o=e.getMonth();return e.setFullYear(e.getFullYear(),o+1,0),e.setHours(23,59,59,999),e}function N(t,n){const e=f(t);return+j(e)==+Y(e)}function I(t,n,e){const[o,a,s]=P(e?.in,t,t,n),i=w(a,s),r=Math.abs(A(a,s));if(r<1)return 0;a.getMonth()===1&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-i*r);let d=w(a,s)===-i;N(o)&&r===1&&w(o,s)===1&&(d=!1);const c=i*(r-+d);return c===0?0:c}function z(t,n,e){const o=_(t,n)/1e3;return O(e?.roundingMethod)(o)}const V={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},L=(t,n,e)=>{let o;const a=V[t];return typeof a=="string"?o=a:n===1?o=a.one:o=a.other.replace("{{count}}",n.toString()),e?.addSuffix?e.comparison&&e.comparison>0?"in "+o:o+" ago":o};function p(t){return(n={})=>{const e=n.width?String(n.width):t.defaultWidth;return t.formats[e]||t.formats[t.defaultWidth]}}const q={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},J={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},R={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},E={date:p({formats:q,defaultWidth:"full"}),time:p({formats:J,defaultWidth:"full"}),dateTime:p({formats:R,defaultWidth:"full"})},H={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Q=(t,n,e,o)=>H[t];function g(t){return(n,e)=>{const o=e?.context?String(e.context):"standalone";let a;if(o==="formatting"&&t.formattingValues){const i=t.defaultFormattingWidth||t.defaultWidth,r=e?.width?String(e.width):i;a=t.formattingValues[r]||t.formattingValues[i]}else{const i=t.defaultWidth,r=e?.width?String(e.width):t.defaultWidth;a=t.values[r]||t.values[i]}const s=t.argumentCallback?t.argumentCallback(n):n;return a[s]}}const U={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},B={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},K={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},$={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},G={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},Z={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},tt=(t,n)=>{const e=Number(t),o=e%100;if(o>20||o<10)switch(o%10){case 1:return e+"st";case 2:return e+"nd";case 3:return e+"rd"}return e+"th"},et={ordinalNumber:tt,era:g({values:U,defaultWidth:"wide"}),quarter:g({values:B,defaultWidth:"wide",argumentCallback:t=>t-1}),month:g({values:K,defaultWidth:"wide"}),day:g({values:$,defaultWidth:"wide"}),dayPeriod:g({values:G,defaultWidth:"wide",formattingValues:Z,defaultFormattingWidth:"wide"})};function b(t){return(n,e={})=>{const o=e.width,a=o&&t.matchPatterns[o]||t.matchPatterns[t.defaultMatchWidth],s=n.match(a);if(!s)return null;const i=s[0],r=o&&t.parsePatterns[o]||t.parsePatterns[t.defaultParseWidth],d=Array.isArray(r)?at(r,y=>y.test(i)):nt(r,y=>y.test(i));let c;c=t.valueCallback?t.valueCallback(d):d,c=e.valueCallback?e.valueCallback(c):c;const l=n.slice(i.length);return{value:c,rest:l}}}function nt(t,n){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e)&&n(t[e]))return e}function at(t,n){for(let e=0;e<t.length;e++)if(n(t[e]))return e}function ot(t){return(n,e={})=>{const o=n.match(t.matchPattern);if(!o)return null;const a=o[0],s=n.match(t.parsePattern);if(!s)return null;let i=t.valueCallback?t.valueCallback(s[0]):s[0];i=e.valueCallback?e.valueCallback(i):i;const r=n.slice(a.length);return{value:i,rest:r}}}const rt=/^(\d+)(th|st|nd|rd)?/i,it=/\d+/i,st={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},ut={any:[/^b/i,/^(a|c)/i]},ct={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},dt={any:[/1/i,/2/i,/3/i,/4/i]},lt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},mt={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},ft={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},ht={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},yt={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},gt={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},bt={ordinalNumber:ot({matchPattern:rt,parsePattern:it,valueCallback:t=>parseInt(t,10)}),era:b({matchPatterns:st,defaultMatchWidth:"wide",parsePatterns:ut,defaultParseWidth:"any"}),quarter:b({matchPatterns:ct,defaultMatchWidth:"wide",parsePatterns:dt,defaultParseWidth:"any",valueCallback:t=>t+1}),month:b({matchPatterns:lt,defaultMatchWidth:"wide",parsePatterns:mt,defaultParseWidth:"any"}),day:b({matchPatterns:ft,defaultMatchWidth:"wide",parsePatterns:ht,defaultParseWidth:"any"}),dayPeriod:b({matchPatterns:yt,defaultMatchWidth:"any",parsePatterns:gt,defaultParseWidth:"any"})},Mt={code:"en-US",formatDistance:L,formatLong:E,formatRelative:Q,localize:et,match:bt,options:{weekStartsOn:0,firstWeekContainsDate:1}};function wt(t,n,e){const o=C(),a=e?.locale??o.locale??Mt,s=2520,i=w(t,n);if(isNaN(i))throw new RangeError("Invalid time value");const r=Object.assign({},e,{addSuffix:e?.addSuffix,comparison:i}),[d,c]=P(e?.in,...i>0?[n,t]:[t,n]),l=z(c,d),y=(k(c)-k(d))/1e3,u=Math.round((l-y)/60);let h;if(u<2)return e?.includeSeconds?l<5?a.formatDistance("lessThanXSeconds",5,r):l<10?a.formatDistance("lessThanXSeconds",10,r):l<20?a.formatDistance("lessThanXSeconds",20,r):l<40?a.formatDistance("halfAMinute",0,r):l<60?a.formatDistance("lessThanXMinutes",1,r):a.formatDistance("xMinutes",1,r):u===0?a.formatDistance("lessThanXMinutes",1,r):a.formatDistance("xMinutes",u,r);if(u<45)return a.formatDistance("xMinutes",u,r);if(u<90)return a.formatDistance("aboutXHours",1,r);if(u<W){const m=Math.round(u/60);return a.formatDistance("aboutXHours",m,r)}else{if(u<s)return a.formatDistance("xDays",1,r);if(u<M){const m=Math.round(u/W);return a.formatDistance("xDays",m,r)}else if(u<M*2)return h=Math.round(u/M),a.formatDistance("aboutXMonths",h,r)}if(h=I(c,d),h<12){const m=Math.round(u/M);return a.formatDistance("xMonths",m,r)}else{const m=h%12,D=Math.trunc(h/12);return m<3?a.formatDistance("aboutXYears",D,r):m<9?a.formatDistance("overXYears",D,r):a.formatDistance("almostXYears",D+1,r)}}function vt(t,n){return wt(t,X(t),n)}export{pt as C,vt as f};
