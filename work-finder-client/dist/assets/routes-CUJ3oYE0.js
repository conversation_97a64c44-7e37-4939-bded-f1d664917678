import{u as tn,c as rn,a as nn,r as on,p as Ft,j as ee,L as sn,R as J}from"./index-BOPfWTHD.js";const Du=({children:e,title:t})=>{const{isAuthenticated:r}=tn(),[n]=rn(),o=n.get("redirectTo"),s=nn();return on.useEffect(()=>{r&&s(o||Ft.app.dashboard.getHref(),{replace:!0})},[r,s,o]),ee.jsxs("div",{className:"flex min-h-screen flex-col justify-center bg-gray-50 py-12 sm:px-6 lg:px-8",children:[ee.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[ee.jsx("div",{className:"flex justify-center",children:ee.jsx(sn,{className:"flex items-center text-blue-600 hover:text-blue-700",to:Ft.home.getHref(),children:ee.jsxs("div",{className:"flex items-center space-x-2",children:[ee.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:ee.jsx("span",{className:"text-white font-bold text-lg",children:"W"})}),ee.jsx("span",{className:"text-xl font-bold",children:"WorkFinder"})]})})}),ee.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:t})]}),ee.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:ee.jsx("div",{className:"bg-white px-4 py-8 shadow sm:rounded-lg sm:px-10",children:e})})]})};var Fe=e=>e.type==="checkbox",me=e=>e instanceof Date,G=e=>e==null;const mr=e=>typeof e=="object";var U=e=>!G(e)&&!Array.isArray(e)&&mr(e)&&!me(e),un=e=>U(e)&&e.target?Fe(e.target)?e.target.checked:e.target.value:e,an=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,cn=(e,t)=>e.has(an(t)),ln=e=>{const t=e.constructor&&e.constructor.prototype;return U(t)&&t.hasOwnProperty("isPrototypeOf")},lt=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function B(e){let t;const r=Array.isArray(e),n=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(!(lt&&(e instanceof Blob||n))&&(r||U(e)))if(t=r?[]:{},!r&&!ln(e))t=e;else for(const o in e)e.hasOwnProperty(o)&&(t[o]=B(e[o]));else return e;return t}var Ge=e=>/^\w*$/.test(e),M=e=>e===void 0,ft=e=>Array.isArray(e)?e.filter(Boolean):[],dt=e=>ft(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{if(!t||!U(e))return r;const n=(Ge(t)?[t]:dt(t)).reduce((o,s)=>G(o)?o:o[s],e);return M(n)||n===e?M(e[t])?r:e[t]:n},ie=e=>typeof e=="boolean",S=(e,t,r)=>{let n=-1;const o=Ge(t)?[t]:dt(t),s=o.length,i=s-1;for(;++n<s;){const c=o[n];let g=r;if(n!==i){const w=e[c];g=U(w)||Array.isArray(w)?w:isNaN(+o[n+1])?{}:[]}if(c==="__proto__"||c==="constructor"||c==="prototype")return;e[c]=g,e=e[c]}};const Dt={BLUR:"blur",FOCUS_OUT:"focusout"},te={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},ae={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},fn=J.createContext(null);fn.displayName="HookFormContext";var dn=(e,t,r,n=!0)=>{const o={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(o,s,{get:()=>{const i=s;return t._proxyFormState[i]!==te.all&&(t._proxyFormState[i]=!n||te.all),e[i]}});return o};const hn=typeof window<"u"?J.useLayoutEffect:J.useEffect;var ue=e=>typeof e=="string",mn=(e,t,r,n,o)=>ue(e)?(n&&t.watch.add(e),p(r,e,o)):Array.isArray(e)?e.map(s=>(n&&t.watch.add(s),p(r,s))):(n&&(t.watchAll=!0),r),nt=e=>G(e)||!mr(e);function de(e,t,r=new WeakSet){if(nt(e)||nt(t))return e===t;if(me(e)&&me(t))return e.getTime()===t.getTime();const n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;if(r.has(e)||r.has(t))return!0;r.add(e),r.add(t);for(const s of n){const i=e[s];if(!o.includes(s))return!1;if(s!=="ref"){const c=t[s];if(me(i)&&me(c)||U(i)&&U(c)||Array.isArray(i)&&Array.isArray(c)?!de(i,c,r):i!==c)return!1}}return!0}var ht=(e,t,r,n,o)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[n]:o||!0}}:{},xe=e=>Array.isArray(e)?e:[e],Tt=()=>{let e=[];return{get observers(){return e},next:o=>{for(const s of e)s.next&&s.next(o)},subscribe:o=>(e.push(o),{unsubscribe:()=>{e=e.filter(s=>s!==o)}}),unsubscribe:()=>{e=[]}}},K=e=>U(e)&&!Object.keys(e).length,mt=e=>e.type==="file",re=e=>typeof e=="function",Le=e=>{if(!lt)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},pr=e=>e.type==="select-multiple",pt=e=>e.type==="radio",pn=e=>pt(e)||Fe(e),rt=e=>Le(e)&&e.isConnected;function _n(e,t){const r=t.slice(0,-1).length;let n=0;for(;n<r;)e=M(e)?n++:e[t[n++]];return e}function gn(e){for(const t in e)if(e.hasOwnProperty(t)&&!M(e[t]))return!1;return!0}function L(e,t){const r=Array.isArray(t)?t:Ge(t)?[t]:dt(t),n=r.length===1?e:_n(e,r),o=r.length-1,s=r[o];return n&&delete n[s],o!==0&&(U(n)&&K(n)||Array.isArray(n)&&gn(n))&&L(e,r.slice(0,-1)),e}var _r=e=>{for(const t in e)if(re(e[t]))return!0;return!1};function Me(e,t={}){const r=Array.isArray(e);if(U(e)||r)for(const n in e)Array.isArray(e[n])||U(e[n])&&!_r(e[n])?(t[n]=Array.isArray(e[n])?[]:{},Me(e[n],t[n])):G(e[n])||(t[n]=!0);return t}function gr(e,t,r){const n=Array.isArray(e);if(U(e)||n)for(const o in e)Array.isArray(e[o])||U(e[o])&&!_r(e[o])?M(t)||nt(r[o])?r[o]=Array.isArray(e[o])?Me(e[o],[]):{...Me(e[o])}:gr(e[o],G(t)?{}:t[o],r[o]):r[o]=!de(e[o],t[o]);return r}var Ze=(e,t)=>gr(e,t,Me(t));const Ot={value:!1,isValid:!1},Vt={value:!0,isValid:!0};var vr=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!M(e[0].attributes.value)?M(e[0].value)||e[0].value===""?Vt:{value:e[0].value,isValid:!0}:Vt:Ot}return Ot},yr=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:n})=>M(e)?e:t?e===""?NaN:e&&+e:r&&ue(e)?new Date(e):n?n(e):e;const Nt={isValid:!1,value:null};var br=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,Nt):Nt;function Pt(e){const t=e.ref;return mt(t)?t.files:pt(t)?br(e.refs).value:pr(t)?[...t.selectedOptions].map(({value:r})=>r):Fe(t)?vr(e.refs).value:yr(M(t.value)?e.ref.value:t.value,e)}var vn=(e,t,r,n)=>{const o={};for(const s of e){const i=p(t,s);i&&S(o,s,i._f)}return{criteriaMode:r,names:[...e],fields:o,shouldUseNativeValidation:n}},Be=e=>e instanceof RegExp,$e=e=>M(e)?e:Be(e)?e.source:U(e)?Be(e.value)?e.value.source:e.value:e,Rt=e=>({isOnSubmit:!e||e===te.onSubmit,isOnBlur:e===te.onBlur,isOnChange:e===te.onChange,isOnAll:e===te.all,isOnTouch:e===te.onTouched});const Ct="AsyncFunction";var yn=e=>!!e&&!!e.validate&&!!(re(e.validate)&&e.validate.constructor.name===Ct||U(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===Ct)),bn=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),Ut=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(n=>e.startsWith(n)&&/^\.\w+/.test(e.slice(n.length))));const Ee=(e,t,r,n)=>{for(const o of r||Object.keys(e)){const s=p(e,o);if(s){const{_f:i,...c}=s;if(i){if(i.refs&&i.refs[0]&&t(i.refs[0],o)&&!n)return!0;if(i.ref&&t(i.ref,i.name)&&!n)return!0;if(Ee(c,t))break}else if(U(c)&&Ee(c,t))break}}};function jt(e,t,r){const n=p(e,r);if(n||Ge(r))return{error:n,name:r};const o=r.split(".");for(;o.length;){const s=o.join("."),i=p(t,s),c=p(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(c&&c.type)return{name:s,error:c};if(c&&c.root&&c.root.type)return{name:`${s}.root`,error:c.root};o.pop()}return{name:r}}var wn=(e,t,r,n)=>{r(e);const{name:o,...s}=e;return K(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(i=>t[i]===(!n||te.all))},kn=(e,t,r)=>!e||!t||e===t||xe(e).some(n=>n&&(r?n===t:n.startsWith(t)||t.startsWith(n))),zn=(e,t,r,n,o)=>o.isOnAll?!1:!r&&o.isOnTouch?!(t||e):(r?n.isOnBlur:o.isOnBlur)?!e:(r?n.isOnChange:o.isOnChange)?e:!0,Zn=(e,t)=>!ft(p(e,t)).length&&L(e,t),$n=(e,t,r)=>{const n=xe(p(e,r));return S(n,"root",t[r]),S(e,r,n),e},Ce=e=>ue(e);function Lt(e,t,r="validate"){if(Ce(e)||Array.isArray(e)&&e.every(Ce)||ie(e)&&!e)return{type:r,message:Ce(e)?e:"",ref:t}}var be=e=>U(e)&&!Be(e)?e:{value:e,message:""},Mt=async(e,t,r,n,o,s)=>{const{ref:i,refs:c,required:g,maxLength:w,minLength:k,min:y,max:_,pattern:$,validate:j,name:E,valueAsNumber:Y,mount:ce}=e._f,z=p(r,E);if(!ce||t.has(E))return{};const A=c?c[0]:i,T=Z=>{o&&A.reportValidity&&(A.setCustomValidity(ie(Z)?"":Z||""),A.reportValidity())},P={},le=pt(i),ne=Fe(i),Je=le||ne,Q=(Y||mt(i))&&M(i.value)&&M(z)||Le(i)&&i.value===""||z===""||Array.isArray(z)&&!z.length,he=ht.bind(null,E,n,P),oe=(Z,I,C,W=ae.maxLength,q=ae.minLength)=>{const se=Z?I:C;P[E]={type:Z?W:q,message:se,ref:i,...he(Z?W:q,se)}};if(s?!Array.isArray(z)||!z.length:g&&(!Je&&(Q||G(z))||ie(z)&&!z||ne&&!vr(c).isValid||le&&!br(c).isValid)){const{value:Z,message:I}=Ce(g)?{value:!!g,message:g}:be(g);if(Z&&(P[E]={type:ae.required,message:I,ref:A,...he(ae.required,I)},!n))return T(I),P}if(!Q&&(!G(y)||!G(_))){let Z,I;const C=be(_),W=be(y);if(!G(z)&&!isNaN(z)){const q=i.valueAsNumber||z&&+z;G(C.value)||(Z=q>C.value),G(W.value)||(I=q<W.value)}else{const q=i.valueAsDate||new Date(z),se=Te=>new Date(new Date().toDateString()+" "+Te),ze=i.type=="time",ye=i.type=="week";ue(C.value)&&z&&(Z=ze?se(z)>se(C.value):ye?z>C.value:q>new Date(C.value)),ue(W.value)&&z&&(I=ze?se(z)<se(W.value):ye?z<W.value:q<new Date(W.value))}if((Z||I)&&(oe(!!Z,C.message,W.message,ae.max,ae.min),!n))return T(P[E].message),P}if((w||k)&&!Q&&(ue(z)||s&&Array.isArray(z))){const Z=be(w),I=be(k),C=!G(Z.value)&&z.length>+Z.value,W=!G(I.value)&&z.length<+I.value;if((C||W)&&(oe(C,Z.message,I.message),!n))return T(P[E].message),P}if($&&!Q&&ue(z)){const{value:Z,message:I}=be($);if(Be(Z)&&!z.match(Z)&&(P[E]={type:ae.pattern,message:I,ref:i,...he(ae.pattern,I)},!n))return T(I),P}if(j){if(re(j)){const Z=await j(z,r),I=Lt(Z,A);if(I&&(P[E]={...I,...he(ae.validate,I.message)},!n))return T(I.message),P}else if(U(j)){let Z={};for(const I in j){if(!K(Z)&&!n)break;const C=Lt(await j[I](z,r),A,I);C&&(Z={...C,...he(I,C.message)},T(C.message),n&&(P[E]=Z))}if(!K(Z)&&(P[E]={ref:A,...Z},!n))return P}}return T(!0),P};const xn={mode:te.onSubmit,reValidateMode:te.onChange,shouldFocusError:!0};function En(e={}){let t={...xn,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:re(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1},n={},o=U(t.defaultValues)||U(t.values)?B(t.defaultValues||t.values)||{}:{},s=t.shouldUnregister?{}:B(o),i={action:!1,mount:!1,watch:!1},c={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},g,w=0;const k={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let y={...k};const _={array:Tt(),state:Tt()},$=t.criteriaMode===te.all,j=u=>a=>{clearTimeout(w),w=setTimeout(u,a)},E=async u=>{if(!t.disabled&&(k.isValid||y.isValid||u)){const a=t.resolver?K((await ne()).errors):await Q(n,!0);a!==r.isValid&&_.state.next({isValid:a})}},Y=(u,a)=>{!t.disabled&&(k.isValidating||k.validatingFields||y.isValidating||y.validatingFields)&&((u||Array.from(c.mount)).forEach(l=>{l&&(a?S(r.validatingFields,l,a):L(r.validatingFields,l))}),_.state.next({validatingFields:r.validatingFields,isValidating:!K(r.validatingFields)}))},ce=(u,a=[],l,m,h=!0,d=!0)=>{if(m&&l&&!t.disabled){if(i.action=!0,d&&Array.isArray(p(n,u))){const b=l(p(n,u),m.argA,m.argB);h&&S(n,u,b)}if(d&&Array.isArray(p(r.errors,u))){const b=l(p(r.errors,u),m.argA,m.argB);h&&S(r.errors,u,b),Zn(r.errors,u)}if((k.touchedFields||y.touchedFields)&&d&&Array.isArray(p(r.touchedFields,u))){const b=l(p(r.touchedFields,u),m.argA,m.argB);h&&S(r.touchedFields,u,b)}(k.dirtyFields||y.dirtyFields)&&(r.dirtyFields=Ze(o,s)),_.state.next({name:u,isDirty:oe(u,a),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else S(s,u,a)},z=(u,a)=>{S(r.errors,u,a),_.state.next({errors:r.errors})},A=u=>{r.errors=u,_.state.next({errors:r.errors,isValid:!1})},T=(u,a,l,m)=>{const h=p(n,u);if(h){const d=p(s,u,M(l)?p(o,u):l);M(d)||m&&m.defaultChecked||a?S(s,u,a?d:Pt(h._f)):C(u,d),i.mount&&E()}},P=(u,a,l,m,h)=>{let d=!1,b=!1;const x={name:u};if(!t.disabled){if(!l||m){(k.isDirty||y.isDirty)&&(b=r.isDirty,r.isDirty=x.isDirty=oe(),d=b!==x.isDirty);const F=de(p(o,u),a);b=!!p(r.dirtyFields,u),F?L(r.dirtyFields,u):S(r.dirtyFields,u,!0),x.dirtyFields=r.dirtyFields,d=d||(k.dirtyFields||y.dirtyFields)&&b!==!F}if(l){const F=p(r.touchedFields,u);F||(S(r.touchedFields,u,l),x.touchedFields=r.touchedFields,d=d||(k.touchedFields||y.touchedFields)&&F!==l)}d&&h&&_.state.next(x)}return d?x:{}},le=(u,a,l,m)=>{const h=p(r.errors,u),d=(k.isValid||y.isValid)&&ie(a)&&r.isValid!==a;if(t.delayError&&l?(g=j(()=>z(u,l)),g(t.delayError)):(clearTimeout(w),g=null,l?S(r.errors,u,l):L(r.errors,u)),(l?!de(h,l):h)||!K(m)||d){const b={...m,...d&&ie(a)?{isValid:a}:{},errors:r.errors,name:u};r={...r,...b},_.state.next(b)}},ne=async u=>{Y(u,!0);const a=await t.resolver(s,t.context,vn(u||c.mount,n,t.criteriaMode,t.shouldUseNativeValidation));return Y(u),a},Je=async u=>{const{errors:a}=await ne(u);if(u)for(const l of u){const m=p(a,l);m?S(r.errors,l,m):L(r.errors,l)}else r.errors=a;return a},Q=async(u,a,l={valid:!0})=>{for(const m in u){const h=u[m];if(h){const{_f:d,...b}=h;if(d){const x=c.array.has(d.name),F=h._f&&yn(h._f);F&&k.validatingFields&&Y([m],!0);const X=await Mt(h,c.disabled,s,$,t.shouldUseNativeValidation&&!a,x);if(F&&k.validatingFields&&Y([m]),X[d.name]&&(l.valid=!1,a))break;!a&&(p(X,d.name)?x?$n(r.errors,X,d.name):S(r.errors,d.name,X[d.name]):L(r.errors,d.name))}!K(b)&&await Q(b,a,l)}}return l.valid},he=()=>{for(const u of c.unMount){const a=p(n,u);a&&(a._f.refs?a._f.refs.every(l=>!rt(l)):!rt(a._f.ref))&&Ye(u)}c.unMount=new Set},oe=(u,a)=>!t.disabled&&(u&&a&&S(s,u,a),!de(Te(),o)),Z=(u,a,l)=>mn(u,c,{...i.mount?s:M(a)?o:ue(u)?{[u]:a}:a},l,a),I=u=>ft(p(i.mount?s:o,u,t.shouldUnregister?p(o,u,[]):[])),C=(u,a,l={})=>{const m=p(n,u);let h=a;if(m){const d=m._f;d&&(!d.disabled&&S(s,u,yr(a,d)),h=Le(d.ref)&&G(a)?"":a,pr(d.ref)?[...d.ref.options].forEach(b=>b.selected=h.includes(b.value)):d.refs?Fe(d.ref)?d.refs.forEach(b=>{(!b.defaultChecked||!b.disabled)&&(Array.isArray(h)?b.checked=!!h.find(x=>x===b.value):b.checked=h===b.value||!!h)}):d.refs.forEach(b=>b.checked=b.value===h):mt(d.ref)?d.ref.value="":(d.ref.value=h,d.ref.type||_.state.next({name:u,values:B(s)})))}(l.shouldDirty||l.shouldTouch)&&P(u,h,l.shouldTouch,l.shouldDirty,!0),l.shouldValidate&&ye(u)},W=(u,a,l)=>{for(const m in a){if(!a.hasOwnProperty(m))return;const h=a[m],d=u+"."+m,b=p(n,d);(c.array.has(u)||U(h)||b&&!b._f)&&!me(h)?W(d,h,l):C(d,h,l)}},q=(u,a,l={})=>{const m=p(n,u),h=c.array.has(u),d=B(a);S(s,u,d),h?(_.array.next({name:u,values:B(s)}),(k.isDirty||k.dirtyFields||y.isDirty||y.dirtyFields)&&l.shouldDirty&&_.state.next({name:u,dirtyFields:Ze(o,s),isDirty:oe(u,d)})):m&&!m._f&&!G(d)?W(u,d,l):C(u,d,l),Ut(u,c)&&_.state.next({...r,name:u}),_.state.next({name:i.mount?u:void 0,values:B(s)})},se=async u=>{i.mount=!0;const a=u.target;let l=a.name,m=!0;const h=p(n,l),d=F=>{m=Number.isNaN(F)||me(F)&&isNaN(F.getTime())||de(F,p(s,l,F))},b=Rt(t.mode),x=Rt(t.reValidateMode);if(h){let F,X;const Oe=a.type?Pt(h._f):un(u),fe=u.type===Dt.BLUR||u.type===Dt.FOCUS_OUT,Xr=!bn(h._f)&&!t.resolver&&!p(r.errors,l)&&!h._f.deps||zn(fe,p(r.touchedFields,l),r.isSubmitted,x,b),et=Ut(l,c,fe);S(s,l,Oe),fe?(h._f.onBlur&&h._f.onBlur(u),g&&g(0)):h._f.onChange&&h._f.onChange(u);const tt=P(l,Oe,fe),Qr=!K(tt)||et;if(!fe&&_.state.next({name:l,type:u.type,values:B(s)}),Xr)return(k.isValid||y.isValid)&&(t.mode==="onBlur"?fe&&E():fe||E()),Qr&&_.state.next({name:l,...et?{}:tt});if(!fe&&et&&_.state.next({...r}),t.resolver){const{errors:It}=await ne([l]);if(d(Oe),m){const en=jt(r.errors,n,l),St=jt(It,n,en.name||l);F=St.error,l=St.name,X=K(It)}}else Y([l],!0),F=(await Mt(h,c.disabled,s,$,t.shouldUseNativeValidation))[l],Y([l]),d(Oe),m&&(F?X=!1:(k.isValid||y.isValid)&&(X=await Q(n,!0)));m&&(h._f.deps&&ye(h._f.deps),le(l,X,F,tt))}},ze=(u,a)=>{if(p(r.errors,a)&&u.focus)return u.focus(),1},ye=async(u,a={})=>{let l,m;const h=xe(u);if(t.resolver){const d=await Je(M(u)?u:h);l=K(d),m=u?!h.some(b=>p(d,b)):l}else u?(m=(await Promise.all(h.map(async d=>{const b=p(n,d);return await Q(b&&b._f?{[d]:b}:b)}))).every(Boolean),!(!m&&!r.isValid)&&E()):m=l=await Q(n);return _.state.next({...!ue(u)||(k.isValid||y.isValid)&&l!==r.isValid?{}:{name:u},...t.resolver||!u?{isValid:l}:{},errors:r.errors}),a.shouldFocus&&!m&&Ee(n,ze,u?h:c.mount),m},Te=u=>{const a={...i.mount?s:o};return M(u)?a:ue(u)?p(a,u):u.map(l=>p(a,l))},wt=(u,a)=>({invalid:!!p((a||r).errors,u),isDirty:!!p((a||r).dirtyFields,u),error:p((a||r).errors,u),isValidating:!!p(r.validatingFields,u),isTouched:!!p((a||r).touchedFields,u)}),Wr=u=>{u&&xe(u).forEach(a=>L(r.errors,a)),_.state.next({errors:u?r.errors:{}})},kt=(u,a,l)=>{const m=(p(n,u,{_f:{}})._f||{}).ref,h=p(r.errors,u)||{},{ref:d,message:b,type:x,...F}=h;S(r.errors,u,{...F,...a,ref:m}),_.state.next({name:u,errors:r.errors,isValid:!1}),l&&l.shouldFocus&&m&&m.focus&&m.focus()},Gr=(u,a)=>re(u)?_.state.subscribe({next:l=>"values"in l&&u(Z(void 0,a),l)}):Z(u,a,!0),zt=u=>_.state.subscribe({next:a=>{kn(u.name,a.name,u.exact)&&wn(a,u.formState||k,Yr,u.reRenderRoot)&&u.callback({values:{...s},...r,...a,defaultValues:o})}}).unsubscribe,qr=u=>(i.mount=!0,y={...y,...u.formState},zt({...u,formState:y})),Ye=(u,a={})=>{for(const l of u?xe(u):c.mount)c.mount.delete(l),c.array.delete(l),a.keepValue||(L(n,l),L(s,l)),!a.keepError&&L(r.errors,l),!a.keepDirty&&L(r.dirtyFields,l),!a.keepTouched&&L(r.touchedFields,l),!a.keepIsValidating&&L(r.validatingFields,l),!t.shouldUnregister&&!a.keepDefaultValue&&L(o,l);_.state.next({values:B(s)}),_.state.next({...r,...a.keepDirty?{isDirty:oe()}:{}}),!a.keepIsValid&&E()},Zt=({disabled:u,name:a})=>{(ie(u)&&i.mount||u||c.disabled.has(a))&&(u?c.disabled.add(a):c.disabled.delete(a))},Xe=(u,a={})=>{let l=p(n,u);const m=ie(a.disabled)||ie(t.disabled);return S(n,u,{...l||{},_f:{...l&&l._f?l._f:{ref:{name:u}},name:u,mount:!0,...a}}),c.mount.add(u),l?Zt({disabled:ie(a.disabled)?a.disabled:t.disabled,name:u}):T(u,!0,a.value),{...m?{disabled:a.disabled||t.disabled}:{},...t.progressive?{required:!!a.required,min:$e(a.min),max:$e(a.max),minLength:$e(a.minLength),maxLength:$e(a.maxLength),pattern:$e(a.pattern)}:{},name:u,onChange:se,onBlur:se,ref:h=>{if(h){Xe(u,a),l=p(n,u);const d=M(h.value)&&h.querySelectorAll&&h.querySelectorAll("input,select,textarea")[0]||h,b=pn(d),x=l._f.refs||[];if(b?x.find(F=>F===d):d===l._f.ref)return;S(n,u,{_f:{...l._f,...b?{refs:[...x.filter(rt),d,...Array.isArray(p(o,u))?[{}]:[]],ref:{type:d.type,name:u}}:{ref:d}}}),T(u,!1,void 0,d)}else l=p(n,u,{}),l._f&&(l._f.mount=!1),(t.shouldUnregister||a.shouldUnregister)&&!(cn(c.array,u)&&i.action)&&c.unMount.add(u)}}},Qe=()=>t.shouldFocusError&&Ee(n,ze,c.mount),Kr=u=>{ie(u)&&(_.state.next({disabled:u}),Ee(n,(a,l)=>{const m=p(n,l);m&&(a.disabled=m._f.disabled||u,Array.isArray(m._f.refs)&&m._f.refs.forEach(h=>{h.disabled=m._f.disabled||u}))},0,!1))},$t=(u,a)=>async l=>{let m;l&&(l.preventDefault&&l.preventDefault(),l.persist&&l.persist());let h=B(s);if(_.state.next({isSubmitting:!0}),t.resolver){const{errors:d,values:b}=await ne();r.errors=d,h=B(b)}else await Q(n);if(c.disabled.size)for(const d of c.disabled)L(h,d);if(L(r.errors,"root"),K(r.errors)){_.state.next({errors:{}});try{await u(h,l)}catch(d){m=d}}else a&&await a({...r.errors},l),Qe(),setTimeout(Qe);if(_.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:K(r.errors)&&!m,submitCount:r.submitCount+1,errors:r.errors}),m)throw m},Hr=(u,a={})=>{p(n,u)&&(M(a.defaultValue)?q(u,B(p(o,u))):(q(u,a.defaultValue),S(o,u,B(a.defaultValue))),a.keepTouched||L(r.touchedFields,u),a.keepDirty||(L(r.dirtyFields,u),r.isDirty=a.defaultValue?oe(u,B(p(o,u))):oe()),a.keepError||(L(r.errors,u),k.isValid&&E()),_.state.next({...r}))},xt=(u,a={})=>{const l=u?B(u):o,m=B(l),h=K(u),d=h?o:m;if(a.keepDefaultValues||(o=l),!a.keepValues){if(a.keepDirtyValues){const b=new Set([...c.mount,...Object.keys(Ze(o,s))]);for(const x of Array.from(b))p(r.dirtyFields,x)?S(d,x,p(s,x)):q(x,p(d,x))}else{if(lt&&M(u))for(const b of c.mount){const x=p(n,b);if(x&&x._f){const F=Array.isArray(x._f.refs)?x._f.refs[0]:x._f.ref;if(Le(F)){const X=F.closest("form");if(X){X.reset();break}}}}if(a.keepFieldsRef)for(const b of c.mount)q(b,p(d,b));else n={}}s=t.shouldUnregister?a.keepDefaultValues?B(o):{}:B(d),_.array.next({values:{...d}}),_.state.next({values:{...d}})}c={mount:a.keepDirtyValues?c.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},i.mount=!k.isValid||!!a.keepIsValid||!!a.keepDirtyValues,i.watch=!!t.shouldUnregister,_.state.next({submitCount:a.keepSubmitCount?r.submitCount:0,isDirty:h?!1:a.keepDirty?r.isDirty:!!(a.keepDefaultValues&&!de(u,o)),isSubmitted:a.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:h?{}:a.keepDirtyValues?a.keepDefaultValues&&s?Ze(o,s):r.dirtyFields:a.keepDefaultValues&&u?Ze(o,u):a.keepDirty?r.dirtyFields:{},touchedFields:a.keepTouched?r.touchedFields:{},errors:a.keepErrors?r.errors:{},isSubmitSuccessful:a.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},Et=(u,a)=>xt(re(u)?u(s):u,a),Jr=(u,a={})=>{const l=p(n,u),m=l&&l._f;if(m){const h=m.refs?m.refs[0]:m.ref;h.focus&&(h.focus(),a.shouldSelect&&re(h.select)&&h.select())}},Yr=u=>{r={...r,...u}},At={control:{register:Xe,unregister:Ye,getFieldState:wt,handleSubmit:$t,setError:kt,_subscribe:zt,_runSchema:ne,_focusError:Qe,_getWatch:Z,_getDirty:oe,_setValid:E,_setFieldArray:ce,_setDisabledField:Zt,_setErrors:A,_getFieldArray:I,_reset:xt,_resetDefaultValues:()=>re(t.defaultValues)&&t.defaultValues().then(u=>{Et(u,t.resetOptions),_.state.next({isLoading:!1})}),_removeUnmounted:he,_disableForm:Kr,_subjects:_,_proxyFormState:k,get _fields(){return n},get _formValues(){return s},get _state(){return i},set _state(u){i=u},get _defaultValues(){return o},get _names(){return c},set _names(u){c=u},get _formState(){return r},get _options(){return t},set _options(u){t={...t,...u}}},subscribe:qr,trigger:ye,register:Xe,handleSubmit:$t,watch:Gr,setValue:q,getValues:Te,reset:Et,resetField:Hr,clearErrors:Wr,unregister:Ye,setError:kt,setFocus:Jr,getFieldState:wt};return{...At,formControl:At}}function Tu(e={}){const t=J.useRef(void 0),r=J.useRef(void 0),[n,o]=J.useState({isDirty:!1,isValidating:!1,isLoading:re(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:re(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:n},e.defaultValues&&!re(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:i,...c}=En(e);t.current={...c,formState:n}}const s=t.current.control;return s._options=e,hn(()=>{const i=s._subscribe({formState:s._proxyFormState,callback:()=>o({...s._formState}),reRenderRoot:!0});return o(c=>({...c,isReady:!0})),s._formState.isReady=!0,i},[s]),J.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),J.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),J.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),J.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),J.useEffect(()=>{if(s._proxyFormState.isDirty){const i=s._getDirty();i!==n.isDirty&&s._subjects.state.next({isDirty:i})}},[s,n.isDirty]),J.useEffect(()=>{e.values&&!de(e.values,r.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),r.current=e.values,o(i=>({...i}))):s._resetDefaultValues()},[s,e.values]),J.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=dn(n,s),t.current}const Bt=(e,t,r)=>{if(e&&"reportValidity"in e){const n=p(r,t);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},ot=(e,t)=>{for(const r in t.fields){const n=t.fields[r];n&&n.ref&&"reportValidity"in n.ref?Bt(n.ref,r,e):n&&n.refs&&n.refs.forEach(o=>Bt(o,r,e))}},Wt=(e,t)=>{t.shouldUseNativeValidation&&ot(e,t);const r={};for(const n in e){const o=p(t.fields,n),s=Object.assign(e[n]||{},{ref:o&&o.ref});if(An(t.names||Object.keys(e),n)){const i=Object.assign({},p(r,n));S(i,"root",s),S(r,n,i)}else S(r,n,s)}return r},An=(e,t)=>{const r=Gt(t);return e.some(n=>Gt(n).match(`^${r}\\.\\d+`))};function Gt(e){return e.replace(/\]|\[/g,"")}function f(e,t,r){function n(c,g){var w;Object.defineProperty(c,"_zod",{value:c._zod??{},enumerable:!1}),(w=c._zod).traits??(w.traits=new Set),c._zod.traits.add(e),t(c,g);for(const k in i.prototype)k in c||Object.defineProperty(c,k,{value:i.prototype[k].bind(c)});c._zod.constr=i,c._zod.def=g}const o=r?.Parent??Object;class s extends o{}Object.defineProperty(s,"name",{value:e});function i(c){var g;const w=r?.Parent?new s:this;n(w,c),(g=w._zod).deferred??(g.deferred=[]);for(const k of w._zod.deferred)k();return w}return Object.defineProperty(i,"init",{value:n}),Object.defineProperty(i,Symbol.hasInstance,{value:c=>r?.Parent&&c instanceof r.Parent?!0:c?._zod?.traits?.has(e)}),Object.defineProperty(i,"name",{value:e}),i}class Ie extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}const wr={};function pe(e){return wr}function In(e){const t=Object.values(e).filter(n=>typeof n=="number");return Object.entries(e).filter(([n,o])=>t.indexOf(+n)===-1).map(([n,o])=>o)}function st(e,t){return typeof t=="bigint"?t.toString():t}function kr(e){return{get value(){{const t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function _t(e){return e==null}function gt(e){const t=e.startsWith("^")?1:0,r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function Sn(e,t){const r=(e.toString().split(".")[1]||"").length,n=t.toString();let o=(n.split(".")[1]||"").length;if(o===0&&/\d?e-\d?/.test(n)){const g=n.match(/\d?e-(\d?)/);g?.[1]&&(o=Number.parseInt(g[1]))}const s=r>o?r:o,i=Number.parseInt(e.toFixed(s).replace(".","")),c=Number.parseInt(t.toFixed(s).replace(".",""));return i%c/10**s}function D(e,t,r){Object.defineProperty(e,t,{get(){{const n=r();return e[t]=n,n}},set(n){Object.defineProperty(e,t,{value:n})},configurable:!0})}function ge(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function ke(...e){const t={};for(const r of e){const n=Object.getOwnPropertyDescriptors(r);Object.assign(t,n)}return Object.defineProperties({},t)}function qt(e){return JSON.stringify(e)}const zr="captureStackTrace"in Error?Error.captureStackTrace:(...e)=>{};function it(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)}const Fn=kr(()=>{if(typeof navigator<"u"&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{const e=Function;return new e(""),!0}catch{return!1}});function ut(e){if(it(e)===!1)return!1;const t=e.constructor;if(t===void 0)return!0;const r=t.prototype;return!(it(r)===!1||Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")===!1)}const Dn=new Set(["string","number","symbol"]);function we(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ve(e,t,r){const n=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(n._zod.parent=e),n}function v(e){const t=e;if(!t)return{};if(typeof t=="string")return{error:()=>t};if(t?.message!==void 0){if(t?.error!==void 0)throw new Error("Cannot specify both `message` and `error` params");t.error=t.message}return delete t.message,typeof t.error=="string"?{...t,error:()=>t.error}:t}function Tn(e){return Object.keys(e).filter(t=>e[t]._zod.optin==="optional"&&e[t]._zod.optout==="optional")}const On={safeint:[Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER],int32:[-2147483648,2147483647],uint32:[0,4294967295],float32:[-34028234663852886e22,34028234663852886e22],float64:[-Number.MAX_VALUE,Number.MAX_VALUE]};function Vn(e,t){const r=e._zod.def,n=ke(e._zod.def,{get shape(){const o={};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&(o[s]=r.shape[s])}return ge(this,"shape",o),o},checks:[]});return ve(e,n)}function Nn(e,t){const r=e._zod.def,n=ke(e._zod.def,{get shape(){const o={...e._zod.def.shape};for(const s in t){if(!(s in r.shape))throw new Error(`Unrecognized key: "${s}"`);t[s]&&delete o[s]}return ge(this,"shape",o),o},checks:[]});return ve(e,n)}function Pn(e,t){if(!ut(t))throw new Error("Invalid input to extend: expected a plain object");const r=ke(e._zod.def,{get shape(){const n={...e._zod.def.shape,...t};return ge(this,"shape",n),n},checks:[]});return ve(e,r)}function Rn(e,t){const r=ke(e._zod.def,{get shape(){const n={...e._zod.def.shape,...t._zod.def.shape};return ge(this,"shape",n),n},get catchall(){return t._zod.def.catchall},checks:[]});return ve(e,r)}function Cn(e,t,r){const n=ke(t._zod.def,{get shape(){const o=t._zod.def.shape,s={...o};if(r)for(const i in r){if(!(i in o))throw new Error(`Unrecognized key: "${i}"`);r[i]&&(s[i]=e?new e({type:"optional",innerType:o[i]}):o[i])}else for(const i in o)s[i]=e?new e({type:"optional",innerType:o[i]}):o[i];return ge(this,"shape",s),s},checks:[]});return ve(t,n)}function Un(e,t,r){const n=ke(t._zod.def,{get shape(){const o=t._zod.def.shape,s={...o};if(r)for(const i in r){if(!(i in s))throw new Error(`Unrecognized key: "${i}"`);r[i]&&(s[i]=new e({type:"nonoptional",innerType:o[i]}))}else for(const i in o)s[i]=new e({type:"nonoptional",innerType:o[i]});return ge(this,"shape",s),s},checks:[]});return ve(t,n)}function Ae(e,t=0){for(let r=t;r<e.issues.length;r++)if(e.issues[r]?.continue!==!0)return!0;return!1}function Zr(e,t){return t.map(r=>{var n;return(n=r).path??(n.path=[]),r.path.unshift(e),r})}function Ve(e){return typeof e=="string"?e:e?.message}function _e(e,t,r){const n={...e,path:e.path??[]};if(!e.message){const o=Ve(e.inst?._zod.def?.error?.(e))??Ve(t?.error?.(e))??Ve(r.customError?.(e))??Ve(r.localeError?.(e))??"Invalid input";n.message=o}return delete n.inst,delete n.continue,t?.reportInput||delete n.input,n}function vt(e){return Array.isArray(e)?"array":typeof e=="string"?"string":"unknown"}function Se(...e){const[t,r,n]=e;return typeof t=="string"?{message:t,code:"custom",input:r,inst:n}:{...t}}const $r=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),e.message=JSON.stringify(t,st,2),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},yt=f("$ZodError",$r),qe=f("$ZodError",$r,{Parent:Error});function jn(e,t=r=>r.message){const r={},n=[];for(const o of e.issues)o.path.length>0?(r[o.path[0]]=r[o.path[0]]||[],r[o.path[0]].push(t(o))):n.push(t(o));return{formErrors:n,fieldErrors:r}}function Ln(e,t){const r=t||function(s){return s.message},n={_errors:[]},o=s=>{for(const i of s.issues)if(i.code==="invalid_union"&&i.errors.length)i.errors.map(c=>o({issues:c}));else if(i.code==="invalid_key")o({issues:i.issues});else if(i.code==="invalid_element")o({issues:i.issues});else if(i.path.length===0)n._errors.push(r(i));else{let c=n,g=0;for(;g<i.path.length;){const w=i.path[g];g===i.path.length-1?(c[w]=c[w]||{_errors:[]},c[w]._errors.push(r(i))):c[w]=c[w]||{_errors:[]},c=c[w],g++}}};return o(e),n}const xr=e=>(t,r,n,o)=>{const s=n?Object.assign(n,{async:!1}):{async:!1},i=t._zod.run({value:r,issues:[]},s);if(i instanceof Promise)throw new Ie;if(i.issues.length){const c=new(o?.Err??e)(i.issues.map(g=>_e(g,s,pe())));throw zr(c,o?.callee),c}return i.value},Mn=xr(qe),Er=e=>async(t,r,n,o)=>{const s=n?Object.assign(n,{async:!0}):{async:!0};let i=t._zod.run({value:r,issues:[]},s);if(i instanceof Promise&&(i=await i),i.issues.length){const c=new(o?.Err??e)(i.issues.map(g=>_e(g,s,pe())));throw zr(c,o?.callee),c}return i.value},Bn=Er(qe),Ar=e=>(t,r,n)=>{const o=n?{...n,async:!1}:{async:!1},s=t._zod.run({value:r,issues:[]},o);if(s instanceof Promise)throw new Ie;return s.issues.length?{success:!1,error:new(e??yt)(s.issues.map(i=>_e(i,o,pe())))}:{success:!0,data:s.value}},Wn=Ar(qe),Ir=e=>async(t,r,n)=>{const o=n?Object.assign(n,{async:!0}):{async:!0};let s=t._zod.run({value:r,issues:[]},o);return s instanceof Promise&&(s=await s),s.issues.length?{success:!1,error:new e(s.issues.map(i=>_e(i,o,pe())))}:{success:!0,data:s.value}},Gn=Ir(qe),qn=/^[cC][^\s-]{8,}$/,Kn=/^[0-9a-z]+$/,Hn=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,Jn=/^[0-9a-vA-V]{20}$/,Yn=/^[A-Za-z0-9]{27}$/,Xn=/^[a-zA-Z0-9_-]{21}$/,Qn=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,eo=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,Kt=e=>e?new RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,to=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,ro="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";function no(){return new RegExp(ro,"u")}const oo=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,so=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,io=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,uo=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,ao=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,Sr=/^[A-Za-z0-9_-]*$/,co=/^(?=.{1,253}\.?$)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[-0-9a-zA-Z]{0,61}[0-9a-zA-Z])?)*\.?$/,lo=/^\+(?:[0-9]){6,14}[0-9]$/,Fr="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",fo=new RegExp(`^${Fr}$`);function Dr(e){const t="(?:[01]\\d|2[0-3]):[0-5]\\d";return typeof e.precision=="number"?e.precision===-1?`${t}`:e.precision===0?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}function ho(e){return new RegExp(`^${Dr(e)}$`)}function mo(e){const t=Dr({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-](?:[01]\\d|2[0-3]):[0-5]\\d)");const n=`${t}(?:${r.join("|")})`;return new RegExp(`^${Fr}T(?:${n})$`)}const po=e=>{const t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return new RegExp(`^${t}$`)},_o=/^\d+$/,go=/^-?\d+(?:\.\d+)?/i,vo=/true|false/i,yo=/^[^A-Z]*$/,bo=/^[^a-z]*$/,H=f("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),Tr={number:"number",bigint:"bigint",object:"date"},Or=f("$ZodCheckLessThan",(e,t)=>{H.init(e,t);const r=Tr[typeof t.value];e._zod.onattach.push(n=>{const o=n._zod.bag,s=(t.inclusive?o.maximum:o.exclusiveMaximum)??Number.POSITIVE_INFINITY;t.value<s&&(t.inclusive?o.maximum=t.value:o.exclusiveMaximum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value<=t.value:n.value<t.value)||n.issues.push({origin:r,code:"too_big",maximum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),Vr=f("$ZodCheckGreaterThan",(e,t)=>{H.init(e,t);const r=Tr[typeof t.value];e._zod.onattach.push(n=>{const o=n._zod.bag,s=(t.inclusive?o.minimum:o.exclusiveMinimum)??Number.NEGATIVE_INFINITY;t.value>s&&(t.inclusive?o.minimum=t.value:o.exclusiveMinimum=t.value)}),e._zod.check=n=>{(t.inclusive?n.value>=t.value:n.value>t.value)||n.issues.push({origin:r,code:"too_small",minimum:t.value,input:n.value,inclusive:t.inclusive,inst:e,continue:!t.abort})}}),wo=f("$ZodCheckMultipleOf",(e,t)=>{H.init(e,t),e._zod.onattach.push(r=>{var n;(n=r._zod.bag).multipleOf??(n.multipleOf=t.value)}),e._zod.check=r=>{if(typeof r.value!=typeof t.value)throw new Error("Cannot mix number and bigint in multiple_of check.");(typeof r.value=="bigint"?r.value%t.value===BigInt(0):Sn(r.value,t.value)===0)||r.issues.push({origin:typeof r.value,code:"not_multiple_of",divisor:t.value,input:r.value,inst:e,continue:!t.abort})}}),ko=f("$ZodCheckNumberFormat",(e,t)=>{H.init(e,t),t.format=t.format||"float64";const r=t.format?.includes("int"),n=r?"int":"number",[o,s]=On[t.format];e._zod.onattach.push(i=>{const c=i._zod.bag;c.format=t.format,c.minimum=o,c.maximum=s,r&&(c.pattern=_o)}),e._zod.check=i=>{const c=i.value;if(r){if(!Number.isInteger(c)){i.issues.push({expected:n,format:t.format,code:"invalid_type",continue:!1,input:c,inst:e});return}if(!Number.isSafeInteger(c)){c>0?i.issues.push({input:c,code:"too_big",maximum:Number.MAX_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort}):i.issues.push({input:c,code:"too_small",minimum:Number.MIN_SAFE_INTEGER,note:"Integers must be within the safe integer range.",inst:e,origin:n,continue:!t.abort});return}}c<o&&i.issues.push({origin:"number",input:c,code:"too_small",minimum:o,inclusive:!0,inst:e,continue:!t.abort}),c>s&&i.issues.push({origin:"number",input:c,code:"too_big",maximum:s,inst:e})}}),zo=f("$ZodCheckMaxLength",(e,t)=>{var r;H.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<o&&(n._zod.bag.maximum=t.maximum)}),e._zod.check=n=>{const o=n.value;if(o.length<=t.maximum)return;const i=vt(o);n.issues.push({origin:i,code:"too_big",maximum:t.maximum,inclusive:!0,input:o,inst:e,continue:!t.abort})}}),Zo=f("$ZodCheckMinLength",(e,t)=>{var r;H.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>o&&(n._zod.bag.minimum=t.minimum)}),e._zod.check=n=>{const o=n.value;if(o.length>=t.minimum)return;const i=vt(o);n.issues.push({origin:i,code:"too_small",minimum:t.minimum,inclusive:!0,input:o,inst:e,continue:!t.abort})}}),$o=f("$ZodCheckLengthEquals",(e,t)=>{var r;H.init(e,t),(r=e._zod.def).when??(r.when=n=>{const o=n.value;return!_t(o)&&o.length!==void 0}),e._zod.onattach.push(n=>{const o=n._zod.bag;o.minimum=t.length,o.maximum=t.length,o.length=t.length}),e._zod.check=n=>{const o=n.value,s=o.length;if(s===t.length)return;const i=vt(o),c=s>t.length;n.issues.push({origin:i,...c?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:n.value,inst:e,continue:!t.abort})}}),Ke=f("$ZodCheckStringFormat",(e,t)=>{var r,n;H.init(e,t),e._zod.onattach.push(o=>{const s=o._zod.bag;s.format=t.format,t.pattern&&(s.patterns??(s.patterns=new Set),s.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=o=>{t.pattern.lastIndex=0,!t.pattern.test(o.value)&&o.issues.push({origin:"string",code:"invalid_format",format:t.format,input:o.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(n=e._zod).check??(n.check=()=>{})}),xo=f("$ZodCheckRegex",(e,t)=>{Ke.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,!t.pattern.test(r.value)&&r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),Eo=f("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=yo),Ke.init(e,t)}),Ao=f("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=bo),Ke.init(e,t)}),Io=f("$ZodCheckIncludes",(e,t)=>{H.init(e,t);const r=we(t.includes),n=new RegExp(typeof t.position=="number"?`^.{${t.position}}${r}`:r);t.pattern=n,e._zod.onattach.push(o=>{const s=o._zod.bag;s.patterns??(s.patterns=new Set),s.patterns.add(n)}),e._zod.check=o=>{o.value.includes(t.includes,t.position)||o.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:o.value,inst:e,continue:!t.abort})}}),So=f("$ZodCheckStartsWith",(e,t)=>{H.init(e,t);const r=new RegExp(`^${we(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const o=n._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(r)}),e._zod.check=n=>{n.value.startsWith(t.prefix)||n.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:n.value,inst:e,continue:!t.abort})}}),Fo=f("$ZodCheckEndsWith",(e,t)=>{H.init(e,t);const r=new RegExp(`.*${we(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(n=>{const o=n._zod.bag;o.patterns??(o.patterns=new Set),o.patterns.add(r)}),e._zod.check=n=>{n.value.endsWith(t.suffix)||n.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:n.value,inst:e,continue:!t.abort})}}),Do=f("$ZodCheckOverwrite",(e,t)=>{H.init(e,t),e._zod.check=r=>{r.value=t.tx(r.value)}});class To{constructor(t=[]){this.content=[],this.indent=0,this&&(this.args=t)}indented(t){this.indent+=1,t(this),this.indent-=1}write(t){if(typeof t=="function"){t(this,{execution:"sync"}),t(this,{execution:"async"});return}const n=t.split(`
`).filter(i=>i),o=Math.min(...n.map(i=>i.length-i.trimStart().length)),s=n.map(i=>i.slice(o)).map(i=>" ".repeat(this.indent*2)+i);for(const i of s)this.content.push(i)}compile(){const t=Function,r=this?.args,o=[...(this?.content??[""]).map(s=>`  ${s}`)];return new t(...r,o.join(`
`))}}const Oo={major:4,minor:0,patch:11},O=f("$ZodType",(e,t)=>{var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=Oo;const n=[...e._zod.def.checks??[]];e._zod.traits.has("$ZodCheck")&&n.unshift(e);for(const o of n)for(const s of o._zod.onattach)s(e);if(n.length===0)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{const o=(s,i,c)=>{let g=Ae(s),w;for(const k of i){if(k._zod.def.when){if(!k._zod.def.when(s))continue}else if(g)continue;const y=s.issues.length,_=k._zod.check(s);if(_ instanceof Promise&&c?.async===!1)throw new Ie;if(w||_ instanceof Promise)w=(w??Promise.resolve()).then(async()=>{await _,s.issues.length!==y&&(g||(g=Ae(s,y)))});else{if(s.issues.length===y)continue;g||(g=Ae(s,y))}}return w?w.then(()=>s):s};e._zod.run=(s,i)=>{const c=e._zod.parse(s,i);if(c instanceof Promise){if(i.async===!1)throw new Ie;return c.then(g=>o(g,n,i))}return o(c,n,i)}}e["~standard"]={validate:o=>{try{const s=Wn(e,o);return s.success?{value:s.data}:{issues:s.error?.issues}}catch{return Gn(e,o).then(i=>i.success?{value:i.data}:{issues:i.error?.issues})}},vendor:"zod",version:1}}),bt=f("$ZodString",(e,t)=>{O.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??po(e._zod.bag),e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=String(r.value)}catch{}return typeof r.value=="string"||r.issues.push({expected:"string",code:"invalid_type",input:r.value,inst:e}),r}}),V=f("$ZodStringFormat",(e,t)=>{Ke.init(e,t),bt.init(e,t)}),Vo=f("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=eo),V.init(e,t)}),No=f("$ZodUUID",(e,t)=>{if(t.version){const n={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(n===void 0)throw new Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=Kt(n))}else t.pattern??(t.pattern=Kt());V.init(e,t)}),Po=f("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=to),V.init(e,t)}),Ro=f("$ZodURL",(e,t)=>{V.init(e,t),e._zod.check=r=>{try{const n=r.value.trim(),o=new URL(n);t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(o.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:co.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(o.protocol.endsWith(":")?o.protocol.slice(0,-1):o.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),t.normalize?r.value=o.href:r.value=n;return}catch{r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),Co=f("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=no()),V.init(e,t)}),Uo=f("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=Xn),V.init(e,t)}),jo=f("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=qn),V.init(e,t)}),Lo=f("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=Kn),V.init(e,t)}),Mo=f("$ZodULID",(e,t)=>{t.pattern??(t.pattern=Hn),V.init(e,t)}),Bo=f("$ZodXID",(e,t)=>{t.pattern??(t.pattern=Jn),V.init(e,t)}),Wo=f("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=Yn),V.init(e,t)}),Go=f("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=mo(t)),V.init(e,t)}),qo=f("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=fo),V.init(e,t)}),Ko=f("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=ho(t)),V.init(e,t)}),Ho=f("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=Qn),V.init(e,t)}),Jo=f("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=oo),V.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv4"})}),Yo=f("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=so),V.init(e,t),e._zod.onattach.push(r=>{const n=r._zod.bag;n.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),Xo=f("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=io),V.init(e,t)}),Qo=f("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=uo),V.init(e,t),e._zod.check=r=>{const[n,o]=r.value.split("/");try{if(!o)throw new Error;const s=Number(o);if(`${s}`!==o)throw new Error;if(s<0||s>128)throw new Error;new URL(`http://[${n}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function Nr(e){if(e==="")return!0;if(e.length%4!==0)return!1;try{return atob(e),!0}catch{return!1}}const es=f("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=ao),V.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{Nr(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}});function ts(e){if(!Sr.test(e))return!1;const t=e.replace(/[-_]/g,n=>n==="-"?"+":"/"),r=t.padEnd(Math.ceil(t.length/4)*4,"=");return Nr(r)}const rs=f("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=Sr),V.init(e,t),e._zod.onattach.push(r=>{r._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{ts(r.value)||r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),ns=f("$ZodE164",(e,t)=>{t.pattern??(t.pattern=lo),V.init(e,t)});function os(e,t=null){try{const r=e.split(".");if(r.length!==3)return!1;const[n]=r;if(!n)return!1;const o=JSON.parse(atob(n));return!("typ"in o&&o?.typ!=="JWT"||!o.alg||t&&(!("alg"in o)||o.alg!==t))}catch{return!1}}const ss=f("$ZodJWT",(e,t)=>{V.init(e,t),e._zod.check=r=>{os(r.value,t.alg)||r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),Pr=f("$ZodNumber",(e,t)=>{O.init(e,t),e._zod.pattern=e._zod.bag.pattern??go,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=Number(r.value)}catch{}const o=r.value;if(typeof o=="number"&&!Number.isNaN(o)&&Number.isFinite(o))return r;const s=typeof o=="number"?Number.isNaN(o)?"NaN":Number.isFinite(o)?void 0:"Infinity":void 0;return r.issues.push({expected:"number",code:"invalid_type",input:o,inst:e,...s?{received:s}:{}}),r}}),is=f("$ZodNumber",(e,t)=>{ko.init(e,t),Pr.init(e,t)}),us=f("$ZodBoolean",(e,t)=>{O.init(e,t),e._zod.pattern=vo,e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=!!r.value}catch{}const o=r.value;return typeof o=="boolean"||r.issues.push({expected:"boolean",code:"invalid_type",input:o,inst:e}),r}}),as=f("$ZodUnknown",(e,t)=>{O.init(e,t),e._zod.parse=r=>r}),cs=f("$ZodNever",(e,t)=>{O.init(e,t),e._zod.parse=(r,n)=>(r.issues.push({expected:"never",code:"invalid_type",input:r.value,inst:e}),r)}),ls=f("$ZodDate",(e,t)=>{O.init(e,t),e._zod.parse=(r,n)=>{if(t.coerce)try{r.value=new Date(r.value)}catch{}const o=r.value,s=o instanceof Date;return s&&!Number.isNaN(o.getTime())||r.issues.push({expected:"date",code:"invalid_type",input:o,...s?{received:"Invalid Date"}:{},inst:e}),r}});function Ht(e,t,r){e.issues.length&&t.issues.push(...Zr(r,e.issues)),t.value[r]=e.value}const fs=f("$ZodArray",(e,t)=>{O.init(e,t),e._zod.parse=(r,n)=>{const o=r.value;if(!Array.isArray(o))return r.issues.push({expected:"array",code:"invalid_type",input:o,inst:e}),r;r.value=Array(o.length);const s=[];for(let i=0;i<o.length;i++){const c=o[i],g=t.element._zod.run({value:c,issues:[]},n);g instanceof Promise?s.push(g.then(w=>Ht(w,r,i))):Ht(g,r,i)}return s.length?Promise.all(s).then(()=>r):r}});function Ne(e,t,r,n){e.issues.length&&t.issues.push(...Zr(r,e.issues)),e.value===void 0?r in n&&(t.value[r]=void 0):t.value[r]=e.value}const ds=f("$ZodObject",(e,t)=>{O.init(e,t);const r=kr(()=>{const y=Object.keys(t.shape);for(const $ of y)if(!(t.shape[$]instanceof O))throw new Error(`Invalid element at key "${$}": expected a Zod schema`);const _=Tn(t.shape);return{shape:t.shape,keys:y,keySet:new Set(y),numKeys:y.length,optionalKeys:new Set(_)}});D(e._zod,"propValues",()=>{const y=t.shape,_={};for(const $ in y){const j=y[$]._zod;if(j.values){_[$]??(_[$]=new Set);for(const E of j.values)_[$].add(E)}}return _});const n=y=>{const _=new To(["shape","payload","ctx"]),$=r.value,j=z=>{const A=qt(z);return`shape[${A}]._zod.run({ value: input[${A}], issues: [] }, ctx)`};_.write("const input = payload.value;");const E=Object.create(null);let Y=0;for(const z of $.keys)E[z]=`key_${Y++}`;_.write("const newResult = {}");for(const z of $.keys){const A=E[z],T=qt(z);_.write(`const ${A} = ${j(z)};`),_.write(`
        if (${A}.issues.length) {
          payload.issues = payload.issues.concat(${A}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${T}, ...iss.path] : [${T}]
          })));
        }
        
        if (${A}.value === undefined) {
          if (${T} in input) {
            newResult[${T}] = undefined;
          }
        } else {
          newResult[${T}] = ${A}.value;
        }
      `)}_.write("payload.value = newResult;"),_.write("return payload;");const ce=_.compile();return(z,A)=>ce(y,z,A)};let o;const s=it,i=!wr.jitless,g=i&&Fn.value,w=t.catchall;let k;e._zod.parse=(y,_)=>{k??(k=r.value);const $=y.value;if(!s($))return y.issues.push({expected:"object",code:"invalid_type",input:$,inst:e}),y;const j=[];if(i&&g&&_?.async===!1&&_.jitless!==!0)o||(o=n(t.shape)),y=o(y,_);else{y.value={};const A=k.shape;for(const T of k.keys){const le=A[T]._zod.run({value:$[T],issues:[]},_);le instanceof Promise?j.push(le.then(ne=>Ne(ne,y,T,$))):Ne(le,y,T,$)}}if(!w)return j.length?Promise.all(j).then(()=>y):y;const E=[],Y=k.keySet,ce=w._zod,z=ce.def.type;for(const A of Object.keys($)){if(Y.has(A))continue;if(z==="never"){E.push(A);continue}const T=ce.run({value:$[A],issues:[]},_);T instanceof Promise?j.push(T.then(P=>Ne(P,y,A,$))):Ne(T,y,A,$)}return E.length&&y.issues.push({code:"unrecognized_keys",keys:E,input:$,inst:e}),j.length?Promise.all(j).then(()=>y):y}});function Jt(e,t,r,n){for(const s of e)if(s.issues.length===0)return t.value=s.value,t;const o=e.filter(s=>!Ae(s));return o.length===1?(t.value=o[0].value,o[0]):(t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(s=>s.issues.map(i=>_e(i,n,pe())))}),t)}const hs=f("$ZodUnion",(e,t)=>{O.init(e,t),D(e._zod,"optin",()=>t.options.some(o=>o._zod.optin==="optional")?"optional":void 0),D(e._zod,"optout",()=>t.options.some(o=>o._zod.optout==="optional")?"optional":void 0),D(e._zod,"values",()=>{if(t.options.every(o=>o._zod.values))return new Set(t.options.flatMap(o=>Array.from(o._zod.values)))}),D(e._zod,"pattern",()=>{if(t.options.every(o=>o._zod.pattern)){const o=t.options.map(s=>s._zod.pattern);return new RegExp(`^(${o.map(s=>gt(s.source)).join("|")})$`)}});const r=t.options.length===1,n=t.options[0]._zod.run;e._zod.parse=(o,s)=>{if(r)return n(o,s);let i=!1;const c=[];for(const g of t.options){const w=g._zod.run({value:o.value,issues:[]},s);if(w instanceof Promise)c.push(w),i=!0;else{if(w.issues.length===0)return w;c.push(w)}}return i?Promise.all(c).then(g=>Jt(g,o,e,s)):Jt(c,o,e,s)}}),ms=f("$ZodIntersection",(e,t)=>{O.init(e,t),e._zod.parse=(r,n)=>{const o=r.value,s=t.left._zod.run({value:o,issues:[]},n),i=t.right._zod.run({value:o,issues:[]},n);return s instanceof Promise||i instanceof Promise?Promise.all([s,i]).then(([g,w])=>Yt(r,g,w)):Yt(r,s,i)}});function at(e,t){if(e===t)return{valid:!0,data:e};if(e instanceof Date&&t instanceof Date&&+e==+t)return{valid:!0,data:e};if(ut(e)&&ut(t)){const r=Object.keys(t),n=Object.keys(e).filter(s=>r.indexOf(s)!==-1),o={...e,...t};for(const s of n){const i=at(e[s],t[s]);if(!i.valid)return{valid:!1,mergeErrorPath:[s,...i.mergeErrorPath]};o[s]=i.data}return{valid:!0,data:o}}if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return{valid:!1,mergeErrorPath:[]};const r=[];for(let n=0;n<e.length;n++){const o=e[n],s=t[n],i=at(o,s);if(!i.valid)return{valid:!1,mergeErrorPath:[n,...i.mergeErrorPath]};r.push(i.data)}return{valid:!0,data:r}}return{valid:!1,mergeErrorPath:[]}}function Yt(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),Ae(e))return e;const n=at(t.value,r.value);if(!n.valid)throw new Error(`Unmergable intersection. Error path: ${JSON.stringify(n.mergeErrorPath)}`);return e.value=n.data,e}const ps=f("$ZodEnum",(e,t)=>{O.init(e,t);const r=In(t.entries),n=new Set(r);e._zod.values=n,e._zod.pattern=new RegExp(`^(${r.filter(o=>Dn.has(typeof o)).map(o=>typeof o=="string"?we(o):o.toString()).join("|")})$`),e._zod.parse=(o,s)=>{const i=o.value;return n.has(i)||o.issues.push({code:"invalid_value",values:r,input:i,inst:e}),o}}),_s=f("$ZodLiteral",(e,t)=>{if(O.init(e,t),t.values.length===0)throw new Error("Cannot create literal schema with no valid values");e._zod.values=new Set(t.values),e._zod.pattern=new RegExp(`^(${t.values.map(r=>typeof r=="string"?we(r):r?we(r.toString()):String(r)).join("|")})$`),e._zod.parse=(r,n)=>{const o=r.value;return e._zod.values.has(o)||r.issues.push({code:"invalid_value",values:t.values,input:o,inst:e}),r}}),gs=f("$ZodTransform",(e,t)=>{O.init(e,t),e._zod.parse=(r,n)=>{const o=t.transform(r.value,r);if(n.async)return(o instanceof Promise?o:Promise.resolve(o)).then(i=>(r.value=i,r));if(o instanceof Promise)throw new Ie;return r.value=o,r}}),vs=f("$ZodOptional",(e,t)=>{O.init(e,t),e._zod.optin="optional",e._zod.optout="optional",D(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),D(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${gt(r.source)})?$`):void 0}),e._zod.parse=(r,n)=>t.innerType._zod.optin==="optional"?t.innerType._zod.run(r,n):r.value===void 0?r:t.innerType._zod.run(r,n)}),ys=f("$ZodNullable",(e,t)=>{O.init(e,t),D(e._zod,"optin",()=>t.innerType._zod.optin),D(e._zod,"optout",()=>t.innerType._zod.optout),D(e._zod,"pattern",()=>{const r=t.innerType._zod.pattern;return r?new RegExp(`^(${gt(r.source)}|null)$`):void 0}),D(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(r,n)=>r.value===null?r:t.innerType._zod.run(r,n)}),bs=f("$ZodDefault",(e,t)=>{O.init(e,t),e._zod.optin="optional",D(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{if(r.value===void 0)return r.value=t.defaultValue,r;const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>Xt(s,t)):Xt(o,t)}});function Xt(e,t){return e.value===void 0&&(e.value=t.defaultValue),e}const ws=f("$ZodPrefault",(e,t)=>{O.init(e,t),e._zod.optin="optional",D(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>(r.value===void 0&&(r.value=t.defaultValue),t.innerType._zod.run(r,n))}),ks=f("$ZodNonOptional",(e,t)=>{O.init(e,t),D(e._zod,"values",()=>{const r=t.innerType._zod.values;return r?new Set([...r].filter(n=>n!==void 0)):void 0}),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>Qt(s,e)):Qt(o,e)}});function Qt(e,t){return!e.issues.length&&e.value===void 0&&e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}const zs=f("$ZodCatch",(e,t)=>{O.init(e,t),D(e._zod,"optin",()=>t.innerType._zod.optin),D(e._zod,"optout",()=>t.innerType._zod.optout),D(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(s=>(r.value=s.value,s.issues.length&&(r.value=t.catchValue({...r,error:{issues:s.issues.map(i=>_e(i,n,pe()))},input:r.value}),r.issues=[]),r)):(r.value=o.value,o.issues.length&&(r.value=t.catchValue({...r,error:{issues:o.issues.map(s=>_e(s,n,pe()))},input:r.value}),r.issues=[]),r)}}),Zs=f("$ZodPipe",(e,t)=>{O.init(e,t),D(e._zod,"values",()=>t.in._zod.values),D(e._zod,"optin",()=>t.in._zod.optin),D(e._zod,"optout",()=>t.out._zod.optout),D(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(r,n)=>{const o=t.in._zod.run(r,n);return o instanceof Promise?o.then(s=>er(s,t,n)):er(o,t,n)}});function er(e,t,r){return e.issues.length?e:t.out._zod.run({value:e.value,issues:e.issues},r)}const $s=f("$ZodReadonly",(e,t)=>{O.init(e,t),D(e._zod,"propValues",()=>t.innerType._zod.propValues),D(e._zod,"values",()=>t.innerType._zod.values),D(e._zod,"optin",()=>t.innerType._zod.optin),D(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(r,n)=>{const o=t.innerType._zod.run(r,n);return o instanceof Promise?o.then(tr):tr(o)}});function tr(e){return e.value=Object.freeze(e.value),e}const xs=f("$ZodCustom",(e,t)=>{H.init(e,t),O.init(e,t),e._zod.parse=(r,n)=>r,e._zod.check=r=>{const n=r.value,o=t.fn(n);if(o instanceof Promise)return o.then(s=>rr(s,r,n,e));rr(o,r,n,e)}});function rr(e,t,r,n){if(!e){const o={code:"custom",input:r,inst:n,path:[...n._zod.def.path??[]],continue:!n._zod.def.abort};n._zod.def.params&&(o.params=n._zod.def.params),t.issues.push(Se(o))}}class Es{constructor(){this._map=new Map,this._idmap=new Map}add(t,...r){const n=r[0];if(this._map.set(t,n),n&&typeof n=="object"&&"id"in n){if(this._idmap.has(n.id))throw new Error(`ID ${n.id} already exists in the registry`);this._idmap.set(n.id,t)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(t){const r=this._map.get(t);return r&&typeof r=="object"&&"id"in r&&this._idmap.delete(r.id),this._map.delete(t),this}get(t){const r=t._zod.parent;if(r){const n={...this.get(r)??{}};delete n.id;const o={...n,...this._map.get(t)};return Object.keys(o).length?o:void 0}return this._map.get(t)}has(t){return this._map.has(t)}}function As(){return new Es}const Pe=As();function Is(e,t){return new e({type:"string",...v(t)})}function Ss(e,t){return new e({type:"string",format:"email",check:"string_format",abort:!1,...v(t)})}function nr(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...v(t)})}function Fs(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,...v(t)})}function Ds(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...v(t)})}function Ts(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...v(t)})}function Os(e,t){return new e({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...v(t)})}function Vs(e,t){return new e({type:"string",format:"url",check:"string_format",abort:!1,...v(t)})}function Ns(e,t){return new e({type:"string",format:"emoji",check:"string_format",abort:!1,...v(t)})}function Ps(e,t){return new e({type:"string",format:"nanoid",check:"string_format",abort:!1,...v(t)})}function Rs(e,t){return new e({type:"string",format:"cuid",check:"string_format",abort:!1,...v(t)})}function Cs(e,t){return new e({type:"string",format:"cuid2",check:"string_format",abort:!1,...v(t)})}function Us(e,t){return new e({type:"string",format:"ulid",check:"string_format",abort:!1,...v(t)})}function js(e,t){return new e({type:"string",format:"xid",check:"string_format",abort:!1,...v(t)})}function Ls(e,t){return new e({type:"string",format:"ksuid",check:"string_format",abort:!1,...v(t)})}function Ms(e,t){return new e({type:"string",format:"ipv4",check:"string_format",abort:!1,...v(t)})}function Bs(e,t){return new e({type:"string",format:"ipv6",check:"string_format",abort:!1,...v(t)})}function Ws(e,t){return new e({type:"string",format:"cidrv4",check:"string_format",abort:!1,...v(t)})}function Gs(e,t){return new e({type:"string",format:"cidrv6",check:"string_format",abort:!1,...v(t)})}function qs(e,t){return new e({type:"string",format:"base64",check:"string_format",abort:!1,...v(t)})}function Ks(e,t){return new e({type:"string",format:"base64url",check:"string_format",abort:!1,...v(t)})}function Hs(e,t){return new e({type:"string",format:"e164",check:"string_format",abort:!1,...v(t)})}function Js(e,t){return new e({type:"string",format:"jwt",check:"string_format",abort:!1,...v(t)})}function Ys(e,t){return new e({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...v(t)})}function Xs(e,t){return new e({type:"string",format:"date",check:"string_format",...v(t)})}function Qs(e,t){return new e({type:"string",format:"time",check:"string_format",precision:null,...v(t)})}function ei(e,t){return new e({type:"string",format:"duration",check:"string_format",...v(t)})}function ti(e,t){return new e({type:"number",checks:[],...v(t)})}function ri(e,t){return new e({type:"number",check:"number_format",abort:!1,format:"safeint",...v(t)})}function ni(e,t){return new e({type:"boolean",...v(t)})}function oi(e){return new e({type:"unknown"})}function si(e,t){return new e({type:"never",...v(t)})}function ii(e,t){return new e({type:"date",...v(t)})}function or(e,t){return new Or({check:"less_than",...v(t),value:e,inclusive:!1})}function Ue(e,t){return new Or({check:"less_than",...v(t),value:e,inclusive:!0})}function sr(e,t){return new Vr({check:"greater_than",...v(t),value:e,inclusive:!1})}function je(e,t){return new Vr({check:"greater_than",...v(t),value:e,inclusive:!0})}function ir(e,t){return new wo({check:"multiple_of",...v(t),value:e})}function Rr(e,t){return new zo({check:"max_length",...v(t),maximum:e})}function We(e,t){return new Zo({check:"min_length",...v(t),minimum:e})}function Cr(e,t){return new $o({check:"length_equals",...v(t),length:e})}function ui(e,t){return new xo({check:"string_format",format:"regex",...v(t),pattern:e})}function ai(e){return new Eo({check:"string_format",format:"lowercase",...v(e)})}function ci(e){return new Ao({check:"string_format",format:"uppercase",...v(e)})}function li(e,t){return new Io({check:"string_format",format:"includes",...v(t),includes:e})}function fi(e,t){return new So({check:"string_format",format:"starts_with",...v(t),prefix:e})}function di(e,t){return new Fo({check:"string_format",format:"ends_with",...v(t),suffix:e})}function De(e){return new Do({check:"overwrite",tx:e})}function hi(e){return De(t=>t.normalize(e))}function mi(){return De(e=>e.trim())}function pi(){return De(e=>e.toLowerCase())}function _i(){return De(e=>e.toUpperCase())}function gi(e,t,r){return new e({type:"array",element:t,...v(r)})}function vi(e,t,r){return new e({type:"custom",check:"custom",fn:t,...v(r)})}function yi(e){const t=bi(r=>(r.addIssue=n=>{if(typeof n=="string")r.issues.push(Se(n,r.value,t._zod.def));else{const o=n;o.fatal&&(o.continue=!1),o.code??(o.code="custom"),o.input??(o.input=r.value),o.inst??(o.inst=t),o.continue??(o.continue=!t._zod.def.abort),r.issues.push(Se(o))}},e(r.value,r)));return t}function bi(e,t){const r=new H({check:"custom",...v(t)});return r._zod.check=e,r}function ur(e,t){try{var r=e()}catch(n){return t(n)}return r&&r.then?r.then(void 0,t):r}function wi(e,t){for(var r={};e.length;){var n=e[0],o=n.code,s=n.message,i=n.path.join(".");if(!r[i])if("unionErrors"in n){var c=n.unionErrors[0].errors[0];r[i]={message:c.message,type:c.code}}else r[i]={message:s,type:o};if("unionErrors"in n&&n.unionErrors.forEach(function(k){return k.errors.forEach(function(y){return e.push(y)})}),t){var g=r[i].types,w=g&&g[n.code];r[i]=ht(i,t,r,o,w?[].concat(w,n.message):n.message)}e.shift()}return r}function ki(e,t){for(var r={};e.length;){var n=e[0],o=n.code,s=n.message,i=n.path.join(".");if(!r[i])if(n.code==="invalid_union"){var c=n.errors[0][0];r[i]={message:c.message,type:c.code}}else r[i]={message:s,type:o};if(n.code==="invalid_union"&&n.errors.forEach(function(k){return k.forEach(function(y){return e.push(y)})}),t){var g=r[i].types,w=g&&g[n.code];r[i]=ht(i,t,r,o,w?[].concat(w,n.message):n.message)}e.shift()}return r}function Ou(e,t,r){if(r===void 0&&(r={}),function(n){return"_def"in n&&typeof n._def=="object"&&"typeName"in n._def}(e))return function(n,o,s){try{return Promise.resolve(ur(function(){return Promise.resolve(e[r.mode==="sync"?"parse":"parseAsync"](n,t)).then(function(i){return s.shouldUseNativeValidation&&ot({},s),{errors:{},values:r.raw?Object.assign({},n):i}})},function(i){if(function(c){return Array.isArray(c?.issues)}(i))return{values:{},errors:Wt(wi(i.errors,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw i}))}catch(i){return Promise.reject(i)}};if(function(n){return"_zod"in n&&typeof n._zod=="object"}(e))return function(n,o,s){try{return Promise.resolve(ur(function(){return Promise.resolve((r.mode==="sync"?Mn:Bn)(e,n,t)).then(function(i){return s.shouldUseNativeValidation&&ot({},s),{errors:{},values:r.raw?Object.assign({},n):i}})},function(i){if(function(c){return c instanceof yt}(i))return{values:{},errors:Wt(ki(i.issues,!s.shouldUseNativeValidation&&s.criteriaMode==="all"),s)};throw i}))}catch(i){return Promise.reject(i)}};throw new Error("Invalid input: not a Zod schema")}const zi=f("ZodISODateTime",(e,t)=>{Go.init(e,t),N.init(e,t)});function Zi(e){return Ys(zi,e)}const $i=f("ZodISODate",(e,t)=>{qo.init(e,t),N.init(e,t)});function xi(e){return Xs($i,e)}const Ei=f("ZodISOTime",(e,t)=>{Ko.init(e,t),N.init(e,t)});function Ai(e){return Qs(Ei,e)}const Ii=f("ZodISODuration",(e,t)=>{Ho.init(e,t),N.init(e,t)});function Si(e){return ei(Ii,e)}const Fi=(e,t)=>{yt.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:r=>Ln(e,r)},flatten:{value:r=>jn(e,r)},addIssue:{value:r=>{e.issues.push(r),e.message=JSON.stringify(e.issues,st,2)}},addIssues:{value:r=>{e.issues.push(...r),e.message=JSON.stringify(e.issues,st,2)}},isEmpty:{get(){return e.issues.length===0}}})},He=f("ZodError",Fi,{Parent:Error}),Di=xr(He),Ti=Er(He),Oi=Ar(He),Vi=Ir(He),R=f("ZodType",(e,t)=>(O.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(n=>typeof n=="function"?{_zod:{check:n,def:{check:"custom"},onattach:[]}}:n)]}),e.clone=(r,n)=>ve(e,r,n),e.brand=()=>e,e.register=(r,n)=>(r.add(e,n),e),e.parse=(r,n)=>Di(e,r,n,{callee:e.parse}),e.safeParse=(r,n)=>Oi(e,r,n),e.parseAsync=async(r,n)=>Ti(e,r,n,{callee:e.parseAsync}),e.safeParseAsync=async(r,n)=>Vi(e,r,n),e.spa=e.safeParseAsync,e.refine=(r,n)=>e.check(Au(r,n)),e.superRefine=r=>e.check(Iu(r)),e.overwrite=r=>e.check(De(r)),e.optional=()=>fr(e),e.nullable=()=>dr(e),e.nullish=()=>fr(dr(e)),e.nonoptional=r=>ku(e,r),e.array=()=>uu(e),e.or=r=>lu([e,r]),e.and=r=>du(e,r),e.transform=r=>hr(e,_u(r)),e.default=r=>yu(e,r),e.prefault=r=>wu(e,r),e.catch=r=>Zu(e,r),e.pipe=r=>hr(e,r),e.readonly=()=>Eu(e),e.describe=r=>{const n=e.clone();return Pe.add(n,{description:r}),n},Object.defineProperty(e,"description",{get(){return Pe.get(e)?.description},configurable:!0}),e.meta=(...r)=>{if(r.length===0)return Pe.get(e);const n=e.clone();return Pe.add(n,r[0]),n},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),Ur=f("_ZodString",(e,t)=>{bt.init(e,t),R.init(e,t);const r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...n)=>e.check(ui(...n)),e.includes=(...n)=>e.check(li(...n)),e.startsWith=(...n)=>e.check(fi(...n)),e.endsWith=(...n)=>e.check(di(...n)),e.min=(...n)=>e.check(We(...n)),e.max=(...n)=>e.check(Rr(...n)),e.length=(...n)=>e.check(Cr(...n)),e.nonempty=(...n)=>e.check(We(1,...n)),e.lowercase=n=>e.check(ai(n)),e.uppercase=n=>e.check(ci(n)),e.trim=()=>e.check(mi()),e.normalize=(...n)=>e.check(hi(...n)),e.toLowerCase=()=>e.check(pi()),e.toUpperCase=()=>e.check(_i())}),Ni=f("ZodString",(e,t)=>{bt.init(e,t),Ur.init(e,t),e.email=r=>e.check(Ss(Pi,r)),e.url=r=>e.check(Vs(Ri,r)),e.jwt=r=>e.check(Js(Qi,r)),e.emoji=r=>e.check(Ns(Ci,r)),e.guid=r=>e.check(nr(ar,r)),e.uuid=r=>e.check(Fs(Re,r)),e.uuidv4=r=>e.check(Ds(Re,r)),e.uuidv6=r=>e.check(Ts(Re,r)),e.uuidv7=r=>e.check(Os(Re,r)),e.nanoid=r=>e.check(Ps(Ui,r)),e.guid=r=>e.check(nr(ar,r)),e.cuid=r=>e.check(Rs(ji,r)),e.cuid2=r=>e.check(Cs(Li,r)),e.ulid=r=>e.check(Us(Mi,r)),e.base64=r=>e.check(qs(Ji,r)),e.base64url=r=>e.check(Ks(Yi,r)),e.xid=r=>e.check(js(Bi,r)),e.ksuid=r=>e.check(Ls(Wi,r)),e.ipv4=r=>e.check(Ms(Gi,r)),e.ipv6=r=>e.check(Bs(qi,r)),e.cidrv4=r=>e.check(Ws(Ki,r)),e.cidrv6=r=>e.check(Gs(Hi,r)),e.e164=r=>e.check(Hs(Xi,r)),e.datetime=r=>e.check(Zi(r)),e.date=r=>e.check(xi(r)),e.time=r=>e.check(Ai(r)),e.duration=r=>e.check(Si(r))});function Vu(e){return Is(Ni,e)}const N=f("ZodStringFormat",(e,t)=>{V.init(e,t),Ur.init(e,t)}),Pi=f("ZodEmail",(e,t)=>{Po.init(e,t),N.init(e,t)}),ar=f("ZodGUID",(e,t)=>{Vo.init(e,t),N.init(e,t)}),Re=f("ZodUUID",(e,t)=>{No.init(e,t),N.init(e,t)}),Ri=f("ZodURL",(e,t)=>{Ro.init(e,t),N.init(e,t)}),Ci=f("ZodEmoji",(e,t)=>{Co.init(e,t),N.init(e,t)}),Ui=f("ZodNanoID",(e,t)=>{Uo.init(e,t),N.init(e,t)}),ji=f("ZodCUID",(e,t)=>{jo.init(e,t),N.init(e,t)}),Li=f("ZodCUID2",(e,t)=>{Lo.init(e,t),N.init(e,t)}),Mi=f("ZodULID",(e,t)=>{Mo.init(e,t),N.init(e,t)}),Bi=f("ZodXID",(e,t)=>{Bo.init(e,t),N.init(e,t)}),Wi=f("ZodKSUID",(e,t)=>{Wo.init(e,t),N.init(e,t)}),Gi=f("ZodIPv4",(e,t)=>{Jo.init(e,t),N.init(e,t)}),qi=f("ZodIPv6",(e,t)=>{Yo.init(e,t),N.init(e,t)}),Ki=f("ZodCIDRv4",(e,t)=>{Xo.init(e,t),N.init(e,t)}),Hi=f("ZodCIDRv6",(e,t)=>{Qo.init(e,t),N.init(e,t)}),Ji=f("ZodBase64",(e,t)=>{es.init(e,t),N.init(e,t)}),Yi=f("ZodBase64URL",(e,t)=>{rs.init(e,t),N.init(e,t)}),Xi=f("ZodE164",(e,t)=>{ns.init(e,t),N.init(e,t)}),Qi=f("ZodJWT",(e,t)=>{ss.init(e,t),N.init(e,t)}),jr=f("ZodNumber",(e,t)=>{Pr.init(e,t),R.init(e,t),e.gt=(n,o)=>e.check(sr(n,o)),e.gte=(n,o)=>e.check(je(n,o)),e.min=(n,o)=>e.check(je(n,o)),e.lt=(n,o)=>e.check(or(n,o)),e.lte=(n,o)=>e.check(Ue(n,o)),e.max=(n,o)=>e.check(Ue(n,o)),e.int=n=>e.check(cr(n)),e.safe=n=>e.check(cr(n)),e.positive=n=>e.check(sr(0,n)),e.nonnegative=n=>e.check(je(0,n)),e.negative=n=>e.check(or(0,n)),e.nonpositive=n=>e.check(Ue(0,n)),e.multipleOf=(n,o)=>e.check(ir(n,o)),e.step=(n,o)=>e.check(ir(n,o)),e.finite=()=>e;const r=e._zod.bag;e.minValue=Math.max(r.minimum??Number.NEGATIVE_INFINITY,r.exclusiveMinimum??Number.NEGATIVE_INFINITY)??null,e.maxValue=Math.min(r.maximum??Number.POSITIVE_INFINITY,r.exclusiveMaximum??Number.POSITIVE_INFINITY)??null,e.isInt=(r.format??"").includes("int")||Number.isSafeInteger(r.multipleOf??.5),e.isFinite=!0,e.format=r.format??null});function Nu(e){return ti(jr,e)}const eu=f("ZodNumberFormat",(e,t)=>{is.init(e,t),jr.init(e,t)});function cr(e){return ri(eu,e)}const tu=f("ZodBoolean",(e,t)=>{us.init(e,t),R.init(e,t)});function Pu(e){return ni(tu,e)}const ru=f("ZodUnknown",(e,t)=>{as.init(e,t),R.init(e,t)});function lr(){return oi(ru)}const nu=f("ZodNever",(e,t)=>{cs.init(e,t),R.init(e,t)});function ou(e){return si(nu,e)}const su=f("ZodDate",(e,t)=>{ls.init(e,t),R.init(e,t),e.min=(n,o)=>e.check(je(n,o)),e.max=(n,o)=>e.check(Ue(n,o));const r=e._zod.bag;e.minDate=r.minimum?new Date(r.minimum):null,e.maxDate=r.maximum?new Date(r.maximum):null});function Ru(e){return ii(su,e)}const iu=f("ZodArray",(e,t)=>{fs.init(e,t),R.init(e,t),e.element=t.element,e.min=(r,n)=>e.check(We(r,n)),e.nonempty=r=>e.check(We(1,r)),e.max=(r,n)=>e.check(Rr(r,n)),e.length=(r,n)=>e.check(Cr(r,n)),e.unwrap=()=>e.element});function uu(e,t){return gi(iu,e,t)}const au=f("ZodObject",(e,t)=>{ds.init(e,t),R.init(e,t),D(e,"shape",()=>t.shape),e.keyof=()=>hu(Object.keys(e._zod.def.shape)),e.catchall=r=>e.clone({...e._zod.def,catchall:r}),e.passthrough=()=>e.clone({...e._zod.def,catchall:lr()}),e.loose=()=>e.clone({...e._zod.def,catchall:lr()}),e.strict=()=>e.clone({...e._zod.def,catchall:ou()}),e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=r=>Pn(e,r),e.merge=r=>Rn(e,r),e.pick=r=>Vn(e,r),e.omit=r=>Nn(e,r),e.partial=(...r)=>Cn(Lr,e,r[0]),e.required=(...r)=>Un(Mr,e,r[0])});function Cu(e,t){const r={type:"object",get shape(){return ge(this,"shape",{...e}),this.shape},...v(t)};return new au(r)}const cu=f("ZodUnion",(e,t)=>{hs.init(e,t),R.init(e,t),e.options=t.options});function lu(e,t){return new cu({type:"union",options:e,...v(t)})}const fu=f("ZodIntersection",(e,t)=>{ms.init(e,t),R.init(e,t)});function du(e,t){return new fu({type:"intersection",left:e,right:t})}const ct=f("ZodEnum",(e,t)=>{ps.init(e,t),R.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);const r=new Set(Object.keys(t.entries));e.extract=(n,o)=>{const s={};for(const i of n)if(r.has(i))s[i]=t.entries[i];else throw new Error(`Key ${i} not found in enum`);return new ct({...t,checks:[],...v(o),entries:s})},e.exclude=(n,o)=>{const s={...t.entries};for(const i of n)if(r.has(i))delete s[i];else throw new Error(`Key ${i} not found in enum`);return new ct({...t,checks:[],...v(o),entries:s})}});function hu(e,t){const r=Array.isArray(e)?Object.fromEntries(e.map(n=>[n,n])):e;return new ct({type:"enum",entries:r,...v(t)})}const mu=f("ZodLiteral",(e,t)=>{_s.init(e,t),R.init(e,t),e.values=new Set(t.values),Object.defineProperty(e,"value",{get(){if(t.values.length>1)throw new Error("This schema contains multiple valid literal values. Use `.values` instead.");return t.values[0]}})});function Uu(e,t){return new mu({type:"literal",values:Array.isArray(e)?e:[e],...v(t)})}const pu=f("ZodTransform",(e,t)=>{gs.init(e,t),R.init(e,t),e._zod.parse=(r,n)=>{r.addIssue=s=>{if(typeof s=="string")r.issues.push(Se(s,r.value,t));else{const i=s;i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=r.value),i.inst??(i.inst=e),r.issues.push(Se(i))}};const o=t.transform(r.value,r);return o instanceof Promise?o.then(s=>(r.value=s,r)):(r.value=o,r)}});function _u(e){return new pu({type:"transform",transform:e})}const Lr=f("ZodOptional",(e,t)=>{vs.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType});function fr(e){return new Lr({type:"optional",innerType:e})}const gu=f("ZodNullable",(e,t)=>{ys.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType});function dr(e){return new gu({type:"nullable",innerType:e})}const vu=f("ZodDefault",(e,t)=>{bs.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap});function yu(e,t){return new vu({type:"default",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const bu=f("ZodPrefault",(e,t)=>{ws.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType});function wu(e,t){return new bu({type:"prefault",innerType:e,get defaultValue(){return typeof t=="function"?t():t}})}const Mr=f("ZodNonOptional",(e,t)=>{ks.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType});function ku(e,t){return new Mr({type:"nonoptional",innerType:e,...v(t)})}const zu=f("ZodCatch",(e,t)=>{zs.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap});function Zu(e,t){return new zu({type:"catch",innerType:e,catchValue:typeof t=="function"?t:()=>t})}const $u=f("ZodPipe",(e,t)=>{Zs.init(e,t),R.init(e,t),e.in=t.in,e.out=t.out});function hr(e,t){return new $u({type:"pipe",in:e,out:t})}const xu=f("ZodReadonly",(e,t)=>{$s.init(e,t),R.init(e,t),e.unwrap=()=>e._zod.def.innerType});function Eu(e){return new xu({type:"readonly",innerType:e})}const Br=f("ZodCustom",(e,t)=>{xs.init(e,t),R.init(e,t)});function Au(e,t={}){return vi(Br,e,t)}function Iu(e){return yi(e)}function ju(e,t={error:`Input not instance of ${e.name}`}){const r=new Br({type:"custom",check:"custom",fn:n=>n instanceof e,abort:!0,...v(t)});return r._zod.bag.Class=e,r}const Lu={LOGIN:"/auth/login",REGISTER:"/auth/register",FORGOT_PASSWORD:"/forgot-password"};export{Lu as A,hu as _,Ou as a,Pu as b,Du as c,Ru as d,ju as e,uu as f,Uu as l,Nu as n,Cu as o,Vu as s,Tu as u};
