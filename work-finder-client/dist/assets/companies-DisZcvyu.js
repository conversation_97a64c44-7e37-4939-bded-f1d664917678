import{c as H,r as i,j as e,f as n}from"./index-Bxm2R2OW.js";import{a as x}from"./mock-data-BqVSz9O0.js";import{S as O,I as T,a as h,b as u,c as p,d as g,e as r}from"./select-454QdpBE.js";import{C as U,a as D,B as c}from"./badge-L1wjnFol.js";import{F as G}from"./FeaturedCompanies-CEj0OwDU.js";import{F as K,A as Q,G as Y,L as W}from"./list-DXfJh1Mw.js";import"./chevron-down-Bu8lQHGF.js";import"./trending-up-CiYyj7aR.js";import"./users-CLiaPoiA.js";import"./map-pin-CKdZFLuR.js";function X(){const[d,b]=H(),[m,N]=i.useState(d.get("q")||""),[a,j]=i.useState({query:d.get("q")||void 0,industry:d.get("industry")?[d.get("industry")]:void 0,page:1,limit:12,sortBy:"name",sortOrder:"asc"}),[S,C]=i.useState("grid"),[f,A]=i.useState("name"),[w]=i.useState(!1),[F,q]=i.useState(x),[v,J]=i.useState(x.length),[y,R]=i.useState(1),P=Array.from(new Set(x.map(s=>s.industry))).sort(),k=[{value:"startup",label:"Startup (1-50)"},{value:"small",label:"Small (51-200)"},{value:"medium",label:"Medium (201-500)"},{value:"large",label:"Large (501-1000)"},{value:"enterprise",label:"Enterprise (1000+)"}];i.useEffect(()=>{let s=[...x];if(a.query){const t=a.query.toLowerCase();s=s.filter(l=>l.name.toLowerCase().includes(t)||l.description.toLowerCase().includes(t)||l.industry.toLowerCase().includes(t)||l.specialties.some(M=>M.toLowerCase().includes(t)))}switch(a.industry?.length&&(s=s.filter(t=>a.industry.includes(t.industry))),a.size?.length&&(s=s.filter(t=>a.size.includes(t.size))),a.type?.length&&(s=s.filter(t=>a.type.includes(t.type))),a.minRating&&(s=s.filter(t=>t.stats.averageRating>=a.minRating)),a.hasJobs&&(s=s.filter(t=>t.stats.totalJobs>0)),a.isVerified&&(s=s.filter(t=>t.isVerified)),f){case"rating":s.sort((t,l)=>l.stats.averageRating-t.stats.averageRating);break;case"jobs":s.sort((t,l)=>l.stats.totalJobs-t.stats.totalJobs);break;case"employees":s.sort((t,l)=>l.stats.totalEmployees-t.stats.totalEmployees);break;case"founded":s.sort((t,l)=>(l.foundedYear||0)-(t.foundedYear||0));break;default:s.sort((t,l)=>t.name.localeCompare(l.name))}q(s),J(s.length)},[a,f]);const V=()=>{const s={...a,query:m||void 0,page:1};j(s),R(1);const t=new URLSearchParams;m&&t.set("q",m),b(t)},o=(s,t)=>{const l={...a,[s]:t,page:1};j(l),R(1)},E=s=>{s.key==="Enter"&&V()},B=[{value:"name",label:"Company Name"},{value:"rating",label:"Highest Rated"},{value:"jobs",label:"Most Jobs"},{value:"employees",label:"Most Employees"},{value:"founded",label:"Recently Founded"}],L=(y-1)*12,I=L+12,z=F.slice(L,I);return e.jsx("div",{className:"min-h-screen bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Explore Companies"}),e.jsx("p",{className:"text-lg text-gray-600",children:"Discover amazing companies and find your next career opportunity"})]}),e.jsx(U,{className:"shadow-sm border-0 mb-8",children:e.jsxs(D,{className:"p-6",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row gap-4 mb-6",children:[e.jsxs("div",{className:"flex-1 relative",children:[e.jsx(O,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),e.jsx(T,{placeholder:"Search companies, industries, or technologies",value:m,onChange:s=>N(s.target.value),onKeyPress:E,className:"pl-10 h-12"})]}),e.jsx(n,{onClick:V,className:"h-12 px-8 font-semibold",children:"Search Companies"})]}),e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"flex-1",children:e.jsxs(h,{value:a.industry?.[0]||"",onValueChange:s=>o("industry",s?[s]:void 0),children:[e.jsx(u,{children:e.jsx(p,{placeholder:"All Industries"})}),e.jsxs(g,{children:[e.jsx(r,{value:"",children:"All Industries"}),P.map(s=>e.jsx(r,{value:s,children:s},s))]})]})}),e.jsx("div",{className:"flex-1",children:e.jsxs(h,{value:a.size?.[0]||"",onValueChange:s=>o("size",s?[s]:void 0),children:[e.jsx(u,{children:e.jsx(p,{placeholder:"All Sizes"})}),e.jsxs(g,{children:[e.jsx(r,{value:"",children:"All Sizes"}),k.map(s=>e.jsx(r,{value:s.value,children:s.label},s.value))]})]})}),e.jsx("div",{className:"flex-1",children:e.jsxs(h,{value:a.minRating?.toString()||"",onValueChange:s=>o("minRating",s?parseFloat(s):void 0),children:[e.jsx(u,{children:e.jsx(p,{placeholder:"All Ratings"})}),e.jsxs(g,{children:[e.jsx(r,{value:"",children:"All Ratings"}),e.jsx(r,{value:"4.5",children:"4.5+ Stars"}),e.jsx(r,{value:"4.0",children:"4.0+ Stars"}),e.jsx(r,{value:"3.5",children:"3.5+ Stars"}),e.jsx(r,{value:"3.0",children:"3.0+ Stars"})]})]})})]}),e.jsxs("div",{className:"flex flex-wrap gap-3 mt-4",children:[e.jsxs(n,{variant:a.hasJobs?"default":"outline",size:"sm",onClick:()=>o("hasJobs",!a.hasJobs),children:[e.jsx(K,{className:"h-4 w-4 mr-2"}),"Actively Hiring"]}),e.jsx(n,{variant:a.isVerified?"default":"outline",size:"sm",onClick:()=>o("isVerified",!a.isVerified),children:"Verified Companies"})]})]})}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900",children:[v.toLocaleString()," Companies"]}),e.jsxs("p",{className:"text-gray-600",children:["Showing ",(y-1)*12+1," - ",Math.min(y*12,v)," of ",v.toLocaleString()," results"]})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Q,{className:"h-4 w-4 text-gray-500"}),e.jsxs(h,{value:f,onValueChange:A,children:[e.jsx(u,{className:"w-40",children:e.jsx(p,{})}),e.jsx(g,{children:B.map(s=>e.jsx(r,{value:s.value,children:s.label},s.value))})]})]}),e.jsxs("div",{className:"flex items-center bg-gray-100 rounded-lg p-1",children:[e.jsx(n,{variant:S==="grid"?"default":"ghost",size:"sm",onClick:()=>C("grid"),className:"px-3",children:e.jsx(Y,{className:"h-4 w-4"})}),e.jsx(n,{variant:S==="list"?"default":"ghost",size:"sm",onClick:()=>C("list"),className:"px-3",children:e.jsx(W,{className:"h-4 w-4"})})]})]})]}),e.jsx(G,{companies:z,isLoading:w}),!w&&z.length===0&&e.jsxs("div",{className:"text-center py-16",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:e.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No companies found"}),e.jsx("p",{className:"text-gray-600",children:"Try adjusting your search criteria or filters to see more results."})]}),(a.query||a.industry?.length||a.size?.length||a.minRating||a.hasJobs||a.isVerified)&&e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-4",children:[e.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Active Filters:"}),e.jsx(n,{variant:"ghost",size:"sm",onClick:()=>{j({page:1,limit:12,sortBy:"name",sortOrder:"asc"}),N(""),b(new URLSearchParams)},className:"text-blue-600 hover:text-blue-700",children:"Clear All"})]}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[a.query&&e.jsxs(c,{variant:"secondary",className:"py-1 px-3",children:['Search: "',a.query,'"']}),a.industry?.map(s=>e.jsxs(c,{variant:"secondary",className:"py-1 px-3",children:["Industry: ",s]},s)),a.size?.map(s=>e.jsxs(c,{variant:"secondary",className:"py-1 px-3",children:["Size: ",k.find(t=>t.value===s)?.label]},s)),a.minRating&&e.jsxs(c,{variant:"secondary",className:"py-1 px-3",children:["Rating: ",a.minRating,"+ Stars"]}),a.hasJobs&&e.jsx(c,{variant:"secondary",className:"py-1 px-3",children:"Actively Hiring"}),a.isVerified&&e.jsx(c,{variant:"secondary",className:"py-1 px-3",children:"Verified Only"})]})]})]})})}const ne=()=>e.jsx(X,{});export{ne as default};
