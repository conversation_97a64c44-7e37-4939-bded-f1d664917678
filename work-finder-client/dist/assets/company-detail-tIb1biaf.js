import{e as g,f as I,a as J,r as m,j as e,g as V,d as r,B as k}from"./index-BOPfWTHD.js";import{i as H,m as $,C as l,a as n,T as E,w as P,x as h,y as p,k as d,n as o,A as W,z as D,B as q}from"./mock-data-CbJZR1PP.js";import{B as x}from"./badge-Bzyz8_QN.js";import{F as C}from"./FeaturedJobs-BwajVF4Z.js";import{A as G}from"./arrow-left-BaQg7S-i.js";import{M as L}from"./map-pin-BR21nmoc.js";import{U as O}from"./users-DpHMEXct.js";import{C as U}from"./calendar-jrt4hqPn.js";import{E as j}from"./external-link-CApOk594.js";import{T as Q,S as Y}from"./trending-up-B7H7FfFe.js";import"./bookmark-DWvq3fkz.js";import"./dollar-sign-C4uAz_6M.js";import"./formatDistanceToNow-Bm5-CVp4.js";/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]],S=g("award",Z);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]],T=g("globe",K);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]],ee=g("linkedin",X);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const se=[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]],ae=g("twitter",se);function te(){const{id:f}=I(),u=J(),[s,A]=m.useState(null),[c,z]=m.useState([]),[F,M]=m.useState(!0),[N,R]=m.useState(!1),b=[{id:"1",userId:"user-1",userName:"Sarah Chen",userAvatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",rating:5,title:"Amazing company culture and growth opportunities",content:"Working at this company has been an incredible experience. The team is supportive, the projects are challenging, and there are plenty of opportunities to learn and grow.",pros:["Great work-life balance","Supportive management","Learning opportunities","Competitive salary"],cons:["Fast-paced environment","Limited remote work options"],advice:"Come prepared to learn and grow. The company invests heavily in employee development.",position:"Senior Software Engineer",employmentType:"current",workLocation:"hybrid",yearsWorked:2,createdAt:"2024-01-15T00:00:00Z",helpful:12,notHelpful:1,isVerified:!0},{id:"2",userId:"user-2",userName:"Michael Rodriguez",userAvatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",rating:4,title:"Solid company with good benefits",content:"Overall a good place to work. The benefits are competitive and the team is friendly. Some processes could be improved but management is open to feedback.",pros:["Good benefits","Friendly team","Open to feedback","Stable company"],cons:["Some outdated processes","Could use more innovation"],advice:"Be proactive about suggesting improvements. Management is receptive to new ideas.",position:"Product Manager",employmentType:"former",workLocation:"on-site",yearsWorked:3,createdAt:"2024-01-10T00:00:00Z",helpful:8,notHelpful:2,isVerified:!0}];m.useEffect(()=>{setTimeout(()=>{const a=H.find(t=>t.id===f);if(a){A(a);const t=$.filter(i=>i.companyId===a.id);z(t)}M(!1)},500)},[f]);const _=()=>{R(!N)},v=a=>{console.log("Save job:",a)},B=(a,t)=>({startup:"1-50",small:"51-200",medium:"201-500",large:"501-1000",enterprise:"1000+"})[a]||`${t}+`,y=a=>{const t=a.locations.find(i=>i.isHeadquarters);return t?`${t.city}, ${t.state}, ${t.country}`:"Multiple Locations"},w=a=>Array.from({length:5},(t,i)=>e.jsx(Y,{className:`h-4 w-4 ${i<Math.floor(a)?"text-yellow-400 fill-current":"text-gray-300"}`},i));return F?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsx("div",{className:"container mx-auto px-4 max-w-7xl",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded w-32 mb-6"}),e.jsx("div",{className:"bg-white rounded-lg shadow-sm border p-8 mb-6",children:e.jsx("div",{className:"flex justify-between items-start mb-6",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gray-200 rounded-xl"}),e.jsxs("div",{children:[e.jsx("div",{className:"h-8 bg-gray-200 rounded w-64 mb-2"}),e.jsx("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2"}),e.jsx("div",{className:"h-4 bg-gray-200 rounded w-40"})]})]})})})]})})}):s?e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsxs("div",{className:"container mx-auto px-4 max-w-7xl",children:[e.jsxs(r,{variant:"ghost",onClick:()=>u(-1),className:"mb-6 p-0 h-auto",children:[e.jsx(G,{className:"h-4 w-4 mr-2"}),"Back to Companies"]}),e.jsxs(l,{className:"shadow-sm border-0 mb-8",children:[s.coverImage&&e.jsxs("div",{className:"h-48 bg-gradient-to-r from-blue-500 to-indigo-600 relative overflow-hidden rounded-t-lg",children:[e.jsx("img",{src:s.coverImage,alt:s.name,className:"w-full h-full object-cover opacity-80"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),e.jsxs(n,{className:"p-8 -mt-12 relative z-10",children:[e.jsxs("div",{className:"flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6",children:[e.jsxs("div",{className:"flex items-start gap-6",children:[s.logo&&e.jsxs("div",{className:"relative flex-shrink-0",children:[e.jsx("img",{src:s.logo,alt:s.name,className:"w-24 h-24 rounded-xl object-cover border-4 border-white shadow-lg bg-white"}),s.isVerified&&e.jsx("div",{className:"absolute -top-2 -right-2 bg-blue-600 text-white rounded-full p-1",children:e.jsx(S,{className:"h-4 w-4"})})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),s.isSponsored&&e.jsx(x,{className:"bg-purple-100 text-purple-800 border-purple-200",children:"Sponsored"})]}),e.jsx("p",{className:"text-xl text-gray-600 mb-4",children:s.industry}),e.jsxs("div",{className:"flex flex-wrap items-center gap-6 text-gray-600",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),e.jsx("span",{children:y(s)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(O,{className:"h-4 w-4 mr-2"}),e.jsxs("span",{children:[B(s.size,s.stats.totalEmployees)," ","employees"]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),e.jsxs("span",{children:["Founded ",s.foundedYear]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),e.jsxs("span",{children:[s.stats.totalJobs," open positions"]})]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-3 lg:flex-shrink-0",children:[e.jsx(r,{variant:"outline",onClick:_,className:N?"text-blue-600 border-blue-600":"",children:N?"Following":"Follow Company"}),s.socialLinks.website&&e.jsxs(r,{variant:"outline",onClick:()=>window.open(s.socialLinks.website,"_blank"),children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Website",e.jsx(j,{className:"h-4 w-4 ml-2"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-6 mt-8 pt-6 border-t",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"flex items-center justify-center mb-2",children:e.jsx("div",{className:"flex items-center",children:w(s.stats.averageRating)})}),e.jsx("div",{className:"text-2xl font-bold text-gray-900",children:s.stats.averageRating}),e.jsxs("div",{className:"text-sm text-gray-600",children:[s.stats.totalReviews," reviews"]})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-600 mb-1",children:[s.stats.responseRate,"%"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Response Rate"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-600 mb-1",children:[s.stats.avgResponseTime,"d"]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Avg Response Time"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(Q,{className:"h-5 w-5 text-green-500 mr-1"}),e.jsxs("span",{className:"text-2xl font-bold text-green-600",children:[s.stats.hiringGrowth,"%"]})]}),e.jsx("div",{className:"text-sm text-gray-600",children:"Hiring Growth"})]})]})]})]}),e.jsxs(E,{defaultValue:"overview",className:"space-y-6",children:[e.jsxs(P,{className:"grid w-full grid-cols-4",children:[e.jsx(h,{value:"overview",children:"Overview"}),e.jsxs(h,{value:"jobs",children:["Jobs (",c.length,")"]}),e.jsxs(h,{value:"reviews",children:["Reviews (",b.length,")"]}),e.jsx(h,{value:"about",children:"About"})]}),e.jsx(p,{value:"overview",className:"space-y-6",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsxs(o,{children:["About ",s.name]})}),e.jsxs(n,{children:[e.jsx("p",{className:"text-gray-700 leading-relaxed mb-6",children:s.description}),s.mission&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Mission"}),e.jsx("p",{className:"text-gray-700",children:s.mission})]}),s.vision&&e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:"Vision"}),e.jsx("p",{className:"text-gray-700",children:s.vision})]}),s.values&&s.values.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-3",children:"Values"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.values.map((a,t)=>e.jsx(x,{variant:"outline",children:a},t))})]})]})]}),c.length>0&&e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Open Positions"})}),e.jsxs(n,{children:[e.jsx(C,{jobs:c.slice(0,3),onSaveJob:v,isLoading:!1}),c.length>3&&e.jsx("div",{className:"text-center mt-6",children:e.jsxs(r,{onClick:()=>u(`/jobs?company=${s.id}`),children:["View All ",c.length," Jobs"]})})]})]})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Company Details"})}),e.jsxs(n,{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Industry"}),e.jsx("span",{className:"font-medium",children:s.industry})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Company Size"}),e.jsx("span",{className:"font-medium capitalize",children:s.size})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Company Type"}),e.jsx("span",{className:"font-medium capitalize",children:s.type})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Founded"}),e.jsx("span",{className:"font-medium",children:s.foundedYear})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-600",children:"Headquarters"}),e.jsx("span",{className:"font-medium",children:y(s)})]})]})]}),s.specialties&&s.specialties.length>0&&e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Specialties"})}),e.jsx(n,{children:e.jsx("div",{className:"flex flex-wrap gap-2",children:s.specialties.map((a,t)=>e.jsx(x,{variant:"outline",children:a},t))})})]}),e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Connect"})}),e.jsxs(n,{className:"space-y-3",children:[s.socialLinks.website&&e.jsxs(r,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open(s.socialLinks.website,"_blank"),children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Visit Website",e.jsx(j,{className:"h-4 w-4 ml-auto"})]}),s.socialLinks.linkedin&&e.jsxs(r,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open(s.socialLinks.linkedin,"_blank"),children:[e.jsx(ee,{className:"h-4 w-4 mr-2"}),"LinkedIn",e.jsx(j,{className:"h-4 w-4 ml-auto"})]}),s.socialLinks.twitter&&e.jsxs(r,{variant:"outline",className:"w-full justify-start",onClick:()=>window.open(s.socialLinks.twitter,"_blank"),children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Twitter",e.jsx(j,{className:"h-4 w-4 ml-auto"})]})]})]})]})]})}),e.jsx(p,{value:"jobs",children:c.length>0?e.jsx(C,{jobs:c,onSaveJob:v,isLoading:!1}):e.jsx(l,{className:"shadow-sm border-0",children:e.jsxs(n,{className:"text-center py-12",children:[e.jsx(k,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"No Open Positions"}),e.jsxs("p",{className:"text-gray-600",children:[s.name," doesn't have any open positions at the moment. Follow them to get notified when new jobs are posted."]})]})})}),e.jsx(p,{value:"reviews",children:e.jsx("div",{className:"space-y-6",children:b.map(a=>e.jsx(l,{className:"shadow-sm border-0",children:e.jsx(n,{className:"p-6",children:e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsxs(W,{children:[e.jsx(D,{src:a.userAvatar}),e.jsx(q,{children:a.userName.split(" ").map(t=>t[0]).join("")})]}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-semibold text-gray-900",children:a.userName}),a.isVerified&&e.jsx(x,{variant:"outline",className:"text-xs",children:"Verified"})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[e.jsx("span",{children:a.position}),e.jsx("span",{children:"•"}),e.jsxs("span",{className:"capitalize",children:[a.employmentType," employee"]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:[a.yearsWorked," year",a.yearsWorked!==1?"s":""]})]})]}),e.jsx("div",{className:"flex items-center",children:w(a.rating)})]}),e.jsx("h4",{className:"font-semibold text-gray-900 mb-2",children:a.title}),e.jsx("p",{className:"text-gray-700 mb-4",children:a.content}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-green-700 mb-2",children:"Pros"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:a.pros.map((t,i)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-green-500 mr-2",children:"+"}),t]},i))})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-700 mb-2",children:"Cons"}),e.jsx("ul",{className:"text-sm text-gray-600 space-y-1",children:a.cons.map((t,i)=>e.jsxs("li",{className:"flex items-start",children:[e.jsx("span",{className:"text-red-500 mr-2",children:"-"}),t]},i))})]})]}),a.advice&&e.jsxs("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[e.jsx("h5",{className:"font-medium text-blue-900 mb-2",children:"Advice to Management"}),e.jsx("p",{className:"text-sm text-blue-800",children:a.advice})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500",children:[e.jsx("span",{children:new Date(a.createdAt).toLocaleDateString()}),e.jsx("div",{className:"flex items-center gap-4",children:e.jsxs("span",{children:[a.helpful," found this helpful"]})})]})]})]})})},a.id))})}),e.jsx(p,{value:"about",children:e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.benefits&&s.benefits.length>0&&e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Benefits & Perks"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:s.benefits.map(a=>e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(S,{className:"h-5 w-5 mt-0.5 text-blue-500 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-gray-900",children:a.name}),e.jsx("div",{className:"text-sm text-gray-600",children:a.description})]})]},a.id))})})]}),e.jsxs(l,{className:"shadow-sm border-0",children:[e.jsx(d,{children:e.jsx(o,{children:"Locations"})}),e.jsx(n,{children:e.jsx("div",{className:"space-y-4",children:s.locations.map(a=>e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(L,{className:"h-5 w-5 mt-0.5 text-gray-500 flex-shrink-0"}),e.jsxs("div",{children:[e.jsxs("div",{className:"font-medium text-gray-900 flex items-center gap-2",children:[a.name,a.isHeadquarters&&e.jsx(x,{variant:"outline",className:"text-xs",children:"HQ"})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[a.address,", ",a.city,","," ",a.state," ",a.postalCode]})]})]},a.id))})})]})]})})]})]})}):e.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:e.jsx("div",{className:"container mx-auto px-4 max-w-7xl",children:e.jsxs("div",{className:"text-center py-16",children:[e.jsx(V,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Company Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"The company you're looking for doesn't exist or has been removed."}),e.jsx(r,{onClick:()=>u("/companies"),children:"Browse All Companies"})]})})})}const ue=()=>e.jsx(te,{});export{ue as default};
