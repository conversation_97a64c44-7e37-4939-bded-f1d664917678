const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/register-CA28aslY.js","assets/schemas-B68yf-xN.js","assets/login-BHX2pz7i.js","assets/useTranslation-DLY10C-4.js","assets/app-layout-CXDDf663.js","assets/chevron-down-Bu8lQHGF.js","assets/x-Ci9JSY07.js","assets/landing-BKHutIY8.js","assets/select-454QdpBE.js","assets/map-pin-CKdZFLuR.js","assets/FeaturedJobs-Dq9tYGgt.js","assets/mock-data-BqVSz9O0.js","assets/badge-L1wjnFol.js","assets/trending-up-CiYyj7aR.js","assets/bookmark-CUB1O_PA.js","assets/dollar-sign-12fYRf7A.js","assets/formatDistanceToNow-DcQq3y4b.js","assets/users-CLiaPoiA.js","assets/FeaturedCompanies-CEj0OwDU.js","assets/jobs-DUschxiO.js","assets/separator-CR9qNBde.js","assets/list-DXfJh1Mw.js","assets/job-detail-Dwtoevrd.js","assets/dialog-DsGkwAMW.js","assets/arrow-left--yQ9esMO.js","assets/share-2-DKwncnMM.js","assets/calendar-BnlTM3Qh.js","assets/circle-check-big-BkizUoxm.js","assets/external-link-5JJn2v5u.js","assets/companies-DisZcvyu.js","assets/company-detail-CNMwiuYK.js","assets/tabs-BgZ9m68D.js","assets/about-D4UVXCQu.js","assets/placeholder-page-CFpcv2Im.js","assets/contact-1_D-2N_s.js","assets/privacy-my1FivdW.js","assets/terms-bmwGTExJ.js","assets/applications-BjDiF06n.js","assets/trash-2-B33stZZy.js","assets/saved-jobs-k6vquDU0.js"])))=>i.map(i=>d[i]);
function Xw(e,r){for(var o=0;o<r.length;o++){const i=r[o];if(typeof i!="string"&&!Array.isArray(i)){for(const a in i)if(a!=="default"&&!(a in e)){const l=Object.getOwnPropertyDescriptor(i,a);l&&Object.defineProperty(e,a,l.get?l:{enumerable:!0,get:()=>i[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))i(a);new MutationObserver(a=>{for(const l of a)if(l.type==="childList")for(const c of l.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&i(c)}).observe(document,{childList:!0,subtree:!0});function o(a){const l={};return a.integrity&&(l.integrity=a.integrity),a.referrerPolicy&&(l.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?l.credentials="include":a.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function i(a){if(a.ep)return;a.ep=!0;const l=o(a);fetch(a.href,l)}})();function Im(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var rc={exports:{}},zi={},oc={exports:{}},De={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ep;function qw(){if(ep)return De;ep=1;var e=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function x(O){return O===null||typeof O!="object"?null:(O=v&&O[v]||O["@@iterator"],typeof O=="function"?O:null)}var S={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},C=Object.assign,E={};function b(O,B,oe){this.props=O,this.context=B,this.refs=E,this.updater=oe||S}b.prototype.isReactComponent={},b.prototype.setState=function(O,B){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,B,"setState")},b.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function P(){}P.prototype=b.prototype;function D(O,B,oe){this.props=O,this.context=B,this.refs=E,this.updater=oe||S}var N=D.prototype=new P;N.constructor=D,C(N,b.prototype),N.isPureReactComponent=!0;var j=Array.isArray,H=Object.prototype.hasOwnProperty,M={current:null},K={key:!0,ref:!0,__self:!0,__source:!0};function W(O,B,oe){var le,Oe={},Pe=null,ue=null;if(B!=null)for(le in B.ref!==void 0&&(ue=B.ref),B.key!==void 0&&(Pe=""+B.key),B)H.call(B,le)&&!K.hasOwnProperty(le)&&(Oe[le]=B[le]);var _e=arguments.length-2;if(_e===1)Oe.children=oe;else if(1<_e){for(var ze=Array(_e),Ie=0;Ie<_e;Ie++)ze[Ie]=arguments[Ie+2];Oe.children=ze}if(O&&O.defaultProps)for(le in _e=O.defaultProps,_e)Oe[le]===void 0&&(Oe[le]=_e[le]);return{$$typeof:e,type:O,key:Pe,ref:ue,props:Oe,_owner:M.current}}function Z(O,B){return{$$typeof:e,type:O.type,key:B,ref:O.ref,props:O.props,_owner:O._owner}}function se(O){return typeof O=="object"&&O!==null&&O.$$typeof===e}function Me(O){var B={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(oe){return B[oe]})}var Re=/\/+/g;function ne(O,B){return typeof O=="object"&&O!==null&&O.key!=null?Me(""+O.key):B.toString(36)}function re(O,B,oe,le,Oe){var Pe=typeof O;(Pe==="undefined"||Pe==="boolean")&&(O=null);var ue=!1;if(O===null)ue=!0;else switch(Pe){case"string":case"number":ue=!0;break;case"object":switch(O.$$typeof){case e:case r:ue=!0}}if(ue)return ue=O,Oe=Oe(ue),O=le===""?"."+ne(ue,0):le,j(Oe)?(oe="",O!=null&&(oe=O.replace(Re,"$&/")+"/"),re(Oe,B,oe,"",function(Ie){return Ie})):Oe!=null&&(se(Oe)&&(Oe=Z(Oe,oe+(!Oe.key||ue&&ue.key===Oe.key?"":(""+Oe.key).replace(Re,"$&/")+"/")+O)),B.push(Oe)),1;if(ue=0,le=le===""?".":le+":",j(O))for(var _e=0;_e<O.length;_e++){Pe=O[_e];var ze=le+ne(Pe,_e);ue+=re(Pe,B,oe,ze,Oe)}else if(ze=x(O),typeof ze=="function")for(O=ze.call(O),_e=0;!(Pe=O.next()).done;)Pe=Pe.value,ze=le+ne(Pe,_e++),ue+=re(Pe,B,oe,ze,Oe);else if(Pe==="object")throw B=String(O),Error("Objects are not valid as a React child (found: "+(B==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":B)+"). If you meant to render a collection of children, use an array instead.");return ue}function be(O,B,oe){if(O==null)return O;var le=[],Oe=0;return re(O,le,"","",function(Pe){return B.call(oe,Pe,Oe++)}),le}function X(O){if(O._status===-1){var B=O._result;B=B(),B.then(function(oe){(O._status===0||O._status===-1)&&(O._status=1,O._result=oe)},function(oe){(O._status===0||O._status===-1)&&(O._status=2,O._result=oe)}),O._status===-1&&(O._status=0,O._result=B)}if(O._status===1)return O._result.default;throw O._result}var ie={current:null},_={transition:null},V={ReactCurrentDispatcher:ie,ReactCurrentBatchConfig:_,ReactCurrentOwner:M};function Q(){throw Error("act(...) is not supported in production builds of React.")}return De.Children={map:be,forEach:function(O,B,oe){be(O,function(){B.apply(this,arguments)},oe)},count:function(O){var B=0;return be(O,function(){B++}),B},toArray:function(O){return be(O,function(B){return B})||[]},only:function(O){if(!se(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},De.Component=b,De.Fragment=o,De.Profiler=a,De.PureComponent=D,De.StrictMode=i,De.Suspense=h,De.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,De.act=Q,De.cloneElement=function(O,B,oe){if(O==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+O+".");var le=C({},O.props),Oe=O.key,Pe=O.ref,ue=O._owner;if(B!=null){if(B.ref!==void 0&&(Pe=B.ref,ue=M.current),B.key!==void 0&&(Oe=""+B.key),O.type&&O.type.defaultProps)var _e=O.type.defaultProps;for(ze in B)H.call(B,ze)&&!K.hasOwnProperty(ze)&&(le[ze]=B[ze]===void 0&&_e!==void 0?_e[ze]:B[ze])}var ze=arguments.length-2;if(ze===1)le.children=oe;else if(1<ze){_e=Array(ze);for(var Ie=0;Ie<ze;Ie++)_e[Ie]=arguments[Ie+2];le.children=_e}return{$$typeof:e,type:O.type,key:Oe,ref:Pe,props:le,_owner:ue}},De.createContext=function(O){return O={$$typeof:c,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},O.Provider={$$typeof:l,_context:O},O.Consumer=O},De.createElement=W,De.createFactory=function(O){var B=W.bind(null,O);return B.type=O,B},De.createRef=function(){return{current:null}},De.forwardRef=function(O){return{$$typeof:d,render:O}},De.isValidElement=se,De.lazy=function(O){return{$$typeof:g,_payload:{_status:-1,_result:O},_init:X}},De.memo=function(O,B){return{$$typeof:p,type:O,compare:B===void 0?null:B}},De.startTransition=function(O){var B=_.transition;_.transition={};try{O()}finally{_.transition=B}},De.unstable_act=Q,De.useCallback=function(O,B){return ie.current.useCallback(O,B)},De.useContext=function(O){return ie.current.useContext(O)},De.useDebugValue=function(){},De.useDeferredValue=function(O){return ie.current.useDeferredValue(O)},De.useEffect=function(O,B){return ie.current.useEffect(O,B)},De.useId=function(){return ie.current.useId()},De.useImperativeHandle=function(O,B,oe){return ie.current.useImperativeHandle(O,B,oe)},De.useInsertionEffect=function(O,B){return ie.current.useInsertionEffect(O,B)},De.useLayoutEffect=function(O,B){return ie.current.useLayoutEffect(O,B)},De.useMemo=function(O,B){return ie.current.useMemo(O,B)},De.useReducer=function(O,B,oe){return ie.current.useReducer(O,B,oe)},De.useRef=function(O){return ie.current.useRef(O)},De.useState=function(O){return ie.current.useState(O)},De.useSyncExternalStore=function(O,B,oe){return ie.current.useSyncExternalStore(O,B,oe)},De.useTransition=function(){return ie.current.useTransition()},De.version="18.3.1",De}var tp;function Zc(){return tp||(tp=1,oc.exports=qw()),oc.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var np;function Zw(){if(np)return zi;np=1;var e=Zc(),r=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,a=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(d,h,p){var g,v={},x=null,S=null;p!==void 0&&(x=""+p),h.key!==void 0&&(x=""+h.key),h.ref!==void 0&&(S=h.ref);for(g in h)i.call(h,g)&&!l.hasOwnProperty(g)&&(v[g]=h[g]);if(d&&d.defaultProps)for(g in h=d.defaultProps,h)v[g]===void 0&&(v[g]=h[g]);return{$$typeof:r,type:d,key:x,ref:S,props:v,_owner:a.current}}return zi.Fragment=o,zi.jsx=c,zi.jsxs=c,zi}var rp;function e0(){return rp||(rp=1,rc.exports=Zw()),rc.exports}var F=e0(),w=Zc();const jt=Im(w),Fm=Xw({__proto__:null,default:jt},[w]);var ka={},ic={exports:{}},Ft={},sc={exports:{}},ac={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var op;function t0(){return op||(op=1,function(e){function r(_,V){var Q=_.length;_.push(V);e:for(;0<Q;){var O=Q-1>>>1,B=_[O];if(0<a(B,V))_[O]=V,_[Q]=B,Q=O;else break e}}function o(_){return _.length===0?null:_[0]}function i(_){if(_.length===0)return null;var V=_[0],Q=_.pop();if(Q!==V){_[0]=Q;e:for(var O=0,B=_.length,oe=B>>>1;O<oe;){var le=2*(O+1)-1,Oe=_[le],Pe=le+1,ue=_[Pe];if(0>a(Oe,Q))Pe<B&&0>a(ue,Oe)?(_[O]=ue,_[Pe]=Q,O=Pe):(_[O]=Oe,_[le]=Q,O=le);else if(Pe<B&&0>a(ue,Q))_[O]=ue,_[Pe]=Q,O=Pe;else break e}}return V}function a(_,V){var Q=_.sortIndex-V.sortIndex;return Q!==0?Q:_.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var c=Date,d=c.now();e.unstable_now=function(){return c.now()-d}}var h=[],p=[],g=1,v=null,x=3,S=!1,C=!1,E=!1,b=typeof setTimeout=="function"?setTimeout:null,P=typeof clearTimeout=="function"?clearTimeout:null,D=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function N(_){for(var V=o(p);V!==null;){if(V.callback===null)i(p);else if(V.startTime<=_)i(p),V.sortIndex=V.expirationTime,r(h,V);else break;V=o(p)}}function j(_){if(E=!1,N(_),!C)if(o(h)!==null)C=!0,X(H);else{var V=o(p);V!==null&&ie(j,V.startTime-_)}}function H(_,V){C=!1,E&&(E=!1,P(W),W=-1),S=!0;var Q=x;try{for(N(V),v=o(h);v!==null&&(!(v.expirationTime>V)||_&&!Me());){var O=v.callback;if(typeof O=="function"){v.callback=null,x=v.priorityLevel;var B=O(v.expirationTime<=V);V=e.unstable_now(),typeof B=="function"?v.callback=B:v===o(h)&&i(h),N(V)}else i(h);v=o(h)}if(v!==null)var oe=!0;else{var le=o(p);le!==null&&ie(j,le.startTime-V),oe=!1}return oe}finally{v=null,x=Q,S=!1}}var M=!1,K=null,W=-1,Z=5,se=-1;function Me(){return!(e.unstable_now()-se<Z)}function Re(){if(K!==null){var _=e.unstable_now();se=_;var V=!0;try{V=K(!0,_)}finally{V?ne():(M=!1,K=null)}}else M=!1}var ne;if(typeof D=="function")ne=function(){D(Re)};else if(typeof MessageChannel<"u"){var re=new MessageChannel,be=re.port2;re.port1.onmessage=Re,ne=function(){be.postMessage(null)}}else ne=function(){b(Re,0)};function X(_){K=_,M||(M=!0,ne())}function ie(_,V){W=b(function(){_(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){C||S||(C=!0,X(H))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Z=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return x},e.unstable_getFirstCallbackNode=function(){return o(h)},e.unstable_next=function(_){switch(x){case 1:case 2:case 3:var V=3;break;default:V=x}var Q=x;x=V;try{return _()}finally{x=Q}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,V){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var Q=x;x=_;try{return V()}finally{x=Q}},e.unstable_scheduleCallback=function(_,V,Q){var O=e.unstable_now();switch(typeof Q=="object"&&Q!==null?(Q=Q.delay,Q=typeof Q=="number"&&0<Q?O+Q:O):Q=O,_){case 1:var B=-1;break;case 2:B=250;break;case 5:B=**********;break;case 4:B=1e4;break;default:B=5e3}return B=Q+B,_={id:g++,callback:V,priorityLevel:_,startTime:Q,expirationTime:B,sortIndex:-1},Q>O?(_.sortIndex=Q,r(p,_),o(h)===null&&_===o(p)&&(E?(P(W),W=-1):E=!0,ie(j,Q-O))):(_.sortIndex=B,r(h,_),C||S||(C=!0,X(H))),_},e.unstable_shouldYield=Me,e.unstable_wrapCallback=function(_){var V=x;return function(){var Q=x;x=V;try{return _.apply(this,arguments)}finally{x=Q}}}}(ac)),ac}var ip;function n0(){return ip||(ip=1,sc.exports=t0()),sc.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var sp;function r0(){if(sp)return Ft;sp=1;var e=Zc(),r=n0();function o(t){for(var n="https://reactjs.org/docs/error-decoder.html?invariant="+t,s=1;s<arguments.length;s++)n+="&args[]="+encodeURIComponent(arguments[s]);return"Minified React error #"+t+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,a={};function l(t,n){c(t,n),c(t+"Capture",n)}function c(t,n){for(a[t]=n,t=0;t<n.length;t++)i.add(n[t])}var d=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),h=Object.prototype.hasOwnProperty,p=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,g={},v={};function x(t){return h.call(v,t)?!0:h.call(g,t)?!1:p.test(t)?v[t]=!0:(g[t]=!0,!1)}function S(t,n,s,u){if(s!==null&&s.type===0)return!1;switch(typeof n){case"function":case"symbol":return!0;case"boolean":return u?!1:s!==null?!s.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function C(t,n,s,u){if(n===null||typeof n>"u"||S(t,n,s,u))return!0;if(u)return!1;if(s!==null)switch(s.type){case 3:return!n;case 4:return n===!1;case 5:return isNaN(n);case 6:return isNaN(n)||1>n}return!1}function E(t,n,s,u,f,m,y){this.acceptsBooleans=n===2||n===3||n===4,this.attributeName=u,this.attributeNamespace=f,this.mustUseProperty=s,this.propertyName=t,this.type=n,this.sanitizeURL=m,this.removeEmptyString=y}var b={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){b[t]=new E(t,0,!1,t,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var n=t[0];b[n]=new E(n,1,!1,t[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(t){b[t]=new E(t,2,!1,t.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){b[t]=new E(t,2,!1,t,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){b[t]=new E(t,3,!1,t.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(t){b[t]=new E(t,3,!0,t,null,!1,!1)}),["capture","download"].forEach(function(t){b[t]=new E(t,4,!1,t,null,!1,!1)}),["cols","rows","size","span"].forEach(function(t){b[t]=new E(t,6,!1,t,null,!1,!1)}),["rowSpan","start"].forEach(function(t){b[t]=new E(t,5,!1,t.toLowerCase(),null,!1,!1)});var P=/[\-:]([a-z])/g;function D(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var n=t.replace(P,D);b[n]=new E(n,1,!1,t,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var n=t.replace(P,D);b[n]=new E(n,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(t){var n=t.replace(P,D);b[n]=new E(n,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(t){b[t]=new E(t,1,!1,t.toLowerCase(),null,!1,!1)}),b.xlinkHref=new E("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(t){b[t]=new E(t,1,!1,t.toLowerCase(),null,!0,!0)});function N(t,n,s,u){var f=b.hasOwnProperty(n)?b[n]:null;(f!==null?f.type!==0:u||!(2<n.length)||n[0]!=="o"&&n[0]!=="O"||n[1]!=="n"&&n[1]!=="N")&&(C(n,s,f,u)&&(s=null),u||f===null?x(n)&&(s===null?t.removeAttribute(n):t.setAttribute(n,""+s)):f.mustUseProperty?t[f.propertyName]=s===null?f.type===3?!1:"":s:(n=f.attributeName,u=f.attributeNamespace,s===null?t.removeAttribute(n):(f=f.type,s=f===3||f===4&&s===!0?"":""+s,u?t.setAttributeNS(u,n,s):t.setAttribute(n,s))))}var j=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,H=Symbol.for("react.element"),M=Symbol.for("react.portal"),K=Symbol.for("react.fragment"),W=Symbol.for("react.strict_mode"),Z=Symbol.for("react.profiler"),se=Symbol.for("react.provider"),Me=Symbol.for("react.context"),Re=Symbol.for("react.forward_ref"),ne=Symbol.for("react.suspense"),re=Symbol.for("react.suspense_list"),be=Symbol.for("react.memo"),X=Symbol.for("react.lazy"),ie=Symbol.for("react.offscreen"),_=Symbol.iterator;function V(t){return t===null||typeof t!="object"?null:(t=_&&t[_]||t["@@iterator"],typeof t=="function"?t:null)}var Q=Object.assign,O;function B(t){if(O===void 0)try{throw Error()}catch(s){var n=s.stack.trim().match(/\n( *(at )?)/);O=n&&n[1]||""}return`
`+O+t}var oe=!1;function le(t,n){if(!t||oe)return"";oe=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(n)if(n=function(){throw Error()},Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(n,[])}catch($){var u=$}Reflect.construct(t,[],n)}else{try{n.call()}catch($){u=$}t.call(n.prototype)}else{try{throw Error()}catch($){u=$}t()}}catch($){if($&&u&&typeof $.stack=="string"){for(var f=$.stack.split(`
`),m=u.stack.split(`
`),y=f.length-1,k=m.length-1;1<=y&&0<=k&&f[y]!==m[k];)k--;for(;1<=y&&0<=k;y--,k--)if(f[y]!==m[k]){if(y!==1||k!==1)do if(y--,k--,0>k||f[y]!==m[k]){var L=`
`+f[y].replace(" at new "," at ");return t.displayName&&L.includes("<anonymous>")&&(L=L.replace("<anonymous>",t.displayName)),L}while(1<=y&&0<=k);break}}}finally{oe=!1,Error.prepareStackTrace=s}return(t=t?t.displayName||t.name:"")?B(t):""}function Oe(t){switch(t.tag){case 5:return B(t.type);case 16:return B("Lazy");case 13:return B("Suspense");case 19:return B("SuspenseList");case 0:case 2:case 15:return t=le(t.type,!1),t;case 11:return t=le(t.type.render,!1),t;case 1:return t=le(t.type,!0),t;default:return""}}function Pe(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case K:return"Fragment";case M:return"Portal";case Z:return"Profiler";case W:return"StrictMode";case ne:return"Suspense";case re:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Me:return(t.displayName||"Context")+".Consumer";case se:return(t._context.displayName||"Context")+".Provider";case Re:var n=t.render;return t=t.displayName,t||(t=n.displayName||n.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case be:return n=t.displayName||null,n!==null?n:Pe(t.type)||"Memo";case X:n=t._payload,t=t._init;try{return Pe(t(n))}catch{}}return null}function ue(t){var n=t.type;switch(t.tag){case 24:return"Cache";case 9:return(n.displayName||"Context")+".Consumer";case 10:return(n._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=n.render,t=t.displayName||t.name||"",n.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return n;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Pe(n);case 8:return n===W?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof n=="function")return n.displayName||n.name||null;if(typeof n=="string")return n}return null}function _e(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function ze(t){var n=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function Ie(t){var n=ze(t)?"checked":"value",s=Object.getOwnPropertyDescriptor(t.constructor.prototype,n),u=""+t[n];if(!t.hasOwnProperty(n)&&typeof s<"u"&&typeof s.get=="function"&&typeof s.set=="function"){var f=s.get,m=s.set;return Object.defineProperty(t,n,{configurable:!0,get:function(){return f.call(this)},set:function(y){u=""+y,m.call(this,y)}}),Object.defineProperty(t,n,{enumerable:s.enumerable}),{getValue:function(){return u},setValue:function(y){u=""+y},stopTracking:function(){t._valueTracker=null,delete t[n]}}}}function mt(t){t._valueTracker||(t._valueTracker=Ie(t))}function Yt(t){if(!t)return!1;var n=t._valueTracker;if(!n)return!0;var s=n.getValue(),u="";return t&&(u=ze(t)?t.checked?"true":"false":t.value),t=u,t!==s?(n.setValue(t),!0):!1}function un(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function cn(t,n){var s=n.checked;return Q({},n,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:s??t._wrapperState.initialChecked})}function ms(t,n){var s=n.defaultValue==null?"":n.defaultValue,u=n.checked!=null?n.checked:n.defaultChecked;s=_e(n.value!=null?n.value:s),t._wrapperState={initialChecked:u,initialValue:s,controlled:n.type==="checkbox"||n.type==="radio"?n.checked!=null:n.value!=null}}function gs(t,n){n=n.checked,n!=null&&N(t,"checked",n,!1)}function so(t,n){gs(t,n);var s=_e(n.value),u=n.type;if(s!=null)u==="number"?(s===0&&t.value===""||t.value!=s)&&(t.value=""+s):t.value!==""+s&&(t.value=""+s);else if(u==="submit"||u==="reset"){t.removeAttribute("value");return}n.hasOwnProperty("value")?Zo(t,n.type,s):n.hasOwnProperty("defaultValue")&&Zo(t,n.type,_e(n.defaultValue)),n.checked==null&&n.defaultChecked!=null&&(t.defaultChecked=!!n.defaultChecked)}function vs(t,n,s){if(n.hasOwnProperty("value")||n.hasOwnProperty("defaultValue")){var u=n.type;if(!(u!=="submit"&&u!=="reset"||n.value!==void 0&&n.value!==null))return;n=""+t._wrapperState.initialValue,s||n===t.value||(t.value=n),t.defaultValue=n}s=t.name,s!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,s!==""&&(t.name=s)}function Zo(t,n,s){(n!=="number"||un(t.ownerDocument)!==t)&&(s==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+s&&(t.defaultValue=""+s))}var Nr=Array.isArray;function qn(t,n,s,u){if(t=t.options,n){n={};for(var f=0;f<s.length;f++)n["$"+s[f]]=!0;for(s=0;s<t.length;s++)f=n.hasOwnProperty("$"+t[s].value),t[s].selected!==f&&(t[s].selected=f),f&&u&&(t[s].defaultSelected=!0)}else{for(s=""+_e(s),n=null,f=0;f<t.length;f++){if(t[f].value===s){t[f].selected=!0,u&&(t[f].defaultSelected=!0);return}n!==null||t[f].disabled||(n=t[f])}n!==null&&(n.selected=!0)}}function dn(t,n){if(n.dangerouslySetInnerHTML!=null)throw Error(o(91));return Q({},n,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Zn(t,n){var s=n.value;if(s==null){if(s=n.children,n=n.defaultValue,s!=null){if(n!=null)throw Error(o(92));if(Nr(s)){if(1<s.length)throw Error(o(93));s=s[0]}n=s}n==null&&(n=""),s=n}t._wrapperState={initialValue:_e(s)}}function ei(t,n){var s=_e(n.value),u=_e(n.defaultValue);s!=null&&(s=""+s,s!==t.value&&(t.value=s),n.defaultValue==null&&t.defaultValue!==s&&(t.defaultValue=s)),u!=null&&(t.defaultValue=""+u)}function ao(t){var n=t.textContent;n===t._wrapperState.initialValue&&n!==""&&n!==null&&(t.value=n)}function fn(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ut(t,n){return t==null||t==="http://www.w3.org/1999/xhtml"?fn(n):t==="http://www.w3.org/2000/svg"&&n==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Dr,lo=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(n,s,u,f){MSApp.execUnsafeLocalFunction(function(){return t(n,s,u,f)})}:t}(function(t,n){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=n;else{for(Dr=Dr||document.createElement("div"),Dr.innerHTML="<svg>"+n.valueOf().toString()+"</svg>",n=Dr.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;n.firstChild;)t.appendChild(n.firstChild)}});function Ar(t,n){if(n){var s=t.firstChild;if(s&&s===t.lastChild&&s.nodeType===3){s.nodeValue=n;return}}t.textContent=n}var Mt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ys=["Webkit","ms","Moz","O"];Object.keys(Mt).forEach(function(t){ys.forEach(function(n){n=n+t.charAt(0).toUpperCase()+t.substring(1),Mt[n]=Mt[t]})});function ti(t,n,s){return n==null||typeof n=="boolean"||n===""?"":s||typeof n!="number"||n===0||Mt.hasOwnProperty(t)&&Mt[t]?(""+n).trim():n+"px"}function ni(t,n){t=t.style;for(var s in n)if(n.hasOwnProperty(s)){var u=s.indexOf("--")===0,f=ti(s,n[s],u);s==="float"&&(s="cssFloat"),u?t.setProperty(s,f):t[s]=f}}var El=Q({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function uo(t,n){if(n){if(El[t]&&(n.children!=null||n.dangerouslySetInnerHTML!=null))throw Error(o(137,t));if(n.dangerouslySetInnerHTML!=null){if(n.children!=null)throw Error(o(60));if(typeof n.dangerouslySetInnerHTML!="object"||!("__html"in n.dangerouslySetInnerHTML))throw Error(o(61))}if(n.style!=null&&typeof n.style!="object")throw Error(o(62))}}function er(t,n){if(t.indexOf("-")===-1)return typeof n.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var co=null;function Ir(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var ri=null,jn=null,tr=null;function oi(t){if(t=ki(t)){if(typeof ri!="function")throw Error(o(280));var n=t.stateNode;n&&(n=zs(n),ri(t.stateNode,t.type,n))}}function Fr(t){jn?tr?tr.push(t):tr=[t]:jn=t}function jr(){if(jn){var t=jn,n=tr;if(tr=jn=null,oi(t),n)for(t=0;t<n.length;t++)oi(n[t])}}function ws(t,n){return t(n)}function xs(){}var R=!1;function A(t,n,s){if(R)return t(n,s);R=!0;try{return ws(t,n,s)}finally{R=!1,(jn!==null||tr!==null)&&(xs(),jr())}}function U(t,n){var s=t.stateNode;if(s===null)return null;var u=zs(s);if(u===null)return null;s=u[n];e:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(t=t.type,u=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!u;break e;default:t=!1}if(t)return null;if(s&&typeof s!="function")throw Error(o(231,n,typeof s));return s}var q=!1;if(d)try{var te={};Object.defineProperty(te,"passive",{get:function(){q=!0}}),window.addEventListener("test",te,te),window.removeEventListener("test",te,te)}catch{q=!1}function we(t,n,s,u,f,m,y,k,L){var $=Array.prototype.slice.call(arguments,3);try{n.apply(s,$)}catch(Y){this.onError(Y)}}var ge=!1,de=null,ye=!1,Ee=null,xe={onError:function(t){ge=!0,de=t}};function Se(t,n,s,u,f,m,y,k,L){ge=!1,de=null,we.apply(xe,arguments)}function Fe(t,n,s,u,f,m,y,k,L){if(Se.apply(this,arguments),ge){if(ge){var $=de;ge=!1,de=null}else throw Error(o(198));ye||(ye=!0,Ee=$)}}function Be(t){var n=t,s=t;if(t.alternate)for(;n.return;)n=n.return;else{t=n;do n=t,(n.flags&4098)!==0&&(s=n.return),t=n.return;while(t)}return n.tag===3?s:null}function st(t){if(t.tag===13){var n=t.memoizedState;if(n===null&&(t=t.alternate,t!==null&&(n=t.memoizedState)),n!==null)return n.dehydrated}return null}function ct(t){if(Be(t)!==t)throw Error(o(188))}function qe(t){var n=t.alternate;if(!n){if(n=Be(t),n===null)throw Error(o(188));return n!==t?null:t}for(var s=t,u=n;;){var f=s.return;if(f===null)break;var m=f.alternate;if(m===null){if(u=f.return,u!==null){s=u;continue}break}if(f.child===m.child){for(m=f.child;m;){if(m===s)return ct(f),t;if(m===u)return ct(f),n;m=m.sibling}throw Error(o(188))}if(s.return!==u.return)s=f,u=m;else{for(var y=!1,k=f.child;k;){if(k===s){y=!0,s=f,u=m;break}if(k===u){y=!0,u=f,s=m;break}k=k.sibling}if(!y){for(k=m.child;k;){if(k===s){y=!0,s=m,u=f;break}if(k===u){y=!0,u=m,s=f;break}k=k.sibling}if(!y)throw Error(o(189))}}if(s.alternate!==u)throw Error(o(190))}if(s.tag!==3)throw Error(o(188));return s.stateNode.current===s?t:n}function Ke(t){return t=qe(t),t!==null?kn(t):null}function kn(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var n=kn(t);if(n!==null)return n;t=t.sibling}return null}var nr=r.unstable_scheduleCallback,hn=r.unstable_cancelCallback,Jt=r.unstable_shouldYield,ii=r.unstable_requestPaint,Qe=r.unstable_now,zn=r.unstable_getCurrentPriorityLevel,rr=r.unstable_ImmediatePriority,zr=r.unstable_UserBlockingPriority,$e=r.unstable_NormalPriority,dt=r.unstable_LowPriority,$r=r.unstable_IdlePriority,bn=null,Ve=null;function fo(t){if(Ve&&typeof Ve.onCommitFiberRoot=="function")try{Ve.onCommitFiberRoot(bn,t,void 0,(t.current.flags&128)===128)}catch{}}var Tt=Math.clz32?Math.clz32:my,Cl=Math.log,py=Math.LN2;function my(t){return t>>>=0,t===0?32:31-(Cl(t)/py|0)|0}var Ss=64,Es=4194304;function si(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Cs(t,n){var s=t.pendingLanes;if(s===0)return 0;var u=0,f=t.suspendedLanes,m=t.pingedLanes,y=s&268435455;if(y!==0){var k=y&~f;k!==0?u=si(k):(m&=y,m!==0&&(u=si(m)))}else y=s&~f,y!==0?u=si(y):m!==0&&(u=si(m));if(u===0)return 0;if(n!==0&&n!==u&&(n&f)===0&&(f=u&-u,m=n&-n,f>=m||f===16&&(m&4194240)!==0))return n;if((u&4)!==0&&(u|=s&16),n=t.entangledLanes,n!==0)for(t=t.entanglements,n&=u;0<n;)s=31-Tt(n),f=1<<s,u|=t[s],n&=~f;return u}function gy(t,n){switch(t){case 1:case 2:case 4:return n+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function vy(t,n){for(var s=t.suspendedLanes,u=t.pingedLanes,f=t.expirationTimes,m=t.pendingLanes;0<m;){var y=31-Tt(m),k=1<<y,L=f[y];L===-1?((k&s)===0||(k&u)!==0)&&(f[y]=gy(k,n)):L<=n&&(t.expiredLanes|=k),m&=~k}}function kl(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Md(){var t=Ss;return Ss<<=1,(Ss&4194240)===0&&(Ss=64),t}function bl(t){for(var n=[],s=0;31>s;s++)n.push(t);return n}function ai(t,n,s){t.pendingLanes|=n,n!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,n=31-Tt(n),t[n]=s}function yy(t,n){var s=t.pendingLanes&~n;t.pendingLanes=n,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=n,t.mutableReadLanes&=n,t.entangledLanes&=n,n=t.entanglements;var u=t.eventTimes;for(t=t.expirationTimes;0<s;){var f=31-Tt(s),m=1<<f;n[f]=0,u[f]=-1,t[f]=-1,s&=~m}}function Pl(t,n){var s=t.entangledLanes|=n;for(t=t.entanglements;s;){var u=31-Tt(s),f=1<<u;f&n|t[u]&n&&(t[u]|=n),s&=~f}}var We=0;function Td(t){return t&=-t,1<t?4<t?(t&268435455)!==0?16:536870912:4:1}var _d,Rl,Nd,Dd,Ad,Ol=!1,ks=[],or=null,ir=null,sr=null,li=new Map,ui=new Map,ar=[],wy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Id(t,n){switch(t){case"focusin":case"focusout":or=null;break;case"dragenter":case"dragleave":ir=null;break;case"mouseover":case"mouseout":sr=null;break;case"pointerover":case"pointerout":li.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":ui.delete(n.pointerId)}}function ci(t,n,s,u,f,m){return t===null||t.nativeEvent!==m?(t={blockedOn:n,domEventName:s,eventSystemFlags:u,nativeEvent:m,targetContainers:[f]},n!==null&&(n=ki(n),n!==null&&Rl(n)),t):(t.eventSystemFlags|=u,n=t.targetContainers,f!==null&&n.indexOf(f)===-1&&n.push(f),t)}function xy(t,n,s,u,f){switch(n){case"focusin":return or=ci(or,t,n,s,u,f),!0;case"dragenter":return ir=ci(ir,t,n,s,u,f),!0;case"mouseover":return sr=ci(sr,t,n,s,u,f),!0;case"pointerover":var m=f.pointerId;return li.set(m,ci(li.get(m)||null,t,n,s,u,f)),!0;case"gotpointercapture":return m=f.pointerId,ui.set(m,ci(ui.get(m)||null,t,n,s,u,f)),!0}return!1}function Fd(t){var n=Ur(t.target);if(n!==null){var s=Be(n);if(s!==null){if(n=s.tag,n===13){if(n=st(s),n!==null){t.blockedOn=n,Ad(t.priority,function(){Nd(s)});return}}else if(n===3&&s.stateNode.current.memoizedState.isDehydrated){t.blockedOn=s.tag===3?s.stateNode.containerInfo:null;return}}}t.blockedOn=null}function bs(t){if(t.blockedOn!==null)return!1;for(var n=t.targetContainers;0<n.length;){var s=Ml(t.domEventName,t.eventSystemFlags,n[0],t.nativeEvent);if(s===null){s=t.nativeEvent;var u=new s.constructor(s.type,s);co=u,s.target.dispatchEvent(u),co=null}else return n=ki(s),n!==null&&Rl(n),t.blockedOn=s,!1;n.shift()}return!0}function jd(t,n,s){bs(t)&&s.delete(n)}function Sy(){Ol=!1,or!==null&&bs(or)&&(or=null),ir!==null&&bs(ir)&&(ir=null),sr!==null&&bs(sr)&&(sr=null),li.forEach(jd),ui.forEach(jd)}function di(t,n){t.blockedOn===n&&(t.blockedOn=null,Ol||(Ol=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Sy)))}function fi(t){function n(f){return di(f,t)}if(0<ks.length){di(ks[0],t);for(var s=1;s<ks.length;s++){var u=ks[s];u.blockedOn===t&&(u.blockedOn=null)}}for(or!==null&&di(or,t),ir!==null&&di(ir,t),sr!==null&&di(sr,t),li.forEach(n),ui.forEach(n),s=0;s<ar.length;s++)u=ar[s],u.blockedOn===t&&(u.blockedOn=null);for(;0<ar.length&&(s=ar[0],s.blockedOn===null);)Fd(s),s.blockedOn===null&&ar.shift()}var ho=j.ReactCurrentBatchConfig,Ps=!0;function Ey(t,n,s,u){var f=We,m=ho.transition;ho.transition=null;try{We=1,Ll(t,n,s,u)}finally{We=f,ho.transition=m}}function Cy(t,n,s,u){var f=We,m=ho.transition;ho.transition=null;try{We=4,Ll(t,n,s,u)}finally{We=f,ho.transition=m}}function Ll(t,n,s,u){if(Ps){var f=Ml(t,n,s,u);if(f===null)Ql(t,n,u,Rs,s),Id(t,u);else if(xy(f,t,n,s,u))u.stopPropagation();else if(Id(t,u),n&4&&-1<wy.indexOf(t)){for(;f!==null;){var m=ki(f);if(m!==null&&_d(m),m=Ml(t,n,s,u),m===null&&Ql(t,n,u,Rs,s),m===f)break;f=m}f!==null&&u.stopPropagation()}else Ql(t,n,u,null,s)}}var Rs=null;function Ml(t,n,s,u){if(Rs=null,t=Ir(u),t=Ur(t),t!==null)if(n=Be(t),n===null)t=null;else if(s=n.tag,s===13){if(t=st(n),t!==null)return t;t=null}else if(s===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;t=null}else n!==t&&(t=null);return Rs=t,null}function zd(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(zn()){case rr:return 1;case zr:return 4;case $e:case dt:return 16;case $r:return 536870912;default:return 16}default:return 16}}var lr=null,Tl=null,Os=null;function $d(){if(Os)return Os;var t,n=Tl,s=n.length,u,f="value"in lr?lr.value:lr.textContent,m=f.length;for(t=0;t<s&&n[t]===f[t];t++);var y=s-t;for(u=1;u<=y&&n[s-u]===f[m-u];u++);return Os=f.slice(t,1<u?1-u:void 0)}function Ls(t){var n=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&n===13&&(t=13)):t=n,t===10&&(t=13),32<=t||t===13?t:0}function Ms(){return!0}function Ud(){return!1}function Ht(t){function n(s,u,f,m,y){this._reactName=s,this._targetInst=f,this.type=u,this.nativeEvent=m,this.target=y,this.currentTarget=null;for(var k in t)t.hasOwnProperty(k)&&(s=t[k],this[k]=s?s(m):m[k]);return this.isDefaultPrevented=(m.defaultPrevented!=null?m.defaultPrevented:m.returnValue===!1)?Ms:Ud,this.isPropagationStopped=Ud,this}return Q(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var s=this.nativeEvent;s&&(s.preventDefault?s.preventDefault():typeof s.returnValue!="unknown"&&(s.returnValue=!1),this.isDefaultPrevented=Ms)},stopPropagation:function(){var s=this.nativeEvent;s&&(s.stopPropagation?s.stopPropagation():typeof s.cancelBubble!="unknown"&&(s.cancelBubble=!0),this.isPropagationStopped=Ms)},persist:function(){},isPersistent:Ms}),n}var po={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_l=Ht(po),hi=Q({},po,{view:0,detail:0}),ky=Ht(hi),Nl,Dl,pi,Ts=Q({},hi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Il,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==pi&&(pi&&t.type==="mousemove"?(Nl=t.screenX-pi.screenX,Dl=t.screenY-pi.screenY):Dl=Nl=0,pi=t),Nl)},movementY:function(t){return"movementY"in t?t.movementY:Dl}}),Hd=Ht(Ts),by=Q({},Ts,{dataTransfer:0}),Py=Ht(by),Ry=Q({},hi,{relatedTarget:0}),Al=Ht(Ry),Oy=Q({},po,{animationName:0,elapsedTime:0,pseudoElement:0}),Ly=Ht(Oy),My=Q({},po,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Ty=Ht(My),_y=Q({},po,{data:0}),Bd=Ht(_y),Ny={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Dy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ay={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Iy(t){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(t):(t=Ay[t])?!!n[t]:!1}function Il(){return Iy}var Fy=Q({},hi,{key:function(t){if(t.key){var n=Ny[t.key]||t.key;if(n!=="Unidentified")return n}return t.type==="keypress"?(t=Ls(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?Dy[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Il,charCode:function(t){return t.type==="keypress"?Ls(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Ls(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),jy=Ht(Fy),zy=Q({},Ts,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vd=Ht(zy),$y=Q({},hi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Il}),Uy=Ht($y),Hy=Q({},po,{propertyName:0,elapsedTime:0,pseudoElement:0}),By=Ht(Hy),Vy=Q({},Ts,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Wy=Ht(Vy),Ky=[9,13,27,32],Fl=d&&"CompositionEvent"in window,mi=null;d&&"documentMode"in document&&(mi=document.documentMode);var Qy=d&&"TextEvent"in window&&!mi,Wd=d&&(!Fl||mi&&8<mi&&11>=mi),Kd=" ",Qd=!1;function Gd(t,n){switch(t){case"keyup":return Ky.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Yd(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var mo=!1;function Gy(t,n){switch(t){case"compositionend":return Yd(n);case"keypress":return n.which!==32?null:(Qd=!0,Kd);case"textInput":return t=n.data,t===Kd&&Qd?null:t;default:return null}}function Yy(t,n){if(mo)return t==="compositionend"||!Fl&&Gd(t,n)?(t=$d(),Os=Tl=lr=null,mo=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return Wd&&n.locale!=="ko"?null:n.data;default:return null}}var Jy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Jd(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n==="input"?!!Jy[t.type]:n==="textarea"}function Xd(t,n,s,u){Fr(u),n=Is(n,"onChange"),0<n.length&&(s=new _l("onChange","change",null,s,u),t.push({event:s,listeners:n}))}var gi=null,vi=null;function Xy(t){gf(t,0)}function _s(t){var n=xo(t);if(Yt(n))return t}function qy(t,n){if(t==="change")return n}var qd=!1;if(d){var jl;if(d){var zl="oninput"in document;if(!zl){var Zd=document.createElement("div");Zd.setAttribute("oninput","return;"),zl=typeof Zd.oninput=="function"}jl=zl}else jl=!1;qd=jl&&(!document.documentMode||9<document.documentMode)}function ef(){gi&&(gi.detachEvent("onpropertychange",tf),vi=gi=null)}function tf(t){if(t.propertyName==="value"&&_s(vi)){var n=[];Xd(n,vi,t,Ir(t)),A(Xy,n)}}function Zy(t,n,s){t==="focusin"?(ef(),gi=n,vi=s,gi.attachEvent("onpropertychange",tf)):t==="focusout"&&ef()}function ew(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return _s(vi)}function tw(t,n){if(t==="click")return _s(n)}function nw(t,n){if(t==="input"||t==="change")return _s(n)}function rw(t,n){return t===n&&(t!==0||1/t===1/n)||t!==t&&n!==n}var pn=typeof Object.is=="function"?Object.is:rw;function yi(t,n){if(pn(t,n))return!0;if(typeof t!="object"||t===null||typeof n!="object"||n===null)return!1;var s=Object.keys(t),u=Object.keys(n);if(s.length!==u.length)return!1;for(u=0;u<s.length;u++){var f=s[u];if(!h.call(n,f)||!pn(t[f],n[f]))return!1}return!0}function nf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function rf(t,n){var s=nf(t);t=0;for(var u;s;){if(s.nodeType===3){if(u=t+s.textContent.length,t<=n&&u>=n)return{node:s,offset:n-t};t=u}e:{for(;s;){if(s.nextSibling){s=s.nextSibling;break e}s=s.parentNode}s=void 0}s=nf(s)}}function of(t,n){return t&&n?t===n?!0:t&&t.nodeType===3?!1:n&&n.nodeType===3?of(t,n.parentNode):"contains"in t?t.contains(n):t.compareDocumentPosition?!!(t.compareDocumentPosition(n)&16):!1:!1}function sf(){for(var t=window,n=un();n instanceof t.HTMLIFrameElement;){try{var s=typeof n.contentWindow.location.href=="string"}catch{s=!1}if(s)t=n.contentWindow;else break;n=un(t.document)}return n}function $l(t){var n=t&&t.nodeName&&t.nodeName.toLowerCase();return n&&(n==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||n==="textarea"||t.contentEditable==="true")}function ow(t){var n=sf(),s=t.focusedElem,u=t.selectionRange;if(n!==s&&s&&s.ownerDocument&&of(s.ownerDocument.documentElement,s)){if(u!==null&&$l(s)){if(n=u.start,t=u.end,t===void 0&&(t=n),"selectionStart"in s)s.selectionStart=n,s.selectionEnd=Math.min(t,s.value.length);else if(t=(n=s.ownerDocument||document)&&n.defaultView||window,t.getSelection){t=t.getSelection();var f=s.textContent.length,m=Math.min(u.start,f);u=u.end===void 0?m:Math.min(u.end,f),!t.extend&&m>u&&(f=u,u=m,m=f),f=rf(s,m);var y=rf(s,u);f&&y&&(t.rangeCount!==1||t.anchorNode!==f.node||t.anchorOffset!==f.offset||t.focusNode!==y.node||t.focusOffset!==y.offset)&&(n=n.createRange(),n.setStart(f.node,f.offset),t.removeAllRanges(),m>u?(t.addRange(n),t.extend(y.node,y.offset)):(n.setEnd(y.node,y.offset),t.addRange(n)))}}for(n=[],t=s;t=t.parentNode;)t.nodeType===1&&n.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof s.focus=="function"&&s.focus(),s=0;s<n.length;s++)t=n[s],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var iw=d&&"documentMode"in document&&11>=document.documentMode,go=null,Ul=null,wi=null,Hl=!1;function af(t,n,s){var u=s.window===s?s.document:s.nodeType===9?s:s.ownerDocument;Hl||go==null||go!==un(u)||(u=go,"selectionStart"in u&&$l(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),wi&&yi(wi,u)||(wi=u,u=Is(Ul,"onSelect"),0<u.length&&(n=new _l("onSelect","select",null,n,s),t.push({event:n,listeners:u}),n.target=go)))}function Ns(t,n){var s={};return s[t.toLowerCase()]=n.toLowerCase(),s["Webkit"+t]="webkit"+n,s["Moz"+t]="moz"+n,s}var vo={animationend:Ns("Animation","AnimationEnd"),animationiteration:Ns("Animation","AnimationIteration"),animationstart:Ns("Animation","AnimationStart"),transitionend:Ns("Transition","TransitionEnd")},Bl={},lf={};d&&(lf=document.createElement("div").style,"AnimationEvent"in window||(delete vo.animationend.animation,delete vo.animationiteration.animation,delete vo.animationstart.animation),"TransitionEvent"in window||delete vo.transitionend.transition);function Ds(t){if(Bl[t])return Bl[t];if(!vo[t])return t;var n=vo[t],s;for(s in n)if(n.hasOwnProperty(s)&&s in lf)return Bl[t]=n[s];return t}var uf=Ds("animationend"),cf=Ds("animationiteration"),df=Ds("animationstart"),ff=Ds("transitionend"),hf=new Map,pf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function ur(t,n){hf.set(t,n),l(n,[t])}for(var Vl=0;Vl<pf.length;Vl++){var Wl=pf[Vl],sw=Wl.toLowerCase(),aw=Wl[0].toUpperCase()+Wl.slice(1);ur(sw,"on"+aw)}ur(uf,"onAnimationEnd"),ur(cf,"onAnimationIteration"),ur(df,"onAnimationStart"),ur("dblclick","onDoubleClick"),ur("focusin","onFocus"),ur("focusout","onBlur"),ur(ff,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var xi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),lw=new Set("cancel close invalid load scroll toggle".split(" ").concat(xi));function mf(t,n,s){var u=t.type||"unknown-event";t.currentTarget=s,Fe(u,n,void 0,t),t.currentTarget=null}function gf(t,n){n=(n&4)!==0;for(var s=0;s<t.length;s++){var u=t[s],f=u.event;u=u.listeners;e:{var m=void 0;if(n)for(var y=u.length-1;0<=y;y--){var k=u[y],L=k.instance,$=k.currentTarget;if(k=k.listener,L!==m&&f.isPropagationStopped())break e;mf(f,k,$),m=L}else for(y=0;y<u.length;y++){if(k=u[y],L=k.instance,$=k.currentTarget,k=k.listener,L!==m&&f.isPropagationStopped())break e;mf(f,k,$),m=L}}}if(ye)throw t=Ee,ye=!1,Ee=null,t}function Ye(t,n){var s=n[Zl];s===void 0&&(s=n[Zl]=new Set);var u=t+"__bubble";s.has(u)||(vf(n,t,2,!1),s.add(u))}function Kl(t,n,s){var u=0;n&&(u|=4),vf(s,t,u,n)}var As="_reactListening"+Math.random().toString(36).slice(2);function Si(t){if(!t[As]){t[As]=!0,i.forEach(function(s){s!=="selectionchange"&&(lw.has(s)||Kl(s,!1,t),Kl(s,!0,t))});var n=t.nodeType===9?t:t.ownerDocument;n===null||n[As]||(n[As]=!0,Kl("selectionchange",!1,n))}}function vf(t,n,s,u){switch(zd(n)){case 1:var f=Ey;break;case 4:f=Cy;break;default:f=Ll}s=f.bind(null,n,s,t),f=void 0,!q||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(f=!0),u?f!==void 0?t.addEventListener(n,s,{capture:!0,passive:f}):t.addEventListener(n,s,!0):f!==void 0?t.addEventListener(n,s,{passive:f}):t.addEventListener(n,s,!1)}function Ql(t,n,s,u,f){var m=u;if((n&1)===0&&(n&2)===0&&u!==null)e:for(;;){if(u===null)return;var y=u.tag;if(y===3||y===4){var k=u.stateNode.containerInfo;if(k===f||k.nodeType===8&&k.parentNode===f)break;if(y===4)for(y=u.return;y!==null;){var L=y.tag;if((L===3||L===4)&&(L=y.stateNode.containerInfo,L===f||L.nodeType===8&&L.parentNode===f))return;y=y.return}for(;k!==null;){if(y=Ur(k),y===null)return;if(L=y.tag,L===5||L===6){u=m=y;continue e}k=k.parentNode}}u=u.return}A(function(){var $=m,Y=Ir(s),J=[];e:{var G=hf.get(t);if(G!==void 0){var ae=_l,fe=t;switch(t){case"keypress":if(Ls(s)===0)break e;case"keydown":case"keyup":ae=jy;break;case"focusin":fe="focus",ae=Al;break;case"focusout":fe="blur",ae=Al;break;case"beforeblur":case"afterblur":ae=Al;break;case"click":if(s.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ae=Hd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ae=Py;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ae=Uy;break;case uf:case cf:case df:ae=Ly;break;case ff:ae=By;break;case"scroll":ae=ky;break;case"wheel":ae=Wy;break;case"copy":case"cut":case"paste":ae=Ty;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ae=Vd}var me=(n&4)!==0,ot=!me&&t==="scroll",I=me?G!==null?G+"Capture":null:G;me=[];for(var T=$,z;T!==null;){z=T;var ee=z.stateNode;if(z.tag===5&&ee!==null&&(z=ee,I!==null&&(ee=U(T,I),ee!=null&&me.push(Ei(T,ee,z)))),ot)break;T=T.return}0<me.length&&(G=new ae(G,fe,null,s,Y),J.push({event:G,listeners:me}))}}if((n&7)===0){e:{if(G=t==="mouseover"||t==="pointerover",ae=t==="mouseout"||t==="pointerout",G&&s!==co&&(fe=s.relatedTarget||s.fromElement)&&(Ur(fe)||fe[$n]))break e;if((ae||G)&&(G=Y.window===Y?Y:(G=Y.ownerDocument)?G.defaultView||G.parentWindow:window,ae?(fe=s.relatedTarget||s.toElement,ae=$,fe=fe?Ur(fe):null,fe!==null&&(ot=Be(fe),fe!==ot||fe.tag!==5&&fe.tag!==6)&&(fe=null)):(ae=null,fe=$),ae!==fe)){if(me=Hd,ee="onMouseLeave",I="onMouseEnter",T="mouse",(t==="pointerout"||t==="pointerover")&&(me=Vd,ee="onPointerLeave",I="onPointerEnter",T="pointer"),ot=ae==null?G:xo(ae),z=fe==null?G:xo(fe),G=new me(ee,T+"leave",ae,s,Y),G.target=ot,G.relatedTarget=z,ee=null,Ur(Y)===$&&(me=new me(I,T+"enter",fe,s,Y),me.target=z,me.relatedTarget=ot,ee=me),ot=ee,ae&&fe)t:{for(me=ae,I=fe,T=0,z=me;z;z=yo(z))T++;for(z=0,ee=I;ee;ee=yo(ee))z++;for(;0<T-z;)me=yo(me),T--;for(;0<z-T;)I=yo(I),z--;for(;T--;){if(me===I||I!==null&&me===I.alternate)break t;me=yo(me),I=yo(I)}me=null}else me=null;ae!==null&&yf(J,G,ae,me,!1),fe!==null&&ot!==null&&yf(J,ot,fe,me,!0)}}e:{if(G=$?xo($):window,ae=G.nodeName&&G.nodeName.toLowerCase(),ae==="select"||ae==="input"&&G.type==="file")var ve=qy;else if(Jd(G))if(qd)ve=nw;else{ve=ew;var Ce=Zy}else(ae=G.nodeName)&&ae.toLowerCase()==="input"&&(G.type==="checkbox"||G.type==="radio")&&(ve=tw);if(ve&&(ve=ve(t,$))){Xd(J,ve,s,Y);break e}Ce&&Ce(t,G,$),t==="focusout"&&(Ce=G._wrapperState)&&Ce.controlled&&G.type==="number"&&Zo(G,"number",G.value)}switch(Ce=$?xo($):window,t){case"focusin":(Jd(Ce)||Ce.contentEditable==="true")&&(go=Ce,Ul=$,wi=null);break;case"focusout":wi=Ul=go=null;break;case"mousedown":Hl=!0;break;case"contextmenu":case"mouseup":case"dragend":Hl=!1,af(J,s,Y);break;case"selectionchange":if(iw)break;case"keydown":case"keyup":af(J,s,Y)}var ke;if(Fl)e:{switch(t){case"compositionstart":var Le="onCompositionStart";break e;case"compositionend":Le="onCompositionEnd";break e;case"compositionupdate":Le="onCompositionUpdate";break e}Le=void 0}else mo?Gd(t,s)&&(Le="onCompositionEnd"):t==="keydown"&&s.keyCode===229&&(Le="onCompositionStart");Le&&(Wd&&s.locale!=="ko"&&(mo||Le!=="onCompositionStart"?Le==="onCompositionEnd"&&mo&&(ke=$d()):(lr=Y,Tl="value"in lr?lr.value:lr.textContent,mo=!0)),Ce=Is($,Le),0<Ce.length&&(Le=new Bd(Le,t,null,s,Y),J.push({event:Le,listeners:Ce}),ke?Le.data=ke:(ke=Yd(s),ke!==null&&(Le.data=ke)))),(ke=Qy?Gy(t,s):Yy(t,s))&&($=Is($,"onBeforeInput"),0<$.length&&(Y=new Bd("onBeforeInput","beforeinput",null,s,Y),J.push({event:Y,listeners:$}),Y.data=ke))}gf(J,n)})}function Ei(t,n,s){return{instance:t,listener:n,currentTarget:s}}function Is(t,n){for(var s=n+"Capture",u=[];t!==null;){var f=t,m=f.stateNode;f.tag===5&&m!==null&&(f=m,m=U(t,s),m!=null&&u.unshift(Ei(t,m,f)),m=U(t,n),m!=null&&u.push(Ei(t,m,f))),t=t.return}return u}function yo(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function yf(t,n,s,u,f){for(var m=n._reactName,y=[];s!==null&&s!==u;){var k=s,L=k.alternate,$=k.stateNode;if(L!==null&&L===u)break;k.tag===5&&$!==null&&(k=$,f?(L=U(s,m),L!=null&&y.unshift(Ei(s,L,k))):f||(L=U(s,m),L!=null&&y.push(Ei(s,L,k)))),s=s.return}y.length!==0&&t.push({event:n,listeners:y})}var uw=/\r\n?/g,cw=/\u0000|\uFFFD/g;function wf(t){return(typeof t=="string"?t:""+t).replace(uw,`
`).replace(cw,"")}function Fs(t,n,s){if(n=wf(n),wf(t)!==n&&s)throw Error(o(425))}function js(){}var Gl=null,Yl=null;function Jl(t,n){return t==="textarea"||t==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Xl=typeof setTimeout=="function"?setTimeout:void 0,dw=typeof clearTimeout=="function"?clearTimeout:void 0,xf=typeof Promise=="function"?Promise:void 0,fw=typeof queueMicrotask=="function"?queueMicrotask:typeof xf<"u"?function(t){return xf.resolve(null).then(t).catch(hw)}:Xl;function hw(t){setTimeout(function(){throw t})}function ql(t,n){var s=n,u=0;do{var f=s.nextSibling;if(t.removeChild(s),f&&f.nodeType===8)if(s=f.data,s==="/$"){if(u===0){t.removeChild(f),fi(n);return}u--}else s!=="$"&&s!=="$?"&&s!=="$!"||u++;s=f}while(s);fi(n)}function cr(t){for(;t!=null;t=t.nextSibling){var n=t.nodeType;if(n===1||n===3)break;if(n===8){if(n=t.data,n==="$"||n==="$!"||n==="$?")break;if(n==="/$")return null}}return t}function Sf(t){t=t.previousSibling;for(var n=0;t;){if(t.nodeType===8){var s=t.data;if(s==="$"||s==="$!"||s==="$?"){if(n===0)return t;n--}else s==="/$"&&n++}t=t.previousSibling}return null}var wo=Math.random().toString(36).slice(2),Pn="__reactFiber$"+wo,Ci="__reactProps$"+wo,$n="__reactContainer$"+wo,Zl="__reactEvents$"+wo,pw="__reactListeners$"+wo,mw="__reactHandles$"+wo;function Ur(t){var n=t[Pn];if(n)return n;for(var s=t.parentNode;s;){if(n=s[$n]||s[Pn]){if(s=n.alternate,n.child!==null||s!==null&&s.child!==null)for(t=Sf(t);t!==null;){if(s=t[Pn])return s;t=Sf(t)}return n}t=s,s=t.parentNode}return null}function ki(t){return t=t[Pn]||t[$n],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function xo(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(o(33))}function zs(t){return t[Ci]||null}var eu=[],So=-1;function dr(t){return{current:t}}function Je(t){0>So||(t.current=eu[So],eu[So]=null,So--)}function Ge(t,n){So++,eu[So]=t.current,t.current=n}var fr={},wt=dr(fr),_t=dr(!1),Hr=fr;function Eo(t,n){var s=t.type.contextTypes;if(!s)return fr;var u=t.stateNode;if(u&&u.__reactInternalMemoizedUnmaskedChildContext===n)return u.__reactInternalMemoizedMaskedChildContext;var f={},m;for(m in s)f[m]=n[m];return u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=n,t.__reactInternalMemoizedMaskedChildContext=f),f}function Nt(t){return t=t.childContextTypes,t!=null}function $s(){Je(_t),Je(wt)}function Ef(t,n,s){if(wt.current!==fr)throw Error(o(168));Ge(wt,n),Ge(_t,s)}function Cf(t,n,s){var u=t.stateNode;if(n=n.childContextTypes,typeof u.getChildContext!="function")return s;u=u.getChildContext();for(var f in u)if(!(f in n))throw Error(o(108,ue(t)||"Unknown",f));return Q({},s,u)}function Us(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||fr,Hr=wt.current,Ge(wt,t),Ge(_t,_t.current),!0}function kf(t,n,s){var u=t.stateNode;if(!u)throw Error(o(169));s?(t=Cf(t,n,Hr),u.__reactInternalMemoizedMergedChildContext=t,Je(_t),Je(wt),Ge(wt,t)):Je(_t),Ge(_t,s)}var Un=null,Hs=!1,tu=!1;function bf(t){Un===null?Un=[t]:Un.push(t)}function gw(t){Hs=!0,bf(t)}function hr(){if(!tu&&Un!==null){tu=!0;var t=0,n=We;try{var s=Un;for(We=1;t<s.length;t++){var u=s[t];do u=u(!0);while(u!==null)}Un=null,Hs=!1}catch(f){throw Un!==null&&(Un=Un.slice(t+1)),nr(rr,hr),f}finally{We=n,tu=!1}}return null}var Co=[],ko=0,Bs=null,Vs=0,Xt=[],qt=0,Br=null,Hn=1,Bn="";function Vr(t,n){Co[ko++]=Vs,Co[ko++]=Bs,Bs=t,Vs=n}function Pf(t,n,s){Xt[qt++]=Hn,Xt[qt++]=Bn,Xt[qt++]=Br,Br=t;var u=Hn;t=Bn;var f=32-Tt(u)-1;u&=~(1<<f),s+=1;var m=32-Tt(n)+f;if(30<m){var y=f-f%5;m=(u&(1<<y)-1).toString(32),u>>=y,f-=y,Hn=1<<32-Tt(n)+f|s<<f|u,Bn=m+t}else Hn=1<<m|s<<f|u,Bn=t}function nu(t){t.return!==null&&(Vr(t,1),Pf(t,1,0))}function ru(t){for(;t===Bs;)Bs=Co[--ko],Co[ko]=null,Vs=Co[--ko],Co[ko]=null;for(;t===Br;)Br=Xt[--qt],Xt[qt]=null,Bn=Xt[--qt],Xt[qt]=null,Hn=Xt[--qt],Xt[qt]=null}var Bt=null,Vt=null,Ze=!1,mn=null;function Rf(t,n){var s=nn(5,null,null,0);s.elementType="DELETED",s.stateNode=n,s.return=t,n=t.deletions,n===null?(t.deletions=[s],t.flags|=16):n.push(s)}function Of(t,n){switch(t.tag){case 5:var s=t.type;return n=n.nodeType!==1||s.toLowerCase()!==n.nodeName.toLowerCase()?null:n,n!==null?(t.stateNode=n,Bt=t,Vt=cr(n.firstChild),!0):!1;case 6:return n=t.pendingProps===""||n.nodeType!==3?null:n,n!==null?(t.stateNode=n,Bt=t,Vt=null,!0):!1;case 13:return n=n.nodeType!==8?null:n,n!==null?(s=Br!==null?{id:Hn,overflow:Bn}:null,t.memoizedState={dehydrated:n,treeContext:s,retryLane:1073741824},s=nn(18,null,null,0),s.stateNode=n,s.return=t,t.child=s,Bt=t,Vt=null,!0):!1;default:return!1}}function ou(t){return(t.mode&1)!==0&&(t.flags&128)===0}function iu(t){if(Ze){var n=Vt;if(n){var s=n;if(!Of(t,n)){if(ou(t))throw Error(o(418));n=cr(s.nextSibling);var u=Bt;n&&Of(t,n)?Rf(u,s):(t.flags=t.flags&-4097|2,Ze=!1,Bt=t)}}else{if(ou(t))throw Error(o(418));t.flags=t.flags&-4097|2,Ze=!1,Bt=t}}}function Lf(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Bt=t}function Ws(t){if(t!==Bt)return!1;if(!Ze)return Lf(t),Ze=!0,!1;var n;if((n=t.tag!==3)&&!(n=t.tag!==5)&&(n=t.type,n=n!=="head"&&n!=="body"&&!Jl(t.type,t.memoizedProps)),n&&(n=Vt)){if(ou(t))throw Mf(),Error(o(418));for(;n;)Rf(t,n),n=cr(n.nextSibling)}if(Lf(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(o(317));e:{for(t=t.nextSibling,n=0;t;){if(t.nodeType===8){var s=t.data;if(s==="/$"){if(n===0){Vt=cr(t.nextSibling);break e}n--}else s!=="$"&&s!=="$!"&&s!=="$?"||n++}t=t.nextSibling}Vt=null}}else Vt=Bt?cr(t.stateNode.nextSibling):null;return!0}function Mf(){for(var t=Vt;t;)t=cr(t.nextSibling)}function bo(){Vt=Bt=null,Ze=!1}function su(t){mn===null?mn=[t]:mn.push(t)}var vw=j.ReactCurrentBatchConfig;function bi(t,n,s){if(t=s.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(s._owner){if(s=s._owner,s){if(s.tag!==1)throw Error(o(309));var u=s.stateNode}if(!u)throw Error(o(147,t));var f=u,m=""+t;return n!==null&&n.ref!==null&&typeof n.ref=="function"&&n.ref._stringRef===m?n.ref:(n=function(y){var k=f.refs;y===null?delete k[m]:k[m]=y},n._stringRef=m,n)}if(typeof t!="string")throw Error(o(284));if(!s._owner)throw Error(o(290,t))}return t}function Ks(t,n){throw t=Object.prototype.toString.call(n),Error(o(31,t==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":t))}function Tf(t){var n=t._init;return n(t._payload)}function _f(t){function n(I,T){if(t){var z=I.deletions;z===null?(I.deletions=[T],I.flags|=16):z.push(T)}}function s(I,T){if(!t)return null;for(;T!==null;)n(I,T),T=T.sibling;return null}function u(I,T){for(I=new Map;T!==null;)T.key!==null?I.set(T.key,T):I.set(T.index,T),T=T.sibling;return I}function f(I,T){return I=Sr(I,T),I.index=0,I.sibling=null,I}function m(I,T,z){return I.index=z,t?(z=I.alternate,z!==null?(z=z.index,z<T?(I.flags|=2,T):z):(I.flags|=2,T)):(I.flags|=1048576,T)}function y(I){return t&&I.alternate===null&&(I.flags|=2),I}function k(I,T,z,ee){return T===null||T.tag!==6?(T=Xu(z,I.mode,ee),T.return=I,T):(T=f(T,z),T.return=I,T)}function L(I,T,z,ee){var ve=z.type;return ve===K?Y(I,T,z.props.children,ee,z.key):T!==null&&(T.elementType===ve||typeof ve=="object"&&ve!==null&&ve.$$typeof===X&&Tf(ve)===T.type)?(ee=f(T,z.props),ee.ref=bi(I,T,z),ee.return=I,ee):(ee=ga(z.type,z.key,z.props,null,I.mode,ee),ee.ref=bi(I,T,z),ee.return=I,ee)}function $(I,T,z,ee){return T===null||T.tag!==4||T.stateNode.containerInfo!==z.containerInfo||T.stateNode.implementation!==z.implementation?(T=qu(z,I.mode,ee),T.return=I,T):(T=f(T,z.children||[]),T.return=I,T)}function Y(I,T,z,ee,ve){return T===null||T.tag!==7?(T=qr(z,I.mode,ee,ve),T.return=I,T):(T=f(T,z),T.return=I,T)}function J(I,T,z){if(typeof T=="string"&&T!==""||typeof T=="number")return T=Xu(""+T,I.mode,z),T.return=I,T;if(typeof T=="object"&&T!==null){switch(T.$$typeof){case H:return z=ga(T.type,T.key,T.props,null,I.mode,z),z.ref=bi(I,null,T),z.return=I,z;case M:return T=qu(T,I.mode,z),T.return=I,T;case X:var ee=T._init;return J(I,ee(T._payload),z)}if(Nr(T)||V(T))return T=qr(T,I.mode,z,null),T.return=I,T;Ks(I,T)}return null}function G(I,T,z,ee){var ve=T!==null?T.key:null;if(typeof z=="string"&&z!==""||typeof z=="number")return ve!==null?null:k(I,T,""+z,ee);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case H:return z.key===ve?L(I,T,z,ee):null;case M:return z.key===ve?$(I,T,z,ee):null;case X:return ve=z._init,G(I,T,ve(z._payload),ee)}if(Nr(z)||V(z))return ve!==null?null:Y(I,T,z,ee,null);Ks(I,z)}return null}function ae(I,T,z,ee,ve){if(typeof ee=="string"&&ee!==""||typeof ee=="number")return I=I.get(z)||null,k(T,I,""+ee,ve);if(typeof ee=="object"&&ee!==null){switch(ee.$$typeof){case H:return I=I.get(ee.key===null?z:ee.key)||null,L(T,I,ee,ve);case M:return I=I.get(ee.key===null?z:ee.key)||null,$(T,I,ee,ve);case X:var Ce=ee._init;return ae(I,T,z,Ce(ee._payload),ve)}if(Nr(ee)||V(ee))return I=I.get(z)||null,Y(T,I,ee,ve,null);Ks(T,ee)}return null}function fe(I,T,z,ee){for(var ve=null,Ce=null,ke=T,Le=T=0,pt=null;ke!==null&&Le<z.length;Le++){ke.index>Le?(pt=ke,ke=null):pt=ke.sibling;var He=G(I,ke,z[Le],ee);if(He===null){ke===null&&(ke=pt);break}t&&ke&&He.alternate===null&&n(I,ke),T=m(He,T,Le),Ce===null?ve=He:Ce.sibling=He,Ce=He,ke=pt}if(Le===z.length)return s(I,ke),Ze&&Vr(I,Le),ve;if(ke===null){for(;Le<z.length;Le++)ke=J(I,z[Le],ee),ke!==null&&(T=m(ke,T,Le),Ce===null?ve=ke:Ce.sibling=ke,Ce=ke);return Ze&&Vr(I,Le),ve}for(ke=u(I,ke);Le<z.length;Le++)pt=ae(ke,I,Le,z[Le],ee),pt!==null&&(t&&pt.alternate!==null&&ke.delete(pt.key===null?Le:pt.key),T=m(pt,T,Le),Ce===null?ve=pt:Ce.sibling=pt,Ce=pt);return t&&ke.forEach(function(Er){return n(I,Er)}),Ze&&Vr(I,Le),ve}function me(I,T,z,ee){var ve=V(z);if(typeof ve!="function")throw Error(o(150));if(z=ve.call(z),z==null)throw Error(o(151));for(var Ce=ve=null,ke=T,Le=T=0,pt=null,He=z.next();ke!==null&&!He.done;Le++,He=z.next()){ke.index>Le?(pt=ke,ke=null):pt=ke.sibling;var Er=G(I,ke,He.value,ee);if(Er===null){ke===null&&(ke=pt);break}t&&ke&&Er.alternate===null&&n(I,ke),T=m(Er,T,Le),Ce===null?ve=Er:Ce.sibling=Er,Ce=Er,ke=pt}if(He.done)return s(I,ke),Ze&&Vr(I,Le),ve;if(ke===null){for(;!He.done;Le++,He=z.next())He=J(I,He.value,ee),He!==null&&(T=m(He,T,Le),Ce===null?ve=He:Ce.sibling=He,Ce=He);return Ze&&Vr(I,Le),ve}for(ke=u(I,ke);!He.done;Le++,He=z.next())He=ae(ke,I,Le,He.value,ee),He!==null&&(t&&He.alternate!==null&&ke.delete(He.key===null?Le:He.key),T=m(He,T,Le),Ce===null?ve=He:Ce.sibling=He,Ce=He);return t&&ke.forEach(function(Jw){return n(I,Jw)}),Ze&&Vr(I,Le),ve}function ot(I,T,z,ee){if(typeof z=="object"&&z!==null&&z.type===K&&z.key===null&&(z=z.props.children),typeof z=="object"&&z!==null){switch(z.$$typeof){case H:e:{for(var ve=z.key,Ce=T;Ce!==null;){if(Ce.key===ve){if(ve=z.type,ve===K){if(Ce.tag===7){s(I,Ce.sibling),T=f(Ce,z.props.children),T.return=I,I=T;break e}}else if(Ce.elementType===ve||typeof ve=="object"&&ve!==null&&ve.$$typeof===X&&Tf(ve)===Ce.type){s(I,Ce.sibling),T=f(Ce,z.props),T.ref=bi(I,Ce,z),T.return=I,I=T;break e}s(I,Ce);break}else n(I,Ce);Ce=Ce.sibling}z.type===K?(T=qr(z.props.children,I.mode,ee,z.key),T.return=I,I=T):(ee=ga(z.type,z.key,z.props,null,I.mode,ee),ee.ref=bi(I,T,z),ee.return=I,I=ee)}return y(I);case M:e:{for(Ce=z.key;T!==null;){if(T.key===Ce)if(T.tag===4&&T.stateNode.containerInfo===z.containerInfo&&T.stateNode.implementation===z.implementation){s(I,T.sibling),T=f(T,z.children||[]),T.return=I,I=T;break e}else{s(I,T);break}else n(I,T);T=T.sibling}T=qu(z,I.mode,ee),T.return=I,I=T}return y(I);case X:return Ce=z._init,ot(I,T,Ce(z._payload),ee)}if(Nr(z))return fe(I,T,z,ee);if(V(z))return me(I,T,z,ee);Ks(I,z)}return typeof z=="string"&&z!==""||typeof z=="number"?(z=""+z,T!==null&&T.tag===6?(s(I,T.sibling),T=f(T,z),T.return=I,I=T):(s(I,T),T=Xu(z,I.mode,ee),T.return=I,I=T),y(I)):s(I,T)}return ot}var Po=_f(!0),Nf=_f(!1),Qs=dr(null),Gs=null,Ro=null,au=null;function lu(){au=Ro=Gs=null}function uu(t){var n=Qs.current;Je(Qs),t._currentValue=n}function cu(t,n,s){for(;t!==null;){var u=t.alternate;if((t.childLanes&n)!==n?(t.childLanes|=n,u!==null&&(u.childLanes|=n)):u!==null&&(u.childLanes&n)!==n&&(u.childLanes|=n),t===s)break;t=t.return}}function Oo(t,n){Gs=t,au=Ro=null,t=t.dependencies,t!==null&&t.firstContext!==null&&((t.lanes&n)!==0&&(Dt=!0),t.firstContext=null)}function Zt(t){var n=t._currentValue;if(au!==t)if(t={context:t,memoizedValue:n,next:null},Ro===null){if(Gs===null)throw Error(o(308));Ro=t,Gs.dependencies={lanes:0,firstContext:t}}else Ro=Ro.next=t;return n}var Wr=null;function du(t){Wr===null?Wr=[t]:Wr.push(t)}function Df(t,n,s,u){var f=n.interleaved;return f===null?(s.next=s,du(n)):(s.next=f.next,f.next=s),n.interleaved=s,Vn(t,u)}function Vn(t,n){t.lanes|=n;var s=t.alternate;for(s!==null&&(s.lanes|=n),s=t,t=t.return;t!==null;)t.childLanes|=n,s=t.alternate,s!==null&&(s.childLanes|=n),s=t,t=t.return;return s.tag===3?s.stateNode:null}var pr=!1;function fu(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Af(t,n){t=t.updateQueue,n.updateQueue===t&&(n.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function Wn(t,n){return{eventTime:t,lane:n,tag:0,payload:null,callback:null,next:null}}function mr(t,n,s){var u=t.updateQueue;if(u===null)return null;if(u=u.shared,(Ue&2)!==0){var f=u.pending;return f===null?n.next=n:(n.next=f.next,f.next=n),u.pending=n,Vn(t,s)}return f=u.interleaved,f===null?(n.next=n,du(u)):(n.next=f.next,f.next=n),u.interleaved=n,Vn(t,s)}function Ys(t,n,s){if(n=n.updateQueue,n!==null&&(n=n.shared,(s&4194240)!==0)){var u=n.lanes;u&=t.pendingLanes,s|=u,n.lanes=s,Pl(t,s)}}function If(t,n){var s=t.updateQueue,u=t.alternate;if(u!==null&&(u=u.updateQueue,s===u)){var f=null,m=null;if(s=s.firstBaseUpdate,s!==null){do{var y={eventTime:s.eventTime,lane:s.lane,tag:s.tag,payload:s.payload,callback:s.callback,next:null};m===null?f=m=y:m=m.next=y,s=s.next}while(s!==null);m===null?f=m=n:m=m.next=n}else f=m=n;s={baseState:u.baseState,firstBaseUpdate:f,lastBaseUpdate:m,shared:u.shared,effects:u.effects},t.updateQueue=s;return}t=s.lastBaseUpdate,t===null?s.firstBaseUpdate=n:t.next=n,s.lastBaseUpdate=n}function Js(t,n,s,u){var f=t.updateQueue;pr=!1;var m=f.firstBaseUpdate,y=f.lastBaseUpdate,k=f.shared.pending;if(k!==null){f.shared.pending=null;var L=k,$=L.next;L.next=null,y===null?m=$:y.next=$,y=L;var Y=t.alternate;Y!==null&&(Y=Y.updateQueue,k=Y.lastBaseUpdate,k!==y&&(k===null?Y.firstBaseUpdate=$:k.next=$,Y.lastBaseUpdate=L))}if(m!==null){var J=f.baseState;y=0,Y=$=L=null,k=m;do{var G=k.lane,ae=k.eventTime;if((u&G)===G){Y!==null&&(Y=Y.next={eventTime:ae,lane:0,tag:k.tag,payload:k.payload,callback:k.callback,next:null});e:{var fe=t,me=k;switch(G=n,ae=s,me.tag){case 1:if(fe=me.payload,typeof fe=="function"){J=fe.call(ae,J,G);break e}J=fe;break e;case 3:fe.flags=fe.flags&-65537|128;case 0:if(fe=me.payload,G=typeof fe=="function"?fe.call(ae,J,G):fe,G==null)break e;J=Q({},J,G);break e;case 2:pr=!0}}k.callback!==null&&k.lane!==0&&(t.flags|=64,G=f.effects,G===null?f.effects=[k]:G.push(k))}else ae={eventTime:ae,lane:G,tag:k.tag,payload:k.payload,callback:k.callback,next:null},Y===null?($=Y=ae,L=J):Y=Y.next=ae,y|=G;if(k=k.next,k===null){if(k=f.shared.pending,k===null)break;G=k,k=G.next,G.next=null,f.lastBaseUpdate=G,f.shared.pending=null}}while(!0);if(Y===null&&(L=J),f.baseState=L,f.firstBaseUpdate=$,f.lastBaseUpdate=Y,n=f.shared.interleaved,n!==null){f=n;do y|=f.lane,f=f.next;while(f!==n)}else m===null&&(f.shared.lanes=0);Gr|=y,t.lanes=y,t.memoizedState=J}}function Ff(t,n,s){if(t=n.effects,n.effects=null,t!==null)for(n=0;n<t.length;n++){var u=t[n],f=u.callback;if(f!==null){if(u.callback=null,u=s,typeof f!="function")throw Error(o(191,f));f.call(u)}}}var Pi={},Rn=dr(Pi),Ri=dr(Pi),Oi=dr(Pi);function Kr(t){if(t===Pi)throw Error(o(174));return t}function hu(t,n){switch(Ge(Oi,n),Ge(Ri,t),Ge(Rn,Pi),t=n.nodeType,t){case 9:case 11:n=(n=n.documentElement)?n.namespaceURI:Ut(null,"");break;default:t=t===8?n.parentNode:n,n=t.namespaceURI||null,t=t.tagName,n=Ut(n,t)}Je(Rn),Ge(Rn,n)}function Lo(){Je(Rn),Je(Ri),Je(Oi)}function jf(t){Kr(Oi.current);var n=Kr(Rn.current),s=Ut(n,t.type);n!==s&&(Ge(Ri,t),Ge(Rn,s))}function pu(t){Ri.current===t&&(Je(Rn),Je(Ri))}var et=dr(0);function Xs(t){for(var n=t;n!==null;){if(n.tag===13){var s=n.memoizedState;if(s!==null&&(s=s.dehydrated,s===null||s.data==="$?"||s.data==="$!"))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}var mu=[];function gu(){for(var t=0;t<mu.length;t++)mu[t]._workInProgressVersionPrimary=null;mu.length=0}var qs=j.ReactCurrentDispatcher,vu=j.ReactCurrentBatchConfig,Qr=0,tt=null,at=null,ft=null,Zs=!1,Li=!1,Mi=0,yw=0;function xt(){throw Error(o(321))}function yu(t,n){if(n===null)return!1;for(var s=0;s<n.length&&s<t.length;s++)if(!pn(t[s],n[s]))return!1;return!0}function wu(t,n,s,u,f,m){if(Qr=m,tt=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,qs.current=t===null||t.memoizedState===null?Ew:Cw,t=s(u,f),Li){m=0;do{if(Li=!1,Mi=0,25<=m)throw Error(o(301));m+=1,ft=at=null,n.updateQueue=null,qs.current=kw,t=s(u,f)}while(Li)}if(qs.current=na,n=at!==null&&at.next!==null,Qr=0,ft=at=tt=null,Zs=!1,n)throw Error(o(300));return t}function xu(){var t=Mi!==0;return Mi=0,t}function On(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ft===null?tt.memoizedState=ft=t:ft=ft.next=t,ft}function en(){if(at===null){var t=tt.alternate;t=t!==null?t.memoizedState:null}else t=at.next;var n=ft===null?tt.memoizedState:ft.next;if(n!==null)ft=n,at=t;else{if(t===null)throw Error(o(310));at=t,t={memoizedState:at.memoizedState,baseState:at.baseState,baseQueue:at.baseQueue,queue:at.queue,next:null},ft===null?tt.memoizedState=ft=t:ft=ft.next=t}return ft}function Ti(t,n){return typeof n=="function"?n(t):n}function Su(t){var n=en(),s=n.queue;if(s===null)throw Error(o(311));s.lastRenderedReducer=t;var u=at,f=u.baseQueue,m=s.pending;if(m!==null){if(f!==null){var y=f.next;f.next=m.next,m.next=y}u.baseQueue=f=m,s.pending=null}if(f!==null){m=f.next,u=u.baseState;var k=y=null,L=null,$=m;do{var Y=$.lane;if((Qr&Y)===Y)L!==null&&(L=L.next={lane:0,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null}),u=$.hasEagerState?$.eagerState:t(u,$.action);else{var J={lane:Y,action:$.action,hasEagerState:$.hasEagerState,eagerState:$.eagerState,next:null};L===null?(k=L=J,y=u):L=L.next=J,tt.lanes|=Y,Gr|=Y}$=$.next}while($!==null&&$!==m);L===null?y=u:L.next=k,pn(u,n.memoizedState)||(Dt=!0),n.memoizedState=u,n.baseState=y,n.baseQueue=L,s.lastRenderedState=u}if(t=s.interleaved,t!==null){f=t;do m=f.lane,tt.lanes|=m,Gr|=m,f=f.next;while(f!==t)}else f===null&&(s.lanes=0);return[n.memoizedState,s.dispatch]}function Eu(t){var n=en(),s=n.queue;if(s===null)throw Error(o(311));s.lastRenderedReducer=t;var u=s.dispatch,f=s.pending,m=n.memoizedState;if(f!==null){s.pending=null;var y=f=f.next;do m=t(m,y.action),y=y.next;while(y!==f);pn(m,n.memoizedState)||(Dt=!0),n.memoizedState=m,n.baseQueue===null&&(n.baseState=m),s.lastRenderedState=m}return[m,u]}function zf(){}function $f(t,n){var s=tt,u=en(),f=n(),m=!pn(u.memoizedState,f);if(m&&(u.memoizedState=f,Dt=!0),u=u.queue,Cu(Bf.bind(null,s,u,t),[t]),u.getSnapshot!==n||m||ft!==null&&ft.memoizedState.tag&1){if(s.flags|=2048,_i(9,Hf.bind(null,s,u,f,n),void 0,null),ht===null)throw Error(o(349));(Qr&30)!==0||Uf(s,n,f)}return f}function Uf(t,n,s){t.flags|=16384,t={getSnapshot:n,value:s},n=tt.updateQueue,n===null?(n={lastEffect:null,stores:null},tt.updateQueue=n,n.stores=[t]):(s=n.stores,s===null?n.stores=[t]:s.push(t))}function Hf(t,n,s,u){n.value=s,n.getSnapshot=u,Vf(n)&&Wf(t)}function Bf(t,n,s){return s(function(){Vf(n)&&Wf(t)})}function Vf(t){var n=t.getSnapshot;t=t.value;try{var s=n();return!pn(t,s)}catch{return!0}}function Wf(t){var n=Vn(t,1);n!==null&&wn(n,t,1,-1)}function Kf(t){var n=On();return typeof t=="function"&&(t=t()),n.memoizedState=n.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Ti,lastRenderedState:t},n.queue=t,t=t.dispatch=Sw.bind(null,tt,t),[n.memoizedState,t]}function _i(t,n,s,u){return t={tag:t,create:n,destroy:s,deps:u,next:null},n=tt.updateQueue,n===null?(n={lastEffect:null,stores:null},tt.updateQueue=n,n.lastEffect=t.next=t):(s=n.lastEffect,s===null?n.lastEffect=t.next=t:(u=s.next,s.next=t,t.next=u,n.lastEffect=t)),t}function Qf(){return en().memoizedState}function ea(t,n,s,u){var f=On();tt.flags|=t,f.memoizedState=_i(1|n,s,void 0,u===void 0?null:u)}function ta(t,n,s,u){var f=en();u=u===void 0?null:u;var m=void 0;if(at!==null){var y=at.memoizedState;if(m=y.destroy,u!==null&&yu(u,y.deps)){f.memoizedState=_i(n,s,m,u);return}}tt.flags|=t,f.memoizedState=_i(1|n,s,m,u)}function Gf(t,n){return ea(8390656,8,t,n)}function Cu(t,n){return ta(2048,8,t,n)}function Yf(t,n){return ta(4,2,t,n)}function Jf(t,n){return ta(4,4,t,n)}function Xf(t,n){if(typeof n=="function")return t=t(),n(t),function(){n(null)};if(n!=null)return t=t(),n.current=t,function(){n.current=null}}function qf(t,n,s){return s=s!=null?s.concat([t]):null,ta(4,4,Xf.bind(null,n,t),s)}function ku(){}function Zf(t,n){var s=en();n=n===void 0?null:n;var u=s.memoizedState;return u!==null&&n!==null&&yu(n,u[1])?u[0]:(s.memoizedState=[t,n],t)}function eh(t,n){var s=en();n=n===void 0?null:n;var u=s.memoizedState;return u!==null&&n!==null&&yu(n,u[1])?u[0]:(t=t(),s.memoizedState=[t,n],t)}function th(t,n,s){return(Qr&21)===0?(t.baseState&&(t.baseState=!1,Dt=!0),t.memoizedState=s):(pn(s,n)||(s=Md(),tt.lanes|=s,Gr|=s,t.baseState=!0),n)}function ww(t,n){var s=We;We=s!==0&&4>s?s:4,t(!0);var u=vu.transition;vu.transition={};try{t(!1),n()}finally{We=s,vu.transition=u}}function nh(){return en().memoizedState}function xw(t,n,s){var u=wr(t);if(s={lane:u,action:s,hasEagerState:!1,eagerState:null,next:null},rh(t))oh(n,s);else if(s=Df(t,n,s,u),s!==null){var f=Pt();wn(s,t,u,f),ih(s,n,u)}}function Sw(t,n,s){var u=wr(t),f={lane:u,action:s,hasEagerState:!1,eagerState:null,next:null};if(rh(t))oh(n,f);else{var m=t.alternate;if(t.lanes===0&&(m===null||m.lanes===0)&&(m=n.lastRenderedReducer,m!==null))try{var y=n.lastRenderedState,k=m(y,s);if(f.hasEagerState=!0,f.eagerState=k,pn(k,y)){var L=n.interleaved;L===null?(f.next=f,du(n)):(f.next=L.next,L.next=f),n.interleaved=f;return}}catch{}finally{}s=Df(t,n,f,u),s!==null&&(f=Pt(),wn(s,t,u,f),ih(s,n,u))}}function rh(t){var n=t.alternate;return t===tt||n!==null&&n===tt}function oh(t,n){Li=Zs=!0;var s=t.pending;s===null?n.next=n:(n.next=s.next,s.next=n),t.pending=n}function ih(t,n,s){if((s&4194240)!==0){var u=n.lanes;u&=t.pendingLanes,s|=u,n.lanes=s,Pl(t,s)}}var na={readContext:Zt,useCallback:xt,useContext:xt,useEffect:xt,useImperativeHandle:xt,useInsertionEffect:xt,useLayoutEffect:xt,useMemo:xt,useReducer:xt,useRef:xt,useState:xt,useDebugValue:xt,useDeferredValue:xt,useTransition:xt,useMutableSource:xt,useSyncExternalStore:xt,useId:xt,unstable_isNewReconciler:!1},Ew={readContext:Zt,useCallback:function(t,n){return On().memoizedState=[t,n===void 0?null:n],t},useContext:Zt,useEffect:Gf,useImperativeHandle:function(t,n,s){return s=s!=null?s.concat([t]):null,ea(4194308,4,Xf.bind(null,n,t),s)},useLayoutEffect:function(t,n){return ea(4194308,4,t,n)},useInsertionEffect:function(t,n){return ea(4,2,t,n)},useMemo:function(t,n){var s=On();return n=n===void 0?null:n,t=t(),s.memoizedState=[t,n],t},useReducer:function(t,n,s){var u=On();return n=s!==void 0?s(n):n,u.memoizedState=u.baseState=n,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},u.queue=t,t=t.dispatch=xw.bind(null,tt,t),[u.memoizedState,t]},useRef:function(t){var n=On();return t={current:t},n.memoizedState=t},useState:Kf,useDebugValue:ku,useDeferredValue:function(t){return On().memoizedState=t},useTransition:function(){var t=Kf(!1),n=t[0];return t=ww.bind(null,t[1]),On().memoizedState=t,[n,t]},useMutableSource:function(){},useSyncExternalStore:function(t,n,s){var u=tt,f=On();if(Ze){if(s===void 0)throw Error(o(407));s=s()}else{if(s=n(),ht===null)throw Error(o(349));(Qr&30)!==0||Uf(u,n,s)}f.memoizedState=s;var m={value:s,getSnapshot:n};return f.queue=m,Gf(Bf.bind(null,u,m,t),[t]),u.flags|=2048,_i(9,Hf.bind(null,u,m,s,n),void 0,null),s},useId:function(){var t=On(),n=ht.identifierPrefix;if(Ze){var s=Bn,u=Hn;s=(u&~(1<<32-Tt(u)-1)).toString(32)+s,n=":"+n+"R"+s,s=Mi++,0<s&&(n+="H"+s.toString(32)),n+=":"}else s=yw++,n=":"+n+"r"+s.toString(32)+":";return t.memoizedState=n},unstable_isNewReconciler:!1},Cw={readContext:Zt,useCallback:Zf,useContext:Zt,useEffect:Cu,useImperativeHandle:qf,useInsertionEffect:Yf,useLayoutEffect:Jf,useMemo:eh,useReducer:Su,useRef:Qf,useState:function(){return Su(Ti)},useDebugValue:ku,useDeferredValue:function(t){var n=en();return th(n,at.memoizedState,t)},useTransition:function(){var t=Su(Ti)[0],n=en().memoizedState;return[t,n]},useMutableSource:zf,useSyncExternalStore:$f,useId:nh,unstable_isNewReconciler:!1},kw={readContext:Zt,useCallback:Zf,useContext:Zt,useEffect:Cu,useImperativeHandle:qf,useInsertionEffect:Yf,useLayoutEffect:Jf,useMemo:eh,useReducer:Eu,useRef:Qf,useState:function(){return Eu(Ti)},useDebugValue:ku,useDeferredValue:function(t){var n=en();return at===null?n.memoizedState=t:th(n,at.memoizedState,t)},useTransition:function(){var t=Eu(Ti)[0],n=en().memoizedState;return[t,n]},useMutableSource:zf,useSyncExternalStore:$f,useId:nh,unstable_isNewReconciler:!1};function gn(t,n){if(t&&t.defaultProps){n=Q({},n),t=t.defaultProps;for(var s in t)n[s]===void 0&&(n[s]=t[s]);return n}return n}function bu(t,n,s,u){n=t.memoizedState,s=s(u,n),s=s==null?n:Q({},n,s),t.memoizedState=s,t.lanes===0&&(t.updateQueue.baseState=s)}var ra={isMounted:function(t){return(t=t._reactInternals)?Be(t)===t:!1},enqueueSetState:function(t,n,s){t=t._reactInternals;var u=Pt(),f=wr(t),m=Wn(u,f);m.payload=n,s!=null&&(m.callback=s),n=mr(t,m,f),n!==null&&(wn(n,t,f,u),Ys(n,t,f))},enqueueReplaceState:function(t,n,s){t=t._reactInternals;var u=Pt(),f=wr(t),m=Wn(u,f);m.tag=1,m.payload=n,s!=null&&(m.callback=s),n=mr(t,m,f),n!==null&&(wn(n,t,f,u),Ys(n,t,f))},enqueueForceUpdate:function(t,n){t=t._reactInternals;var s=Pt(),u=wr(t),f=Wn(s,u);f.tag=2,n!=null&&(f.callback=n),n=mr(t,f,u),n!==null&&(wn(n,t,u,s),Ys(n,t,u))}};function sh(t,n,s,u,f,m,y){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(u,m,y):n.prototype&&n.prototype.isPureReactComponent?!yi(s,u)||!yi(f,m):!0}function ah(t,n,s){var u=!1,f=fr,m=n.contextType;return typeof m=="object"&&m!==null?m=Zt(m):(f=Nt(n)?Hr:wt.current,u=n.contextTypes,m=(u=u!=null)?Eo(t,f):fr),n=new n(s,m),t.memoizedState=n.state!==null&&n.state!==void 0?n.state:null,n.updater=ra,t.stateNode=n,n._reactInternals=t,u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=f,t.__reactInternalMemoizedMaskedChildContext=m),n}function lh(t,n,s,u){t=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(s,u),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(s,u),n.state!==t&&ra.enqueueReplaceState(n,n.state,null)}function Pu(t,n,s,u){var f=t.stateNode;f.props=s,f.state=t.memoizedState,f.refs={},fu(t);var m=n.contextType;typeof m=="object"&&m!==null?f.context=Zt(m):(m=Nt(n)?Hr:wt.current,f.context=Eo(t,m)),f.state=t.memoizedState,m=n.getDerivedStateFromProps,typeof m=="function"&&(bu(t,n,m,s),f.state=t.memoizedState),typeof n.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(n=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),n!==f.state&&ra.enqueueReplaceState(f,f.state,null),Js(t,s,f,u),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308)}function Mo(t,n){try{var s="",u=n;do s+=Oe(u),u=u.return;while(u);var f=s}catch(m){f=`
Error generating stack: `+m.message+`
`+m.stack}return{value:t,source:n,stack:f,digest:null}}function Ru(t,n,s){return{value:t,source:null,stack:s??null,digest:n??null}}function Ou(t,n){try{console.error(n.value)}catch(s){setTimeout(function(){throw s})}}var bw=typeof WeakMap=="function"?WeakMap:Map;function uh(t,n,s){s=Wn(-1,s),s.tag=3,s.payload={element:null};var u=n.value;return s.callback=function(){ca||(ca=!0,Bu=u),Ou(t,n)},s}function ch(t,n,s){s=Wn(-1,s),s.tag=3;var u=t.type.getDerivedStateFromError;if(typeof u=="function"){var f=n.value;s.payload=function(){return u(f)},s.callback=function(){Ou(t,n)}}var m=t.stateNode;return m!==null&&typeof m.componentDidCatch=="function"&&(s.callback=function(){Ou(t,n),typeof u!="function"&&(vr===null?vr=new Set([this]):vr.add(this));var y=n.stack;this.componentDidCatch(n.value,{componentStack:y!==null?y:""})}),s}function dh(t,n,s){var u=t.pingCache;if(u===null){u=t.pingCache=new bw;var f=new Set;u.set(n,f)}else f=u.get(n),f===void 0&&(f=new Set,u.set(n,f));f.has(s)||(f.add(s),t=zw.bind(null,t,n,s),n.then(t,t))}function fh(t){do{var n;if((n=t.tag===13)&&(n=t.memoizedState,n=n!==null?n.dehydrated!==null:!0),n)return t;t=t.return}while(t!==null);return null}function hh(t,n,s,u,f){return(t.mode&1)===0?(t===n?t.flags|=65536:(t.flags|=128,s.flags|=131072,s.flags&=-52805,s.tag===1&&(s.alternate===null?s.tag=17:(n=Wn(-1,1),n.tag=2,mr(s,n,1))),s.lanes|=1),t):(t.flags|=65536,t.lanes=f,t)}var Pw=j.ReactCurrentOwner,Dt=!1;function bt(t,n,s,u){n.child=t===null?Nf(n,null,s,u):Po(n,t.child,s,u)}function ph(t,n,s,u,f){s=s.render;var m=n.ref;return Oo(n,f),u=wu(t,n,s,u,m,f),s=xu(),t!==null&&!Dt?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~f,Kn(t,n,f)):(Ze&&s&&nu(n),n.flags|=1,bt(t,n,u,f),n.child)}function mh(t,n,s,u,f){if(t===null){var m=s.type;return typeof m=="function"&&!Ju(m)&&m.defaultProps===void 0&&s.compare===null&&s.defaultProps===void 0?(n.tag=15,n.type=m,gh(t,n,m,u,f)):(t=ga(s.type,null,u,n,n.mode,f),t.ref=n.ref,t.return=n,n.child=t)}if(m=t.child,(t.lanes&f)===0){var y=m.memoizedProps;if(s=s.compare,s=s!==null?s:yi,s(y,u)&&t.ref===n.ref)return Kn(t,n,f)}return n.flags|=1,t=Sr(m,u),t.ref=n.ref,t.return=n,n.child=t}function gh(t,n,s,u,f){if(t!==null){var m=t.memoizedProps;if(yi(m,u)&&t.ref===n.ref)if(Dt=!1,n.pendingProps=u=m,(t.lanes&f)!==0)(t.flags&131072)!==0&&(Dt=!0);else return n.lanes=t.lanes,Kn(t,n,f)}return Lu(t,n,s,u,f)}function vh(t,n,s){var u=n.pendingProps,f=u.children,m=t!==null?t.memoizedState:null;if(u.mode==="hidden")if((n.mode&1)===0)n.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ge(_o,Wt),Wt|=s;else{if((s&1073741824)===0)return t=m!==null?m.baseLanes|s:s,n.lanes=n.childLanes=1073741824,n.memoizedState={baseLanes:t,cachePool:null,transitions:null},n.updateQueue=null,Ge(_o,Wt),Wt|=t,null;n.memoizedState={baseLanes:0,cachePool:null,transitions:null},u=m!==null?m.baseLanes:s,Ge(_o,Wt),Wt|=u}else m!==null?(u=m.baseLanes|s,n.memoizedState=null):u=s,Ge(_o,Wt),Wt|=u;return bt(t,n,f,s),n.child}function yh(t,n){var s=n.ref;(t===null&&s!==null||t!==null&&t.ref!==s)&&(n.flags|=512,n.flags|=2097152)}function Lu(t,n,s,u,f){var m=Nt(s)?Hr:wt.current;return m=Eo(n,m),Oo(n,f),s=wu(t,n,s,u,m,f),u=xu(),t!==null&&!Dt?(n.updateQueue=t.updateQueue,n.flags&=-2053,t.lanes&=~f,Kn(t,n,f)):(Ze&&u&&nu(n),n.flags|=1,bt(t,n,s,f),n.child)}function wh(t,n,s,u,f){if(Nt(s)){var m=!0;Us(n)}else m=!1;if(Oo(n,f),n.stateNode===null)ia(t,n),ah(n,s,u),Pu(n,s,u,f),u=!0;else if(t===null){var y=n.stateNode,k=n.memoizedProps;y.props=k;var L=y.context,$=s.contextType;typeof $=="object"&&$!==null?$=Zt($):($=Nt(s)?Hr:wt.current,$=Eo(n,$));var Y=s.getDerivedStateFromProps,J=typeof Y=="function"||typeof y.getSnapshotBeforeUpdate=="function";J||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(k!==u||L!==$)&&lh(n,y,u,$),pr=!1;var G=n.memoizedState;y.state=G,Js(n,u,y,f),L=n.memoizedState,k!==u||G!==L||_t.current||pr?(typeof Y=="function"&&(bu(n,s,Y,u),L=n.memoizedState),(k=pr||sh(n,s,k,u,G,L,$))?(J||typeof y.UNSAFE_componentWillMount!="function"&&typeof y.componentWillMount!="function"||(typeof y.componentWillMount=="function"&&y.componentWillMount(),typeof y.UNSAFE_componentWillMount=="function"&&y.UNSAFE_componentWillMount()),typeof y.componentDidMount=="function"&&(n.flags|=4194308)):(typeof y.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=u,n.memoizedState=L),y.props=u,y.state=L,y.context=$,u=k):(typeof y.componentDidMount=="function"&&(n.flags|=4194308),u=!1)}else{y=n.stateNode,Af(t,n),k=n.memoizedProps,$=n.type===n.elementType?k:gn(n.type,k),y.props=$,J=n.pendingProps,G=y.context,L=s.contextType,typeof L=="object"&&L!==null?L=Zt(L):(L=Nt(s)?Hr:wt.current,L=Eo(n,L));var ae=s.getDerivedStateFromProps;(Y=typeof ae=="function"||typeof y.getSnapshotBeforeUpdate=="function")||typeof y.UNSAFE_componentWillReceiveProps!="function"&&typeof y.componentWillReceiveProps!="function"||(k!==J||G!==L)&&lh(n,y,u,L),pr=!1,G=n.memoizedState,y.state=G,Js(n,u,y,f);var fe=n.memoizedState;k!==J||G!==fe||_t.current||pr?(typeof ae=="function"&&(bu(n,s,ae,u),fe=n.memoizedState),($=pr||sh(n,s,$,u,G,fe,L)||!1)?(Y||typeof y.UNSAFE_componentWillUpdate!="function"&&typeof y.componentWillUpdate!="function"||(typeof y.componentWillUpdate=="function"&&y.componentWillUpdate(u,fe,L),typeof y.UNSAFE_componentWillUpdate=="function"&&y.UNSAFE_componentWillUpdate(u,fe,L)),typeof y.componentDidUpdate=="function"&&(n.flags|=4),typeof y.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof y.componentDidUpdate!="function"||k===t.memoizedProps&&G===t.memoizedState||(n.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||k===t.memoizedProps&&G===t.memoizedState||(n.flags|=1024),n.memoizedProps=u,n.memoizedState=fe),y.props=u,y.state=fe,y.context=L,u=$):(typeof y.componentDidUpdate!="function"||k===t.memoizedProps&&G===t.memoizedState||(n.flags|=4),typeof y.getSnapshotBeforeUpdate!="function"||k===t.memoizedProps&&G===t.memoizedState||(n.flags|=1024),u=!1)}return Mu(t,n,s,u,m,f)}function Mu(t,n,s,u,f,m){yh(t,n);var y=(n.flags&128)!==0;if(!u&&!y)return f&&kf(n,s,!1),Kn(t,n,m);u=n.stateNode,Pw.current=n;var k=y&&typeof s.getDerivedStateFromError!="function"?null:u.render();return n.flags|=1,t!==null&&y?(n.child=Po(n,t.child,null,m),n.child=Po(n,null,k,m)):bt(t,n,k,m),n.memoizedState=u.state,f&&kf(n,s,!0),n.child}function xh(t){var n=t.stateNode;n.pendingContext?Ef(t,n.pendingContext,n.pendingContext!==n.context):n.context&&Ef(t,n.context,!1),hu(t,n.containerInfo)}function Sh(t,n,s,u,f){return bo(),su(f),n.flags|=256,bt(t,n,s,u),n.child}var Tu={dehydrated:null,treeContext:null,retryLane:0};function _u(t){return{baseLanes:t,cachePool:null,transitions:null}}function Eh(t,n,s){var u=n.pendingProps,f=et.current,m=!1,y=(n.flags&128)!==0,k;if((k=y)||(k=t!==null&&t.memoizedState===null?!1:(f&2)!==0),k?(m=!0,n.flags&=-129):(t===null||t.memoizedState!==null)&&(f|=1),Ge(et,f&1),t===null)return iu(n),t=n.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?((n.mode&1)===0?n.lanes=1:t.data==="$!"?n.lanes=8:n.lanes=1073741824,null):(y=u.children,t=u.fallback,m?(u=n.mode,m=n.child,y={mode:"hidden",children:y},(u&1)===0&&m!==null?(m.childLanes=0,m.pendingProps=y):m=va(y,u,0,null),t=qr(t,u,s,null),m.return=n,t.return=n,m.sibling=t,n.child=m,n.child.memoizedState=_u(s),n.memoizedState=Tu,t):Nu(n,y));if(f=t.memoizedState,f!==null&&(k=f.dehydrated,k!==null))return Rw(t,n,y,u,k,f,s);if(m){m=u.fallback,y=n.mode,f=t.child,k=f.sibling;var L={mode:"hidden",children:u.children};return(y&1)===0&&n.child!==f?(u=n.child,u.childLanes=0,u.pendingProps=L,n.deletions=null):(u=Sr(f,L),u.subtreeFlags=f.subtreeFlags&14680064),k!==null?m=Sr(k,m):(m=qr(m,y,s,null),m.flags|=2),m.return=n,u.return=n,u.sibling=m,n.child=u,u=m,m=n.child,y=t.child.memoizedState,y=y===null?_u(s):{baseLanes:y.baseLanes|s,cachePool:null,transitions:y.transitions},m.memoizedState=y,m.childLanes=t.childLanes&~s,n.memoizedState=Tu,u}return m=t.child,t=m.sibling,u=Sr(m,{mode:"visible",children:u.children}),(n.mode&1)===0&&(u.lanes=s),u.return=n,u.sibling=null,t!==null&&(s=n.deletions,s===null?(n.deletions=[t],n.flags|=16):s.push(t)),n.child=u,n.memoizedState=null,u}function Nu(t,n){return n=va({mode:"visible",children:n},t.mode,0,null),n.return=t,t.child=n}function oa(t,n,s,u){return u!==null&&su(u),Po(n,t.child,null,s),t=Nu(n,n.pendingProps.children),t.flags|=2,n.memoizedState=null,t}function Rw(t,n,s,u,f,m,y){if(s)return n.flags&256?(n.flags&=-257,u=Ru(Error(o(422))),oa(t,n,y,u)):n.memoizedState!==null?(n.child=t.child,n.flags|=128,null):(m=u.fallback,f=n.mode,u=va({mode:"visible",children:u.children},f,0,null),m=qr(m,f,y,null),m.flags|=2,u.return=n,m.return=n,u.sibling=m,n.child=u,(n.mode&1)!==0&&Po(n,t.child,null,y),n.child.memoizedState=_u(y),n.memoizedState=Tu,m);if((n.mode&1)===0)return oa(t,n,y,null);if(f.data==="$!"){if(u=f.nextSibling&&f.nextSibling.dataset,u)var k=u.dgst;return u=k,m=Error(o(419)),u=Ru(m,u,void 0),oa(t,n,y,u)}if(k=(y&t.childLanes)!==0,Dt||k){if(u=ht,u!==null){switch(y&-y){case 4:f=2;break;case 16:f=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:f=32;break;case 536870912:f=268435456;break;default:f=0}f=(f&(u.suspendedLanes|y))!==0?0:f,f!==0&&f!==m.retryLane&&(m.retryLane=f,Vn(t,f),wn(u,t,f,-1))}return Yu(),u=Ru(Error(o(421))),oa(t,n,y,u)}return f.data==="$?"?(n.flags|=128,n.child=t.child,n=$w.bind(null,t),f._reactRetry=n,null):(t=m.treeContext,Vt=cr(f.nextSibling),Bt=n,Ze=!0,mn=null,t!==null&&(Xt[qt++]=Hn,Xt[qt++]=Bn,Xt[qt++]=Br,Hn=t.id,Bn=t.overflow,Br=n),n=Nu(n,u.children),n.flags|=4096,n)}function Ch(t,n,s){t.lanes|=n;var u=t.alternate;u!==null&&(u.lanes|=n),cu(t.return,n,s)}function Du(t,n,s,u,f){var m=t.memoizedState;m===null?t.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:u,tail:s,tailMode:f}:(m.isBackwards=n,m.rendering=null,m.renderingStartTime=0,m.last=u,m.tail=s,m.tailMode=f)}function kh(t,n,s){var u=n.pendingProps,f=u.revealOrder,m=u.tail;if(bt(t,n,u.children,s),u=et.current,(u&2)!==0)u=u&1|2,n.flags|=128;else{if(t!==null&&(t.flags&128)!==0)e:for(t=n.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Ch(t,s,n);else if(t.tag===19)Ch(t,s,n);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===n)break e;for(;t.sibling===null;){if(t.return===null||t.return===n)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}u&=1}if(Ge(et,u),(n.mode&1)===0)n.memoizedState=null;else switch(f){case"forwards":for(s=n.child,f=null;s!==null;)t=s.alternate,t!==null&&Xs(t)===null&&(f=s),s=s.sibling;s=f,s===null?(f=n.child,n.child=null):(f=s.sibling,s.sibling=null),Du(n,!1,f,s,m);break;case"backwards":for(s=null,f=n.child,n.child=null;f!==null;){if(t=f.alternate,t!==null&&Xs(t)===null){n.child=f;break}t=f.sibling,f.sibling=s,s=f,f=t}Du(n,!0,s,null,m);break;case"together":Du(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function ia(t,n){(n.mode&1)===0&&t!==null&&(t.alternate=null,n.alternate=null,n.flags|=2)}function Kn(t,n,s){if(t!==null&&(n.dependencies=t.dependencies),Gr|=n.lanes,(s&n.childLanes)===0)return null;if(t!==null&&n.child!==t.child)throw Error(o(153));if(n.child!==null){for(t=n.child,s=Sr(t,t.pendingProps),n.child=s,s.return=n;t.sibling!==null;)t=t.sibling,s=s.sibling=Sr(t,t.pendingProps),s.return=n;s.sibling=null}return n.child}function Ow(t,n,s){switch(n.tag){case 3:xh(n),bo();break;case 5:jf(n);break;case 1:Nt(n.type)&&Us(n);break;case 4:hu(n,n.stateNode.containerInfo);break;case 10:var u=n.type._context,f=n.memoizedProps.value;Ge(Qs,u._currentValue),u._currentValue=f;break;case 13:if(u=n.memoizedState,u!==null)return u.dehydrated!==null?(Ge(et,et.current&1),n.flags|=128,null):(s&n.child.childLanes)!==0?Eh(t,n,s):(Ge(et,et.current&1),t=Kn(t,n,s),t!==null?t.sibling:null);Ge(et,et.current&1);break;case 19:if(u=(s&n.childLanes)!==0,(t.flags&128)!==0){if(u)return kh(t,n,s);n.flags|=128}if(f=n.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),Ge(et,et.current),u)break;return null;case 22:case 23:return n.lanes=0,vh(t,n,s)}return Kn(t,n,s)}var bh,Au,Ph,Rh;bh=function(t,n){for(var s=n.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===n)break;for(;s.sibling===null;){if(s.return===null||s.return===n)return;s=s.return}s.sibling.return=s.return,s=s.sibling}},Au=function(){},Ph=function(t,n,s,u){var f=t.memoizedProps;if(f!==u){t=n.stateNode,Kr(Rn.current);var m=null;switch(s){case"input":f=cn(t,f),u=cn(t,u),m=[];break;case"select":f=Q({},f,{value:void 0}),u=Q({},u,{value:void 0}),m=[];break;case"textarea":f=dn(t,f),u=dn(t,u),m=[];break;default:typeof f.onClick!="function"&&typeof u.onClick=="function"&&(t.onclick=js)}uo(s,u);var y;s=null;for($ in f)if(!u.hasOwnProperty($)&&f.hasOwnProperty($)&&f[$]!=null)if($==="style"){var k=f[$];for(y in k)k.hasOwnProperty(y)&&(s||(s={}),s[y]="")}else $!=="dangerouslySetInnerHTML"&&$!=="children"&&$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&$!=="autoFocus"&&(a.hasOwnProperty($)?m||(m=[]):(m=m||[]).push($,null));for($ in u){var L=u[$];if(k=f?.[$],u.hasOwnProperty($)&&L!==k&&(L!=null||k!=null))if($==="style")if(k){for(y in k)!k.hasOwnProperty(y)||L&&L.hasOwnProperty(y)||(s||(s={}),s[y]="");for(y in L)L.hasOwnProperty(y)&&k[y]!==L[y]&&(s||(s={}),s[y]=L[y])}else s||(m||(m=[]),m.push($,s)),s=L;else $==="dangerouslySetInnerHTML"?(L=L?L.__html:void 0,k=k?k.__html:void 0,L!=null&&k!==L&&(m=m||[]).push($,L)):$==="children"?typeof L!="string"&&typeof L!="number"||(m=m||[]).push($,""+L):$!=="suppressContentEditableWarning"&&$!=="suppressHydrationWarning"&&(a.hasOwnProperty($)?(L!=null&&$==="onScroll"&&Ye("scroll",t),m||k===L||(m=[])):(m=m||[]).push($,L))}s&&(m=m||[]).push("style",s);var $=m;(n.updateQueue=$)&&(n.flags|=4)}},Rh=function(t,n,s,u){s!==u&&(n.flags|=4)};function Ni(t,n){if(!Ze)switch(t.tailMode){case"hidden":n=t.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?t.tail=null:s.sibling=null;break;case"collapsed":s=t.tail;for(var u=null;s!==null;)s.alternate!==null&&(u=s),s=s.sibling;u===null?n||t.tail===null?t.tail=null:t.tail.sibling=null:u.sibling=null}}function St(t){var n=t.alternate!==null&&t.alternate.child===t.child,s=0,u=0;if(n)for(var f=t.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags&14680064,u|=f.flags&14680064,f.return=t,f=f.sibling;else for(f=t.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags,u|=f.flags,f.return=t,f=f.sibling;return t.subtreeFlags|=u,t.childLanes=s,n}function Lw(t,n,s){var u=n.pendingProps;switch(ru(n),n.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return St(n),null;case 1:return Nt(n.type)&&$s(),St(n),null;case 3:return u=n.stateNode,Lo(),Je(_t),Je(wt),gu(),u.pendingContext&&(u.context=u.pendingContext,u.pendingContext=null),(t===null||t.child===null)&&(Ws(n)?n.flags|=4:t===null||t.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,mn!==null&&(Ku(mn),mn=null))),Au(t,n),St(n),null;case 5:pu(n);var f=Kr(Oi.current);if(s=n.type,t!==null&&n.stateNode!=null)Ph(t,n,s,u,f),t.ref!==n.ref&&(n.flags|=512,n.flags|=2097152);else{if(!u){if(n.stateNode===null)throw Error(o(166));return St(n),null}if(t=Kr(Rn.current),Ws(n)){u=n.stateNode,s=n.type;var m=n.memoizedProps;switch(u[Pn]=n,u[Ci]=m,t=(n.mode&1)!==0,s){case"dialog":Ye("cancel",u),Ye("close",u);break;case"iframe":case"object":case"embed":Ye("load",u);break;case"video":case"audio":for(f=0;f<xi.length;f++)Ye(xi[f],u);break;case"source":Ye("error",u);break;case"img":case"image":case"link":Ye("error",u),Ye("load",u);break;case"details":Ye("toggle",u);break;case"input":ms(u,m),Ye("invalid",u);break;case"select":u._wrapperState={wasMultiple:!!m.multiple},Ye("invalid",u);break;case"textarea":Zn(u,m),Ye("invalid",u)}uo(s,m),f=null;for(var y in m)if(m.hasOwnProperty(y)){var k=m[y];y==="children"?typeof k=="string"?u.textContent!==k&&(m.suppressHydrationWarning!==!0&&Fs(u.textContent,k,t),f=["children",k]):typeof k=="number"&&u.textContent!==""+k&&(m.suppressHydrationWarning!==!0&&Fs(u.textContent,k,t),f=["children",""+k]):a.hasOwnProperty(y)&&k!=null&&y==="onScroll"&&Ye("scroll",u)}switch(s){case"input":mt(u),vs(u,m,!0);break;case"textarea":mt(u),ao(u);break;case"select":case"option":break;default:typeof m.onClick=="function"&&(u.onclick=js)}u=f,n.updateQueue=u,u!==null&&(n.flags|=4)}else{y=f.nodeType===9?f:f.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=fn(s)),t==="http://www.w3.org/1999/xhtml"?s==="script"?(t=y.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof u.is=="string"?t=y.createElement(s,{is:u.is}):(t=y.createElement(s),s==="select"&&(y=t,u.multiple?y.multiple=!0:u.size&&(y.size=u.size))):t=y.createElementNS(t,s),t[Pn]=n,t[Ci]=u,bh(t,n,!1,!1),n.stateNode=t;e:{switch(y=er(s,u),s){case"dialog":Ye("cancel",t),Ye("close",t),f=u;break;case"iframe":case"object":case"embed":Ye("load",t),f=u;break;case"video":case"audio":for(f=0;f<xi.length;f++)Ye(xi[f],t);f=u;break;case"source":Ye("error",t),f=u;break;case"img":case"image":case"link":Ye("error",t),Ye("load",t),f=u;break;case"details":Ye("toggle",t),f=u;break;case"input":ms(t,u),f=cn(t,u),Ye("invalid",t);break;case"option":f=u;break;case"select":t._wrapperState={wasMultiple:!!u.multiple},f=Q({},u,{value:void 0}),Ye("invalid",t);break;case"textarea":Zn(t,u),f=dn(t,u),Ye("invalid",t);break;default:f=u}uo(s,f),k=f;for(m in k)if(k.hasOwnProperty(m)){var L=k[m];m==="style"?ni(t,L):m==="dangerouslySetInnerHTML"?(L=L?L.__html:void 0,L!=null&&lo(t,L)):m==="children"?typeof L=="string"?(s!=="textarea"||L!=="")&&Ar(t,L):typeof L=="number"&&Ar(t,""+L):m!=="suppressContentEditableWarning"&&m!=="suppressHydrationWarning"&&m!=="autoFocus"&&(a.hasOwnProperty(m)?L!=null&&m==="onScroll"&&Ye("scroll",t):L!=null&&N(t,m,L,y))}switch(s){case"input":mt(t),vs(t,u,!1);break;case"textarea":mt(t),ao(t);break;case"option":u.value!=null&&t.setAttribute("value",""+_e(u.value));break;case"select":t.multiple=!!u.multiple,m=u.value,m!=null?qn(t,!!u.multiple,m,!1):u.defaultValue!=null&&qn(t,!!u.multiple,u.defaultValue,!0);break;default:typeof f.onClick=="function"&&(t.onclick=js)}switch(s){case"button":case"input":case"select":case"textarea":u=!!u.autoFocus;break e;case"img":u=!0;break e;default:u=!1}}u&&(n.flags|=4)}n.ref!==null&&(n.flags|=512,n.flags|=2097152)}return St(n),null;case 6:if(t&&n.stateNode!=null)Rh(t,n,t.memoizedProps,u);else{if(typeof u!="string"&&n.stateNode===null)throw Error(o(166));if(s=Kr(Oi.current),Kr(Rn.current),Ws(n)){if(u=n.stateNode,s=n.memoizedProps,u[Pn]=n,(m=u.nodeValue!==s)&&(t=Bt,t!==null))switch(t.tag){case 3:Fs(u.nodeValue,s,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Fs(u.nodeValue,s,(t.mode&1)!==0)}m&&(n.flags|=4)}else u=(s.nodeType===9?s:s.ownerDocument).createTextNode(u),u[Pn]=n,n.stateNode=u}return St(n),null;case 13:if(Je(et),u=n.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(Ze&&Vt!==null&&(n.mode&1)!==0&&(n.flags&128)===0)Mf(),bo(),n.flags|=98560,m=!1;else if(m=Ws(n),u!==null&&u.dehydrated!==null){if(t===null){if(!m)throw Error(o(318));if(m=n.memoizedState,m=m!==null?m.dehydrated:null,!m)throw Error(o(317));m[Pn]=n}else bo(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;St(n),m=!1}else mn!==null&&(Ku(mn),mn=null),m=!0;if(!m)return n.flags&65536?n:null}return(n.flags&128)!==0?(n.lanes=s,n):(u=u!==null,u!==(t!==null&&t.memoizedState!==null)&&u&&(n.child.flags|=8192,(n.mode&1)!==0&&(t===null||(et.current&1)!==0?lt===0&&(lt=3):Yu())),n.updateQueue!==null&&(n.flags|=4),St(n),null);case 4:return Lo(),Au(t,n),t===null&&Si(n.stateNode.containerInfo),St(n),null;case 10:return uu(n.type._context),St(n),null;case 17:return Nt(n.type)&&$s(),St(n),null;case 19:if(Je(et),m=n.memoizedState,m===null)return St(n),null;if(u=(n.flags&128)!==0,y=m.rendering,y===null)if(u)Ni(m,!1);else{if(lt!==0||t!==null&&(t.flags&128)!==0)for(t=n.child;t!==null;){if(y=Xs(t),y!==null){for(n.flags|=128,Ni(m,!1),u=y.updateQueue,u!==null&&(n.updateQueue=u,n.flags|=4),n.subtreeFlags=0,u=s,s=n.child;s!==null;)m=s,t=u,m.flags&=14680066,y=m.alternate,y===null?(m.childLanes=0,m.lanes=t,m.child=null,m.subtreeFlags=0,m.memoizedProps=null,m.memoizedState=null,m.updateQueue=null,m.dependencies=null,m.stateNode=null):(m.childLanes=y.childLanes,m.lanes=y.lanes,m.child=y.child,m.subtreeFlags=0,m.deletions=null,m.memoizedProps=y.memoizedProps,m.memoizedState=y.memoizedState,m.updateQueue=y.updateQueue,m.type=y.type,t=y.dependencies,m.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),s=s.sibling;return Ge(et,et.current&1|2),n.child}t=t.sibling}m.tail!==null&&Qe()>No&&(n.flags|=128,u=!0,Ni(m,!1),n.lanes=4194304)}else{if(!u)if(t=Xs(y),t!==null){if(n.flags|=128,u=!0,s=t.updateQueue,s!==null&&(n.updateQueue=s,n.flags|=4),Ni(m,!0),m.tail===null&&m.tailMode==="hidden"&&!y.alternate&&!Ze)return St(n),null}else 2*Qe()-m.renderingStartTime>No&&s!==1073741824&&(n.flags|=128,u=!0,Ni(m,!1),n.lanes=4194304);m.isBackwards?(y.sibling=n.child,n.child=y):(s=m.last,s!==null?s.sibling=y:n.child=y,m.last=y)}return m.tail!==null?(n=m.tail,m.rendering=n,m.tail=n.sibling,m.renderingStartTime=Qe(),n.sibling=null,s=et.current,Ge(et,u?s&1|2:s&1),n):(St(n),null);case 22:case 23:return Gu(),u=n.memoizedState!==null,t!==null&&t.memoizedState!==null!==u&&(n.flags|=8192),u&&(n.mode&1)!==0?(Wt&1073741824)!==0&&(St(n),n.subtreeFlags&6&&(n.flags|=8192)):St(n),null;case 24:return null;case 25:return null}throw Error(o(156,n.tag))}function Mw(t,n){switch(ru(n),n.tag){case 1:return Nt(n.type)&&$s(),t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 3:return Lo(),Je(_t),Je(wt),gu(),t=n.flags,(t&65536)!==0&&(t&128)===0?(n.flags=t&-65537|128,n):null;case 5:return pu(n),null;case 13:if(Je(et),t=n.memoizedState,t!==null&&t.dehydrated!==null){if(n.alternate===null)throw Error(o(340));bo()}return t=n.flags,t&65536?(n.flags=t&-65537|128,n):null;case 19:return Je(et),null;case 4:return Lo(),null;case 10:return uu(n.type._context),null;case 22:case 23:return Gu(),null;case 24:return null;default:return null}}var sa=!1,Et=!1,Tw=typeof WeakSet=="function"?WeakSet:Set,ce=null;function To(t,n){var s=t.ref;if(s!==null)if(typeof s=="function")try{s(null)}catch(u){nt(t,n,u)}else s.current=null}function Iu(t,n,s){try{s()}catch(u){nt(t,n,u)}}var Oh=!1;function _w(t,n){if(Gl=Ps,t=sf(),$l(t)){if("selectionStart"in t)var s={start:t.selectionStart,end:t.selectionEnd};else e:{s=(s=t.ownerDocument)&&s.defaultView||window;var u=s.getSelection&&s.getSelection();if(u&&u.rangeCount!==0){s=u.anchorNode;var f=u.anchorOffset,m=u.focusNode;u=u.focusOffset;try{s.nodeType,m.nodeType}catch{s=null;break e}var y=0,k=-1,L=-1,$=0,Y=0,J=t,G=null;t:for(;;){for(var ae;J!==s||f!==0&&J.nodeType!==3||(k=y+f),J!==m||u!==0&&J.nodeType!==3||(L=y+u),J.nodeType===3&&(y+=J.nodeValue.length),(ae=J.firstChild)!==null;)G=J,J=ae;for(;;){if(J===t)break t;if(G===s&&++$===f&&(k=y),G===m&&++Y===u&&(L=y),(ae=J.nextSibling)!==null)break;J=G,G=J.parentNode}J=ae}s=k===-1||L===-1?null:{start:k,end:L}}else s=null}s=s||{start:0,end:0}}else s=null;for(Yl={focusedElem:t,selectionRange:s},Ps=!1,ce=n;ce!==null;)if(n=ce,t=n.child,(n.subtreeFlags&1028)!==0&&t!==null)t.return=n,ce=t;else for(;ce!==null;){n=ce;try{var fe=n.alternate;if((n.flags&1024)!==0)switch(n.tag){case 0:case 11:case 15:break;case 1:if(fe!==null){var me=fe.memoizedProps,ot=fe.memoizedState,I=n.stateNode,T=I.getSnapshotBeforeUpdate(n.elementType===n.type?me:gn(n.type,me),ot);I.__reactInternalSnapshotBeforeUpdate=T}break;case 3:var z=n.stateNode.containerInfo;z.nodeType===1?z.textContent="":z.nodeType===9&&z.documentElement&&z.removeChild(z.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(ee){nt(n,n.return,ee)}if(t=n.sibling,t!==null){t.return=n.return,ce=t;break}ce=n.return}return fe=Oh,Oh=!1,fe}function Di(t,n,s){var u=n.updateQueue;if(u=u!==null?u.lastEffect:null,u!==null){var f=u=u.next;do{if((f.tag&t)===t){var m=f.destroy;f.destroy=void 0,m!==void 0&&Iu(n,s,m)}f=f.next}while(f!==u)}}function aa(t,n){if(n=n.updateQueue,n=n!==null?n.lastEffect:null,n!==null){var s=n=n.next;do{if((s.tag&t)===t){var u=s.create;s.destroy=u()}s=s.next}while(s!==n)}}function Fu(t){var n=t.ref;if(n!==null){var s=t.stateNode;switch(t.tag){case 5:t=s;break;default:t=s}typeof n=="function"?n(t):n.current=t}}function Lh(t){var n=t.alternate;n!==null&&(t.alternate=null,Lh(n)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(n=t.stateNode,n!==null&&(delete n[Pn],delete n[Ci],delete n[Zl],delete n[pw],delete n[mw])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Mh(t){return t.tag===5||t.tag===3||t.tag===4}function Th(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Mh(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function ju(t,n,s){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?s.nodeType===8?s.parentNode.insertBefore(t,n):s.insertBefore(t,n):(s.nodeType===8?(n=s.parentNode,n.insertBefore(t,s)):(n=s,n.appendChild(t)),s=s._reactRootContainer,s!=null||n.onclick!==null||(n.onclick=js));else if(u!==4&&(t=t.child,t!==null))for(ju(t,n,s),t=t.sibling;t!==null;)ju(t,n,s),t=t.sibling}function zu(t,n,s){var u=t.tag;if(u===5||u===6)t=t.stateNode,n?s.insertBefore(t,n):s.appendChild(t);else if(u!==4&&(t=t.child,t!==null))for(zu(t,n,s),t=t.sibling;t!==null;)zu(t,n,s),t=t.sibling}var gt=null,vn=!1;function gr(t,n,s){for(s=s.child;s!==null;)_h(t,n,s),s=s.sibling}function _h(t,n,s){if(Ve&&typeof Ve.onCommitFiberUnmount=="function")try{Ve.onCommitFiberUnmount(bn,s)}catch{}switch(s.tag){case 5:Et||To(s,n);case 6:var u=gt,f=vn;gt=null,gr(t,n,s),gt=u,vn=f,gt!==null&&(vn?(t=gt,s=s.stateNode,t.nodeType===8?t.parentNode.removeChild(s):t.removeChild(s)):gt.removeChild(s.stateNode));break;case 18:gt!==null&&(vn?(t=gt,s=s.stateNode,t.nodeType===8?ql(t.parentNode,s):t.nodeType===1&&ql(t,s),fi(t)):ql(gt,s.stateNode));break;case 4:u=gt,f=vn,gt=s.stateNode.containerInfo,vn=!0,gr(t,n,s),gt=u,vn=f;break;case 0:case 11:case 14:case 15:if(!Et&&(u=s.updateQueue,u!==null&&(u=u.lastEffect,u!==null))){f=u=u.next;do{var m=f,y=m.destroy;m=m.tag,y!==void 0&&((m&2)!==0||(m&4)!==0)&&Iu(s,n,y),f=f.next}while(f!==u)}gr(t,n,s);break;case 1:if(!Et&&(To(s,n),u=s.stateNode,typeof u.componentWillUnmount=="function"))try{u.props=s.memoizedProps,u.state=s.memoizedState,u.componentWillUnmount()}catch(k){nt(s,n,k)}gr(t,n,s);break;case 21:gr(t,n,s);break;case 22:s.mode&1?(Et=(u=Et)||s.memoizedState!==null,gr(t,n,s),Et=u):gr(t,n,s);break;default:gr(t,n,s)}}function Nh(t){var n=t.updateQueue;if(n!==null){t.updateQueue=null;var s=t.stateNode;s===null&&(s=t.stateNode=new Tw),n.forEach(function(u){var f=Uw.bind(null,t,u);s.has(u)||(s.add(u),u.then(f,f))})}}function yn(t,n){var s=n.deletions;if(s!==null)for(var u=0;u<s.length;u++){var f=s[u];try{var m=t,y=n,k=y;e:for(;k!==null;){switch(k.tag){case 5:gt=k.stateNode,vn=!1;break e;case 3:gt=k.stateNode.containerInfo,vn=!0;break e;case 4:gt=k.stateNode.containerInfo,vn=!0;break e}k=k.return}if(gt===null)throw Error(o(160));_h(m,y,f),gt=null,vn=!1;var L=f.alternate;L!==null&&(L.return=null),f.return=null}catch($){nt(f,n,$)}}if(n.subtreeFlags&12854)for(n=n.child;n!==null;)Dh(n,t),n=n.sibling}function Dh(t,n){var s=t.alternate,u=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(yn(n,t),Ln(t),u&4){try{Di(3,t,t.return),aa(3,t)}catch(me){nt(t,t.return,me)}try{Di(5,t,t.return)}catch(me){nt(t,t.return,me)}}break;case 1:yn(n,t),Ln(t),u&512&&s!==null&&To(s,s.return);break;case 5:if(yn(n,t),Ln(t),u&512&&s!==null&&To(s,s.return),t.flags&32){var f=t.stateNode;try{Ar(f,"")}catch(me){nt(t,t.return,me)}}if(u&4&&(f=t.stateNode,f!=null)){var m=t.memoizedProps,y=s!==null?s.memoizedProps:m,k=t.type,L=t.updateQueue;if(t.updateQueue=null,L!==null)try{k==="input"&&m.type==="radio"&&m.name!=null&&gs(f,m),er(k,y);var $=er(k,m);for(y=0;y<L.length;y+=2){var Y=L[y],J=L[y+1];Y==="style"?ni(f,J):Y==="dangerouslySetInnerHTML"?lo(f,J):Y==="children"?Ar(f,J):N(f,Y,J,$)}switch(k){case"input":so(f,m);break;case"textarea":ei(f,m);break;case"select":var G=f._wrapperState.wasMultiple;f._wrapperState.wasMultiple=!!m.multiple;var ae=m.value;ae!=null?qn(f,!!m.multiple,ae,!1):G!==!!m.multiple&&(m.defaultValue!=null?qn(f,!!m.multiple,m.defaultValue,!0):qn(f,!!m.multiple,m.multiple?[]:"",!1))}f[Ci]=m}catch(me){nt(t,t.return,me)}}break;case 6:if(yn(n,t),Ln(t),u&4){if(t.stateNode===null)throw Error(o(162));f=t.stateNode,m=t.memoizedProps;try{f.nodeValue=m}catch(me){nt(t,t.return,me)}}break;case 3:if(yn(n,t),Ln(t),u&4&&s!==null&&s.memoizedState.isDehydrated)try{fi(n.containerInfo)}catch(me){nt(t,t.return,me)}break;case 4:yn(n,t),Ln(t);break;case 13:yn(n,t),Ln(t),f=t.child,f.flags&8192&&(m=f.memoizedState!==null,f.stateNode.isHidden=m,!m||f.alternate!==null&&f.alternate.memoizedState!==null||(Hu=Qe())),u&4&&Nh(t);break;case 22:if(Y=s!==null&&s.memoizedState!==null,t.mode&1?(Et=($=Et)||Y,yn(n,t),Et=$):yn(n,t),Ln(t),u&8192){if($=t.memoizedState!==null,(t.stateNode.isHidden=$)&&!Y&&(t.mode&1)!==0)for(ce=t,Y=t.child;Y!==null;){for(J=ce=Y;ce!==null;){switch(G=ce,ae=G.child,G.tag){case 0:case 11:case 14:case 15:Di(4,G,G.return);break;case 1:To(G,G.return);var fe=G.stateNode;if(typeof fe.componentWillUnmount=="function"){u=G,s=G.return;try{n=u,fe.props=n.memoizedProps,fe.state=n.memoizedState,fe.componentWillUnmount()}catch(me){nt(u,s,me)}}break;case 5:To(G,G.return);break;case 22:if(G.memoizedState!==null){Fh(J);continue}}ae!==null?(ae.return=G,ce=ae):Fh(J)}Y=Y.sibling}e:for(Y=null,J=t;;){if(J.tag===5){if(Y===null){Y=J;try{f=J.stateNode,$?(m=f.style,typeof m.setProperty=="function"?m.setProperty("display","none","important"):m.display="none"):(k=J.stateNode,L=J.memoizedProps.style,y=L!=null&&L.hasOwnProperty("display")?L.display:null,k.style.display=ti("display",y))}catch(me){nt(t,t.return,me)}}}else if(J.tag===6){if(Y===null)try{J.stateNode.nodeValue=$?"":J.memoizedProps}catch(me){nt(t,t.return,me)}}else if((J.tag!==22&&J.tag!==23||J.memoizedState===null||J===t)&&J.child!==null){J.child.return=J,J=J.child;continue}if(J===t)break e;for(;J.sibling===null;){if(J.return===null||J.return===t)break e;Y===J&&(Y=null),J=J.return}Y===J&&(Y=null),J.sibling.return=J.return,J=J.sibling}}break;case 19:yn(n,t),Ln(t),u&4&&Nh(t);break;case 21:break;default:yn(n,t),Ln(t)}}function Ln(t){var n=t.flags;if(n&2){try{e:{for(var s=t.return;s!==null;){if(Mh(s)){var u=s;break e}s=s.return}throw Error(o(160))}switch(u.tag){case 5:var f=u.stateNode;u.flags&32&&(Ar(f,""),u.flags&=-33);var m=Th(t);zu(t,m,f);break;case 3:case 4:var y=u.stateNode.containerInfo,k=Th(t);ju(t,k,y);break;default:throw Error(o(161))}}catch(L){nt(t,t.return,L)}t.flags&=-3}n&4096&&(t.flags&=-4097)}function Nw(t,n,s){ce=t,Ah(t)}function Ah(t,n,s){for(var u=(t.mode&1)!==0;ce!==null;){var f=ce,m=f.child;if(f.tag===22&&u){var y=f.memoizedState!==null||sa;if(!y){var k=f.alternate,L=k!==null&&k.memoizedState!==null||Et;k=sa;var $=Et;if(sa=y,(Et=L)&&!$)for(ce=f;ce!==null;)y=ce,L=y.child,y.tag===22&&y.memoizedState!==null?jh(f):L!==null?(L.return=y,ce=L):jh(f);for(;m!==null;)ce=m,Ah(m),m=m.sibling;ce=f,sa=k,Et=$}Ih(t)}else(f.subtreeFlags&8772)!==0&&m!==null?(m.return=f,ce=m):Ih(t)}}function Ih(t){for(;ce!==null;){var n=ce;if((n.flags&8772)!==0){var s=n.alternate;try{if((n.flags&8772)!==0)switch(n.tag){case 0:case 11:case 15:Et||aa(5,n);break;case 1:var u=n.stateNode;if(n.flags&4&&!Et)if(s===null)u.componentDidMount();else{var f=n.elementType===n.type?s.memoizedProps:gn(n.type,s.memoizedProps);u.componentDidUpdate(f,s.memoizedState,u.__reactInternalSnapshotBeforeUpdate)}var m=n.updateQueue;m!==null&&Ff(n,m,u);break;case 3:var y=n.updateQueue;if(y!==null){if(s=null,n.child!==null)switch(n.child.tag){case 5:s=n.child.stateNode;break;case 1:s=n.child.stateNode}Ff(n,y,s)}break;case 5:var k=n.stateNode;if(s===null&&n.flags&4){s=k;var L=n.memoizedProps;switch(n.type){case"button":case"input":case"select":case"textarea":L.autoFocus&&s.focus();break;case"img":L.src&&(s.src=L.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(n.memoizedState===null){var $=n.alternate;if($!==null){var Y=$.memoizedState;if(Y!==null){var J=Y.dehydrated;J!==null&&fi(J)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}Et||n.flags&512&&Fu(n)}catch(G){nt(n,n.return,G)}}if(n===t){ce=null;break}if(s=n.sibling,s!==null){s.return=n.return,ce=s;break}ce=n.return}}function Fh(t){for(;ce!==null;){var n=ce;if(n===t){ce=null;break}var s=n.sibling;if(s!==null){s.return=n.return,ce=s;break}ce=n.return}}function jh(t){for(;ce!==null;){var n=ce;try{switch(n.tag){case 0:case 11:case 15:var s=n.return;try{aa(4,n)}catch(L){nt(n,s,L)}break;case 1:var u=n.stateNode;if(typeof u.componentDidMount=="function"){var f=n.return;try{u.componentDidMount()}catch(L){nt(n,f,L)}}var m=n.return;try{Fu(n)}catch(L){nt(n,m,L)}break;case 5:var y=n.return;try{Fu(n)}catch(L){nt(n,y,L)}}}catch(L){nt(n,n.return,L)}if(n===t){ce=null;break}var k=n.sibling;if(k!==null){k.return=n.return,ce=k;break}ce=n.return}}var Dw=Math.ceil,la=j.ReactCurrentDispatcher,$u=j.ReactCurrentOwner,tn=j.ReactCurrentBatchConfig,Ue=0,ht=null,it=null,vt=0,Wt=0,_o=dr(0),lt=0,Ai=null,Gr=0,ua=0,Uu=0,Ii=null,At=null,Hu=0,No=1/0,Qn=null,ca=!1,Bu=null,vr=null,da=!1,yr=null,fa=0,Fi=0,Vu=null,ha=-1,pa=0;function Pt(){return(Ue&6)!==0?Qe():ha!==-1?ha:ha=Qe()}function wr(t){return(t.mode&1)===0?1:(Ue&2)!==0&&vt!==0?vt&-vt:vw.transition!==null?(pa===0&&(pa=Md()),pa):(t=We,t!==0||(t=window.event,t=t===void 0?16:zd(t.type)),t)}function wn(t,n,s,u){if(50<Fi)throw Fi=0,Vu=null,Error(o(185));ai(t,s,u),((Ue&2)===0||t!==ht)&&(t===ht&&((Ue&2)===0&&(ua|=s),lt===4&&xr(t,vt)),It(t,u),s===1&&Ue===0&&(n.mode&1)===0&&(No=Qe()+500,Hs&&hr()))}function It(t,n){var s=t.callbackNode;vy(t,n);var u=Cs(t,t===ht?vt:0);if(u===0)s!==null&&hn(s),t.callbackNode=null,t.callbackPriority=0;else if(n=u&-u,t.callbackPriority!==n){if(s!=null&&hn(s),n===1)t.tag===0?gw($h.bind(null,t)):bf($h.bind(null,t)),fw(function(){(Ue&6)===0&&hr()}),s=null;else{switch(Td(u)){case 1:s=rr;break;case 4:s=zr;break;case 16:s=$e;break;case 536870912:s=$r;break;default:s=$e}s=Gh(s,zh.bind(null,t))}t.callbackPriority=n,t.callbackNode=s}}function zh(t,n){if(ha=-1,pa=0,(Ue&6)!==0)throw Error(o(327));var s=t.callbackNode;if(Do()&&t.callbackNode!==s)return null;var u=Cs(t,t===ht?vt:0);if(u===0)return null;if((u&30)!==0||(u&t.expiredLanes)!==0||n)n=ma(t,u);else{n=u;var f=Ue;Ue|=2;var m=Hh();(ht!==t||vt!==n)&&(Qn=null,No=Qe()+500,Jr(t,n));do try{Fw();break}catch(k){Uh(t,k)}while(!0);lu(),la.current=m,Ue=f,it!==null?n=0:(ht=null,vt=0,n=lt)}if(n!==0){if(n===2&&(f=kl(t),f!==0&&(u=f,n=Wu(t,f))),n===1)throw s=Ai,Jr(t,0),xr(t,u),It(t,Qe()),s;if(n===6)xr(t,u);else{if(f=t.current.alternate,(u&30)===0&&!Aw(f)&&(n=ma(t,u),n===2&&(m=kl(t),m!==0&&(u=m,n=Wu(t,m))),n===1))throw s=Ai,Jr(t,0),xr(t,u),It(t,Qe()),s;switch(t.finishedWork=f,t.finishedLanes=u,n){case 0:case 1:throw Error(o(345));case 2:Xr(t,At,Qn);break;case 3:if(xr(t,u),(u&130023424)===u&&(n=Hu+500-Qe(),10<n)){if(Cs(t,0)!==0)break;if(f=t.suspendedLanes,(f&u)!==u){Pt(),t.pingedLanes|=t.suspendedLanes&f;break}t.timeoutHandle=Xl(Xr.bind(null,t,At,Qn),n);break}Xr(t,At,Qn);break;case 4:if(xr(t,u),(u&4194240)===u)break;for(n=t.eventTimes,f=-1;0<u;){var y=31-Tt(u);m=1<<y,y=n[y],y>f&&(f=y),u&=~m}if(u=f,u=Qe()-u,u=(120>u?120:480>u?480:1080>u?1080:1920>u?1920:3e3>u?3e3:4320>u?4320:1960*Dw(u/1960))-u,10<u){t.timeoutHandle=Xl(Xr.bind(null,t,At,Qn),u);break}Xr(t,At,Qn);break;case 5:Xr(t,At,Qn);break;default:throw Error(o(329))}}}return It(t,Qe()),t.callbackNode===s?zh.bind(null,t):null}function Wu(t,n){var s=Ii;return t.current.memoizedState.isDehydrated&&(Jr(t,n).flags|=256),t=ma(t,n),t!==2&&(n=At,At=s,n!==null&&Ku(n)),t}function Ku(t){At===null?At=t:At.push.apply(At,t)}function Aw(t){for(var n=t;;){if(n.flags&16384){var s=n.updateQueue;if(s!==null&&(s=s.stores,s!==null))for(var u=0;u<s.length;u++){var f=s[u],m=f.getSnapshot;f=f.value;try{if(!pn(m(),f))return!1}catch{return!1}}}if(s=n.child,n.subtreeFlags&16384&&s!==null)s.return=n,n=s;else{if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function xr(t,n){for(n&=~Uu,n&=~ua,t.suspendedLanes|=n,t.pingedLanes&=~n,t=t.expirationTimes;0<n;){var s=31-Tt(n),u=1<<s;t[s]=-1,n&=~u}}function $h(t){if((Ue&6)!==0)throw Error(o(327));Do();var n=Cs(t,0);if((n&1)===0)return It(t,Qe()),null;var s=ma(t,n);if(t.tag!==0&&s===2){var u=kl(t);u!==0&&(n=u,s=Wu(t,u))}if(s===1)throw s=Ai,Jr(t,0),xr(t,n),It(t,Qe()),s;if(s===6)throw Error(o(345));return t.finishedWork=t.current.alternate,t.finishedLanes=n,Xr(t,At,Qn),It(t,Qe()),null}function Qu(t,n){var s=Ue;Ue|=1;try{return t(n)}finally{Ue=s,Ue===0&&(No=Qe()+500,Hs&&hr())}}function Yr(t){yr!==null&&yr.tag===0&&(Ue&6)===0&&Do();var n=Ue;Ue|=1;var s=tn.transition,u=We;try{if(tn.transition=null,We=1,t)return t()}finally{We=u,tn.transition=s,Ue=n,(Ue&6)===0&&hr()}}function Gu(){Wt=_o.current,Je(_o)}function Jr(t,n){t.finishedWork=null,t.finishedLanes=0;var s=t.timeoutHandle;if(s!==-1&&(t.timeoutHandle=-1,dw(s)),it!==null)for(s=it.return;s!==null;){var u=s;switch(ru(u),u.tag){case 1:u=u.type.childContextTypes,u!=null&&$s();break;case 3:Lo(),Je(_t),Je(wt),gu();break;case 5:pu(u);break;case 4:Lo();break;case 13:Je(et);break;case 19:Je(et);break;case 10:uu(u.type._context);break;case 22:case 23:Gu()}s=s.return}if(ht=t,it=t=Sr(t.current,null),vt=Wt=n,lt=0,Ai=null,Uu=ua=Gr=0,At=Ii=null,Wr!==null){for(n=0;n<Wr.length;n++)if(s=Wr[n],u=s.interleaved,u!==null){s.interleaved=null;var f=u.next,m=s.pending;if(m!==null){var y=m.next;m.next=f,u.next=y}s.pending=u}Wr=null}return t}function Uh(t,n){do{var s=it;try{if(lu(),qs.current=na,Zs){for(var u=tt.memoizedState;u!==null;){var f=u.queue;f!==null&&(f.pending=null),u=u.next}Zs=!1}if(Qr=0,ft=at=tt=null,Li=!1,Mi=0,$u.current=null,s===null||s.return===null){lt=1,Ai=n,it=null;break}e:{var m=t,y=s.return,k=s,L=n;if(n=vt,k.flags|=32768,L!==null&&typeof L=="object"&&typeof L.then=="function"){var $=L,Y=k,J=Y.tag;if((Y.mode&1)===0&&(J===0||J===11||J===15)){var G=Y.alternate;G?(Y.updateQueue=G.updateQueue,Y.memoizedState=G.memoizedState,Y.lanes=G.lanes):(Y.updateQueue=null,Y.memoizedState=null)}var ae=fh(y);if(ae!==null){ae.flags&=-257,hh(ae,y,k,m,n),ae.mode&1&&dh(m,$,n),n=ae,L=$;var fe=n.updateQueue;if(fe===null){var me=new Set;me.add(L),n.updateQueue=me}else fe.add(L);break e}else{if((n&1)===0){dh(m,$,n),Yu();break e}L=Error(o(426))}}else if(Ze&&k.mode&1){var ot=fh(y);if(ot!==null){(ot.flags&65536)===0&&(ot.flags|=256),hh(ot,y,k,m,n),su(Mo(L,k));break e}}m=L=Mo(L,k),lt!==4&&(lt=2),Ii===null?Ii=[m]:Ii.push(m),m=y;do{switch(m.tag){case 3:m.flags|=65536,n&=-n,m.lanes|=n;var I=uh(m,L,n);If(m,I);break e;case 1:k=L;var T=m.type,z=m.stateNode;if((m.flags&128)===0&&(typeof T.getDerivedStateFromError=="function"||z!==null&&typeof z.componentDidCatch=="function"&&(vr===null||!vr.has(z)))){m.flags|=65536,n&=-n,m.lanes|=n;var ee=ch(m,k,n);If(m,ee);break e}}m=m.return}while(m!==null)}Vh(s)}catch(ve){n=ve,it===s&&s!==null&&(it=s=s.return);continue}break}while(!0)}function Hh(){var t=la.current;return la.current=na,t===null?na:t}function Yu(){(lt===0||lt===3||lt===2)&&(lt=4),ht===null||(Gr&268435455)===0&&(ua&268435455)===0||xr(ht,vt)}function ma(t,n){var s=Ue;Ue|=2;var u=Hh();(ht!==t||vt!==n)&&(Qn=null,Jr(t,n));do try{Iw();break}catch(f){Uh(t,f)}while(!0);if(lu(),Ue=s,la.current=u,it!==null)throw Error(o(261));return ht=null,vt=0,lt}function Iw(){for(;it!==null;)Bh(it)}function Fw(){for(;it!==null&&!Jt();)Bh(it)}function Bh(t){var n=Qh(t.alternate,t,Wt);t.memoizedProps=t.pendingProps,n===null?Vh(t):it=n,$u.current=null}function Vh(t){var n=t;do{var s=n.alternate;if(t=n.return,(n.flags&32768)===0){if(s=Lw(s,n,Wt),s!==null){it=s;return}}else{if(s=Mw(s,n),s!==null){s.flags&=32767,it=s;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{lt=6,it=null;return}}if(n=n.sibling,n!==null){it=n;return}it=n=t}while(n!==null);lt===0&&(lt=5)}function Xr(t,n,s){var u=We,f=tn.transition;try{tn.transition=null,We=1,jw(t,n,s,u)}finally{tn.transition=f,We=u}return null}function jw(t,n,s,u){do Do();while(yr!==null);if((Ue&6)!==0)throw Error(o(327));s=t.finishedWork;var f=t.finishedLanes;if(s===null)return null;if(t.finishedWork=null,t.finishedLanes=0,s===t.current)throw Error(o(177));t.callbackNode=null,t.callbackPriority=0;var m=s.lanes|s.childLanes;if(yy(t,m),t===ht&&(it=ht=null,vt=0),(s.subtreeFlags&2064)===0&&(s.flags&2064)===0||da||(da=!0,Gh($e,function(){return Do(),null})),m=(s.flags&15990)!==0,(s.subtreeFlags&15990)!==0||m){m=tn.transition,tn.transition=null;var y=We;We=1;var k=Ue;Ue|=4,$u.current=null,_w(t,s),Dh(s,t),ow(Yl),Ps=!!Gl,Yl=Gl=null,t.current=s,Nw(s),ii(),Ue=k,We=y,tn.transition=m}else t.current=s;if(da&&(da=!1,yr=t,fa=f),m=t.pendingLanes,m===0&&(vr=null),fo(s.stateNode),It(t,Qe()),n!==null)for(u=t.onRecoverableError,s=0;s<n.length;s++)f=n[s],u(f.value,{componentStack:f.stack,digest:f.digest});if(ca)throw ca=!1,t=Bu,Bu=null,t;return(fa&1)!==0&&t.tag!==0&&Do(),m=t.pendingLanes,(m&1)!==0?t===Vu?Fi++:(Fi=0,Vu=t):Fi=0,hr(),null}function Do(){if(yr!==null){var t=Td(fa),n=tn.transition,s=We;try{if(tn.transition=null,We=16>t?16:t,yr===null)var u=!1;else{if(t=yr,yr=null,fa=0,(Ue&6)!==0)throw Error(o(331));var f=Ue;for(Ue|=4,ce=t.current;ce!==null;){var m=ce,y=m.child;if((ce.flags&16)!==0){var k=m.deletions;if(k!==null){for(var L=0;L<k.length;L++){var $=k[L];for(ce=$;ce!==null;){var Y=ce;switch(Y.tag){case 0:case 11:case 15:Di(8,Y,m)}var J=Y.child;if(J!==null)J.return=Y,ce=J;else for(;ce!==null;){Y=ce;var G=Y.sibling,ae=Y.return;if(Lh(Y),Y===$){ce=null;break}if(G!==null){G.return=ae,ce=G;break}ce=ae}}}var fe=m.alternate;if(fe!==null){var me=fe.child;if(me!==null){fe.child=null;do{var ot=me.sibling;me.sibling=null,me=ot}while(me!==null)}}ce=m}}if((m.subtreeFlags&2064)!==0&&y!==null)y.return=m,ce=y;else e:for(;ce!==null;){if(m=ce,(m.flags&2048)!==0)switch(m.tag){case 0:case 11:case 15:Di(9,m,m.return)}var I=m.sibling;if(I!==null){I.return=m.return,ce=I;break e}ce=m.return}}var T=t.current;for(ce=T;ce!==null;){y=ce;var z=y.child;if((y.subtreeFlags&2064)!==0&&z!==null)z.return=y,ce=z;else e:for(y=T;ce!==null;){if(k=ce,(k.flags&2048)!==0)try{switch(k.tag){case 0:case 11:case 15:aa(9,k)}}catch(ve){nt(k,k.return,ve)}if(k===y){ce=null;break e}var ee=k.sibling;if(ee!==null){ee.return=k.return,ce=ee;break e}ce=k.return}}if(Ue=f,hr(),Ve&&typeof Ve.onPostCommitFiberRoot=="function")try{Ve.onPostCommitFiberRoot(bn,t)}catch{}u=!0}return u}finally{We=s,tn.transition=n}}return!1}function Wh(t,n,s){n=Mo(s,n),n=uh(t,n,1),t=mr(t,n,1),n=Pt(),t!==null&&(ai(t,1,n),It(t,n))}function nt(t,n,s){if(t.tag===3)Wh(t,t,s);else for(;n!==null;){if(n.tag===3){Wh(n,t,s);break}else if(n.tag===1){var u=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(vr===null||!vr.has(u))){t=Mo(s,t),t=ch(n,t,1),n=mr(n,t,1),t=Pt(),n!==null&&(ai(n,1,t),It(n,t));break}}n=n.return}}function zw(t,n,s){var u=t.pingCache;u!==null&&u.delete(n),n=Pt(),t.pingedLanes|=t.suspendedLanes&s,ht===t&&(vt&s)===s&&(lt===4||lt===3&&(vt&130023424)===vt&&500>Qe()-Hu?Jr(t,0):Uu|=s),It(t,n)}function Kh(t,n){n===0&&((t.mode&1)===0?n=1:(n=Es,Es<<=1,(Es&130023424)===0&&(Es=4194304)));var s=Pt();t=Vn(t,n),t!==null&&(ai(t,n,s),It(t,s))}function $w(t){var n=t.memoizedState,s=0;n!==null&&(s=n.retryLane),Kh(t,s)}function Uw(t,n){var s=0;switch(t.tag){case 13:var u=t.stateNode,f=t.memoizedState;f!==null&&(s=f.retryLane);break;case 19:u=t.stateNode;break;default:throw Error(o(314))}u!==null&&u.delete(n),Kh(t,s)}var Qh;Qh=function(t,n,s){if(t!==null)if(t.memoizedProps!==n.pendingProps||_t.current)Dt=!0;else{if((t.lanes&s)===0&&(n.flags&128)===0)return Dt=!1,Ow(t,n,s);Dt=(t.flags&131072)!==0}else Dt=!1,Ze&&(n.flags&1048576)!==0&&Pf(n,Vs,n.index);switch(n.lanes=0,n.tag){case 2:var u=n.type;ia(t,n),t=n.pendingProps;var f=Eo(n,wt.current);Oo(n,s),f=wu(null,n,u,t,f,s);var m=xu();return n.flags|=1,typeof f=="object"&&f!==null&&typeof f.render=="function"&&f.$$typeof===void 0?(n.tag=1,n.memoizedState=null,n.updateQueue=null,Nt(u)?(m=!0,Us(n)):m=!1,n.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,fu(n),f.updater=ra,n.stateNode=f,f._reactInternals=n,Pu(n,u,t,s),n=Mu(null,n,u,!0,m,s)):(n.tag=0,Ze&&m&&nu(n),bt(null,n,f,s),n=n.child),n;case 16:u=n.elementType;e:{switch(ia(t,n),t=n.pendingProps,f=u._init,u=f(u._payload),n.type=u,f=n.tag=Bw(u),t=gn(u,t),f){case 0:n=Lu(null,n,u,t,s);break e;case 1:n=wh(null,n,u,t,s);break e;case 11:n=ph(null,n,u,t,s);break e;case 14:n=mh(null,n,u,gn(u.type,t),s);break e}throw Error(o(306,u,""))}return n;case 0:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:gn(u,f),Lu(t,n,u,f,s);case 1:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:gn(u,f),wh(t,n,u,f,s);case 3:e:{if(xh(n),t===null)throw Error(o(387));u=n.pendingProps,m=n.memoizedState,f=m.element,Af(t,n),Js(n,u,null,s);var y=n.memoizedState;if(u=y.element,m.isDehydrated)if(m={element:u,isDehydrated:!1,cache:y.cache,pendingSuspenseBoundaries:y.pendingSuspenseBoundaries,transitions:y.transitions},n.updateQueue.baseState=m,n.memoizedState=m,n.flags&256){f=Mo(Error(o(423)),n),n=Sh(t,n,u,s,f);break e}else if(u!==f){f=Mo(Error(o(424)),n),n=Sh(t,n,u,s,f);break e}else for(Vt=cr(n.stateNode.containerInfo.firstChild),Bt=n,Ze=!0,mn=null,s=Nf(n,null,u,s),n.child=s;s;)s.flags=s.flags&-3|4096,s=s.sibling;else{if(bo(),u===f){n=Kn(t,n,s);break e}bt(t,n,u,s)}n=n.child}return n;case 5:return jf(n),t===null&&iu(n),u=n.type,f=n.pendingProps,m=t!==null?t.memoizedProps:null,y=f.children,Jl(u,f)?y=null:m!==null&&Jl(u,m)&&(n.flags|=32),yh(t,n),bt(t,n,y,s),n.child;case 6:return t===null&&iu(n),null;case 13:return Eh(t,n,s);case 4:return hu(n,n.stateNode.containerInfo),u=n.pendingProps,t===null?n.child=Po(n,null,u,s):bt(t,n,u,s),n.child;case 11:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:gn(u,f),ph(t,n,u,f,s);case 7:return bt(t,n,n.pendingProps,s),n.child;case 8:return bt(t,n,n.pendingProps.children,s),n.child;case 12:return bt(t,n,n.pendingProps.children,s),n.child;case 10:e:{if(u=n.type._context,f=n.pendingProps,m=n.memoizedProps,y=f.value,Ge(Qs,u._currentValue),u._currentValue=y,m!==null)if(pn(m.value,y)){if(m.children===f.children&&!_t.current){n=Kn(t,n,s);break e}}else for(m=n.child,m!==null&&(m.return=n);m!==null;){var k=m.dependencies;if(k!==null){y=m.child;for(var L=k.firstContext;L!==null;){if(L.context===u){if(m.tag===1){L=Wn(-1,s&-s),L.tag=2;var $=m.updateQueue;if($!==null){$=$.shared;var Y=$.pending;Y===null?L.next=L:(L.next=Y.next,Y.next=L),$.pending=L}}m.lanes|=s,L=m.alternate,L!==null&&(L.lanes|=s),cu(m.return,s,n),k.lanes|=s;break}L=L.next}}else if(m.tag===10)y=m.type===n.type?null:m.child;else if(m.tag===18){if(y=m.return,y===null)throw Error(o(341));y.lanes|=s,k=y.alternate,k!==null&&(k.lanes|=s),cu(y,s,n),y=m.sibling}else y=m.child;if(y!==null)y.return=m;else for(y=m;y!==null;){if(y===n){y=null;break}if(m=y.sibling,m!==null){m.return=y.return,y=m;break}y=y.return}m=y}bt(t,n,f.children,s),n=n.child}return n;case 9:return f=n.type,u=n.pendingProps.children,Oo(n,s),f=Zt(f),u=u(f),n.flags|=1,bt(t,n,u,s),n.child;case 14:return u=n.type,f=gn(u,n.pendingProps),f=gn(u.type,f),mh(t,n,u,f,s);case 15:return gh(t,n,n.type,n.pendingProps,s);case 17:return u=n.type,f=n.pendingProps,f=n.elementType===u?f:gn(u,f),ia(t,n),n.tag=1,Nt(u)?(t=!0,Us(n)):t=!1,Oo(n,s),ah(n,u,f),Pu(n,u,f,s),Mu(null,n,u,!0,t,s);case 19:return kh(t,n,s);case 22:return vh(t,n,s)}throw Error(o(156,n.tag))};function Gh(t,n){return nr(t,n)}function Hw(t,n,s,u){this.tag=t,this.key=s,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function nn(t,n,s,u){return new Hw(t,n,s,u)}function Ju(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Bw(t){if(typeof t=="function")return Ju(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Re)return 11;if(t===be)return 14}return 2}function Sr(t,n){var s=t.alternate;return s===null?(s=nn(t.tag,n,t.key,t.mode),s.elementType=t.elementType,s.type=t.type,s.stateNode=t.stateNode,s.alternate=t,t.alternate=s):(s.pendingProps=n,s.type=t.type,s.flags=0,s.subtreeFlags=0,s.deletions=null),s.flags=t.flags&14680064,s.childLanes=t.childLanes,s.lanes=t.lanes,s.child=t.child,s.memoizedProps=t.memoizedProps,s.memoizedState=t.memoizedState,s.updateQueue=t.updateQueue,n=t.dependencies,s.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},s.sibling=t.sibling,s.index=t.index,s.ref=t.ref,s}function ga(t,n,s,u,f,m){var y=2;if(u=t,typeof t=="function")Ju(t)&&(y=1);else if(typeof t=="string")y=5;else e:switch(t){case K:return qr(s.children,f,m,n);case W:y=8,f|=8;break;case Z:return t=nn(12,s,n,f|2),t.elementType=Z,t.lanes=m,t;case ne:return t=nn(13,s,n,f),t.elementType=ne,t.lanes=m,t;case re:return t=nn(19,s,n,f),t.elementType=re,t.lanes=m,t;case ie:return va(s,f,m,n);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case se:y=10;break e;case Me:y=9;break e;case Re:y=11;break e;case be:y=14;break e;case X:y=16,u=null;break e}throw Error(o(130,t==null?t:typeof t,""))}return n=nn(y,s,n,f),n.elementType=t,n.type=u,n.lanes=m,n}function qr(t,n,s,u){return t=nn(7,t,u,n),t.lanes=s,t}function va(t,n,s,u){return t=nn(22,t,u,n),t.elementType=ie,t.lanes=s,t.stateNode={isHidden:!1},t}function Xu(t,n,s){return t=nn(6,t,null,n),t.lanes=s,t}function qu(t,n,s){return n=nn(4,t.children!==null?t.children:[],t.key,n),n.lanes=s,n.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},n}function Vw(t,n,s,u,f){this.tag=n,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=bl(0),this.expirationTimes=bl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=bl(0),this.identifierPrefix=u,this.onRecoverableError=f,this.mutableSourceEagerHydrationData=null}function Zu(t,n,s,u,f,m,y,k,L){return t=new Vw(t,n,s,k,L),n===1?(n=1,m===!0&&(n|=8)):n=0,m=nn(3,null,null,n),t.current=m,m.stateNode=t,m.memoizedState={element:u,isDehydrated:s,cache:null,transitions:null,pendingSuspenseBoundaries:null},fu(m),t}function Ww(t,n,s){var u=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:M,key:u==null?null:""+u,children:t,containerInfo:n,implementation:s}}function Yh(t){if(!t)return fr;t=t._reactInternals;e:{if(Be(t)!==t||t.tag!==1)throw Error(o(170));var n=t;do{switch(n.tag){case 3:n=n.stateNode.context;break e;case 1:if(Nt(n.type)){n=n.stateNode.__reactInternalMemoizedMergedChildContext;break e}}n=n.return}while(n!==null);throw Error(o(171))}if(t.tag===1){var s=t.type;if(Nt(s))return Cf(t,s,n)}return n}function Jh(t,n,s,u,f,m,y,k,L){return t=Zu(s,u,!0,t,f,m,y,k,L),t.context=Yh(null),s=t.current,u=Pt(),f=wr(s),m=Wn(u,f),m.callback=n??null,mr(s,m,f),t.current.lanes=f,ai(t,f,u),It(t,u),t}function ya(t,n,s,u){var f=n.current,m=Pt(),y=wr(f);return s=Yh(s),n.context===null?n.context=s:n.pendingContext=s,n=Wn(m,y),n.payload={element:t},u=u===void 0?null:u,u!==null&&(n.callback=u),t=mr(f,n,y),t!==null&&(wn(t,f,y,m),Ys(t,f,y)),y}function wa(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Xh(t,n){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var s=t.retryLane;t.retryLane=s!==0&&s<n?s:n}}function ec(t,n){Xh(t,n),(t=t.alternate)&&Xh(t,n)}function Kw(){return null}var qh=typeof reportError=="function"?reportError:function(t){console.error(t)};function tc(t){this._internalRoot=t}xa.prototype.render=tc.prototype.render=function(t){var n=this._internalRoot;if(n===null)throw Error(o(409));ya(t,n,null,null)},xa.prototype.unmount=tc.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var n=t.containerInfo;Yr(function(){ya(null,t,null,null)}),n[$n]=null}};function xa(t){this._internalRoot=t}xa.prototype.unstable_scheduleHydration=function(t){if(t){var n=Dd();t={blockedOn:null,target:t,priority:n};for(var s=0;s<ar.length&&n!==0&&n<ar[s].priority;s++);ar.splice(s,0,t),s===0&&Fd(t)}};function nc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Sa(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Zh(){}function Qw(t,n,s,u,f){if(f){if(typeof u=="function"){var m=u;u=function(){var $=wa(y);m.call($)}}var y=Jh(n,u,t,0,null,!1,!1,"",Zh);return t._reactRootContainer=y,t[$n]=y.current,Si(t.nodeType===8?t.parentNode:t),Yr(),y}for(;f=t.lastChild;)t.removeChild(f);if(typeof u=="function"){var k=u;u=function(){var $=wa(L);k.call($)}}var L=Zu(t,0,!1,null,null,!1,!1,"",Zh);return t._reactRootContainer=L,t[$n]=L.current,Si(t.nodeType===8?t.parentNode:t),Yr(function(){ya(n,L,s,u)}),L}function Ea(t,n,s,u,f){var m=s._reactRootContainer;if(m){var y=m;if(typeof f=="function"){var k=f;f=function(){var L=wa(y);k.call(L)}}ya(n,y,t,f)}else y=Qw(s,n,t,f,u);return wa(y)}_d=function(t){switch(t.tag){case 3:var n=t.stateNode;if(n.current.memoizedState.isDehydrated){var s=si(n.pendingLanes);s!==0&&(Pl(n,s|1),It(n,Qe()),(Ue&6)===0&&(No=Qe()+500,hr()))}break;case 13:Yr(function(){var u=Vn(t,1);if(u!==null){var f=Pt();wn(u,t,1,f)}}),ec(t,1)}},Rl=function(t){if(t.tag===13){var n=Vn(t,134217728);if(n!==null){var s=Pt();wn(n,t,134217728,s)}ec(t,134217728)}},Nd=function(t){if(t.tag===13){var n=wr(t),s=Vn(t,n);if(s!==null){var u=Pt();wn(s,t,n,u)}ec(t,n)}},Dd=function(){return We},Ad=function(t,n){var s=We;try{return We=t,n()}finally{We=s}},ri=function(t,n,s){switch(n){case"input":if(so(t,s),n=s.name,s.type==="radio"&&n!=null){for(s=t;s.parentNode;)s=s.parentNode;for(s=s.querySelectorAll("input[name="+JSON.stringify(""+n)+'][type="radio"]'),n=0;n<s.length;n++){var u=s[n];if(u!==t&&u.form===t.form){var f=zs(u);if(!f)throw Error(o(90));Yt(u),so(u,f)}}}break;case"textarea":ei(t,s);break;case"select":n=s.value,n!=null&&qn(t,!!s.multiple,n,!1)}},ws=Qu,xs=Yr;var Gw={usingClientEntryPoint:!1,Events:[ki,xo,zs,Fr,jr,Qu]},ji={findFiberByHostInstance:Ur,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Yw={bundleType:ji.bundleType,version:ji.version,rendererPackageName:ji.rendererPackageName,rendererConfig:ji.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:j.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Ke(t),t===null?null:t.stateNode},findFiberByHostInstance:ji.findFiberByHostInstance||Kw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ca=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ca.isDisabled&&Ca.supportsFiber)try{bn=Ca.inject(Yw),Ve=Ca}catch{}}return Ft.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Gw,Ft.createPortal=function(t,n){var s=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!nc(n))throw Error(o(200));return Ww(t,n,null,s)},Ft.createRoot=function(t,n){if(!nc(t))throw Error(o(299));var s=!1,u="",f=qh;return n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onRecoverableError!==void 0&&(f=n.onRecoverableError)),n=Zu(t,1,!1,null,null,s,!1,u,f),t[$n]=n.current,Si(t.nodeType===8?t.parentNode:t),new tc(n)},Ft.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var n=t._reactInternals;if(n===void 0)throw typeof t.render=="function"?Error(o(188)):(t=Object.keys(t).join(","),Error(o(268,t)));return t=Ke(n),t=t===null?null:t.stateNode,t},Ft.flushSync=function(t){return Yr(t)},Ft.hydrate=function(t,n,s){if(!Sa(n))throw Error(o(200));return Ea(null,t,n,!0,s)},Ft.hydrateRoot=function(t,n,s){if(!nc(t))throw Error(o(405));var u=s!=null&&s.hydratedSources||null,f=!1,m="",y=qh;if(s!=null&&(s.unstable_strictMode===!0&&(f=!0),s.identifierPrefix!==void 0&&(m=s.identifierPrefix),s.onRecoverableError!==void 0&&(y=s.onRecoverableError)),n=Jh(n,null,t,1,s??null,f,!1,m,y),t[$n]=n.current,Si(t),u)for(t=0;t<u.length;t++)s=u[t],f=s._getVersion,f=f(s._source),n.mutableSourceEagerHydrationData==null?n.mutableSourceEagerHydrationData=[s,f]:n.mutableSourceEagerHydrationData.push(s,f);return new xa(n)},Ft.render=function(t,n,s){if(!Sa(n))throw Error(o(200));return Ea(null,t,n,!1,s)},Ft.unmountComponentAtNode=function(t){if(!Sa(t))throw Error(o(40));return t._reactRootContainer?(Yr(function(){Ea(null,null,t,!1,function(){t._reactRootContainer=null,t[$n]=null})}),!0):!1},Ft.unstable_batchedUpdates=Qu,Ft.unstable_renderSubtreeIntoContainer=function(t,n,s,u){if(!Sa(s))throw Error(o(200));if(t==null||t._reactInternals===void 0)throw Error(o(38));return Ea(t,n,s,!1,u)},Ft.version="18.3.1-next-f1338f8080-20240426",Ft}var ap;function jm(){if(ap)return ic.exports;ap=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(r){console.error(r)}}return e(),ic.exports=r0(),ic.exports}var lp;function o0(){if(lp)return ka;lp=1;var e=jm();return ka.createRoot=e.createRoot,ka.hydrateRoot=e.hydrateRoot,ka}var i0=o0();const Te=e=>typeof e=="string",$i=()=>{let e,r;const o=new Promise((i,a)=>{e=i,r=a});return o.resolve=e,o.reject=r,o},up=e=>e==null?"":""+e,s0=(e,r,o)=>{e.forEach(i=>{r[i]&&(o[i]=r[i])})},a0=/###/g,cp=e=>e&&e.indexOf("###")>-1?e.replace(a0,"."):e,dp=e=>!e||Te(e),Qi=(e,r,o)=>{const i=Te(r)?r.split("."):r;let a=0;for(;a<i.length-1;){if(dp(e))return{};const l=cp(i[a]);!e[l]&&o&&(e[l]=new o),Object.prototype.hasOwnProperty.call(e,l)?e=e[l]:e={},++a}return dp(e)?{}:{obj:e,k:cp(i[a])}},fp=(e,r,o)=>{const{obj:i,k:a}=Qi(e,r,Object);if(i!==void 0||r.length===1){i[a]=o;return}let l=r[r.length-1],c=r.slice(0,r.length-1),d=Qi(e,c,Object);for(;d.obj===void 0&&c.length;)l=`${c[c.length-1]}.${l}`,c=c.slice(0,c.length-1),d=Qi(e,c,Object),d?.obj&&typeof d.obj[`${d.k}.${l}`]<"u"&&(d.obj=void 0);d.obj[`${d.k}.${l}`]=o},l0=(e,r,o,i)=>{const{obj:a,k:l}=Qi(e,r,Object);a[l]=a[l]||[],a[l].push(o)},Ka=(e,r)=>{const{obj:o,k:i}=Qi(e,r);if(o&&Object.prototype.hasOwnProperty.call(o,i))return o[i]},u0=(e,r,o)=>{const i=Ka(e,o);return i!==void 0?i:Ka(r,o)},zm=(e,r,o)=>{for(const i in r)i!=="__proto__"&&i!=="constructor"&&(i in e?Te(e[i])||e[i]instanceof String||Te(r[i])||r[i]instanceof String?o&&(e[i]=r[i]):zm(e[i],r[i],o):e[i]=r[i]);return e},Ao=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var c0={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const d0=e=>Te(e)?e.replace(/[&<>"'\/]/g,r=>c0[r]):e;class f0{constructor(r){this.capacity=r,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(r){const o=this.regExpMap.get(r);if(o!==void 0)return o;const i=new RegExp(r);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(r,i),this.regExpQueue.push(r),i}}const h0=[" ",",","?","!",";"],p0=new f0(20),m0=(e,r,o)=>{r=r||"",o=o||"";const i=h0.filter(c=>r.indexOf(c)<0&&o.indexOf(c)<0);if(i.length===0)return!0;const a=p0.getRegExp(`(${i.map(c=>c==="?"?"\\?":c).join("|")})`);let l=!a.test(e);if(!l){const c=e.indexOf(o);c>0&&!a.test(e.substring(0,c))&&(l=!0)}return l},Tc=(e,r,o=".")=>{if(!e)return;if(e[r])return Object.prototype.hasOwnProperty.call(e,r)?e[r]:void 0;const i=r.split(o);let a=e;for(let l=0;l<i.length;){if(!a||typeof a!="object")return;let c,d="";for(let h=l;h<i.length;++h)if(h!==l&&(d+=o),d+=i[h],c=a[d],c!==void 0){if(["string","number","boolean"].indexOf(typeof c)>-1&&h<i.length-1)continue;l+=h-l+1;break}a=c}return a},Gi=e=>e?.replace("_","-"),g0={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,r){console?.[e]?.apply?.(console,r)}};class Qa{constructor(r,o={}){this.init(r,o)}init(r,o={}){this.prefix=o.prefix||"i18next:",this.logger=r||g0,this.options=o,this.debug=o.debug}log(...r){return this.forward(r,"log","",!0)}warn(...r){return this.forward(r,"warn","",!0)}error(...r){return this.forward(r,"error","")}deprecate(...r){return this.forward(r,"warn","WARNING DEPRECATED: ",!0)}forward(r,o,i,a){return a&&!this.debug?null:(Te(r[0])&&(r[0]=`${i}${this.prefix} ${r[0]}`),this.logger[o](r))}create(r){return new Qa(this.logger,{prefix:`${this.prefix}:${r}:`,...this.options})}clone(r){return r=r||this.options,r.prefix=r.prefix||this.prefix,new Qa(this.logger,r)}}var Tn=new Qa;class il{constructor(){this.observers={}}on(r,o){return r.split(" ").forEach(i=>{this.observers[i]||(this.observers[i]=new Map);const a=this.observers[i].get(o)||0;this.observers[i].set(o,a+1)}),this}off(r,o){if(this.observers[r]){if(!o){delete this.observers[r];return}this.observers[r].delete(o)}}emit(r,...o){this.observers[r]&&Array.from(this.observers[r].entries()).forEach(([a,l])=>{for(let c=0;c<l;c++)a(...o)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(([a,l])=>{for(let c=0;c<l;c++)a.apply(a,[r,...o])})}}class hp extends il{constructor(r,o={ns:["translation"],defaultNS:"translation"}){super(),this.data=r||{},this.options=o,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(r){this.options.ns.indexOf(r)<0&&this.options.ns.push(r)}removeNamespaces(r){const o=this.options.ns.indexOf(r);o>-1&&this.options.ns.splice(o,1)}getResource(r,o,i,a={}){const l=a.keySeparator!==void 0?a.keySeparator:this.options.keySeparator,c=a.ignoreJSONStructure!==void 0?a.ignoreJSONStructure:this.options.ignoreJSONStructure;let d;r.indexOf(".")>-1?d=r.split("."):(d=[r,o],i&&(Array.isArray(i)?d.push(...i):Te(i)&&l?d.push(...i.split(l)):d.push(i)));const h=Ka(this.data,d);return!h&&!o&&!i&&r.indexOf(".")>-1&&(r=d[0],o=d[1],i=d.slice(2).join(".")),h||!c||!Te(i)?h:Tc(this.data?.[r]?.[o],i,l)}addResource(r,o,i,a,l={silent:!1}){const c=l.keySeparator!==void 0?l.keySeparator:this.options.keySeparator;let d=[r,o];i&&(d=d.concat(c?i.split(c):i)),r.indexOf(".")>-1&&(d=r.split("."),a=o,o=d[1]),this.addNamespaces(o),fp(this.data,d,a),l.silent||this.emit("added",r,o,i,a)}addResources(r,o,i,a={silent:!1}){for(const l in i)(Te(i[l])||Array.isArray(i[l]))&&this.addResource(r,o,l,i[l],{silent:!0});a.silent||this.emit("added",r,o,i)}addResourceBundle(r,o,i,a,l,c={silent:!1,skipCopy:!1}){let d=[r,o];r.indexOf(".")>-1&&(d=r.split("."),a=i,i=o,o=d[1]),this.addNamespaces(o);let h=Ka(this.data,d)||{};c.skipCopy||(i=JSON.parse(JSON.stringify(i))),a?zm(h,i,l):h={...h,...i},fp(this.data,d,h),c.silent||this.emit("added",r,o,i)}removeResourceBundle(r,o){this.hasResourceBundle(r,o)&&delete this.data[r][o],this.removeNamespaces(o),this.emit("removed",r,o)}hasResourceBundle(r,o){return this.getResource(r,o)!==void 0}getResourceBundle(r,o){return o||(o=this.options.defaultNS),this.getResource(r,o)}getDataByLanguage(r){return this.data[r]}hasLanguageSomeTranslations(r){const o=this.getDataByLanguage(r);return!!(o&&Object.keys(o)||[]).find(a=>o[a]&&Object.keys(o[a]).length>0)}toJSON(){return this.data}}var $m={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,r,o,i,a){return e.forEach(l=>{r=this.processors[l]?.process(r,o,i,a)??r}),r}};const pp={},mp=e=>!Te(e)&&typeof e!="boolean"&&typeof e!="number";class Ga extends il{constructor(r,o={}){super(),s0(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],r,this),this.options=o,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=Tn.create("translator")}changeLanguage(r){r&&(this.language=r)}exists(r,o={interpolation:{}}){const i={...o};return r==null?!1:this.resolve(r,i)?.res!==void 0}extractFromKey(r,o){let i=o.nsSeparator!==void 0?o.nsSeparator:this.options.nsSeparator;i===void 0&&(i=":");const a=o.keySeparator!==void 0?o.keySeparator:this.options.keySeparator;let l=o.ns||this.options.defaultNS||[];const c=i&&r.indexOf(i)>-1,d=!this.options.userDefinedKeySeparator&&!o.keySeparator&&!this.options.userDefinedNsSeparator&&!o.nsSeparator&&!m0(r,i,a);if(c&&!d){const h=r.match(this.interpolator.nestingRegexp);if(h&&h.length>0)return{key:r,namespaces:Te(l)?[l]:l};const p=r.split(i);(i!==a||i===a&&this.options.ns.indexOf(p[0])>-1)&&(l=p.shift()),r=p.join(a)}return{key:r,namespaces:Te(l)?[l]:l}}translate(r,o,i){let a=typeof o=="object"?{...o}:o;if(typeof a!="object"&&this.options.overloadTranslationOptionHandler&&(a=this.options.overloadTranslationOptionHandler(arguments)),typeof options=="object"&&(a={...a}),a||(a={}),r==null)return"";Array.isArray(r)||(r=[String(r)]);const l=a.returnDetails!==void 0?a.returnDetails:this.options.returnDetails,c=a.keySeparator!==void 0?a.keySeparator:this.options.keySeparator,{key:d,namespaces:h}=this.extractFromKey(r[r.length-1],a),p=h[h.length-1];let g=a.nsSeparator!==void 0?a.nsSeparator:this.options.nsSeparator;g===void 0&&(g=":");const v=a.lng||this.language,x=a.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(v?.toLowerCase()==="cimode")return x?l?{res:`${p}${g}${d}`,usedKey:d,exactUsedKey:d,usedLng:v,usedNS:p,usedParams:this.getUsedParamsDetails(a)}:`${p}${g}${d}`:l?{res:d,usedKey:d,exactUsedKey:d,usedLng:v,usedNS:p,usedParams:this.getUsedParamsDetails(a)}:d;const S=this.resolve(r,a);let C=S?.res;const E=S?.usedKey||d,b=S?.exactUsedKey||d,P=["[object Number]","[object Function]","[object RegExp]"],D=a.joinArrays!==void 0?a.joinArrays:this.options.joinArrays,N=!this.i18nFormat||this.i18nFormat.handleAsObject,j=a.count!==void 0&&!Te(a.count),H=Ga.hasDefaultValue(a),M=j?this.pluralResolver.getSuffix(v,a.count,a):"",K=a.ordinal&&j?this.pluralResolver.getSuffix(v,a.count,{ordinal:!1}):"",W=j&&!a.ordinal&&a.count===0,Z=W&&a[`defaultValue${this.options.pluralSeparator}zero`]||a[`defaultValue${M}`]||a[`defaultValue${K}`]||a.defaultValue;let se=C;N&&!C&&H&&(se=Z);const Me=mp(se),Re=Object.prototype.toString.apply(se);if(N&&se&&Me&&P.indexOf(Re)<0&&!(Te(D)&&Array.isArray(se))){if(!a.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const ne=this.options.returnedObjectHandler?this.options.returnedObjectHandler(E,se,{...a,ns:h}):`key '${d} (${this.language})' returned an object instead of string.`;return l?(S.res=ne,S.usedParams=this.getUsedParamsDetails(a),S):ne}if(c){const ne=Array.isArray(se),re=ne?[]:{},be=ne?b:E;for(const X in se)if(Object.prototype.hasOwnProperty.call(se,X)){const ie=`${be}${c}${X}`;H&&!C?re[X]=this.translate(ie,{...a,defaultValue:mp(Z)?Z[X]:void 0,joinArrays:!1,ns:h}):re[X]=this.translate(ie,{...a,joinArrays:!1,ns:h}),re[X]===ie&&(re[X]=se[X])}C=re}}else if(N&&Te(D)&&Array.isArray(C))C=C.join(D),C&&(C=this.extendTranslation(C,r,a,i));else{let ne=!1,re=!1;!this.isValidLookup(C)&&H&&(ne=!0,C=Z),this.isValidLookup(C)||(re=!0,C=d);const X=(a.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&re?void 0:C,ie=H&&Z!==C&&this.options.updateMissing;if(re||ne||ie){if(this.logger.log(ie?"updateKey":"missingKey",v,p,d,ie?Z:C),c){const O=this.resolve(d,{...a,keySeparator:!1});O&&O.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let _=[];const V=this.languageUtils.getFallbackCodes(this.options.fallbackLng,a.lng||this.language);if(this.options.saveMissingTo==="fallback"&&V&&V[0])for(let O=0;O<V.length;O++)_.push(V[O]);else this.options.saveMissingTo==="all"?_=this.languageUtils.toResolveHierarchy(a.lng||this.language):_.push(a.lng||this.language);const Q=(O,B,oe)=>{const le=H&&oe!==C?oe:X;this.options.missingKeyHandler?this.options.missingKeyHandler(O,p,B,le,ie,a):this.backendConnector?.saveMissing&&this.backendConnector.saveMissing(O,p,B,le,ie,a),this.emit("missingKey",O,p,B,C)};this.options.saveMissing&&(this.options.saveMissingPlurals&&j?_.forEach(O=>{const B=this.pluralResolver.getSuffixes(O,a);W&&a[`defaultValue${this.options.pluralSeparator}zero`]&&B.indexOf(`${this.options.pluralSeparator}zero`)<0&&B.push(`${this.options.pluralSeparator}zero`),B.forEach(oe=>{Q([O],d+oe,a[`defaultValue${oe}`]||Z)})}):Q(_,d,Z))}C=this.extendTranslation(C,r,a,S,i),re&&C===d&&this.options.appendNamespaceToMissingKey&&(C=`${p}${g}${d}`),(re||ne)&&this.options.parseMissingKeyHandler&&(C=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${p}${g}${d}`:d,ne?C:void 0,a))}return l?(S.res=C,S.usedParams=this.getUsedParamsDetails(a),S):C}extendTranslation(r,o,i,a,l){if(this.i18nFormat?.parse)r=this.i18nFormat.parse(r,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||a.usedLng,a.usedNS,a.usedKey,{resolved:a});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const h=Te(r)&&(i?.interpolation?.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let p;if(h){const v=r.match(this.interpolator.nestingRegexp);p=v&&v.length}let g=i.replace&&!Te(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(g={...this.options.interpolation.defaultVariables,...g}),r=this.interpolator.interpolate(r,g,i.lng||this.language||a.usedLng,i),h){const v=r.match(this.interpolator.nestingRegexp),x=v&&v.length;p<x&&(i.nest=!1)}!i.lng&&a&&a.res&&(i.lng=this.language||a.usedLng),i.nest!==!1&&(r=this.interpolator.nest(r,(...v)=>l?.[0]===v[0]&&!i.context?(this.logger.warn(`It seems you are nesting recursively key: ${v[0]} in key: ${o[0]}`),null):this.translate(...v,o),i)),i.interpolation&&this.interpolator.reset()}const c=i.postProcess||this.options.postProcess,d=Te(c)?[c]:c;return r!=null&&d?.length&&i.applyPostProcessor!==!1&&(r=$m.handle(d,r,o,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...a,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),r}resolve(r,o={}){let i,a,l,c,d;return Te(r)&&(r=[r]),r.forEach(h=>{if(this.isValidLookup(i))return;const p=this.extractFromKey(h,o),g=p.key;a=g;let v=p.namespaces;this.options.fallbackNS&&(v=v.concat(this.options.fallbackNS));const x=o.count!==void 0&&!Te(o.count),S=x&&!o.ordinal&&o.count===0,C=o.context!==void 0&&(Te(o.context)||typeof o.context=="number")&&o.context!=="",E=o.lngs?o.lngs:this.languageUtils.toResolveHierarchy(o.lng||this.language,o.fallbackLng);v.forEach(b=>{this.isValidLookup(i)||(d=b,!pp[`${E[0]}-${b}`]&&this.utils?.hasLoadedNamespace&&!this.utils?.hasLoadedNamespace(d)&&(pp[`${E[0]}-${b}`]=!0,this.logger.warn(`key "${a}" for languages "${E.join(", ")}" won't get resolved as namespace "${d}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),E.forEach(P=>{if(this.isValidLookup(i))return;c=P;const D=[g];if(this.i18nFormat?.addLookupKeys)this.i18nFormat.addLookupKeys(D,g,P,b,o);else{let j;x&&(j=this.pluralResolver.getSuffix(P,o.count,o));const H=`${this.options.pluralSeparator}zero`,M=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(x&&(D.push(g+j),o.ordinal&&j.indexOf(M)===0&&D.push(g+j.replace(M,this.options.pluralSeparator)),S&&D.push(g+H)),C){const K=`${g}${this.options.contextSeparator}${o.context}`;D.push(K),x&&(D.push(K+j),o.ordinal&&j.indexOf(M)===0&&D.push(K+j.replace(M,this.options.pluralSeparator)),S&&D.push(K+H))}}let N;for(;N=D.pop();)this.isValidLookup(i)||(l=N,i=this.getResource(P,b,N,o))}))})}),{res:i,usedKey:a,exactUsedKey:l,usedLng:c,usedNS:d}}isValidLookup(r){return r!==void 0&&!(!this.options.returnNull&&r===null)&&!(!this.options.returnEmptyString&&r==="")}getResource(r,o,i,a={}){return this.i18nFormat?.getResource?this.i18nFormat.getResource(r,o,i,a):this.resourceStore.getResource(r,o,i,a)}getUsedParamsDetails(r={}){const o=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=r.replace&&!Te(r.replace);let a=i?r.replace:r;if(i&&typeof r.count<"u"&&(a.count=r.count),this.options.interpolation.defaultVariables&&(a={...this.options.interpolation.defaultVariables,...a}),!i){a={...a};for(const l of o)delete a[l]}return a}static hasDefaultValue(r){const o="defaultValue";for(const i in r)if(Object.prototype.hasOwnProperty.call(r,i)&&o===i.substring(0,o.length)&&r[i]!==void 0)return!0;return!1}}class gp{constructor(r){this.options=r,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Tn.create("languageUtils")}getScriptPartFromCode(r){if(r=Gi(r),!r||r.indexOf("-")<0)return null;const o=r.split("-");return o.length===2||(o.pop(),o[o.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(o.join("-"))}getLanguagePartFromCode(r){if(r=Gi(r),!r||r.indexOf("-")<0)return r;const o=r.split("-");return this.formatLanguageCode(o[0])}formatLanguageCode(r){if(Te(r)&&r.indexOf("-")>-1){let o;try{o=Intl.getCanonicalLocales(r)[0]}catch{}return o&&this.options.lowerCaseLng&&(o=o.toLowerCase()),o||(this.options.lowerCaseLng?r.toLowerCase():r)}return this.options.cleanCode||this.options.lowerCaseLng?r.toLowerCase():r}isSupportedCode(r){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(r=this.getLanguagePartFromCode(r)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(r)>-1}getBestMatchFromCodes(r){if(!r)return null;let o;return r.forEach(i=>{if(o)return;const a=this.formatLanguageCode(i);(!this.options.supportedLngs||this.isSupportedCode(a))&&(o=a)}),!o&&this.options.supportedLngs&&r.forEach(i=>{if(o)return;const a=this.getScriptPartFromCode(i);if(this.isSupportedCode(a))return o=a;const l=this.getLanguagePartFromCode(i);if(this.isSupportedCode(l))return o=l;o=this.options.supportedLngs.find(c=>{if(c===l)return c;if(!(c.indexOf("-")<0&&l.indexOf("-")<0)&&(c.indexOf("-")>0&&l.indexOf("-")<0&&c.substring(0,c.indexOf("-"))===l||c.indexOf(l)===0&&l.length>1))return c})}),o||(o=this.getFallbackCodes(this.options.fallbackLng)[0]),o}getFallbackCodes(r,o){if(!r)return[];if(typeof r=="function"&&(r=r(o)),Te(r)&&(r=[r]),Array.isArray(r))return r;if(!o)return r.default||[];let i=r[o];return i||(i=r[this.getScriptPartFromCode(o)]),i||(i=r[this.formatLanguageCode(o)]),i||(i=r[this.getLanguagePartFromCode(o)]),i||(i=r.default),i||[]}toResolveHierarchy(r,o){const i=this.getFallbackCodes((o===!1?[]:o)||this.options.fallbackLng||[],r),a=[],l=c=>{c&&(this.isSupportedCode(c)?a.push(c):this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`))};return Te(r)&&(r.indexOf("-")>-1||r.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&l(this.formatLanguageCode(r)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&l(this.getScriptPartFromCode(r)),this.options.load!=="currentOnly"&&l(this.getLanguagePartFromCode(r))):Te(r)&&l(this.formatLanguageCode(r)),i.forEach(c=>{a.indexOf(c)<0&&l(this.formatLanguageCode(c))}),a}}const vp={zero:0,one:1,two:2,few:3,many:4,other:5},yp={select:e=>e===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class v0{constructor(r,o={}){this.languageUtils=r,this.options=o,this.logger=Tn.create("pluralResolver"),this.pluralRulesCache={}}addRule(r,o){this.rules[r]=o}clearCache(){this.pluralRulesCache={}}getRule(r,o={}){const i=Gi(r==="dev"?"en":r),a=o.ordinal?"ordinal":"cardinal",l=JSON.stringify({cleanedCode:i,type:a});if(l in this.pluralRulesCache)return this.pluralRulesCache[l];let c;try{c=new Intl.PluralRules(i,{type:a})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),yp;if(!r.match(/-|_/))return yp;const h=this.languageUtils.getLanguagePartFromCode(r);c=this.getRule(h,o)}return this.pluralRulesCache[l]=c,c}needsPlural(r,o={}){let i=this.getRule(r,o);return i||(i=this.getRule("dev",o)),i?.resolvedOptions().pluralCategories.length>1}getPluralFormsOfKey(r,o,i={}){return this.getSuffixes(r,i).map(a=>`${o}${a}`)}getSuffixes(r,o={}){let i=this.getRule(r,o);return i||(i=this.getRule("dev",o)),i?i.resolvedOptions().pluralCategories.sort((a,l)=>vp[a]-vp[l]).map(a=>`${this.options.prepend}${o.ordinal?`ordinal${this.options.prepend}`:""}${a}`):[]}getSuffix(r,o,i={}){const a=this.getRule(r,i);return a?`${this.options.prepend}${i.ordinal?`ordinal${this.options.prepend}`:""}${a.select(o)}`:(this.logger.warn(`no plural rule found for: ${r}`),this.getSuffix("dev",o,i))}}const wp=(e,r,o,i=".",a=!0)=>{let l=u0(e,r,o);return!l&&a&&Te(o)&&(l=Tc(e,o,i),l===void 0&&(l=Tc(r,o,i))),l},lc=e=>e.replace(/\$/g,"$$$$");class y0{constructor(r={}){this.logger=Tn.create("interpolator"),this.options=r,this.format=r?.interpolation?.format||(o=>o),this.init(r)}init(r={}){r.interpolation||(r.interpolation={escapeValue:!0});const{escape:o,escapeValue:i,useRawValueToEscape:a,prefix:l,prefixEscaped:c,suffix:d,suffixEscaped:h,formatSeparator:p,unescapeSuffix:g,unescapePrefix:v,nestingPrefix:x,nestingPrefixEscaped:S,nestingSuffix:C,nestingSuffixEscaped:E,nestingOptionsSeparator:b,maxReplaces:P,alwaysFormat:D}=r.interpolation;this.escape=o!==void 0?o:d0,this.escapeValue=i!==void 0?i:!0,this.useRawValueToEscape=a!==void 0?a:!1,this.prefix=l?Ao(l):c||"{{",this.suffix=d?Ao(d):h||"}}",this.formatSeparator=p||",",this.unescapePrefix=g?"":v||"-",this.unescapeSuffix=this.unescapePrefix?"":g||"",this.nestingPrefix=x?Ao(x):S||Ao("$t("),this.nestingSuffix=C?Ao(C):E||Ao(")"),this.nestingOptionsSeparator=b||",",this.maxReplaces=P||1e3,this.alwaysFormat=D!==void 0?D:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const r=(o,i)=>o?.source===i?(o.lastIndex=0,o):new RegExp(i,"g");this.regexp=r(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=r(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=r(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(r,o,i,a){let l,c,d;const h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},p=S=>{if(S.indexOf(this.formatSeparator)<0){const P=wp(o,h,S,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(P,void 0,i,{...a,...o,interpolationkey:S}):P}const C=S.split(this.formatSeparator),E=C.shift().trim(),b=C.join(this.formatSeparator).trim();return this.format(wp(o,h,E,this.options.keySeparator,this.options.ignoreJSONStructure),b,i,{...a,...o,interpolationkey:E})};this.resetRegExp();const g=a?.missingInterpolationHandler||this.options.missingInterpolationHandler,v=a?.interpolation?.skipOnVariables!==void 0?a.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:S=>lc(S)},{regex:this.regexp,safeValue:S=>this.escapeValue?lc(this.escape(S)):lc(S)}].forEach(S=>{for(d=0;l=S.regex.exec(r);){const C=l[1].trim();if(c=p(C),c===void 0)if(typeof g=="function"){const b=g(r,l,a);c=Te(b)?b:""}else if(a&&Object.prototype.hasOwnProperty.call(a,C))c="";else if(v){c=l[0];continue}else this.logger.warn(`missed to pass in variable ${C} for interpolating ${r}`),c="";else!Te(c)&&!this.useRawValueToEscape&&(c=up(c));const E=S.safeValue(c);if(r=r.replace(l[0],E),v?(S.regex.lastIndex+=c.length,S.regex.lastIndex-=l[0].length):S.regex.lastIndex=0,d++,d>=this.maxReplaces)break}}),r}nest(r,o,i={}){let a,l,c;const d=(h,p)=>{const g=this.nestingOptionsSeparator;if(h.indexOf(g)<0)return h;const v=h.split(new RegExp(`${g}[ ]*{`));let x=`{${v[1]}`;h=v[0],x=this.interpolate(x,c);const S=x.match(/'/g),C=x.match(/"/g);((S?.length??0)%2===0&&!C||C.length%2!==0)&&(x=x.replace(/'/g,'"'));try{c=JSON.parse(x),p&&(c={...p,...c})}catch(E){return this.logger.warn(`failed parsing options string in nesting for key ${h}`,E),`${h}${g}${x}`}return c.defaultValue&&c.defaultValue.indexOf(this.prefix)>-1&&delete c.defaultValue,h};for(;a=this.nestingRegexp.exec(r);){let h=[];c={...i},c=c.replace&&!Te(c.replace)?c.replace:c,c.applyPostProcessor=!1,delete c.defaultValue;const p=/{.*}/.test(a[1])?a[1].lastIndexOf("}")+1:a[1].indexOf(this.formatSeparator);if(p!==-1&&(h=a[1].slice(p).split(this.formatSeparator).map(g=>g.trim()).filter(Boolean),a[1]=a[1].slice(0,p)),l=o(d.call(this,a[1].trim(),c),c),l&&a[0]===r&&!Te(l))return l;Te(l)||(l=up(l)),l||(this.logger.warn(`missed to resolve ${a[1]} for nesting ${r}`),l=""),h.length&&(l=h.reduce((g,v)=>this.format(g,v,i.lng,{...i,interpolationkey:a[1].trim()}),l.trim())),r=r.replace(a[0],l),this.regexp.lastIndex=0}return r}}const w0=e=>{let r=e.toLowerCase().trim();const o={};if(e.indexOf("(")>-1){const i=e.split("(");r=i[0].toLowerCase().trim();const a=i[1].substring(0,i[1].length-1);r==="currency"&&a.indexOf(":")<0?o.currency||(o.currency=a.trim()):r==="relativetime"&&a.indexOf(":")<0?o.range||(o.range=a.trim()):a.split(";").forEach(c=>{if(c){const[d,...h]=c.split(":"),p=h.join(":").trim().replace(/^'+|'+$/g,""),g=d.trim();o[g]||(o[g]=p),p==="false"&&(o[g]=!1),p==="true"&&(o[g]=!0),isNaN(p)||(o[g]=parseInt(p,10))}})}return{formatName:r,formatOptions:o}},xp=e=>{const r={};return(o,i,a)=>{let l=a;a&&a.interpolationkey&&a.formatParams&&a.formatParams[a.interpolationkey]&&a[a.interpolationkey]&&(l={...l,[a.interpolationkey]:void 0});const c=i+JSON.stringify(l);let d=r[c];return d||(d=e(Gi(i),a),r[c]=d),d(o)}},x0=e=>(r,o,i)=>e(Gi(o),i)(r);class S0{constructor(r={}){this.logger=Tn.create("formatter"),this.options=r,this.init(r)}init(r,o={interpolation:{}}){this.formatSeparator=o.interpolation.formatSeparator||",";const i=o.cacheInBuiltFormats?xp:x0;this.formats={number:i((a,l)=>{const c=new Intl.NumberFormat(a,{...l});return d=>c.format(d)}),currency:i((a,l)=>{const c=new Intl.NumberFormat(a,{...l,style:"currency"});return d=>c.format(d)}),datetime:i((a,l)=>{const c=new Intl.DateTimeFormat(a,{...l});return d=>c.format(d)}),relativetime:i((a,l)=>{const c=new Intl.RelativeTimeFormat(a,{...l});return d=>c.format(d,l.range||"day")}),list:i((a,l)=>{const c=new Intl.ListFormat(a,{...l});return d=>c.format(d)})}}add(r,o){this.formats[r.toLowerCase().trim()]=o}addCached(r,o){this.formats[r.toLowerCase().trim()]=xp(o)}format(r,o,i,a={}){const l=o.split(this.formatSeparator);if(l.length>1&&l[0].indexOf("(")>1&&l[0].indexOf(")")<0&&l.find(d=>d.indexOf(")")>-1)){const d=l.findIndex(h=>h.indexOf(")")>-1);l[0]=[l[0],...l.splice(1,d)].join(this.formatSeparator)}return l.reduce((d,h)=>{const{formatName:p,formatOptions:g}=w0(h);if(this.formats[p]){let v=d;try{const x=a?.formatParams?.[a.interpolationkey]||{},S=x.locale||x.lng||a.locale||a.lng||i;v=this.formats[p](d,S,{...g,...a,...x})}catch(x){this.logger.warn(x)}return v}else this.logger.warn(`there was no format function for ${p}`);return d},r)}}const E0=(e,r)=>{e.pending[r]!==void 0&&(delete e.pending[r],e.pendingCount--)};class C0 extends il{constructor(r,o,i,a={}){super(),this.backend=r,this.store=o,this.services=i,this.languageUtils=i.languageUtils,this.options=a,this.logger=Tn.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=a.maxParallelReads||10,this.readingCalls=0,this.maxRetries=a.maxRetries>=0?a.maxRetries:5,this.retryTimeout=a.retryTimeout>=1?a.retryTimeout:350,this.state={},this.queue=[],this.backend?.init?.(i,a.backend,a)}queueLoad(r,o,i,a){const l={},c={},d={},h={};return r.forEach(p=>{let g=!0;o.forEach(v=>{const x=`${p}|${v}`;!i.reload&&this.store.hasResourceBundle(p,v)?this.state[x]=2:this.state[x]<0||(this.state[x]===1?c[x]===void 0&&(c[x]=!0):(this.state[x]=1,g=!1,c[x]===void 0&&(c[x]=!0),l[x]===void 0&&(l[x]=!0),h[v]===void 0&&(h[v]=!0)))}),g||(d[p]=!0)}),(Object.keys(l).length||Object.keys(c).length)&&this.queue.push({pending:c,pendingCount:Object.keys(c).length,loaded:{},errors:[],callback:a}),{toLoad:Object.keys(l),pending:Object.keys(c),toLoadLanguages:Object.keys(d),toLoadNamespaces:Object.keys(h)}}loaded(r,o,i){const a=r.split("|"),l=a[0],c=a[1];o&&this.emit("failedLoading",l,c,o),!o&&i&&this.store.addResourceBundle(l,c,i,void 0,void 0,{skipCopy:!0}),this.state[r]=o?-1:2,o&&i&&(this.state[r]=0);const d={};this.queue.forEach(h=>{l0(h.loaded,[l],c),E0(h,r),o&&h.errors.push(o),h.pendingCount===0&&!h.done&&(Object.keys(h.loaded).forEach(p=>{d[p]||(d[p]={});const g=h.loaded[p];g.length&&g.forEach(v=>{d[p][v]===void 0&&(d[p][v]=!0)})}),h.done=!0,h.errors.length?h.callback(h.errors):h.callback())}),this.emit("loaded",d),this.queue=this.queue.filter(h=>!h.done)}read(r,o,i,a=0,l=this.retryTimeout,c){if(!r.length)return c(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:r,ns:o,fcName:i,tried:a,wait:l,callback:c});return}this.readingCalls++;const d=(p,g)=>{if(this.readingCalls--,this.waitingReads.length>0){const v=this.waitingReads.shift();this.read(v.lng,v.ns,v.fcName,v.tried,v.wait,v.callback)}if(p&&g&&a<this.maxRetries){setTimeout(()=>{this.read.call(this,r,o,i,a+1,l*2,c)},l);return}c(p,g)},h=this.backend[i].bind(this.backend);if(h.length===2){try{const p=h(r,o);p&&typeof p.then=="function"?p.then(g=>d(null,g)).catch(d):d(null,p)}catch(p){d(p)}return}return h(r,o,d)}prepareLoading(r,o,i={},a){if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),a&&a();Te(r)&&(r=this.languageUtils.toResolveHierarchy(r)),Te(o)&&(o=[o]);const l=this.queueLoad(r,o,i,a);if(!l.toLoad.length)return l.pending.length||a(),null;l.toLoad.forEach(c=>{this.loadOne(c)})}load(r,o,i){this.prepareLoading(r,o,{},i)}reload(r,o,i){this.prepareLoading(r,o,{reload:!0},i)}loadOne(r,o=""){const i=r.split("|"),a=i[0],l=i[1];this.read(a,l,"read",void 0,void 0,(c,d)=>{c&&this.logger.warn(`${o}loading namespace ${l} for language ${a} failed`,c),!c&&d&&this.logger.log(`${o}loaded namespace ${l} for language ${a}`,d),this.loaded(r,c,d)})}saveMissing(r,o,i,a,l,c={},d=()=>{}){if(this.services?.utils?.hasLoadedNamespace&&!this.services?.utils?.hasLoadedNamespace(o)){this.logger.warn(`did not save key "${i}" as the namespace "${o}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(i==null||i==="")){if(this.backend?.create){const h={...c,isUpdate:l},p=this.backend.create.bind(this.backend);if(p.length<6)try{let g;p.length===5?g=p(r,o,i,a,h):g=p(r,o,i,a),g&&typeof g.then=="function"?g.then(v=>d(null,v)).catch(d):d(null,g)}catch(g){d(g)}else p(r,o,i,a,d,h)}!r||!r[0]||this.store.addResource(r[0],o,i,a)}}}const Sp=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let r={};if(typeof e[1]=="object"&&(r=e[1]),Te(e[1])&&(r.defaultValue=e[1]),Te(e[2])&&(r.tDescription=e[2]),typeof e[2]=="object"||typeof e[3]=="object"){const o=e[3]||e[2];Object.keys(o).forEach(i=>{r[i]=o[i]})}return r},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0},cacheInBuiltFormats:!0}),Ep=e=>(Te(e.ns)&&(e.ns=[e.ns]),Te(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),Te(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs?.indexOf?.("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),typeof e.initImmediate=="boolean"&&(e.initAsync=e.initImmediate),e),ba=()=>{},k0=e=>{Object.getOwnPropertyNames(Object.getPrototypeOf(e)).forEach(o=>{typeof e[o]=="function"&&(e[o]=e[o].bind(e))})};class Yi extends il{constructor(r={},o){if(super(),this.options=Ep(r),this.services={},this.logger=Tn,this.modules={external:[]},k0(this),o&&!this.isInitialized&&!r.isClone){if(!this.options.initAsync)return this.init(r,o),this;setTimeout(()=>{this.init(r,o)},0)}}init(r={},o){this.isInitializing=!0,typeof r=="function"&&(o=r,r={}),r.defaultNS==null&&r.ns&&(Te(r.ns)?r.defaultNS=r.ns:r.ns.indexOf("translation")<0&&(r.defaultNS=r.ns[0]));const i=Sp();this.options={...i,...this.options,...Ep(r)},this.options.interpolation={...i.interpolation,...this.options.interpolation},r.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=r.keySeparator),r.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=r.nsSeparator);const a=p=>p?typeof p=="function"?new p:p:null;if(!this.options.isClone){this.modules.logger?Tn.init(a(this.modules.logger),this.options):Tn.init(null,this.options);let p;this.modules.formatter?p=this.modules.formatter:p=S0;const g=new gp(this.options);this.store=new hp(this.options.resources,this.options);const v=this.services;v.logger=Tn,v.resourceStore=this.store,v.languageUtils=g,v.pluralResolver=new v0(g,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),this.options.interpolation.format&&this.options.interpolation.format!==i.interpolation.format&&this.logger.warn("init: you are still using the legacy format function, please use the new approach: https://www.i18next.com/translation-function/formatting"),p&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(v.formatter=a(p),v.formatter.init&&v.formatter.init(v,this.options),this.options.interpolation.format=v.formatter.format.bind(v.formatter)),v.interpolator=new y0(this.options),v.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},v.backendConnector=new C0(a(this.modules.backend),v.resourceStore,v,this.options),v.backendConnector.on("*",(S,...C)=>{this.emit(S,...C)}),this.modules.languageDetector&&(v.languageDetector=a(this.modules.languageDetector),v.languageDetector.init&&v.languageDetector.init(v,this.options.detection,this.options)),this.modules.i18nFormat&&(v.i18nFormat=a(this.modules.i18nFormat),v.i18nFormat.init&&v.i18nFormat.init(this)),this.translator=new Ga(this.services,this.options),this.translator.on("*",(S,...C)=>{this.emit(S,...C)}),this.modules.external.forEach(S=>{S.init&&S.init(this)})}if(this.format=this.options.interpolation.format,o||(o=ba),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const p=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);p.length>0&&p[0]!=="dev"&&(this.options.lng=p[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(p=>{this[p]=(...g)=>this.store[p](...g)}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(p=>{this[p]=(...g)=>(this.store[p](...g),this)});const d=$i(),h=()=>{const p=(g,v)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),d.resolve(v),o(g,v)};if(this.languages&&!this.isInitialized)return p(null,this.t.bind(this));this.changeLanguage(this.options.lng,p)};return this.options.resources||!this.options.initAsync?h():setTimeout(h,0),d}loadResources(r,o=ba){let i=o;const a=Te(r)?r:this.language;if(typeof r=="function"&&(i=r),!this.options.resources||this.options.partialBundledLanguages){if(a?.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return i();const l=[],c=d=>{if(!d||d==="cimode")return;this.services.languageUtils.toResolveHierarchy(d).forEach(p=>{p!=="cimode"&&l.indexOf(p)<0&&l.push(p)})};a?c(a):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(h=>c(h)),this.options.preload?.forEach?.(d=>c(d)),this.services.backendConnector.load(l,this.options.ns,d=>{!d&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),i(d)})}else i(null)}reloadResources(r,o,i){const a=$i();return typeof r=="function"&&(i=r,r=void 0),typeof o=="function"&&(i=o,o=void 0),r||(r=this.languages),o||(o=this.options.ns),i||(i=ba),this.services.backendConnector.reload(r,o,l=>{a.resolve(),i(l)}),a}use(r){if(!r)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!r.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return r.type==="backend"&&(this.modules.backend=r),(r.type==="logger"||r.log&&r.warn&&r.error)&&(this.modules.logger=r),r.type==="languageDetector"&&(this.modules.languageDetector=r),r.type==="i18nFormat"&&(this.modules.i18nFormat=r),r.type==="postProcessor"&&$m.addPostProcessor(r),r.type==="formatter"&&(this.modules.formatter=r),r.type==="3rdParty"&&this.modules.external.push(r),this}setResolvedLanguage(r){if(!(!r||!this.languages)&&!(["cimode","dev"].indexOf(r)>-1)){for(let o=0;o<this.languages.length;o++){const i=this.languages[o];if(!(["cimode","dev"].indexOf(i)>-1)&&this.store.hasLanguageSomeTranslations(i)){this.resolvedLanguage=i;break}}!this.resolvedLanguage&&this.languages.indexOf(r)<0&&this.store.hasLanguageSomeTranslations(r)&&(this.resolvedLanguage=r,this.languages.unshift(r))}}changeLanguage(r,o){this.isLanguageChangingTo=r;const i=$i();this.emit("languageChanging",r);const a=d=>{this.language=d,this.languages=this.services.languageUtils.toResolveHierarchy(d),this.resolvedLanguage=void 0,this.setResolvedLanguage(d)},l=(d,h)=>{h?this.isLanguageChangingTo===r&&(a(h),this.translator.changeLanguage(h),this.isLanguageChangingTo=void 0,this.emit("languageChanged",h),this.logger.log("languageChanged",h)):this.isLanguageChangingTo=void 0,i.resolve((...p)=>this.t(...p)),o&&o(d,(...p)=>this.t(...p))},c=d=>{!r&&!d&&this.services.languageDetector&&(d=[]);const h=Te(d)?d:d&&d[0],p=this.store.hasLanguageSomeTranslations(h)?h:this.services.languageUtils.getBestMatchFromCodes(Te(d)?[d]:d);p&&(this.language||a(p),this.translator.language||this.translator.changeLanguage(p),this.services.languageDetector?.cacheUserLanguage?.(p)),this.loadResources(p,g=>{l(g,p)})};return!r&&this.services.languageDetector&&!this.services.languageDetector.async?c(this.services.languageDetector.detect()):!r&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(c):this.services.languageDetector.detect(c):c(r),i}getFixedT(r,o,i){const a=(l,c,...d)=>{let h;typeof c!="object"?h=this.options.overloadTranslationOptionHandler([l,c].concat(d)):h={...c},h.lng=h.lng||a.lng,h.lngs=h.lngs||a.lngs,h.ns=h.ns||a.ns,h.keyPrefix!==""&&(h.keyPrefix=h.keyPrefix||i||a.keyPrefix);const p=this.options.keySeparator||".";let g;return h.keyPrefix&&Array.isArray(l)?g=l.map(v=>`${h.keyPrefix}${p}${v}`):g=h.keyPrefix?`${h.keyPrefix}${p}${l}`:l,this.t(g,h)};return Te(r)?a.lng=r:a.lngs=r,a.ns=o,a.keyPrefix=i,a}t(...r){return this.translator?.translate(...r)}exists(...r){return this.translator?.exists(...r)}setDefaultNamespace(r){this.options.defaultNS=r}hasLoadedNamespace(r,o={}){if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const i=o.lng||this.resolvedLanguage||this.languages[0],a=this.options?this.options.fallbackLng:!1,l=this.languages[this.languages.length-1];if(i.toLowerCase()==="cimode")return!0;const c=(d,h)=>{const p=this.services.backendConnector.state[`${d}|${h}`];return p===-1||p===0||p===2};if(o.precheck){const d=o.precheck(this,c);if(d!==void 0)return d}return!!(this.hasResourceBundle(i,r)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||c(i,r)&&(!a||c(l,r)))}loadNamespaces(r,o){const i=$i();return this.options.ns?(Te(r)&&(r=[r]),r.forEach(a=>{this.options.ns.indexOf(a)<0&&this.options.ns.push(a)}),this.loadResources(a=>{i.resolve(),o&&o(a)}),i):(o&&o(),Promise.resolve())}loadLanguages(r,o){const i=$i();Te(r)&&(r=[r]);const a=this.options.preload||[],l=r.filter(c=>a.indexOf(c)<0&&this.services.languageUtils.isSupportedCode(c));return l.length?(this.options.preload=a.concat(l),this.loadResources(c=>{i.resolve(),o&&o(c)}),i):(o&&o(),Promise.resolve())}dir(r){if(r||(r=this.resolvedLanguage||(this.languages?.length>0?this.languages[0]:this.language)),!r)return"rtl";try{const a=new Intl.Locale(r);if(a&&a.getTextInfo){const l=a.getTextInfo();if(l&&l.direction)return l.direction}}catch{}const o=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],i=this.services?.languageUtils||new gp(Sp());return r.toLowerCase().indexOf("-latn")>1?"ltr":o.indexOf(i.getLanguagePartFromCode(r))>-1||r.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(r={},o){return new Yi(r,o)}cloneInstance(r={},o=ba){const i=r.forkResourceStore;i&&delete r.forkResourceStore;const a={...this.options,...r,isClone:!0},l=new Yi(a);if((r.debug!==void 0||r.prefix!==void 0)&&(l.logger=l.logger.clone(r)),["store","services","language"].forEach(d=>{l[d]=this[d]}),l.services={...this.services},l.services.utils={hasLoadedNamespace:l.hasLoadedNamespace.bind(l)},i){const d=Object.keys(this.store.data).reduce((h,p)=>(h[p]={...this.store.data[p]},h[p]=Object.keys(h[p]).reduce((g,v)=>(g[v]={...h[p][v]},g),h[p]),h),{});l.store=new hp(d,a),l.services.resourceStore=l.store}return l.translator=new Ga(l.services,a),l.translator.on("*",(d,...h)=>{l.emit(d,...h)}),l.init(a,o),l.translator.options=a,l.translator.backendConnector.services.utils={hasLoadedNamespace:l.hasLoadedNamespace.bind(l)},l}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const yt=Yi.createInstance();yt.createInstance=Yi.createInstance;yt.createInstance;yt.dir;yt.init;yt.loadResources;yt.reloadResources;yt.use;yt.changeLanguage;yt.getFixedT;yt.t;yt.exists;yt.setDefaultNamespace;yt.hasLoadedNamespace;yt.loadNamespaces;yt.loadLanguages;const b0=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,P0={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},R0=e=>P0[e],O0=e=>e.replace(b0,R0);let _c={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:O0};const L0=(e={})=>{_c={..._c,...e}},eO=()=>_c;let Um;const M0=e=>{Um=e},tO=()=>Um,T0={type:"3rdParty",init(e){L0(e.options.react),M0(e)}},_0=w.createContext();class nO{constructor(){this.usedNamespaces={}}addUsedNamespaces(r){r.forEach(o=>{this.usedNamespaces[o]||(this.usedNamespaces[o]=!0)})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function N0({i18n:e,defaultNS:r,children:o}){const i=w.useMemo(()=>({i18n:e,defaultNS:r}),[e,r]);return w.createElement(_0.Provider,{value:i},o)}const{slice:D0,forEach:A0}=[];function I0(e){return A0.call(D0.call(arguments,1),r=>{if(r)for(const o in r)e[o]===void 0&&(e[o]=r[o])}),e}function F0(e){return typeof e!="string"?!1:[/<\s*script.*?>/i,/<\s*\/\s*script\s*>/i,/<\s*img.*?on\w+\s*=/i,/<\s*\w+\s*on\w+\s*=.*?>/i,/javascript\s*:/i,/vbscript\s*:/i,/expression\s*\(/i,/eval\s*\(/i,/alert\s*\(/i,/document\.cookie/i,/document\.write\s*\(/i,/window\.location/i,/innerHTML/i].some(o=>o.test(e))}const Cp=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,j0=function(e,r){const i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{path:"/"},a=encodeURIComponent(r);let l=`${e}=${a}`;if(i.maxAge>0){const c=i.maxAge-0;if(Number.isNaN(c))throw new Error("maxAge should be a Number");l+=`; Max-Age=${Math.floor(c)}`}if(i.domain){if(!Cp.test(i.domain))throw new TypeError("option domain is invalid");l+=`; Domain=${i.domain}`}if(i.path){if(!Cp.test(i.path))throw new TypeError("option path is invalid");l+=`; Path=${i.path}`}if(i.expires){if(typeof i.expires.toUTCString!="function")throw new TypeError("option expires is invalid");l+=`; Expires=${i.expires.toUTCString()}`}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch(typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite){case!0:l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"strict":l+="; SameSite=Strict";break;case"none":l+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return i.partitioned&&(l+="; Partitioned"),l},kp={create(e,r,o,i){let a=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};o&&(a.expires=new Date,a.expires.setTime(a.expires.getTime()+o*60*1e3)),i&&(a.domain=i),document.cookie=j0(e,r,a)},read(e){const r=`${e}=`,o=document.cookie.split(";");for(let i=0;i<o.length;i++){let a=o[i];for(;a.charAt(0)===" ";)a=a.substring(1,a.length);if(a.indexOf(r)===0)return a.substring(r.length,a.length)}return null},remove(e,r){this.create(e,"",-1,r)}};var z0={name:"cookie",lookup(e){let{lookupCookie:r}=e;if(r&&typeof document<"u")return kp.read(r)||void 0},cacheUserLanguage(e,r){let{lookupCookie:o,cookieMinutes:i,cookieDomain:a,cookieOptions:l}=r;o&&typeof document<"u"&&kp.create(o,e,i,a,l)}},$0={name:"querystring",lookup(e){let{lookupQuerystring:r}=e,o;if(typeof window<"u"){let{search:i}=window.location;!window.location.search&&window.location.hash?.indexOf("?")>-1&&(i=window.location.hash.substring(window.location.hash.indexOf("?")));const l=i.substring(1).split("&");for(let c=0;c<l.length;c++){const d=l[c].indexOf("=");d>0&&l[c].substring(0,d)===r&&(o=l[c].substring(d+1))}}return o}},U0={name:"hash",lookup(e){let{lookupHash:r,lookupFromHashIndex:o}=e,i;if(typeof window<"u"){const{hash:a}=window.location;if(a&&a.length>2){const l=a.substring(1);if(r){const c=l.split("&");for(let d=0;d<c.length;d++){const h=c[d].indexOf("=");h>0&&c[d].substring(0,h)===r&&(i=c[d].substring(h+1))}}if(i)return i;if(!i&&o>-1){const c=a.match(/\/([a-zA-Z-]*)/g);return Array.isArray(c)?c[typeof o=="number"?o:0]?.replace("/",""):void 0}}}return i}};let Io=null;const bp=()=>{if(Io!==null)return Io;try{if(Io=typeof window<"u"&&window.localStorage!==null,!Io)return!1;const e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch{Io=!1}return Io};var H0={name:"localStorage",lookup(e){let{lookupLocalStorage:r}=e;if(r&&bp())return window.localStorage.getItem(r)||void 0},cacheUserLanguage(e,r){let{lookupLocalStorage:o}=r;o&&bp()&&window.localStorage.setItem(o,e)}};let Fo=null;const Pp=()=>{if(Fo!==null)return Fo;try{if(Fo=typeof window<"u"&&window.sessionStorage!==null,!Fo)return!1;const e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch{Fo=!1}return Fo};var B0={name:"sessionStorage",lookup(e){let{lookupSessionStorage:r}=e;if(r&&Pp())return window.sessionStorage.getItem(r)||void 0},cacheUserLanguage(e,r){let{lookupSessionStorage:o}=r;o&&Pp()&&window.sessionStorage.setItem(o,e)}},V0={name:"navigator",lookup(e){const r=[];if(typeof navigator<"u"){const{languages:o,userLanguage:i,language:a}=navigator;if(o)for(let l=0;l<o.length;l++)r.push(o[l]);i&&r.push(i),a&&r.push(a)}return r.length>0?r:void 0}},W0={name:"htmlTag",lookup(e){let{htmlTag:r}=e,o;const i=r||(typeof document<"u"?document.documentElement:null);return i&&typeof i.getAttribute=="function"&&(o=i.getAttribute("lang")),o}},K0={name:"path",lookup(e){let{lookupFromPathIndex:r}=e;if(typeof window>"u")return;const o=window.location.pathname.match(/\/([a-zA-Z-]*)/g);return Array.isArray(o)?o[typeof r=="number"?r:0]?.replace("/",""):void 0}},Q0={name:"subdomain",lookup(e){let{lookupFromSubdomainIndex:r}=e;const o=typeof r=="number"?r+1:1,i=typeof window<"u"&&window.location?.hostname?.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(i)return i[o]}};let Hm=!1;try{document.cookie,Hm=!0}catch{}const Bm=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Hm||Bm.splice(1,1);const G0=()=>({order:Bm,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:e=>e});class Vm{constructor(r){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.type="languageDetector",this.detectors={},this.init(r,o)}init(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{languageUtils:{}},o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=r,this.options=I0(o,this.options||{},G0()),typeof this.options.convertDetectedLanguage=="string"&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=a=>a.replace("-","_")),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=i,this.addDetector(z0),this.addDetector($0),this.addDetector(H0),this.addDetector(B0),this.addDetector(V0),this.addDetector(W0),this.addDetector(K0),this.addDetector(Q0),this.addDetector(U0)}addDetector(r){return this.detectors[r.name]=r,this}detect(){let r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.options.order,o=[];return r.forEach(i=>{if(this.detectors[i]){let a=this.detectors[i].lookup(this.options);a&&typeof a=="string"&&(a=[a]),a&&(o=o.concat(a))}}),o=o.filter(i=>i!=null&&!F0(i)).map(i=>this.options.convertDetectedLanguage(i)),this.services&&this.services.languageUtils&&this.services.languageUtils.getBestMatchFromCodes?o:o.length>0?o[0]:null}cacheUserLanguage(r){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.options.caches;o&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(r)>-1||o.forEach(i=>{this.detectors[i]&&this.detectors[i].cacheUserLanguage(r,this.options)}))}}Vm.type="languageDetector";const Y0={navigation:{home:"Home",findJobs:"Find Jobs",employers:"Employers",candidates:"Candidates",blog:"Blog"},actions:{uploadCV:"Upload your CV",jobPost:"Job Post",login:"Login",register:"Register",loginRegister:"Login / Register"},userMenu:{completeProfile:"Complete Profile",myApplications:"My Applications",savedJobs:"Saved Jobs",settings:"Settings",signOut:"Sign Out"},language:{switchLanguage:"Switch Language",english:"English",vietnamese:"Tiếng Việt"}},J0={loading:"Loading...",error:"Error",success:"Success",cancel:"Cancel",save:"Save",edit:"Edit",delete:"Delete",search:"Search",filter:"Filter",sort:"Sort",apply:"Apply",close:"Close",back:"Back",next:"Next",previous:"Previous",submit:"Submit",reset:"Reset"},X0={login:{title:"Sign In",subtitle:"Welcome back! Please sign in to your account",email:"Email Address",emailPlaceholder:"Enter your email address",password:"Password",passwordPlaceholder:"Enter your password",rememberMe:"Remember me",forgotPassword:"Forgot password?",signIn:"Sign In",signingIn:"Signing in...",noAccount:"Don't have an account?",signUp:"Sign up for free",authenticationError:"Authentication Error",loginFailed:"Login failed. Please try again."},register:{title:"Create Account",subtitle:"Join WorkFinder to find your dream job",firstName:"First Name",lastName:"Last Name",email:"Email Address",password:"Password",confirmPassword:"Confirm Password",agreeTerms:"I agree to the Terms of Service and Privacy Policy",createAccount:"Create Account",hasAccount:"Already have an account?",signIn:"Sign in here"}},q0={title:"Find Your Dream Job",searchPlaceholder:"Job title, keywords, or company",locationPlaceholder:"City or remote",searchButton:"Search Jobs",filters:{jobType:"Job Type",experience:"Experience Level",salary:"Salary Range",company:"Company",location:"Location"},jobTypes:{fullTime:"Full-time",partTime:"Part-time",contract:"Contract",freelance:"Freelance",internship:"Internship"},experienceLevels:{entry:"Entry Level",mid:"Mid Level",senior:"Senior Level",lead:"Lead/Manager"}},Z0={title:{main:"New Beginnings – Big Opportunities –",highlight:"Real Success",subtitle:""},description:"Start your career journey with amazing opportunities",search:{title:"Find Your Dream Job",subtitle:"Discover thousands of job opportunities from leading companies",jobPlaceholder:"Job title, keywords, or company",locationPlaceholder:"City or postcode",button:"Find Jobs"},popularSearches:"Popular Searches"},ex={header:Y0,common:J0,auth:X0,jobs:q0,hero:Z0},tx={navigation:{home:"Trang chủ",findJobs:"Tìm việc làm",employers:"Nhà tuyển dụng",candidates:"Ứng viên",blog:"Blog"},actions:{uploadCV:"Tải lên CV của bạn",jobPost:"Đăng tin tuyển dụng",login:"Đăng nhập",register:"Đăng ký",loginRegister:"Đăng nhập / Đăng ký"},userMenu:{completeProfile:"Hoàn thiện hồ sơ",myApplications:"Đơn ứng tuyển của tôi",savedJobs:"Việc làm đã lưu",settings:"Cài đặt",signOut:"Đăng xuất"},language:{switchLanguage:"Chuyển đổi ngôn ngữ",english:"English",vietnamese:"Tiếng Việt"}},nx={loading:"Đang tải...",error:"Lỗi",success:"Thành công",cancel:"Hủy",save:"Lưu",edit:"Chỉnh sửa",delete:"Xóa",search:"Tìm kiếm",filter:"Lọc",sort:"Sắp xếp",apply:"Áp dụng",close:"Đóng",back:"Quay lại",next:"Tiếp theo",previous:"Trước đó",submit:"Gửi",reset:"Đặt lại"},rx={login:{title:"Đăng nhập",subtitle:"Chào mừng bạn trở lại! Vui lòng đăng nhập vào tài khoản của bạn",email:"Địa chỉ email",emailPlaceholder:"Nhập địa chỉ email của bạn",password:"Mật khẩu",passwordPlaceholder:"Nhập mật khẩu của bạn",rememberMe:"Ghi nhớ đăng nhập",forgotPassword:"Quên mật khẩu?",signIn:"Đăng nhập",signingIn:"Đang đăng nhập...",noAccount:"Chưa có tài khoản?",signUp:"Đăng ký miễn phí",authenticationError:"Lỗi xác thực",loginFailed:"Đăng nhập thất bại. Vui lòng thử lại."},register:{title:"Tạo tài khoản",subtitle:"Tham gia WorkFinder để tìm công việc mơ ước của bạn",firstName:"Tên",lastName:"Họ",email:"Địa chỉ email",password:"Mật khẩu",confirmPassword:"Xác nhận mật khẩu",agreeTerms:"Tôi đồng ý với Điều khoản dịch vụ và Chính sách bảo mật",createAccount:"Tạo tài khoản",hasAccount:"Đã có tài khoản?",signIn:"Đăng nhập tại đây"}},ox={title:"Tìm công việc mơ ước của bạn",searchPlaceholder:"Tên công việc, từ khóa hoặc công ty",locationPlaceholder:"Thành phố hoặc làm việc từ xa",searchButton:"Tìm việc làm",filters:{jobType:"Loại công việc",experience:"Mức độ kinh nghiệm",salary:"Mức lương",company:"Công ty",location:"Địa điểm"},jobTypes:{fullTime:"Toàn thời gian",partTime:"Bán thời gian",contract:"Hợp đồng",freelance:"Tự do",internship:"Thực tập"},experienceLevels:{entry:"Mới bắt đầu",mid:"Trung cấp",senior:"Cao cấp",lead:"Trưởng nhóm/Quản lý"}},ix={title:{main:"Khởi đầu mới – Cơ hội lớn –",highlight:"Thành công thật",subtitle:""},description:"Bắt đầu hành trình sự nghiệp với những cơ hội tuyệt vời",search:{title:"Tìm việc làm mơ ước",subtitle:"Khám phá hàng nghìn cơ hội việc làm từ các công ty hàng đầu",jobPlaceholder:"Tên công việc, từ khóa hoặc công ty",locationPlaceholder:"Thành phố hoặc mã bưu điện",button:"Tìm việc làm"},popularSearches:"Tìm kiếm phổ biến"},sx={header:tx,common:nx,auth:rx,jobs:ox,hero:ix},rO={en:"English",vi:"Tiếng Việt"};yt.use(Vm).use(T0).init({resources:{en:{translation:ex},vi:{translation:sx}},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],excludeCacheFor:["cimode"]},fallbackLng:"en",defaultNS:"translation",debug:!1,interpolation:{escapeValue:!1},react:{bindI18n:"languageChanged",bindI18nStore:"added removed",useSuspense:!1}});const ax="modulepreload",lx=function(e){return"/"+e},Rp={},Ct=function(r,o,i){let a=Promise.resolve();if(o&&o.length>0){let h=function(p){return Promise.all(p.map(g=>Promise.resolve(g).then(v=>({status:"fulfilled",value:v}),v=>({status:"rejected",reason:v}))))};document.getElementsByTagName("link");const c=document.querySelector("meta[property=csp-nonce]"),d=c?.nonce||c?.getAttribute("nonce");a=h(o.map(p=>{if(p=lx(p),p in Rp)return;Rp[p]=!0;const g=p.endsWith(".css"),v=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${p}"]${v}`))return;const x=document.createElement("link");if(x.rel=g?"stylesheet":ax,g||(x.as="script"),x.crossOrigin="",x.href=p,d&&x.setAttribute("nonce",d),document.head.appendChild(x),g)return new Promise((S,C)=>{x.addEventListener("load",S),x.addEventListener("error",()=>C(new Error(`Unable to preload CSS for ${p}`)))})}))}function l(c){const d=new Event("vite:preloadError",{cancelable:!0});if(d.payload=c,window.dispatchEvent(d),!d.defaultPrevented)throw c}return a.then(c=>{for(const d of c||[])d.status==="rejected"&&l(d.reason);return r().catch(l)})};var sl=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},al=typeof window>"u"||"Deno"in globalThis;function xn(){}function ux(e,r){return typeof e=="function"?e(r):e}function cx(e){return typeof e=="number"&&e>=0&&e!==1/0}function dx(e,r){return Math.max(e+(r||0)-Date.now(),0)}function Nc(e,r){return typeof e=="function"?e(r):e}function fx(e,r){return typeof e=="function"?e(r):e}function Op(e,r){const{type:o="all",exact:i,fetchStatus:a,predicate:l,queryKey:c,stale:d}=e;if(c){if(i){if(r.queryHash!==ed(c,r.options))return!1}else if(!Xi(r.queryKey,c))return!1}if(o!=="all"){const h=r.isActive();if(o==="active"&&!h||o==="inactive"&&h)return!1}return!(typeof d=="boolean"&&r.isStale()!==d||a&&a!==r.state.fetchStatus||l&&!l(r))}function Lp(e,r){const{exact:o,status:i,predicate:a,mutationKey:l}=e;if(l){if(!r.options.mutationKey)return!1;if(o){if(Ji(r.options.mutationKey)!==Ji(l))return!1}else if(!Xi(r.options.mutationKey,l))return!1}return!(i&&r.state.status!==i||a&&!a(r))}function ed(e,r){return(r?.queryKeyHashFn||Ji)(e)}function Ji(e){return JSON.stringify(e,(r,o)=>Dc(o)?Object.keys(o).sort().reduce((i,a)=>(i[a]=o[a],i),{}):o)}function Xi(e,r){return e===r?!0:typeof e!=typeof r?!1:e&&r&&typeof e=="object"&&typeof r=="object"?Object.keys(r).every(o=>Xi(e[o],r[o])):!1}function Wm(e,r){if(e===r)return e;const o=Mp(e)&&Mp(r);if(o||Dc(e)&&Dc(r)){const i=o?e:Object.keys(e),a=i.length,l=o?r:Object.keys(r),c=l.length,d=o?[]:{},h=new Set(i);let p=0;for(let g=0;g<c;g++){const v=o?g:l[g];(!o&&h.has(v)||o)&&e[v]===void 0&&r[v]===void 0?(d[v]=void 0,p++):(d[v]=Wm(e[v],r[v]),d[v]===e[v]&&e[v]!==void 0&&p++)}return a===c&&p===a?e:d}return r}function Mp(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Dc(e){if(!Tp(e))return!1;const r=e.constructor;if(r===void 0)return!0;const o=r.prototype;return!(!Tp(o)||!o.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Tp(e){return Object.prototype.toString.call(e)==="[object Object]"}function hx(e){return new Promise(r=>{setTimeout(r,e)})}function px(e,r,o){return typeof o.structuralSharing=="function"?o.structuralSharing(e,r):o.structuralSharing!==!1?Wm(e,r):r}function mx(e,r,o=0){const i=[...e,r];return o&&i.length>o?i.slice(1):i}function gx(e,r,o=0){const i=[r,...e];return o&&i.length>o?i.slice(0,-1):i}var td=Symbol();function Km(e,r){return!e.queryFn&&r?.initialPromise?()=>r.initialPromise:!e.queryFn||e.queryFn===td?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var vx=class extends sl{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!al&&window.addEventListener){const r=()=>e();return window.addEventListener("visibilitychange",r,!1),()=>{window.removeEventListener("visibilitychange",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){const e=this.isFocused();this.listeners.forEach(r=>{r(e)})}isFocused(){return typeof this.#e=="boolean"?this.#e:globalThis.document?.visibilityState!=="hidden"}},Qm=new vx,yx=class extends sl{#e=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!al&&window.addEventListener){const r=()=>e(!0),o=()=>e(!1);return window.addEventListener("online",r,!1),window.addEventListener("offline",o,!1),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",o)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#e!==e&&(this.#e=e,this.listeners.forEach(o=>{o(e)}))}isOnline(){return this.#e}},Ya=new yx;function wx(){let e,r;const o=new Promise((a,l)=>{e=a,r=l});o.status="pending",o.catch(()=>{});function i(a){Object.assign(o,a),delete o.resolve,delete o.reject}return o.resolve=a=>{i({status:"fulfilled",value:a}),e(a)},o.reject=a=>{i({status:"rejected",reason:a}),r(a)},o}function xx(e){return Math.min(1e3*2**e,3e4)}function Gm(e){return(e??"online")==="online"?Ya.isOnline():!0}var Ym=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function uc(e){return e instanceof Ym}function Jm(e){let r=!1,o=0,i=!1,a;const l=wx(),c=E=>{i||(x(new Ym(E)),e.abort?.())},d=()=>{r=!0},h=()=>{r=!1},p=()=>Qm.isFocused()&&(e.networkMode==="always"||Ya.isOnline())&&e.canRun(),g=()=>Gm(e.networkMode)&&e.canRun(),v=E=>{i||(i=!0,e.onSuccess?.(E),a?.(),l.resolve(E))},x=E=>{i||(i=!0,e.onError?.(E),a?.(),l.reject(E))},S=()=>new Promise(E=>{a=b=>{(i||p())&&E(b)},e.onPause?.()}).then(()=>{a=void 0,i||e.onContinue?.()}),C=()=>{if(i)return;let E;const b=o===0?e.initialPromise:void 0;try{E=b??e.fn()}catch(P){E=Promise.reject(P)}Promise.resolve(E).then(v).catch(P=>{if(i)return;const D=e.retry??(al?0:3),N=e.retryDelay??xx,j=typeof N=="function"?N(o,P):N,H=D===!0||typeof D=="number"&&o<D||typeof D=="function"&&D(o,P);if(r||!H){x(P);return}o++,e.onFail?.(o,P),hx(j).then(()=>p()?void 0:S()).then(()=>{r?x(P):C()})})};return{promise:l,cancel:c,continue:()=>(a?.(),l),cancelRetry:d,continueRetry:h,canStart:g,start:()=>(g()?C():S().then(C),l)}}var Sx=e=>setTimeout(e,0);function Ex(){let e=[],r=0,o=d=>{d()},i=d=>{d()},a=Sx;const l=d=>{r?e.push(d):a(()=>{o(d)})},c=()=>{const d=e;e=[],d.length&&a(()=>{i(()=>{d.forEach(h=>{o(h)})})})};return{batch:d=>{let h;r++;try{h=d()}finally{r--,r||c()}return h},batchCalls:d=>(...h)=>{l(()=>{d(...h)})},schedule:l,setNotifyFunction:d=>{o=d},setBatchNotifyFunction:d=>{i=d},setScheduler:d=>{a=d}}}var Rt=Ex(),Xm=class{#e;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),cx(this.gcTime)&&(this.#e=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(al?1/0:300*1e3))}clearGcTimeout(){this.#e&&(clearTimeout(this.#e),this.#e=void 0)}},Cx=class extends Xm{#e;#t;#n;#o;#r;#s;#a;constructor(e){super(),this.#a=!1,this.#s=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#o=e.client,this.#n=this.#o.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#e=bx(this.options),this.state=e.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#r?.promise}setOptions(e){this.options={...this.#s,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&this.#n.remove(this)}setData(e,r){const o=px(this.state.data,e,this.options);return this.#i({data:o,type:"success",dataUpdatedAt:r?.updatedAt,manual:r?.manual}),o}setState(e,r){this.#i({type:"setState",state:e,setStateOptions:r})}cancel(e){const r=this.#r?.promise;return this.#r?.cancel(e),r?r.then(xn).catch(xn):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(e=>fx(e.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===td||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStatic(){return this.getObserversCount()>0?this.observers.some(e=>Nc(e.options.staleTime,this)==="static"):!1}isStale(){return this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):this.state.data===void 0||this.state.isInvalidated}isStaleByTime(e=0){return this.state.data===void 0?!0:e==="static"?!1:this.state.isInvalidated?!0:!dx(this.state.dataUpdatedAt,e)}onFocus(){this.observers.find(r=>r.shouldFetchOnWindowFocus())?.refetch({cancelRefetch:!1}),this.#r?.continue()}onOnline(){this.observers.find(r=>r.shouldFetchOnReconnect())?.refetch({cancelRefetch:!1}),this.#r?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#n.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(r=>r!==e),this.observers.length||(this.#r&&(this.#a?this.#r.cancel({revert:!0}):this.#r.cancelRetry()),this.scheduleGc()),this.#n.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#i({type:"invalidate"})}fetch(e,r){if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&r?.cancelRefetch)this.cancel({silent:!0});else if(this.#r)return this.#r.continueRetry(),this.#r.promise}if(e&&this.setOptions(e),!this.options.queryFn){const h=this.observers.find(p=>p.options.queryFn);h&&this.setOptions(h.options)}const o=new AbortController,i=h=>{Object.defineProperty(h,"signal",{enumerable:!0,get:()=>(this.#a=!0,o.signal)})},a=()=>{const h=Km(this.options,r),g=(()=>{const v={client:this.#o,queryKey:this.queryKey,meta:this.meta};return i(v),v})();return this.#a=!1,this.options.persister?this.options.persister(h,g,this):h(g)},c=(()=>{const h={fetchOptions:r,options:this.options,queryKey:this.queryKey,client:this.#o,state:this.state,fetchFn:a};return i(h),h})();this.options.behavior?.onFetch(c,this),this.#t=this.state,(this.state.fetchStatus==="idle"||this.state.fetchMeta!==c.fetchOptions?.meta)&&this.#i({type:"fetch",meta:c.fetchOptions?.meta});const d=h=>{uc(h)&&h.silent||this.#i({type:"error",error:h}),uc(h)||(this.#n.config.onError?.(h,this),this.#n.config.onSettled?.(this.state.data,h,this)),this.scheduleGc()};return this.#r=Jm({initialPromise:r?.initialPromise,fn:c.fetchFn,abort:o.abort.bind(o),onSuccess:h=>{if(h===void 0){d(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(h)}catch(p){d(p);return}this.#n.config.onSuccess?.(h,this),this.#n.config.onSettled?.(h,this.state.error,this),this.scheduleGc()},onError:d,onFail:(h,p)=>{this.#i({type:"failed",failureCount:h,error:p})},onPause:()=>{this.#i({type:"pause"})},onContinue:()=>{this.#i({type:"continue"})},retry:c.options.retry,retryDelay:c.options.retryDelay,networkMode:c.options.networkMode,canRun:()=>!0}),this.#r.start()}#i(e){const r=o=>{switch(e.type){case"failed":return{...o,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...o,fetchStatus:"paused"};case"continue":return{...o,fetchStatus:"fetching"};case"fetch":return{...o,...kx(o.data,this.options),fetchMeta:e.meta??null};case"success":return this.#t=void 0,{...o,data:e.data,dataUpdateCount:o.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const i=e.error;return uc(i)&&i.revert&&this.#t?{...this.#t,fetchStatus:"idle"}:{...o,error:i,errorUpdateCount:o.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:o.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"};case"invalidate":return{...o,isInvalidated:!0};case"setState":return{...o,...e.state}}};this.state=r(this.state),Rt.batch(()=>{this.observers.forEach(o=>{o.onQueryUpdate()}),this.#n.notify({query:this,type:"updated",action:e})})}};function kx(e,r){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Gm(r.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function bx(e){const r=typeof e.initialData=="function"?e.initialData():e.initialData,o=r!==void 0,i=o?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:r,dataUpdateCount:0,dataUpdatedAt:o?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:o?"success":"pending",fetchStatus:"idle"}}var Px=class extends sl{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,r,o){const i=r.queryKey,a=r.queryHash??ed(i,r);let l=this.get(a);return l||(l=new Cx({client:e,queryKey:i,queryHash:a,options:e.defaultQueryOptions(r),state:o,defaultOptions:e.getQueryDefaults(i)}),this.add(l)),l}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){const r=this.#e.get(e.queryHash);r&&(e.destroy(),r===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){Rt.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){const r={exact:!0,...e};return this.getAll().find(o=>Op(r,o))}findAll(e={}){const r=this.getAll();return Object.keys(e).length>0?r.filter(o=>Op(e,o)):r}notify(e){Rt.batch(()=>{this.listeners.forEach(r=>{r(e)})})}onFocus(){Rt.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){Rt.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},Rx=class extends Xm{#e;#t;#n;constructor(e){super(),this.mutationId=e.mutationId,this.#t=e.mutationCache,this.#e=[],this.state=e.state||Ox(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#e.includes(e)||(this.#e.push(e),this.clearGcTimeout(),this.#t.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#e=this.#e.filter(r=>r!==e),this.scheduleGc(),this.#t.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#e.length||(this.state.status==="pending"?this.scheduleGc():this.#t.remove(this))}continue(){return this.#n?.continue()??this.execute(this.state.variables)}async execute(e){const r=()=>{this.#o({type:"continue"})};this.#n=Jm({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(new Error("No mutationFn found")),onFail:(a,l)=>{this.#o({type:"failed",failureCount:a,error:l})},onPause:()=>{this.#o({type:"pause"})},onContinue:r,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#t.canRun(this)});const o=this.state.status==="pending",i=!this.#n.canStart();try{if(o)r();else{this.#o({type:"pending",variables:e,isPaused:i}),await this.#t.config.onMutate?.(e,this);const l=await this.options.onMutate?.(e);l!==this.state.context&&this.#o({type:"pending",context:l,variables:e,isPaused:i})}const a=await this.#n.start();return await this.#t.config.onSuccess?.(a,e,this.state.context,this),await this.options.onSuccess?.(a,e,this.state.context),await this.#t.config.onSettled?.(a,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(a,null,e,this.state.context),this.#o({type:"success",data:a}),a}catch(a){try{throw await this.#t.config.onError?.(a,e,this.state.context,this),await this.options.onError?.(a,e,this.state.context),await this.#t.config.onSettled?.(void 0,a,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,a,e,this.state.context),a}finally{this.#o({type:"error",error:a})}}finally{this.#t.runNext(this)}}#o(e){const r=o=>{switch(e.type){case"failed":return{...o,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...o,isPaused:!0};case"continue":return{...o,isPaused:!1};case"pending":return{...o,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...o,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...o,data:void 0,error:e.error,failureCount:o.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}};this.state=r(this.state),Rt.batch(()=>{this.#e.forEach(o=>{o.onMutationUpdate(e)}),this.#t.notify({mutation:this,type:"updated",action:e})})}};function Ox(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Lx=class extends sl{constructor(e={}){super(),this.config=e,this.#e=new Set,this.#t=new Map,this.#n=0}#e;#t;#n;build(e,r,o){const i=new Rx({mutationCache:this,mutationId:++this.#n,options:e.defaultMutationOptions(r),state:o});return this.add(i),i}add(e){this.#e.add(e);const r=Pa(e);if(typeof r=="string"){const o=this.#t.get(r);o?o.push(e):this.#t.set(r,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#e.delete(e)){const r=Pa(e);if(typeof r=="string"){const o=this.#t.get(r);if(o)if(o.length>1){const i=o.indexOf(e);i!==-1&&o.splice(i,1)}else o[0]===e&&this.#t.delete(r)}}this.notify({type:"removed",mutation:e})}canRun(e){const r=Pa(e);if(typeof r=="string"){const i=this.#t.get(r)?.find(a=>a.state.status==="pending");return!i||i===e}else return!0}runNext(e){const r=Pa(e);return typeof r=="string"?this.#t.get(r)?.find(i=>i!==e&&i.state.isPaused)?.continue()??Promise.resolve():Promise.resolve()}clear(){Rt.batch(()=>{this.#e.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#e.clear(),this.#t.clear()})}getAll(){return Array.from(this.#e)}find(e){const r={exact:!0,...e};return this.getAll().find(o=>Lp(r,o))}findAll(e={}){return this.getAll().filter(r=>Lp(e,r))}notify(e){Rt.batch(()=>{this.listeners.forEach(r=>{r(e)})})}resumePausedMutations(){const e=this.getAll().filter(r=>r.state.isPaused);return Rt.batch(()=>Promise.all(e.map(r=>r.continue().catch(xn))))}};function Pa(e){return e.options.scope?.id}function _p(e){return{onFetch:(r,o)=>{const i=r.options,a=r.fetchOptions?.meta?.fetchMore?.direction,l=r.state.data?.pages||[],c=r.state.data?.pageParams||[];let d={pages:[],pageParams:[]},h=0;const p=async()=>{let g=!1;const v=C=>{Object.defineProperty(C,"signal",{enumerable:!0,get:()=>(r.signal.aborted?g=!0:r.signal.addEventListener("abort",()=>{g=!0}),r.signal)})},x=Km(r.options,r.fetchOptions),S=async(C,E,b)=>{if(g)return Promise.reject();if(E==null&&C.pages.length)return Promise.resolve(C);const D=(()=>{const M={client:r.client,queryKey:r.queryKey,pageParam:E,direction:b?"backward":"forward",meta:r.options.meta};return v(M),M})(),N=await x(D),{maxPages:j}=r.options,H=b?gx:mx;return{pages:H(C.pages,N,j),pageParams:H(C.pageParams,E,j)}};if(a&&l.length){const C=a==="backward",E=C?Mx:Np,b={pages:l,pageParams:c},P=E(i,b);d=await S(b,P,C)}else{const C=e??l.length;do{const E=h===0?c[0]??i.initialPageParam:Np(i,d);if(h>0&&E==null)break;d=await S(d,E),h++}while(h<C)}return d};r.options.persister?r.fetchFn=()=>r.options.persister?.(p,{client:r.client,queryKey:r.queryKey,meta:r.options.meta,signal:r.signal},o):r.fetchFn=p}}}function Np(e,{pages:r,pageParams:o}){const i=r.length-1;return r.length>0?e.getNextPageParam(r[i],r,o[i],o):void 0}function Mx(e,{pages:r,pageParams:o}){return r.length>0?e.getPreviousPageParam?.(r[0],r,o[0],o):void 0}var Tx=class{#e;#t;#n;#o;#r;#s;#a;#i;constructor(e={}){this.#e=e.queryCache||new Px,this.#t=e.mutationCache||new Lx,this.#n=e.defaultOptions||{},this.#o=new Map,this.#r=new Map,this.#s=0}mount(){this.#s++,this.#s===1&&(this.#a=Qm.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#e.onFocus())}),this.#i=Ya.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#e.onOnline())}))}unmount(){this.#s--,this.#s===0&&(this.#a?.(),this.#a=void 0,this.#i?.(),this.#i=void 0)}isFetching(e){return this.#e.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#t.findAll({...e,status:"pending"}).length}getQueryData(e){const r=this.defaultQueryOptions({queryKey:e});return this.#e.get(r.queryHash)?.state.data}ensureQueryData(e){const r=this.defaultQueryOptions(e),o=this.#e.build(this,r),i=o.state.data;return i===void 0?this.fetchQuery(e):(e.revalidateIfStale&&o.isStaleByTime(Nc(r.staleTime,o))&&this.prefetchQuery(r),Promise.resolve(i))}getQueriesData(e){return this.#e.findAll(e).map(({queryKey:r,state:o})=>{const i=o.data;return[r,i]})}setQueryData(e,r,o){const i=this.defaultQueryOptions({queryKey:e}),l=this.#e.get(i.queryHash)?.state.data,c=ux(r,l);if(c!==void 0)return this.#e.build(this,i).setData(c,{...o,manual:!0})}setQueriesData(e,r,o){return Rt.batch(()=>this.#e.findAll(e).map(({queryKey:i})=>[i,this.setQueryData(i,r,o)]))}getQueryState(e){const r=this.defaultQueryOptions({queryKey:e});return this.#e.get(r.queryHash)?.state}removeQueries(e){const r=this.#e;Rt.batch(()=>{r.findAll(e).forEach(o=>{r.remove(o)})})}resetQueries(e,r){const o=this.#e;return Rt.batch(()=>(o.findAll(e).forEach(i=>{i.reset()}),this.refetchQueries({type:"active",...e},r)))}cancelQueries(e,r={}){const o={revert:!0,...r},i=Rt.batch(()=>this.#e.findAll(e).map(a=>a.cancel(o)));return Promise.all(i).then(xn).catch(xn)}invalidateQueries(e,r={}){return Rt.batch(()=>(this.#e.findAll(e).forEach(o=>{o.invalidate()}),e?.refetchType==="none"?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},r)))}refetchQueries(e,r={}){const o={...r,cancelRefetch:r.cancelRefetch??!0},i=Rt.batch(()=>this.#e.findAll(e).filter(a=>!a.isDisabled()&&!a.isStatic()).map(a=>{let l=a.fetch(void 0,o);return o.throwOnError||(l=l.catch(xn)),a.state.fetchStatus==="paused"?Promise.resolve():l}));return Promise.all(i).then(xn)}fetchQuery(e){const r=this.defaultQueryOptions(e);r.retry===void 0&&(r.retry=!1);const o=this.#e.build(this,r);return o.isStaleByTime(Nc(r.staleTime,o))?o.fetch(r):Promise.resolve(o.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(xn).catch(xn)}fetchInfiniteQuery(e){return e.behavior=_p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(xn).catch(xn)}ensureInfiniteQueryData(e){return e.behavior=_p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ya.isOnline()?this.#t.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#e}getMutationCache(){return this.#t}getDefaultOptions(){return this.#n}setDefaultOptions(e){this.#n=e}setQueryDefaults(e,r){this.#o.set(Ji(e),{queryKey:e,defaultOptions:r})}getQueryDefaults(e){const r=[...this.#o.values()],o={};return r.forEach(i=>{Xi(e,i.queryKey)&&Object.assign(o,i.defaultOptions)}),o}setMutationDefaults(e,r){this.#r.set(Ji(e),{mutationKey:e,defaultOptions:r})}getMutationDefaults(e){const r=[...this.#r.values()],o={};return r.forEach(i=>{Xi(e,i.mutationKey)&&Object.assign(o,i.defaultOptions)}),o}defaultQueryOptions(e){if(e._defaulted)return e;const r={...this.#n.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return r.queryHash||(r.queryHash=ed(r.queryKey,r)),r.refetchOnReconnect===void 0&&(r.refetchOnReconnect=r.networkMode!=="always"),r.throwOnError===void 0&&(r.throwOnError=!!r.suspense),!r.networkMode&&r.persister&&(r.networkMode="offlineFirst"),r.queryFn===td&&(r.enabled=!1),r}defaultMutationOptions(e){return e?._defaulted?e:{...this.#n.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#e.clear(),this.#t.clear()}},qm=w.createContext(void 0),_x=e=>{const r=w.useContext(qm);if(!r)throw new Error("No QueryClient set, use QueryClientProvider to set one");return r},Nx=({client:e,children:r})=>(w.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),F.jsx(qm.Provider,{value:e,children:r}));/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var Zm=e=>{throw TypeError(e)},Dx=(e,r,o)=>r.has(e)||Zm("Cannot "+o),cc=(e,r,o)=>(Dx(e,r,"read from private field"),o?o.call(e):r.get(e)),Ax=(e,r,o)=>r.has(e)?Zm("Cannot add the same private member more than once"):r instanceof WeakSet?r.add(e):r.set(e,o),Dp="popstate";function Ix(e={}){function r(i,a){let{pathname:l,search:c,hash:d}=i.location;return qi("",{pathname:l,search:c,hash:d},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function o(i,a){return typeof a=="string"?a:Rr(a)}return jx(r,o,null,e)}function Ae(e,r){if(e===!1||e===null||typeof e>"u")throw new Error(r)}function rt(e,r){if(!e){typeof console<"u"&&console.warn(r);try{throw new Error(r)}catch{}}}function Fx(){return Math.random().toString(36).substring(2,10)}function Ap(e,r){return{usr:e.state,key:e.key,idx:r}}function qi(e,r,o=null,i){return{pathname:typeof e=="string"?e:e.pathname,search:"",hash:"",...typeof r=="string"?Tr(r):r,state:o,key:r&&r.key||i||Fx()}}function Rr({pathname:e="/",search:r="",hash:o=""}){return r&&r!=="?"&&(e+=r.charAt(0)==="?"?r:"?"+r),o&&o!=="#"&&(e+=o.charAt(0)==="#"?o:"#"+o),e}function Tr(e){let r={};if(e){let o=e.indexOf("#");o>=0&&(r.hash=e.substring(o),e=e.substring(0,o));let i=e.indexOf("?");i>=0&&(r.search=e.substring(i),e=e.substring(0,i)),e&&(r.pathname=e)}return r}function jx(e,r,o,i={}){let{window:a=document.defaultView,v5Compat:l=!1}=i,c=a.history,d="POP",h=null,p=g();p==null&&(p=0,c.replaceState({...c.state,idx:p},""));function g(){return(c.state||{idx:null}).idx}function v(){d="POP";let b=g(),P=b==null?null:b-p;p=b,h&&h({action:d,location:E.location,delta:P})}function x(b,P){d="PUSH";let D=qi(E.location,b,P);p=g()+1;let N=Ap(D,p),j=E.createHref(D);try{c.pushState(N,"",j)}catch(H){if(H instanceof DOMException&&H.name==="DataCloneError")throw H;a.location.assign(j)}l&&h&&h({action:d,location:E.location,delta:1})}function S(b,P){d="REPLACE";let D=qi(E.location,b,P);p=g();let N=Ap(D,p),j=E.createHref(D);c.replaceState(N,"",j),l&&h&&h({action:d,location:E.location,delta:0})}function C(b){return eg(b)}let E={get action(){return d},get location(){return e(a,c)},listen(b){if(h)throw new Error("A history only accepts one active listener");return a.addEventListener(Dp,v),h=b,()=>{a.removeEventListener(Dp,v),h=null}},createHref(b){return r(a,b)},createURL:C,encodeLocation(b){let P=C(b);return{pathname:P.pathname,search:P.search,hash:P.hash}},push:x,replace:S,go(b){return c.go(b)}};return E}function eg(e,r=!1){let o="http://localhost";typeof window<"u"&&(o=window.location.origin!=="null"?window.location.origin:window.location.href),Ae(o,"No window.location.(origin|href) available to create URL");let i=typeof e=="string"?e:Rr(e);return i=i.replace(/ $/,"%20"),!r&&i.startsWith("//")&&(i=o+i),new URL(i,o)}var Wi,Ip=class{constructor(e){if(Ax(this,Wi,new Map),e)for(let[r,o]of e)this.set(r,o)}get(e){if(cc(this,Wi).has(e))return cc(this,Wi).get(e);if(e.defaultValue!==void 0)return e.defaultValue;throw new Error("No value found for context")}set(e,r){cc(this,Wi).set(e,r)}};Wi=new WeakMap;var zx=new Set(["lazy","caseSensitive","path","id","index","children"]);function $x(e){return zx.has(e)}var Ux=new Set(["lazy","caseSensitive","path","id","index","unstable_middleware","children"]);function Hx(e){return Ux.has(e)}function Bx(e){return e.index===!0}function Zi(e,r,o=[],i={},a=!1){return e.map((l,c)=>{let d=[...o,String(c)],h=typeof l.id=="string"?l.id:d.join("-");if(Ae(l.index!==!0||!l.children,"Cannot specify children on an index route"),Ae(a||!i[h],`Found a route id collision on id "${h}".  Route id's must be globally unique within Data Router usages`),Bx(l)){let p={...l,...r(l),id:h};return i[h]=p,p}else{let p={...l,...r(l),id:h,children:void 0};return i[h]=p,l.children&&(p.children=Zi(l.children,r,d,i,a)),p}})}function Pr(e,r,o="/"){return ja(e,r,o,!1)}function ja(e,r,o,i){let a=typeof r=="string"?Tr(r):r,l=an(a.pathname||"/",o);if(l==null)return null;let c=tg(e);Wx(c);let d=null;for(let h=0;d==null&&h<c.length;++h){let p=nS(l);d=eS(c[h],p,i)}return d}function Vx(e,r){let{route:o,pathname:i,params:a}=e;return{id:o.id,pathname:i,params:a,data:r[o.id],handle:o.handle}}function tg(e,r=[],o=[],i=""){let a=(l,c,d)=>{let h={relativePath:d===void 0?l.path||"":d,caseSensitive:l.caseSensitive===!0,childrenIndex:c,route:l};h.relativePath.startsWith("/")&&(Ae(h.relativePath.startsWith(i),`Absolute route path "${h.relativePath}" nested under path "${i}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),h.relativePath=h.relativePath.slice(i.length));let p=Nn([i,h.relativePath]),g=o.concat(h);l.children&&l.children.length>0&&(Ae(l.index!==!0,`Index routes must not have child routes. Please remove all child routes from route path "${p}".`),tg(l.children,r,g,p)),!(l.path==null&&!l.index)&&r.push({path:p,score:qx(p,l.index),routesMeta:g})};return e.forEach((l,c)=>{if(l.path===""||!l.path?.includes("?"))a(l,c);else for(let d of ng(l.path))a(l,c,d)}),r}function ng(e){let r=e.split("/");if(r.length===0)return[];let[o,...i]=r,a=o.endsWith("?"),l=o.replace(/\?$/,"");if(i.length===0)return a?[l,""]:[l];let c=ng(i.join("/")),d=[];return d.push(...c.map(h=>h===""?l:[l,h].join("/"))),a&&d.push(...c),d.map(h=>e.startsWith("/")&&h===""?"/":h)}function Wx(e){e.sort((r,o)=>r.score!==o.score?o.score-r.score:Zx(r.routesMeta.map(i=>i.childrenIndex),o.routesMeta.map(i=>i.childrenIndex)))}var Kx=/^:[\w-]+$/,Qx=3,Gx=2,Yx=1,Jx=10,Xx=-2,Fp=e=>e==="*";function qx(e,r){let o=e.split("/"),i=o.length;return o.some(Fp)&&(i+=Xx),r&&(i+=Gx),o.filter(a=>!Fp(a)).reduce((a,l)=>a+(Kx.test(l)?Qx:l===""?Yx:Jx),i)}function Zx(e,r){return e.length===r.length&&e.slice(0,-1).every((i,a)=>i===r[a])?e[e.length-1]-r[r.length-1]:0}function eS(e,r,o=!1){let{routesMeta:i}=e,a={},l="/",c=[];for(let d=0;d<i.length;++d){let h=i[d],p=d===i.length-1,g=l==="/"?r:r.slice(l.length)||"/",v=Ja({path:h.relativePath,caseSensitive:h.caseSensitive,end:p},g),x=h.route;if(!v&&p&&o&&!i[i.length-1].route.index&&(v=Ja({path:h.relativePath,caseSensitive:h.caseSensitive,end:!1},g)),!v)return null;Object.assign(a,v.params),c.push({params:a,pathname:Nn([l,v.pathname]),pathnameBase:sS(Nn([l,v.pathnameBase])),route:x}),v.pathnameBase!=="/"&&(l=Nn([l,v.pathnameBase]))}return c}function Ja(e,r){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[o,i]=tS(e.path,e.caseSensitive,e.end),a=r.match(o);if(!a)return null;let l=a[0],c=l.replace(/(.)\/+$/,"$1"),d=a.slice(1);return{params:i.reduce((p,{paramName:g,isOptional:v},x)=>{if(g==="*"){let C=d[x]||"";c=l.slice(0,l.length-C.length).replace(/(.)\/+$/,"$1")}const S=d[x];return v&&!S?p[g]=void 0:p[g]=(S||"").replace(/%2F/g,"/"),p},{}),pathname:l,pathnameBase:c,pattern:e}}function tS(e,r=!1,o=!0){rt(e==="*"||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let i=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(c,d,h)=>(i.push({paramName:d,isOptional:h!=null}),h?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(i.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,r?void 0:"i"),i]}function nS(e){try{return e.split("/").map(r=>decodeURIComponent(r).replace(/\//g,"%2F")).join("/")}catch(r){return rt(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${r}).`),e}}function an(e,r){if(r==="/")return e;if(!e.toLowerCase().startsWith(r.toLowerCase()))return null;let o=r.endsWith("/")?r.length-1:r.length,i=e.charAt(o);return i&&i!=="/"?null:e.slice(o)||"/"}function rS({basename:e,pathname:r}){return r==="/"?e:Nn([e,r])}function oS(e,r="/"){let{pathname:o,search:i="",hash:a=""}=typeof e=="string"?Tr(e):e;return{pathname:o?o.startsWith("/")?o:iS(o,r):r,search:aS(i),hash:lS(a)}}function iS(e,r){let o=r.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?o.length>1&&o.pop():a!=="."&&o.push(a)}),o.length>1?o.join("/"):"/"}function dc(e,r,o,i){return`Cannot include a '${e}' character in a manually specified \`to.${r}\` field [${JSON.stringify(i)}].  Please separate it out to the \`to.${o}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function rg(e){return e.filter((r,o)=>o===0||r.route.path&&r.route.path.length>0)}function ll(e){let r=rg(e);return r.map((o,i)=>i===r.length-1?o.pathname:o.pathnameBase)}function ul(e,r,o,i=!1){let a;typeof e=="string"?a=Tr(e):(a={...e},Ae(!a.pathname||!a.pathname.includes("?"),dc("?","pathname","search",a)),Ae(!a.pathname||!a.pathname.includes("#"),dc("#","pathname","hash",a)),Ae(!a.search||!a.search.includes("#"),dc("#","search","hash",a)));let l=e===""||a.pathname==="",c=l?"/":a.pathname,d;if(c==null)d=o;else{let v=r.length-1;if(!i&&c.startsWith("..")){let x=c.split("/");for(;x[0]==="..";)x.shift(),v-=1;a.pathname=x.join("/")}d=v>=0?r[v]:"/"}let h=oS(a,d),p=c&&c!=="/"&&c.endsWith("/"),g=(l||c===".")&&o.endsWith("/");return!h.pathname.endsWith("/")&&(p||g)&&(h.pathname+="/"),h}var Nn=e=>e.join("/").replace(/\/\/+/g,"/"),sS=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),aS=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,lS=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e,Xa=class{constructor(e,r,o,i=!1){this.status=e,this.statusText=r||"",this.internal=i,o instanceof Error?(this.data=o.toString(),this.error=o):this.data=o}};function es(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}var og=["POST","PUT","PATCH","DELETE"],uS=new Set(og),cS=["GET",...og],dS=new Set(cS),fS=new Set([301,302,303,307,308]),hS=new Set([307,308]),fc={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},pS={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Ui={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},mS=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nd=e=>mS.test(e),gS=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),ig="remix-router-transitions",sg=Symbol("ResetLoaderData");function vS(e){const r=e.window?e.window:typeof window<"u"?window:void 0,o=typeof r<"u"&&typeof r.document<"u"&&typeof r.document.createElement<"u";Ae(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let i=e.hydrationRouteProperties||[],a=e.mapRouteProperties||gS,l={},c=Zi(e.routes,a,void 0,l),d,h=e.basename||"/",p=e.dataStrategy||ES,g={unstable_middleware:!1,...e.future},v=null,x=new Set,S=null,C=null,E=null,b=e.hydrationData!=null,P=Pr(c,e.history.location,h),D=!1,N=null,j;if(P==null&&!e.patchRoutesOnNavigation){let R=on(404,{pathname:e.history.location.pathname}),{matches:A,route:U}=Yp(c);j=!0,P=A,N={[U.id]:R}}else if(P&&!e.hydrationData&&Fr(P,c,e.history.location.pathname).active&&(P=null),P)if(P.some(R=>R.route.lazy))j=!1;else if(!P.some(R=>R.route.loader))j=!0;else{let R=e.hydrationData?e.hydrationData.loaderData:null,A=e.hydrationData?e.hydrationData.errors:null;if(A){let U=P.findIndex(q=>A[q.route.id]!==void 0);j=P.slice(0,U+1).every(q=>!Ic(q.route,R,A))}else j=P.every(U=>!Ic(U.route,R,A))}else{j=!1,P=[];let R=Fr(null,c,e.history.location.pathname);R.active&&R.matches&&(D=!0,P=R.matches)}let H,M={historyAction:e.history.action,location:e.history.location,matches:P,initialized:j,navigation:fc,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||N,fetchers:new Map,blockers:new Map},K="POP",W=!1,Z,se=!1,Me=new Map,Re=null,ne=!1,re=!1,be=new Set,X=new Map,ie=0,_=-1,V=new Map,Q=new Set,O=new Map,B=new Map,oe=new Set,le=new Map,Oe,Pe=null;function ue(){if(v=e.history.listen(({action:R,location:A,delta:U})=>{if(Oe){Oe(),Oe=void 0;return}rt(le.size===0||U!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let q=co({currentLocation:M.location,nextLocation:A,historyAction:R});if(q&&U!=null){let te=new Promise(we=>{Oe=we});e.history.go(U*-1),er(q,{state:"blocked",location:A,proceed(){er(q,{state:"proceeding",proceed:void 0,reset:void 0,location:A}),te.then(()=>e.history.go(U))},reset(){let we=new Map(M.blockers);we.set(q,Ui),Ie({blockers:we})}});return}return cn(R,A)}),o){NS(r,Me);let R=()=>DS(r,Me);r.addEventListener("pagehide",R),Re=()=>r.removeEventListener("pagehide",R)}return M.initialized||cn("POP",M.location,{initialHydration:!0}),H}function _e(){v&&v(),Re&&Re(),x.clear(),Z&&Z.abort(),M.fetchers.forEach((R,A)=>lo(A)),M.blockers.forEach((R,A)=>uo(A))}function ze(R){return x.add(R),()=>x.delete(R)}function Ie(R,A={}){R.matches&&(R.matches=R.matches.map(te=>{let we=l[te.route.id],ge=te.route;return ge.element!==we.element||ge.errorElement!==we.errorElement||ge.hydrateFallbackElement!==we.hydrateFallbackElement?{...te,route:we}:te})),M={...M,...R};let U=[],q=[];M.fetchers.forEach((te,we)=>{te.state==="idle"&&(oe.has(we)?U.push(we):q.push(we))}),oe.forEach(te=>{!M.fetchers.has(te)&&!X.has(te)&&U.push(te)}),[...x].forEach(te=>te(M,{deletedFetchers:U,viewTransitionOpts:A.viewTransitionOpts,flushSync:A.flushSync===!0})),U.forEach(te=>lo(te)),q.forEach(te=>M.fetchers.delete(te))}function mt(R,A,{flushSync:U}={}){let q=M.actionData!=null&&M.navigation.formMethod!=null&&zt(M.navigation.formMethod)&&M.navigation.state==="loading"&&R.state?._isRedirect!==!0,te;A.actionData?Object.keys(A.actionData).length>0?te=A.actionData:te=null:q?te=M.actionData:te=null;let we=A.loaderData?Qp(M.loaderData,A.loaderData,A.matches||[],A.errors):M.loaderData,ge=M.blockers;ge.size>0&&(ge=new Map(ge),ge.forEach((xe,Se)=>ge.set(Se,Ui)));let de=ne?!1:oi(R,A.matches||M.matches),ye=W===!0||M.navigation.formMethod!=null&&zt(M.navigation.formMethod)&&R.state?._isRedirect!==!0;d&&(c=d,d=void 0),ne||K==="POP"||(K==="PUSH"?e.history.push(R,R.state):K==="REPLACE"&&e.history.replace(R,R.state));let Ee;if(K==="POP"){let xe=Me.get(M.location.pathname);xe&&xe.has(R.pathname)?Ee={currentLocation:M.location,nextLocation:R}:Me.has(R.pathname)&&(Ee={currentLocation:R,nextLocation:M.location})}else if(se){let xe=Me.get(M.location.pathname);xe?xe.add(R.pathname):(xe=new Set([R.pathname]),Me.set(M.location.pathname,xe)),Ee={currentLocation:M.location,nextLocation:R}}Ie({...A,actionData:te,loaderData:we,historyAction:K,location:R,initialized:!0,navigation:fc,revalidation:"idle",restoreScrollPosition:de,preventScrollReset:ye,blockers:ge},{viewTransitionOpts:Ee,flushSync:U===!0}),K="POP",W=!1,se=!1,ne=!1,re=!1,Pe?.resolve(),Pe=null}async function Yt(R,A){if(typeof R=="number"){e.history.go(R);return}let U=Ac(M.location,M.matches,h,R,A?.fromRouteId,A?.relative),{path:q,submission:te,error:we}=jp(!1,U,A),ge=M.location,de=qi(M.location,q,A&&A.state);de={...de,...e.history.encodeLocation(de)};let ye=A&&A.replace!=null?A.replace:void 0,Ee="PUSH";ye===!0?Ee="REPLACE":ye===!1||te!=null&&zt(te.formMethod)&&te.formAction===M.location.pathname+M.location.search&&(Ee="REPLACE");let xe=A&&"preventScrollReset"in A?A.preventScrollReset===!0:void 0,Se=(A&&A.flushSync)===!0,Fe=co({currentLocation:ge,nextLocation:de,historyAction:Ee});if(Fe){er(Fe,{state:"blocked",location:de,proceed(){er(Fe,{state:"proceeding",proceed:void 0,reset:void 0,location:de}),Yt(R,A)},reset(){let Be=new Map(M.blockers);Be.set(Fe,Ui),Ie({blockers:Be})}});return}await cn(Ee,de,{submission:te,pendingError:we,preventScrollReset:xe,replace:A&&A.replace,enableViewTransition:A&&A.viewTransition,flushSync:Se})}function un(){Pe||(Pe=AS()),ao(),Ie({revalidation:"loading"});let R=Pe.promise;return M.navigation.state==="submitting"?R:M.navigation.state==="idle"?(cn(M.historyAction,M.location,{startUninterruptedRevalidation:!0}),R):(cn(K||M.historyAction,M.navigation.location,{overrideNavigation:M.navigation,enableViewTransition:se===!0}),R)}async function cn(R,A,U){Z&&Z.abort(),Z=null,K=R,ne=(U&&U.startUninterruptedRevalidation)===!0,tr(M.location,M.matches),W=(U&&U.preventScrollReset)===!0,se=(U&&U.enableViewTransition)===!0;let q=d||c,te=U&&U.overrideNavigation,we=U?.initialHydration&&M.matches&&M.matches.length>0&&!D?M.matches:Pr(q,A,h),ge=(U&&U.flushSync)===!0;if(we&&M.initialized&&!re&&OS(M.location,A)&&!(U&&U.submission&&zt(U.submission.formMethod))){mt(A,{matches:we},{flushSync:ge});return}let de=Fr(we,q,A.pathname);if(de.active&&de.matches&&(we=de.matches),!we){let{error:ct,notFoundMatches:qe,route:Ke}=Ir(A.pathname);mt(A,{matches:qe,loaderData:{},errors:{[Ke.id]:ct}},{flushSync:ge});return}Z=new AbortController;let ye=Ho(e.history,A,Z.signal,U&&U.submission),Ee=new Ip(e.unstable_getContext?await e.unstable_getContext():void 0),xe;if(U&&U.pendingError)xe=[eo(we).route.id,{type:"error",error:U.pendingError}];else if(U&&U.submission&&zt(U.submission.formMethod)){let ct=await ms(ye,A,U.submission,we,Ee,de.active,U&&U.initialHydration===!0,{replace:U.replace,flushSync:ge});if(ct.shortCircuited)return;if(ct.pendingActionResult){let[qe,Ke]=ct.pendingActionResult;if(Kt(Ke)&&es(Ke.error)&&Ke.error.status===404){Z=null,mt(A,{matches:ct.matches,loaderData:{},errors:{[qe]:Ke.error}});return}}we=ct.matches||we,xe=ct.pendingActionResult,te=hc(A,U.submission),ge=!1,de.active=!1,ye=Ho(e.history,ye.url,ye.signal)}let{shortCircuited:Se,matches:Fe,loaderData:Be,errors:st}=await gs(ye,A,we,Ee,de.active,te,U&&U.submission,U&&U.fetcherSubmission,U&&U.replace,U&&U.initialHydration===!0,ge,xe);Se||(Z=null,mt(A,{matches:Fe||we,...Gp(xe),loaderData:Be,errors:st}))}async function ms(R,A,U,q,te,we,ge,de={}){ao();let ye=TS(A,U);if(Ie({navigation:ye},{flushSync:de.flushSync===!0}),we){let Se=await jr(q,A.pathname,R.signal);if(Se.type==="aborted")return{shortCircuited:!0};if(Se.type==="error"){let Fe=eo(Se.partialMatches).route.id;return{matches:Se.partialMatches,pendingActionResult:[Fe,{type:"error",error:Se.error}]}}else if(Se.matches)q=Se.matches;else{let{notFoundMatches:Fe,error:Be,route:st}=Ir(A.pathname);return{matches:Fe,pendingActionResult:[st.id,{type:"error",error:Be}]}}}let Ee,xe=za(q,A);if(!xe.route.action&&!xe.route.lazy)Ee={type:"error",error:on(405,{method:R.method,pathname:A.pathname,routeId:xe.route.id})};else{let Se=Bo(a,l,R,q,xe,ge?[]:i,te),Fe=await Zn(R,Se,te,null);if(Ee=Fe[xe.route.id],!Ee){for(let Be of q)if(Fe[Be.route.id]){Ee=Fe[Be.route.id];break}}if(R.signal.aborted)return{shortCircuited:!0}}if(to(Ee)){let Se;return de&&de.replace!=null?Se=de.replace:Se=Vp(Ee.response.headers.get("Location"),new URL(R.url),h)===M.location.pathname+M.location.search,await dn(R,Ee,!0,{submission:U,replace:Se}),{shortCircuited:!0}}if(Kt(Ee)){let Se=eo(q,xe.route.id);return(de&&de.replace)!==!0&&(K="PUSH"),{matches:q,pendingActionResult:[Se.route.id,Ee,xe.route.id]}}return{matches:q,pendingActionResult:[xe.route.id,Ee]}}async function gs(R,A,U,q,te,we,ge,de,ye,Ee,xe,Se){let Fe=we||hc(A,ge),Be=ge||de||Xp(Fe),st=!ne&&!Ee;if(te){if(st){let dt=so(Se);Ie({navigation:Fe,...dt!==void 0?{actionData:dt}:{}},{flushSync:xe})}let $e=await jr(U,A.pathname,R.signal);if($e.type==="aborted")return{shortCircuited:!0};if($e.type==="error"){let dt=eo($e.partialMatches).route.id;return{matches:$e.partialMatches,loaderData:{},errors:{[dt]:$e.error}}}else if($e.matches)U=$e.matches;else{let{error:dt,notFoundMatches:$r,route:bn}=Ir(A.pathname);return{matches:$r,loaderData:{},errors:{[bn.id]:dt}}}}let ct=d||c,{dsMatches:qe,revalidatingFetchers:Ke}=zp(R,q,a,l,e.history,M,U,Be,A,Ee?[]:i,Ee===!0,re,be,oe,O,Q,ct,h,e.patchRoutesOnNavigation!=null,Se);if(_=++ie,!e.dataStrategy&&!qe.some($e=>$e.shouldLoad)&&Ke.length===0){let $e=ti();return mt(A,{matches:U,loaderData:{},errors:Se&&Kt(Se[1])?{[Se[0]]:Se[1].error}:null,...Gp(Se),...$e?{fetchers:new Map(M.fetchers)}:{}},{flushSync:xe}),{shortCircuited:!0}}if(st){let $e={};if(!te){$e.navigation=Fe;let dt=so(Se);dt!==void 0&&($e.actionData=dt)}Ke.length>0&&($e.fetchers=vs(Ke)),Ie($e,{flushSync:xe})}Ke.forEach($e=>{Mt($e.key),$e.controller&&X.set($e.key,$e.controller)});let kn=()=>Ke.forEach($e=>Mt($e.key));Z&&Z.signal.addEventListener("abort",kn);let{loaderResults:nr,fetcherResults:hn}=await ei(qe,Ke,R,q);if(R.signal.aborted)return{shortCircuited:!0};Z&&Z.signal.removeEventListener("abort",kn),Ke.forEach($e=>X.delete($e.key));let Jt=Ra(nr);if(Jt)return await dn(R,Jt.result,!0,{replace:ye}),{shortCircuited:!0};if(Jt=Ra(hn),Jt)return Q.add(Jt.key),await dn(R,Jt.result,!0,{replace:ye}),{shortCircuited:!0};let{loaderData:ii,errors:Qe}=Kp(M,U,nr,Se,Ke,hn);Ee&&M.errors&&(Qe={...M.errors,...Qe});let zn=ti(),rr=ni(_),zr=zn||rr||Ke.length>0;return{matches:U,loaderData:ii,errors:Qe,...zr?{fetchers:new Map(M.fetchers)}:{}}}function so(R){if(R&&!Kt(R[1]))return{[R[0]]:R[1].data};if(M.actionData)return Object.keys(M.actionData).length===0?null:M.actionData}function vs(R){return R.forEach(A=>{let U=M.fetchers.get(A.key),q=Hi(void 0,U?U.data:void 0);M.fetchers.set(A.key,q)}),new Map(M.fetchers)}async function Zo(R,A,U,q){Mt(R);let te=(q&&q.flushSync)===!0,we=d||c,ge=Ac(M.location,M.matches,h,U,A,q?.relative),de=Pr(we,ge,h),ye=Fr(de,we,ge);if(ye.active&&ye.matches&&(de=ye.matches),!de){Ut(R,A,on(404,{pathname:ge}),{flushSync:te});return}let{path:Ee,submission:xe,error:Se}=jp(!0,ge,q);if(Se){Ut(R,A,Se,{flushSync:te});return}let Fe=new Ip(e.unstable_getContext?await e.unstable_getContext():void 0),Be=(q&&q.preventScrollReset)===!0;if(xe&&zt(xe.formMethod)){await Nr(R,A,Ee,de,Fe,ye.active,te,Be,xe);return}O.set(R,{routeId:A,path:Ee}),await qn(R,A,Ee,de,Fe,ye.active,te,Be,xe)}async function Nr(R,A,U,q,te,we,ge,de,ye){ao(),O.delete(R);let Ee=M.fetchers.get(R);fn(R,_S(ye,Ee),{flushSync:ge});let xe=new AbortController,Se=Ho(e.history,U,xe.signal,ye);if(we){let Ve=await jr(q,new URL(Se.url).pathname,Se.signal,R);if(Ve.type==="aborted")return;if(Ve.type==="error"){Ut(R,A,Ve.error,{flushSync:ge});return}else if(Ve.matches)q=Ve.matches;else{Ut(R,A,on(404,{pathname:U}),{flushSync:ge});return}}let Fe=za(q,U);if(!Fe.route.action&&!Fe.route.lazy){let Ve=on(405,{method:ye.formMethod,pathname:U,routeId:A});Ut(R,A,Ve,{flushSync:ge});return}X.set(R,xe);let Be=ie,st=Bo(a,l,Se,q,Fe,i,te),qe=(await Zn(Se,st,te,R))[Fe.route.id];if(Se.signal.aborted){X.get(R)===xe&&X.delete(R);return}if(oe.has(R)){if(to(qe)||Kt(qe)){fn(R,kr(void 0));return}}else{if(to(qe))if(X.delete(R),_>Be){fn(R,kr(void 0));return}else return Q.add(R),fn(R,Hi(ye)),dn(Se,qe,!1,{fetcherSubmission:ye,preventScrollReset:de});if(Kt(qe)){Ut(R,A,qe.error);return}}let Ke=M.navigation.location||M.location,kn=Ho(e.history,Ke,xe.signal),nr=d||c,hn=M.navigation.state!=="idle"?Pr(nr,M.navigation.location,h):M.matches;Ae(hn,"Didn't find any matches after fetcher action");let Jt=++ie;V.set(R,Jt);let ii=Hi(ye,qe.data);M.fetchers.set(R,ii);let{dsMatches:Qe,revalidatingFetchers:zn}=zp(kn,te,a,l,e.history,M,hn,ye,Ke,i,!1,re,be,oe,O,Q,nr,h,e.patchRoutesOnNavigation!=null,[Fe.route.id,qe]);zn.filter(Ve=>Ve.key!==R).forEach(Ve=>{let fo=Ve.key,Tt=M.fetchers.get(fo),Cl=Hi(void 0,Tt?Tt.data:void 0);M.fetchers.set(fo,Cl),Mt(fo),Ve.controller&&X.set(fo,Ve.controller)}),Ie({fetchers:new Map(M.fetchers)});let rr=()=>zn.forEach(Ve=>Mt(Ve.key));xe.signal.addEventListener("abort",rr);let{loaderResults:zr,fetcherResults:$e}=await ei(Qe,zn,kn,te);if(xe.signal.aborted)return;if(xe.signal.removeEventListener("abort",rr),V.delete(R),X.delete(R),zn.forEach(Ve=>X.delete(Ve.key)),M.fetchers.has(R)){let Ve=kr(qe.data);M.fetchers.set(R,Ve)}let dt=Ra(zr);if(dt)return dn(kn,dt.result,!1,{preventScrollReset:de});if(dt=Ra($e),dt)return Q.add(dt.key),dn(kn,dt.result,!1,{preventScrollReset:de});let{loaderData:$r,errors:bn}=Kp(M,hn,zr,void 0,zn,$e);ni(Jt),M.navigation.state==="loading"&&Jt>_?(Ae(K,"Expected pending action"),Z&&Z.abort(),mt(M.navigation.location,{matches:hn,loaderData:$r,errors:bn,fetchers:new Map(M.fetchers)})):(Ie({errors:bn,loaderData:Qp(M.loaderData,$r,hn,bn),fetchers:new Map(M.fetchers)}),re=!1)}async function qn(R,A,U,q,te,we,ge,de,ye){let Ee=M.fetchers.get(R);fn(R,Hi(ye,Ee?Ee.data:void 0),{flushSync:ge});let xe=new AbortController,Se=Ho(e.history,U,xe.signal);if(we){let Ke=await jr(q,new URL(Se.url).pathname,Se.signal,R);if(Ke.type==="aborted")return;if(Ke.type==="error"){Ut(R,A,Ke.error,{flushSync:ge});return}else if(Ke.matches)q=Ke.matches;else{Ut(R,A,on(404,{pathname:U}),{flushSync:ge});return}}let Fe=za(q,U);X.set(R,xe);let Be=ie,st=Bo(a,l,Se,q,Fe,i,te),qe=(await Zn(Se,st,te,R))[Fe.route.id];if(X.get(R)===xe&&X.delete(R),!Se.signal.aborted){if(oe.has(R)){fn(R,kr(void 0));return}if(to(qe))if(_>Be){fn(R,kr(void 0));return}else{Q.add(R),await dn(Se,qe,!1,{preventScrollReset:de});return}if(Kt(qe)){Ut(R,A,qe.error);return}fn(R,kr(qe.data))}}async function dn(R,A,U,{submission:q,fetcherSubmission:te,preventScrollReset:we,replace:ge}={}){A.response.headers.has("X-Remix-Revalidate")&&(re=!0);let de=A.response.headers.get("Location");Ae(de,"Expected a Location header on the redirect Response"),de=Vp(de,new URL(R.url),h);let ye=qi(M.location,de,{_isRedirect:!0});if(o){let st=!1;if(A.response.headers.has("X-Remix-Reload-Document"))st=!0;else if(nd(de)){const ct=eg(de,!0);st=ct.origin!==r.location.origin||an(ct.pathname,h)==null}if(st){ge?r.location.replace(de):r.location.assign(de);return}}Z=null;let Ee=ge===!0||A.response.headers.has("X-Remix-Replace")?"REPLACE":"PUSH",{formMethod:xe,formAction:Se,formEncType:Fe}=M.navigation;!q&&!te&&xe&&Se&&Fe&&(q=Xp(M.navigation));let Be=q||te;if(hS.has(A.response.status)&&Be&&zt(Be.formMethod))await cn(Ee,ye,{submission:{...Be,formAction:de},preventScrollReset:we||W,enableViewTransition:U?se:void 0});else{let st=hc(ye,q);await cn(Ee,ye,{overrideNavigation:st,fetcherSubmission:te,preventScrollReset:we||W,enableViewTransition:U?se:void 0})}}async function Zn(R,A,U,q){let te,we={};try{te=await CS(p,R,A,q,U,!1)}catch(ge){return A.filter(de=>de.shouldLoad).forEach(de=>{we[de.route.id]={type:"error",error:ge}}),we}if(R.signal.aborted)return we;for(let[ge,de]of Object.entries(te))if(LS(de)){let ye=de.result;we[ge]={type:"redirect",response:PS(ye,R,ge,A,h)}}else we[ge]=await bS(de);return we}async function ei(R,A,U,q){let te=Zn(U,R,q,null),we=Promise.all(A.map(async ye=>{if(ye.matches&&ye.match&&ye.request&&ye.controller){let xe=(await Zn(ye.request,ye.matches,q,ye.key))[ye.match.route.id];return{[ye.key]:xe}}else return Promise.resolve({[ye.key]:{type:"error",error:on(404,{pathname:ye.path})}})})),ge=await te,de=(await we).reduce((ye,Ee)=>Object.assign(ye,Ee),{});return{loaderResults:ge,fetcherResults:de}}function ao(){re=!0,O.forEach((R,A)=>{X.has(A)&&be.add(A),Mt(A)})}function fn(R,A,U={}){M.fetchers.set(R,A),Ie({fetchers:new Map(M.fetchers)},{flushSync:(U&&U.flushSync)===!0})}function Ut(R,A,U,q={}){let te=eo(M.matches,A);lo(R),Ie({errors:{[te.route.id]:U},fetchers:new Map(M.fetchers)},{flushSync:(q&&q.flushSync)===!0})}function Dr(R){return B.set(R,(B.get(R)||0)+1),oe.has(R)&&oe.delete(R),M.fetchers.get(R)||pS}function lo(R){let A=M.fetchers.get(R);X.has(R)&&!(A&&A.state==="loading"&&V.has(R))&&Mt(R),O.delete(R),V.delete(R),Q.delete(R),oe.delete(R),be.delete(R),M.fetchers.delete(R)}function Ar(R){let A=(B.get(R)||0)-1;A<=0?(B.delete(R),oe.add(R)):B.set(R,A),Ie({fetchers:new Map(M.fetchers)})}function Mt(R){let A=X.get(R);A&&(A.abort(),X.delete(R))}function ys(R){for(let A of R){let U=Dr(A),q=kr(U.data);M.fetchers.set(A,q)}}function ti(){let R=[],A=!1;for(let U of Q){let q=M.fetchers.get(U);Ae(q,`Expected fetcher: ${U}`),q.state==="loading"&&(Q.delete(U),R.push(U),A=!0)}return ys(R),A}function ni(R){let A=[];for(let[U,q]of V)if(q<R){let te=M.fetchers.get(U);Ae(te,`Expected fetcher: ${U}`),te.state==="loading"&&(Mt(U),V.delete(U),A.push(U))}return ys(A),A.length>0}function El(R,A){let U=M.blockers.get(R)||Ui;return le.get(R)!==A&&le.set(R,A),U}function uo(R){M.blockers.delete(R),le.delete(R)}function er(R,A){let U=M.blockers.get(R)||Ui;Ae(U.state==="unblocked"&&A.state==="blocked"||U.state==="blocked"&&A.state==="blocked"||U.state==="blocked"&&A.state==="proceeding"||U.state==="blocked"&&A.state==="unblocked"||U.state==="proceeding"&&A.state==="unblocked",`Invalid blocker state transition: ${U.state} -> ${A.state}`);let q=new Map(M.blockers);q.set(R,A),Ie({blockers:q})}function co({currentLocation:R,nextLocation:A,historyAction:U}){if(le.size===0)return;le.size>1&&rt(!1,"A router only supports one blocker at a time");let q=Array.from(le.entries()),[te,we]=q[q.length-1],ge=M.blockers.get(te);if(!(ge&&ge.state==="proceeding")&&we({currentLocation:R,nextLocation:A,historyAction:U}))return te}function Ir(R){let A=on(404,{pathname:R}),U=d||c,{matches:q,route:te}=Yp(U);return{notFoundMatches:q,route:te,error:A}}function ri(R,A,U){if(S=R,E=A,C=U||null,!b&&M.navigation===fc){b=!0;let q=oi(M.location,M.matches);q!=null&&Ie({restoreScrollPosition:q})}return()=>{S=null,E=null,C=null}}function jn(R,A){return C&&C(R,A.map(q=>Vx(q,M.loaderData)))||R.key}function tr(R,A){if(S&&E){let U=jn(R,A);S[U]=E()}}function oi(R,A){if(S){let U=jn(R,A),q=S[U];if(typeof q=="number")return q}return null}function Fr(R,A,U){if(e.patchRoutesOnNavigation)if(R){if(Object.keys(R[0].params).length>0)return{active:!0,matches:ja(A,U,h,!0)}}else return{active:!0,matches:ja(A,U,h,!0)||[]};return{active:!1,matches:null}}async function jr(R,A,U,q){if(!e.patchRoutesOnNavigation)return{type:"success",matches:R};let te=R;for(;;){let we=d==null,ge=d||c,de=l;try{await e.patchRoutesOnNavigation({signal:U,path:A,matches:te,fetcherKey:q,patch:(xe,Se)=>{U.aborted||$p(xe,Se,ge,de,a,!1)}})}catch(xe){return{type:"error",error:xe,partialMatches:te}}finally{we&&!U.aborted&&(c=[...c])}if(U.aborted)return{type:"aborted"};let ye=Pr(ge,A,h);if(ye)return{type:"success",matches:ye};let Ee=ja(ge,A,h,!0);if(!Ee||te.length===Ee.length&&te.every((xe,Se)=>xe.route.id===Ee[Se].route.id))return{type:"success",matches:null};te=Ee}}function ws(R){l={},d=Zi(R,a,void 0,l)}function xs(R,A,U=!1){let q=d==null;$p(R,A,d||c,l,a,U),q&&(c=[...c],Ie({}))}return H={get basename(){return h},get future(){return g},get state(){return M},get routes(){return c},get window(){return r},initialize:ue,subscribe:ze,enableScrollRestoration:ri,navigate:Yt,fetch:Zo,revalidate:un,createHref:R=>e.history.createHref(R),encodeLocation:R=>e.history.encodeLocation(R),getFetcher:Dr,deleteFetcher:Ar,dispose:_e,getBlocker:El,deleteBlocker:uo,patchRoutes:xs,_internalFetchControllers:X,_internalSetRoutes:ws,_internalSetStateDoNotUseOrYouWillBreakYourApp(R){Ie(R)}},H}function yS(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function Ac(e,r,o,i,a,l){let c,d;if(a){c=[];for(let p of r)if(c.push(p),p.route.id===a){d=p;break}}else c=r,d=r[r.length-1];let h=ul(i||".",ll(c),an(e.pathname,o)||e.pathname,l==="path");if(i==null&&(h.search=e.search,h.hash=e.hash),(i==null||i===""||i===".")&&d){let p=rd(h.search);if(d.route.index&&!p)h.search=h.search?h.search.replace(/^\?/,"?index&"):"?index";else if(!d.route.index&&p){let g=new URLSearchParams(h.search),v=g.getAll("index");g.delete("index"),v.filter(S=>S).forEach(S=>g.append("index",S));let x=g.toString();h.search=x?`?${x}`:""}}return o!=="/"&&(h.pathname=rS({basename:o,pathname:h.pathname})),Rr(h)}function jp(e,r,o){if(!o||!yS(o))return{path:r};if(o.formMethod&&!MS(o.formMethod))return{path:r,error:on(405,{method:o.formMethod})};let i=()=>({path:r,error:on(400,{type:"invalid-body"})}),l=(o.formMethod||"get").toUpperCase(),c=fg(r);if(o.body!==void 0){if(o.formEncType==="text/plain"){if(!zt(l))return i();let v=typeof o.body=="string"?o.body:o.body instanceof FormData||o.body instanceof URLSearchParams?Array.from(o.body.entries()).reduce((x,[S,C])=>`${x}${S}=${C}
`,""):String(o.body);return{path:r,submission:{formMethod:l,formAction:c,formEncType:o.formEncType,formData:void 0,json:void 0,text:v}}}else if(o.formEncType==="application/json"){if(!zt(l))return i();try{let v=typeof o.body=="string"?JSON.parse(o.body):o.body;return{path:r,submission:{formMethod:l,formAction:c,formEncType:o.formEncType,formData:void 0,json:v,text:void 0}}}catch{return i()}}}Ae(typeof FormData=="function","FormData is not available in this environment");let d,h;if(o.formData)d=jc(o.formData),h=o.formData;else if(o.body instanceof FormData)d=jc(o.body),h=o.body;else if(o.body instanceof URLSearchParams)d=o.body,h=Wp(d);else if(o.body==null)d=new URLSearchParams,h=new FormData;else try{d=new URLSearchParams(o.body),h=Wp(d)}catch{return i()}let p={formMethod:l,formAction:c,formEncType:o&&o.formEncType||"application/x-www-form-urlencoded",formData:h,json:void 0,text:void 0};if(zt(p.formMethod))return{path:r,submission:p};let g=Tr(r);return e&&g.search&&rd(g.search)&&d.append("index",""),g.search=`?${d}`,{path:Rr(g),submission:p}}function zp(e,r,o,i,a,l,c,d,h,p,g,v,x,S,C,E,b,P,D,N){let j=N?Kt(N[1])?N[1].error:N[1].data:void 0,H=a.createURL(l.location),M=a.createURL(h),K;if(g&&l.errors){let ne=Object.keys(l.errors)[0];K=c.findIndex(re=>re.route.id===ne)}else if(N&&Kt(N[1])){let ne=N[0];K=c.findIndex(re=>re.route.id===ne)-1}let W=N?N[1].statusCode:void 0,Z=W&&W>=400,se={currentUrl:H,currentParams:l.matches[0]?.params||{},nextUrl:M,nextParams:c[0].params,...d,actionResult:j,actionStatus:W},Me=c.map((ne,re)=>{let{route:be}=ne,X=null;if(K!=null&&re>K?X=!1:be.lazy?X=!0:be.loader==null?X=!1:g?X=Ic(be,l.loaderData,l.errors):wS(l.loaderData,l.matches[re],ne)&&(X=!0),X!==null)return Fc(o,i,e,ne,p,r,X);let ie=Z?!1:v||H.pathname+H.search===M.pathname+M.search||H.search!==M.search||xS(l.matches[re],ne),_={...se,defaultShouldRevalidate:ie},V=qa(ne,_);return Fc(o,i,e,ne,p,r,V,_)}),Re=[];return C.forEach((ne,re)=>{if(g||!c.some(B=>B.route.id===ne.routeId)||S.has(re))return;let be=l.fetchers.get(re),X=be&&be.state!=="idle"&&be.data===void 0,ie=Pr(b,ne.path,P);if(!ie){if(D&&X)return;Re.push({key:re,routeId:ne.routeId,path:ne.path,matches:null,match:null,request:null,controller:null});return}if(E.has(re))return;let _=za(ie,ne.path),V=new AbortController,Q=Ho(a,ne.path,V.signal),O=null;if(x.has(re))x.delete(re),O=Bo(o,i,Q,ie,_,p,r);else if(X)v&&(O=Bo(o,i,Q,ie,_,p,r));else{let B={...se,defaultShouldRevalidate:Z?!1:v};qa(_,B)&&(O=Bo(o,i,Q,ie,_,p,r,B))}O&&Re.push({key:re,routeId:ne.routeId,path:ne.path,matches:O,match:_,request:Q,controller:V})}),{dsMatches:Me,revalidatingFetchers:Re}}function Ic(e,r,o){if(e.lazy)return!0;if(!e.loader)return!1;let i=r!=null&&e.id in r,a=o!=null&&o[e.id]!==void 0;return!i&&a?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!i&&!a}function wS(e,r,o){let i=!r||o.route.id!==r.route.id,a=!e.hasOwnProperty(o.route.id);return i||a}function xS(e,r){let o=e.route.path;return e.pathname!==r.pathname||o!=null&&o.endsWith("*")&&e.params["*"]!==r.params["*"]}function qa(e,r){if(e.route.shouldRevalidate){let o=e.route.shouldRevalidate(r);if(typeof o=="boolean")return o}return r.defaultShouldRevalidate}function $p(e,r,o,i,a,l){let c;if(e){let p=i[e];Ae(p,`No route found to patch children into: routeId = ${e}`),p.children||(p.children=[]),c=p.children}else c=o;let d=[],h=[];if(r.forEach(p=>{let g=c.find(v=>ag(p,v));g?h.push({existingRoute:g,newRoute:p}):d.push(p)}),d.length>0){let p=Zi(d,a,[e||"_","patch",String(c?.length||"0")],i);c.push(...p)}if(l&&h.length>0)for(let p=0;p<h.length;p++){let{existingRoute:g,newRoute:v}=h[p],x=g,[S]=Zi([v],a,[],{},!0);Object.assign(x,{element:S.element?S.element:x.element,errorElement:S.errorElement?S.errorElement:x.errorElement,hydrateFallbackElement:S.hydrateFallbackElement?S.hydrateFallbackElement:x.hydrateFallbackElement})}}function ag(e,r){return"id"in e&&"id"in r&&e.id===r.id?!0:e.index===r.index&&e.path===r.path&&e.caseSensitive===r.caseSensitive?(!e.children||e.children.length===0)&&(!r.children||r.children.length===0)?!0:e.children.every((o,i)=>r.children?.some(a=>ag(o,a))):!1}var Up=new WeakMap,lg=({key:e,route:r,manifest:o,mapRouteProperties:i})=>{let a=o[r.id];if(Ae(a,"No route found in manifest"),!a.lazy||typeof a.lazy!="object")return;let l=a.lazy[e];if(!l)return;let c=Up.get(a);c||(c={},Up.set(a,c));let d=c[e];if(d)return d;let h=(async()=>{let p=$x(e),v=a[e]!==void 0&&e!=="hasErrorBoundary";if(p)rt(!p,"Route property "+e+" is not a supported lazy route property. This property will be ignored."),c[e]=Promise.resolve();else if(v)rt(!1,`Route "${a.id}" has a static property "${e}" defined. The lazy property will be ignored.`);else{let x=await l();x!=null&&(Object.assign(a,{[e]:x}),Object.assign(a,i(a)))}typeof a.lazy=="object"&&(a.lazy[e]=void 0,Object.values(a.lazy).every(x=>x===void 0)&&(a.lazy=void 0))})();return c[e]=h,h},Hp=new WeakMap;function SS(e,r,o,i,a){let l=o[e.id];if(Ae(l,"No route found in manifest"),!e.lazy)return{lazyRoutePromise:void 0,lazyHandlerPromise:void 0};if(typeof e.lazy=="function"){let g=Hp.get(l);if(g)return{lazyRoutePromise:g,lazyHandlerPromise:g};let v=(async()=>{Ae(typeof e.lazy=="function","No lazy route function found");let x=await e.lazy(),S={};for(let C in x){let E=x[C];if(E===void 0)continue;let b=Hx(C),D=l[C]!==void 0&&C!=="hasErrorBoundary";b?rt(!b,"Route property "+C+" is not a supported property to be returned from a lazy route function. This property will be ignored."):D?rt(!D,`Route "${l.id}" has a static property "${C}" defined but its lazy function is also returning a value for this property. The lazy route property "${C}" will be ignored.`):S[C]=E}Object.assign(l,S),Object.assign(l,{...i(l),lazy:void 0})})();return Hp.set(l,v),v.catch(()=>{}),{lazyRoutePromise:v,lazyHandlerPromise:v}}let c=Object.keys(e.lazy),d=[],h;for(let g of c){if(a&&a.includes(g))continue;let v=lg({key:g,route:e,manifest:o,mapRouteProperties:i});v&&(d.push(v),g===r&&(h=v))}let p=d.length>0?Promise.all(d).then(()=>{}):void 0;return p?.catch(()=>{}),h?.catch(()=>{}),{lazyRoutePromise:p,lazyHandlerPromise:h}}async function Bp(e){let r=e.matches.filter(a=>a.shouldLoad),o={};return(await Promise.all(r.map(a=>a.resolve()))).forEach((a,l)=>{o[r[l].route.id]=a}),o}async function ES(e){return e.matches.some(r=>r.route.unstable_middleware)?ug(e,!1,()=>Bp(e),(r,o)=>({[o]:{type:"error",result:r}})):Bp(e)}async function ug(e,r,o,i){let{matches:a,request:l,params:c,context:d}=e,h={handlerResult:void 0};try{let p=a.flatMap(v=>v.route.unstable_middleware?v.route.unstable_middleware.map(x=>[v.route.id,x]):[]),g=await cg({request:l,params:c,context:d},p,r,h,o);return r?g:h.handlerResult}catch(p){if(!h.middlewareError)throw p;let g=await i(h.middlewareError.error,h.middlewareError.routeId);return h.handlerResult?Object.assign(h.handlerResult,g):g}}async function cg(e,r,o,i,a,l=0){let{request:c}=e;if(c.signal.aborted)throw c.signal.reason?c.signal.reason:new Error(`Request aborted without an \`AbortSignal.reason\`: ${c.method} ${c.url}`);let d=r[l];if(!d)return i.handlerResult=await a(),i.handlerResult;let[h,p]=d,g=!1,v,x=async()=>{if(g)throw new Error("You may only call `next()` once per middleware");g=!0,await cg(e,r,o,i,a,l+1)};try{let S=await p({request:e.request,params:e.params,context:e.context},x);return g?S===void 0?v:S:x()}catch(S){throw i.middlewareError?i.middlewareError.error!==S&&(i.middlewareError={routeId:h,error:S}):i.middlewareError={routeId:h,error:S},S}}function dg(e,r,o,i,a){let l=lg({key:"unstable_middleware",route:i.route,manifest:r,mapRouteProperties:e}),c=SS(i.route,zt(o.method)?"action":"loader",r,e,a);return{middleware:l,route:c.lazyRoutePromise,handler:c.lazyHandlerPromise}}function Fc(e,r,o,i,a,l,c,d=null){let h=!1,p=dg(e,r,o,i,a);return{...i,_lazyPromises:p,shouldLoad:c,unstable_shouldRevalidateArgs:d,unstable_shouldCallHandler(g){return h=!0,d?typeof g=="boolean"?qa(i,{...d,defaultShouldRevalidate:g}):qa(i,d):c},resolve(g){return h||c||g&&!zt(o.method)&&(i.route.lazy||i.route.loader)?kS({request:o,match:i,lazyHandlerPromise:p?.handler,lazyRoutePromise:p?.route,handlerOverride:g,scopedContext:l}):Promise.resolve({type:"data",result:void 0})}}}function Bo(e,r,o,i,a,l,c,d=null){return i.map(h=>h.route.id!==a.route.id?{...h,shouldLoad:!1,unstable_shouldRevalidateArgs:d,unstable_shouldCallHandler:()=>!1,_lazyPromises:dg(e,r,o,h,l),resolve:()=>Promise.resolve({type:"data",result:void 0})}:Fc(e,r,o,h,l,c,!0,d))}async function CS(e,r,o,i,a,l){o.some(p=>p._lazyPromises?.middleware)&&await Promise.all(o.map(p=>p._lazyPromises?.middleware));let c={request:r,params:o[0].params,context:a,matches:o},h=await e({...c,fetcherKey:i,unstable_runClientMiddleware:p=>{let g=c;return ug(g,!1,()=>p({...g,fetcherKey:i,unstable_runClientMiddleware:()=>{throw new Error("Cannot call `unstable_runClientMiddleware()` from within an `unstable_runClientMiddleware` handler")}}),(v,x)=>({[x]:{type:"error",result:v}}))}});try{await Promise.all(o.flatMap(p=>[p._lazyPromises?.handler,p._lazyPromises?.route]))}catch{}return h}async function kS({request:e,match:r,lazyHandlerPromise:o,lazyRoutePromise:i,handlerOverride:a,scopedContext:l}){let c,d,h=zt(e.method),p=h?"action":"loader",g=v=>{let x,S=new Promise((b,P)=>x=P);d=()=>x(),e.signal.addEventListener("abort",d);let C=b=>typeof v!="function"?Promise.reject(new Error(`You cannot call the handler for a route which defines a boolean "${p}" [routeId: ${r.route.id}]`)):v({request:e,params:r.params,context:l},...b!==void 0?[b]:[]),E=(async()=>{try{return{type:"data",result:await(a?a(P=>C(P)):C())}}catch(b){return{type:"error",result:b}}})();return Promise.race([E,S])};try{let v=h?r.route.action:r.route.loader;if(o||i)if(v){let x,[S]=await Promise.all([g(v).catch(C=>{x=C}),o,i]);if(x!==void 0)throw x;c=S}else{await o;let x=h?r.route.action:r.route.loader;if(x)[c]=await Promise.all([g(x),i]);else if(p==="action"){let S=new URL(e.url),C=S.pathname+S.search;throw on(405,{method:e.method,pathname:C,routeId:r.route.id})}else return{type:"data",result:void 0}}else if(v)c=await g(v);else{let x=new URL(e.url),S=x.pathname+x.search;throw on(404,{pathname:S})}}catch(v){return{type:"error",result:v}}finally{d&&e.signal.removeEventListener("abort",d)}return c}async function bS(e){let{result:r,type:o}=e;if(hg(r)){let i;try{let a=r.headers.get("Content-Type");a&&/\bapplication\/json\b/.test(a)?r.body==null?i=null:i=await r.json():i=await r.text()}catch(a){return{type:"error",error:a}}return o==="error"?{type:"error",error:new Xa(r.status,r.statusText,i),statusCode:r.status,headers:r.headers}:{type:"data",data:i,statusCode:r.status,headers:r.headers}}return o==="error"?Jp(r)?r.data instanceof Error?{type:"error",error:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:new Xa(r.init?.status||500,void 0,r.data),statusCode:es(r)?r.status:void 0,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"error",error:r,statusCode:es(r)?r.status:void 0}:Jp(r)?{type:"data",data:r.data,statusCode:r.init?.status,headers:r.init?.headers?new Headers(r.init.headers):void 0}:{type:"data",data:r}}function PS(e,r,o,i,a){let l=e.headers.get("Location");if(Ae(l,"Redirects returned/thrown from loaders/actions must have a Location header"),!nd(l)){let c=i.slice(0,i.findIndex(d=>d.route.id===o)+1);l=Ac(new URL(r.url),c,a,l),e.headers.set("Location",l)}return e}function Vp(e,r,o){if(nd(e)){let i=e,a=i.startsWith("//")?new URL(r.protocol+i):new URL(i),l=an(a.pathname,o)!=null;if(a.origin===r.origin&&l)return a.pathname+a.search+a.hash}return e}function Ho(e,r,o,i){let a=e.createURL(fg(r)).toString(),l={signal:o};if(i&&zt(i.formMethod)){let{formMethod:c,formEncType:d}=i;l.method=c.toUpperCase(),d==="application/json"?(l.headers=new Headers({"Content-Type":d}),l.body=JSON.stringify(i.json)):d==="text/plain"?l.body=i.text:d==="application/x-www-form-urlencoded"&&i.formData?l.body=jc(i.formData):l.body=i.formData}return new Request(a,l)}function jc(e){let r=new URLSearchParams;for(let[o,i]of e.entries())r.append(o,typeof i=="string"?i:i.name);return r}function Wp(e){let r=new FormData;for(let[o,i]of e.entries())r.append(o,i);return r}function RS(e,r,o,i=!1,a=!1){let l={},c=null,d,h=!1,p={},g=o&&Kt(o[1])?o[1].error:void 0;return e.forEach(v=>{if(!(v.route.id in r))return;let x=v.route.id,S=r[x];if(Ae(!to(S),"Cannot handle redirect results in processLoaderData"),Kt(S)){let C=S.error;if(g!==void 0&&(C=g,g=void 0),c=c||{},a)c[x]=C;else{let E=eo(e,x);c[E.route.id]==null&&(c[E.route.id]=C)}i||(l[x]=sg),h||(h=!0,d=es(S.error)?S.error.status:500),S.headers&&(p[x]=S.headers)}else l[x]=S.data,S.statusCode&&S.statusCode!==200&&!h&&(d=S.statusCode),S.headers&&(p[x]=S.headers)}),g!==void 0&&o&&(c={[o[0]]:g},o[2]&&(l[o[2]]=void 0)),{loaderData:l,errors:c,statusCode:d||200,loaderHeaders:p}}function Kp(e,r,o,i,a,l){let{loaderData:c,errors:d}=RS(r,o,i);return a.filter(h=>!h.matches||h.matches.some(p=>p.shouldLoad)).forEach(h=>{let{key:p,match:g,controller:v}=h,x=l[p];if(Ae(x,"Did not find corresponding fetcher result"),!(v&&v.signal.aborted))if(Kt(x)){let S=eo(e.matches,g?.route.id);d&&d[S.route.id]||(d={...d,[S.route.id]:x.error}),e.fetchers.delete(p)}else if(to(x))Ae(!1,"Unhandled fetcher revalidation redirect");else{let S=kr(x.data);e.fetchers.set(p,S)}}),{loaderData:c,errors:d}}function Qp(e,r,o,i){let a=Object.entries(r).filter(([,l])=>l!==sg).reduce((l,[c,d])=>(l[c]=d,l),{});for(let l of o){let c=l.route.id;if(!r.hasOwnProperty(c)&&e.hasOwnProperty(c)&&l.route.loader&&(a[c]=e[c]),i&&i.hasOwnProperty(c))break}return a}function Gp(e){return e?Kt(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function eo(e,r){return(r?e.slice(0,e.findIndex(i=>i.route.id===r)+1):[...e]).reverse().find(i=>i.route.hasErrorBoundary===!0)||e[0]}function Yp(e){let r=e.length===1?e[0]:e.find(o=>o.index||!o.path||o.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:r}],route:r}}function on(e,{pathname:r,routeId:o,method:i,type:a,message:l}={}){let c="Unknown Server Error",d="Unknown @remix-run/router error";return e===400?(c="Bad Request",i&&r&&o?d=`You made a ${i} request to "${r}" but did not provide a \`loader\` for route "${o}", so there is no way to handle the request.`:a==="invalid-body"&&(d="Unable to encode submission body")):e===403?(c="Forbidden",d=`Route "${o}" does not match URL "${r}"`):e===404?(c="Not Found",d=`No route matches URL "${r}"`):e===405&&(c="Method Not Allowed",i&&r&&o?d=`You made a ${i.toUpperCase()} request to "${r}" but did not provide an \`action\` for route "${o}", so there is no way to handle the request.`:i&&(d=`Invalid request method "${i.toUpperCase()}"`)),new Xa(e||500,c,new Error(d),!0)}function Ra(e){let r=Object.entries(e);for(let o=r.length-1;o>=0;o--){let[i,a]=r[o];if(to(a))return{key:i,result:a}}}function fg(e){let r=typeof e=="string"?Tr(e):e;return Rr({...r,hash:""})}function OS(e,r){return e.pathname!==r.pathname||e.search!==r.search?!1:e.hash===""?r.hash!=="":e.hash===r.hash?!0:r.hash!==""}function LS(e){return hg(e.result)&&fS.has(e.result.status)}function Kt(e){return e.type==="error"}function to(e){return(e&&e.type)==="redirect"}function Jp(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function hg(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function MS(e){return dS.has(e.toUpperCase())}function zt(e){return uS.has(e.toUpperCase())}function rd(e){return new URLSearchParams(e).getAll("index").some(r=>r==="")}function za(e,r){let o=typeof r=="string"?Tr(r).search:r.search;if(e[e.length-1].route.index&&rd(o||""))return e[e.length-1];let i=rg(e);return i[i.length-1]}function Xp(e){let{formMethod:r,formAction:o,formEncType:i,text:a,formData:l,json:c}=e;if(!(!r||!o||!i)){if(a!=null)return{formMethod:r,formAction:o,formEncType:i,formData:void 0,json:void 0,text:a};if(l!=null)return{formMethod:r,formAction:o,formEncType:i,formData:l,json:void 0,text:void 0};if(c!==void 0)return{formMethod:r,formAction:o,formEncType:i,formData:void 0,json:c,text:void 0}}}function hc(e,r){return r?{state:"loading",location:e,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function TS(e,r){return{state:"submitting",location:e,formMethod:r.formMethod,formAction:r.formAction,formEncType:r.formEncType,formData:r.formData,json:r.json,text:r.text}}function Hi(e,r){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:r}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:r}}function _S(e,r){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:r?r.data:void 0}}function kr(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function NS(e,r){try{let o=e.sessionStorage.getItem(ig);if(o){let i=JSON.parse(o);for(let[a,l]of Object.entries(i||{}))l&&Array.isArray(l)&&r.set(a,new Set(l||[]))}}catch{}}function DS(e,r){if(r.size>0){let o={};for(let[i,a]of r)o[i]=[...a];try{e.sessionStorage.setItem(ig,JSON.stringify(o))}catch(i){rt(!1,`Failed to save applied view transitions in sessionStorage (${i}).`)}}}function AS(){let e,r,o=new Promise((i,a)=>{e=async l=>{i(l);try{await o}catch{}},r=async l=>{a(l);try{await o}catch{}}});return{promise:o,resolve:e,reject:r}}var ro=w.createContext(null);ro.displayName="DataRouter";var as=w.createContext(null);as.displayName="DataRouterState";w.createContext(!1);var od=w.createContext({isTransitioning:!1});od.displayName="ViewTransition";var pg=w.createContext(new Map);pg.displayName="Fetchers";var IS=w.createContext(null);IS.displayName="Await";var Cn=w.createContext(null);Cn.displayName="Navigation";var cl=w.createContext(null);cl.displayName="Location";var ln=w.createContext({outlet:null,matches:[],isDataRoute:!1});ln.displayName="Route";var id=w.createContext(null);id.displayName="RouteError";function FS(e,{relative:r}={}){Ae(Go(),"useHref() may be used only in the context of a <Router> component.");let{basename:o,navigator:i}=w.useContext(Cn),{hash:a,pathname:l,search:c}=ls(e,{relative:r}),d=l;return o!=="/"&&(d=l==="/"?o:Nn([o,l])),i.createHref({pathname:d,search:c,hash:a})}function Go(){return w.useContext(cl)!=null}function In(){return Ae(Go(),"useLocation() may be used only in the context of a <Router> component."),w.useContext(cl).location}var mg="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function gg(e){w.useContext(Cn).static||w.useLayoutEffect(e)}function dl(){let{isDataRoute:e}=w.useContext(ln);return e?qS():jS()}function jS(){Ae(Go(),"useNavigate() may be used only in the context of a <Router> component.");let e=w.useContext(ro),{basename:r,navigator:o}=w.useContext(Cn),{matches:i}=w.useContext(ln),{pathname:a}=In(),l=JSON.stringify(ll(i)),c=w.useRef(!1);return gg(()=>{c.current=!0}),w.useCallback((h,p={})=>{if(rt(c.current,mg),!c.current)return;if(typeof h=="number"){o.go(h);return}let g=ul(h,JSON.parse(l),a,p.relative==="path");e==null&&r!=="/"&&(g.pathname=g.pathname==="/"?r:Nn([r,g.pathname])),(p.replace?o.replace:o.push)(g,p.state,p)},[r,o,l,a,e])}var zS=w.createContext(null);function $S(e){let r=w.useContext(ln).outlet;return r&&w.createElement(zS.Provider,{value:e},r)}function oO(){let{matches:e}=w.useContext(ln),r=e[e.length-1];return r?r.params:{}}function ls(e,{relative:r}={}){let{matches:o}=w.useContext(ln),{pathname:i}=In(),a=JSON.stringify(ll(o));return w.useMemo(()=>ul(e,JSON.parse(a),i,r==="path"),[e,a,i,r])}function US(e,r,o,i){Ae(Go(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:a}=w.useContext(Cn),{matches:l}=w.useContext(ln),c=l[l.length-1],d=c?c.params:{},h=c?c.pathname:"/",p=c?c.pathnameBase:"/",g=c&&c.route;{let P=g&&g.path||"";yg(h,!g||P.endsWith("*")||P.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${h}" (under <Route path="${P}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.

Please change the parent <Route path="${P}"> to <Route path="${P==="/"?"*":`${P}/*`}">.`)}let v=In(),x;x=v;let S=x.pathname||"/",C=S;if(p!=="/"){let P=p.replace(/^\//,"").split("/");C="/"+S.replace(/^\//,"").split("/").slice(P.length).join("/")}let E=Pr(e,{pathname:C});return rt(g||E!=null,`No routes matched location "${x.pathname}${x.search}${x.hash}" `),rt(E==null||E[E.length-1].route.element!==void 0||E[E.length-1].route.Component!==void 0||E[E.length-1].route.lazy!==void 0,`Matched leaf route at location "${x.pathname}${x.search}${x.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`),KS(E&&E.map(P=>Object.assign({},P,{params:Object.assign({},d,P.params),pathname:Nn([p,a.encodeLocation?a.encodeLocation(P.pathname).pathname:P.pathname]),pathnameBase:P.pathnameBase==="/"?p:Nn([p,a.encodeLocation?a.encodeLocation(P.pathnameBase).pathname:P.pathnameBase])})),l,o,i)}function HS(){let e=XS(),r=es(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),o=e instanceof Error?e.stack:null,i="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:i},l={padding:"2px 4px",backgroundColor:i},c=null;return console.error("Error handled by React Router default ErrorBoundary:",e),c=w.createElement(w.Fragment,null,w.createElement("p",null,"💿 Hey developer 👋"),w.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",w.createElement("code",{style:l},"ErrorBoundary")," or"," ",w.createElement("code",{style:l},"errorElement")," prop on your route.")),w.createElement(w.Fragment,null,w.createElement("h2",null,"Unexpected Application Error!"),w.createElement("h3",{style:{fontStyle:"italic"}},r),o?w.createElement("pre",{style:a},o):null,c)}var BS=w.createElement(HS,null),VS=class extends w.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,r){return r.location!==e.location||r.revalidation!=="idle"&&e.revalidation==="idle"?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:e.error!==void 0?e.error:r.error,location:r.location,revalidation:e.revalidation||r.revalidation}}componentDidCatch(e,r){console.error("React Router caught the following error during render",e,r)}render(){return this.state.error!==void 0?w.createElement(ln.Provider,{value:this.props.routeContext},w.createElement(id.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function WS({routeContext:e,match:r,children:o}){let i=w.useContext(ro);return i&&i.static&&i.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=r.route.id),w.createElement(ln.Provider,{value:e},o)}function KS(e,r=[],o=null,i=null){if(e==null){if(!o)return null;if(o.errors)e=o.matches;else if(r.length===0&&!o.initialized&&o.matches.length>0)e=o.matches;else return null}let a=e,l=o?.errors;if(l!=null){let h=a.findIndex(p=>p.route.id&&l?.[p.route.id]!==void 0);Ae(h>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(l).join(",")}`),a=a.slice(0,Math.min(a.length,h+1))}let c=!1,d=-1;if(o)for(let h=0;h<a.length;h++){let p=a[h];if((p.route.HydrateFallback||p.route.hydrateFallbackElement)&&(d=h),p.route.id){let{loaderData:g,errors:v}=o,x=p.route.loader&&!g.hasOwnProperty(p.route.id)&&(!v||v[p.route.id]===void 0);if(p.route.lazy||x){c=!0,d>=0?a=a.slice(0,d+1):a=[a[0]];break}}}return a.reduceRight((h,p,g)=>{let v,x=!1,S=null,C=null;o&&(v=l&&p.route.id?l[p.route.id]:void 0,S=p.route.errorElement||BS,c&&(d<0&&g===0?(yg("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),x=!0,C=null):d===g&&(x=!0,C=p.route.hydrateFallbackElement||null)));let E=r.concat(a.slice(0,g+1)),b=()=>{let P;return v?P=S:x?P=C:p.route.Component?P=w.createElement(p.route.Component,null):p.route.element?P=p.route.element:P=h,w.createElement(WS,{match:p,routeContext:{outlet:h,matches:E,isDataRoute:o!=null},children:P})};return o&&(p.route.ErrorBoundary||p.route.errorElement||g===0)?w.createElement(VS,{location:o.location,revalidation:o.revalidation,component:S,error:v,children:b(),routeContext:{outlet:null,matches:E,isDataRoute:!0}}):b()},null)}function sd(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function QS(e){let r=w.useContext(ro);return Ae(r,sd(e)),r}function vg(e){let r=w.useContext(as);return Ae(r,sd(e)),r}function GS(e){let r=w.useContext(ln);return Ae(r,sd(e)),r}function ad(e){let r=GS(e),o=r.matches[r.matches.length-1];return Ae(o.route.id,`${e} can only be used on routes that contain a unique "id"`),o.route.id}function YS(){return ad("useRouteId")}function JS(){return vg("useNavigation").navigation}function XS(){let e=w.useContext(id),r=vg("useRouteError"),o=ad("useRouteError");return e!==void 0?e:r.errors?.[o]}function qS(){let{router:e}=QS("useNavigate"),r=ad("useNavigate"),o=w.useRef(!1);return gg(()=>{o.current=!0}),w.useCallback(async(a,l={})=>{rt(o.current,mg),o.current&&(typeof a=="number"?e.navigate(a):await e.navigate(a,{fromRouteId:r,...l}))},[e,r])}var qp={};function yg(e,r,o){!r&&!qp[e]&&(qp[e]=!0,rt(!1,o))}var Zp={};function em(e,r){!e&&!Zp[r]&&(Zp[r]=!0,console.warn(r))}function ZS(e){let r={hasErrorBoundary:e.hasErrorBoundary||e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&(e.element&&rt(!1,"You should not include both `Component` and `element` on your route - `Component` will be used."),Object.assign(r,{element:w.createElement(e.Component),Component:void 0})),e.HydrateFallback&&(e.hydrateFallbackElement&&rt(!1,"You should not include both `HydrateFallback` and `hydrateFallbackElement` on your route - `HydrateFallback` will be used."),Object.assign(r,{hydrateFallbackElement:w.createElement(e.HydrateFallback),HydrateFallback:void 0})),e.ErrorBoundary&&(e.errorElement&&rt(!1,"You should not include both `ErrorBoundary` and `errorElement` on your route - `ErrorBoundary` will be used."),Object.assign(r,{errorElement:w.createElement(e.ErrorBoundary),ErrorBoundary:void 0})),r}var eE=["HydrateFallback","hydrateFallbackElement"],tE=class{constructor(){this.status="pending",this.promise=new Promise((e,r)=>{this.resolve=o=>{this.status==="pending"&&(this.status="resolved",e(o))},this.reject=o=>{this.status==="pending"&&(this.status="rejected",r(o))}})}};function nE({router:e,flushSync:r}){let[o,i]=w.useState(e.state),[a,l]=w.useState(),[c,d]=w.useState({isTransitioning:!1}),[h,p]=w.useState(),[g,v]=w.useState(),[x,S]=w.useState(),C=w.useRef(new Map),E=w.useCallback((N,{deletedFetchers:j,flushSync:H,viewTransitionOpts:M})=>{N.fetchers.forEach((W,Z)=>{W.data!==void 0&&C.current.set(Z,W.data)}),j.forEach(W=>C.current.delete(W)),em(H===!1||r!=null,'You provided the `flushSync` option to a router update, but you are not using the `<RouterProvider>` from `react-router/dom` so `ReactDOM.flushSync()` is unavailable.  Please update your app to `import { RouterProvider } from "react-router/dom"` and ensure you have `react-dom` installed as a dependency to use the `flushSync` option.');let K=e.window!=null&&e.window.document!=null&&typeof e.window.document.startViewTransition=="function";if(em(M==null||K,"You provided the `viewTransition` option to a router update, but you do not appear to be running in a DOM environment as `window.startViewTransition` is not available."),!M||!K){r&&H?r(()=>i(N)):w.startTransition(()=>i(N));return}if(r&&H){r(()=>{g&&(h&&h.resolve(),g.skipTransition()),d({isTransitioning:!0,flushSync:!0,currentLocation:M.currentLocation,nextLocation:M.nextLocation})});let W=e.window.document.startViewTransition(()=>{r(()=>i(N))});W.finished.finally(()=>{r(()=>{p(void 0),v(void 0),l(void 0),d({isTransitioning:!1})})}),r(()=>v(W));return}g?(h&&h.resolve(),g.skipTransition(),S({state:N,currentLocation:M.currentLocation,nextLocation:M.nextLocation})):(l(N),d({isTransitioning:!0,flushSync:!1,currentLocation:M.currentLocation,nextLocation:M.nextLocation}))},[e.window,r,g,h]);w.useLayoutEffect(()=>e.subscribe(E),[e,E]),w.useEffect(()=>{c.isTransitioning&&!c.flushSync&&p(new tE)},[c]),w.useEffect(()=>{if(h&&a&&e.window){let N=a,j=h.promise,H=e.window.document.startViewTransition(async()=>{w.startTransition(()=>i(N)),await j});H.finished.finally(()=>{p(void 0),v(void 0),l(void 0),d({isTransitioning:!1})}),v(H)}},[a,h,e.window]),w.useEffect(()=>{h&&a&&o.location.key===a.location.key&&h.resolve()},[h,g,o.location,a]),w.useEffect(()=>{!c.isTransitioning&&x&&(l(x.state),d({isTransitioning:!0,flushSync:!1,currentLocation:x.currentLocation,nextLocation:x.nextLocation}),S(void 0))},[c.isTransitioning,x]);let b=w.useMemo(()=>({createHref:e.createHref,encodeLocation:e.encodeLocation,go:N=>e.navigate(N),push:(N,j,H)=>e.navigate(N,{state:j,preventScrollReset:H?.preventScrollReset}),replace:(N,j,H)=>e.navigate(N,{replace:!0,state:j,preventScrollReset:H?.preventScrollReset})}),[e]),P=e.basename||"/",D=w.useMemo(()=>({router:e,navigator:b,static:!1,basename:P}),[e,b,P]);return w.createElement(w.Fragment,null,w.createElement(ro.Provider,{value:D},w.createElement(as.Provider,{value:o},w.createElement(pg.Provider,{value:C.current},w.createElement(od.Provider,{value:c},w.createElement(aE,{basename:P,location:o.location,navigationType:o.historyAction,navigator:b},w.createElement(rE,{routes:e.routes,future:e.future,state:o})))))),null)}var rE=w.memo(oE);function oE({routes:e,future:r,state:o}){return US(e,void 0,o,r)}function iE({to:e,replace:r,state:o,relative:i}){Ae(Go(),"<Navigate> may be used only in the context of a <Router> component.");let{static:a}=w.useContext(Cn);rt(!a,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:l}=w.useContext(ln),{pathname:c}=In(),d=dl(),h=ul(e,ll(l),c,i==="path"),p=JSON.stringify(h);return w.useEffect(()=>{d(JSON.parse(p),{replace:r,state:o,relative:i})},[d,p,i,r,o]),null}function sE(e){return $S(e.context)}function aE({basename:e="/",children:r=null,location:o,navigationType:i="POP",navigator:a,static:l=!1}){Ae(!Go(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let c=e.replace(/^\/*/,"/"),d=w.useMemo(()=>({basename:c,navigator:a,static:l,future:{}}),[c,a,l]);typeof o=="string"&&(o=Tr(o));let{pathname:h="/",search:p="",hash:g="",state:v=null,key:x="default"}=o,S=w.useMemo(()=>{let C=an(h,c);return C==null?null:{location:{pathname:C,search:p,hash:g,state:v,key:x},navigationType:i}},[c,h,p,g,v,x,i]);return rt(S!=null,`<Router basename="${c}"> is not able to match the URL "${h}${p}${g}" because it does not start with the basename, so the <Router> won't render anything.`),S==null?null:w.createElement(Cn.Provider,{value:d},w.createElement(cl.Provider,{children:r,value:S}))}var $a="get",Ua="application/x-www-form-urlencoded";function fl(e){return e!=null&&typeof e.tagName=="string"}function lE(e){return fl(e)&&e.tagName.toLowerCase()==="button"}function uE(e){return fl(e)&&e.tagName.toLowerCase()==="form"}function cE(e){return fl(e)&&e.tagName.toLowerCase()==="input"}function dE(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function fE(e,r){return e.button===0&&(!r||r==="_self")&&!dE(e)}function zc(e=""){return new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((r,o)=>{let i=e[o];return r.concat(Array.isArray(i)?i.map(a=>[o,a]):[[o,i]])},[]))}function hE(e,r){let o=zc(e);return r&&r.forEach((i,a)=>{o.has(a)||r.getAll(a).forEach(l=>{o.append(a,l)})}),o}var Oa=null;function pE(){if(Oa===null)try{new FormData(document.createElement("form"),0),Oa=!1}catch{Oa=!0}return Oa}var mE=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function pc(e){return e!=null&&!mE.has(e)?(rt(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Ua}"`),null):e}function gE(e,r){let o,i,a,l,c;if(uE(e)){let d=e.getAttribute("action");i=d?an(d,r):null,o=e.getAttribute("method")||$a,a=pc(e.getAttribute("enctype"))||Ua,l=new FormData(e)}else if(lE(e)||cE(e)&&(e.type==="submit"||e.type==="image")){let d=e.form;if(d==null)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let h=e.getAttribute("formaction")||d.getAttribute("action");if(i=h?an(h,r):null,o=e.getAttribute("formmethod")||d.getAttribute("method")||$a,a=pc(e.getAttribute("formenctype"))||pc(d.getAttribute("enctype"))||Ua,l=new FormData(d,e),!pE()){let{name:p,type:g,value:v}=e;if(g==="image"){let x=p?`${p}.`:"";l.append(`${x}x`,"0"),l.append(`${x}y`,"0")}else p&&l.append(p,v)}}else{if(fl(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');o=$a,i=null,a=Ua,c=e}return l&&a==="text/plain"&&(c=l,l=void 0),{action:i,method:o.toLowerCase(),encType:a,formData:l,body:c}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");function ld(e,r){if(e===!1||e===null||typeof e>"u")throw new Error(r)}function vE(e,r,o){let i=typeof e=="string"?new URL(e,typeof window>"u"?"server://singlefetch/":window.location.origin):e;return i.pathname==="/"?i.pathname=`_root.${o}`:r&&an(i.pathname,r)==="/"?i.pathname=`${r.replace(/\/$/,"")}/_root.${o}`:i.pathname=`${i.pathname.replace(/\/$/,"")}.${o}`,i}async function yE(e,r){if(e.id in r)return r[e.id];try{let o=await import(e.module);return r[e.id]=o,o}catch(o){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(o),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function wE(e){return e==null?!1:e.href==null?e.rel==="preload"&&typeof e.imageSrcSet=="string"&&typeof e.imageSizes=="string":typeof e.rel=="string"&&typeof e.href=="string"}async function xE(e,r,o){let i=await Promise.all(e.map(async a=>{let l=r.routes[a.route.id];if(l){let c=await yE(l,o);return c.links?c.links():[]}return[]}));return kE(i.flat(1).filter(wE).filter(a=>a.rel==="stylesheet"||a.rel==="preload").map(a=>a.rel==="stylesheet"?{...a,rel:"prefetch",as:"style"}:{...a,rel:"prefetch"}))}function tm(e,r,o,i,a,l){let c=(h,p)=>o[p]?h.route.id!==o[p].route.id:!0,d=(h,p)=>o[p].pathname!==h.pathname||o[p].route.path?.endsWith("*")&&o[p].params["*"]!==h.params["*"];return l==="assets"?r.filter((h,p)=>c(h,p)||d(h,p)):l==="data"?r.filter((h,p)=>{let g=i.routes[h.route.id];if(!g||!g.hasLoader)return!1;if(c(h,p)||d(h,p))return!0;if(h.route.shouldRevalidate){let v=h.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:o[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:h.params,defaultShouldRevalidate:!0});if(typeof v=="boolean")return v}return!0}):[]}function SE(e,r,{includeHydrateFallback:o}={}){return EE(e.map(i=>{let a=r.routes[i.route.id];if(!a)return[];let l=[a.module];return a.clientActionModule&&(l=l.concat(a.clientActionModule)),a.clientLoaderModule&&(l=l.concat(a.clientLoaderModule)),o&&a.hydrateFallbackModule&&(l=l.concat(a.hydrateFallbackModule)),a.imports&&(l=l.concat(a.imports)),l}).flat(1))}function EE(e){return[...new Set(e)]}function CE(e){let r={},o=Object.keys(e).sort();for(let i of o)r[i]=e[i];return r}function kE(e,r){let o=new Set;return new Set(r),e.reduce((i,a)=>{let l=JSON.stringify(CE(a));return o.has(l)||(o.add(l),i.push({key:l,link:a})),i},[])}function wg(){let e=w.useContext(ro);return ld(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function bE(){let e=w.useContext(as);return ld(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var ud=w.createContext(void 0);ud.displayName="FrameworkContext";function xg(){let e=w.useContext(ud);return ld(e,"You must render this element inside a <HydratedRouter> element"),e}function PE(e,r){let o=w.useContext(ud),[i,a]=w.useState(!1),[l,c]=w.useState(!1),{onFocus:d,onBlur:h,onMouseEnter:p,onMouseLeave:g,onTouchStart:v}=r,x=w.useRef(null);w.useEffect(()=>{if(e==="render"&&c(!0),e==="viewport"){let E=P=>{P.forEach(D=>{c(D.isIntersecting)})},b=new IntersectionObserver(E,{threshold:.5});return x.current&&b.observe(x.current),()=>{b.disconnect()}}},[e]),w.useEffect(()=>{if(i){let E=setTimeout(()=>{c(!0)},100);return()=>{clearTimeout(E)}}},[i]);let S=()=>{a(!0)},C=()=>{a(!1),c(!1)};return o?e!=="intent"?[l,x,{}]:[l,x,{onFocus:Bi(d,S),onBlur:Bi(h,C),onMouseEnter:Bi(p,S),onMouseLeave:Bi(g,C),onTouchStart:Bi(v,S)}]:[!1,x,{}]}function Bi(e,r){return o=>{e&&e(o),o.defaultPrevented||r(o)}}function RE({page:e,...r}){let{router:o}=wg(),i=w.useMemo(()=>Pr(o.routes,e,o.basename),[o.routes,e,o.basename]);return i?w.createElement(LE,{page:e,matches:i,...r}):null}function OE(e){let{manifest:r,routeModules:o}=xg(),[i,a]=w.useState([]);return w.useEffect(()=>{let l=!1;return xE(e,r,o).then(c=>{l||a(c)}),()=>{l=!0}},[e,r,o]),i}function LE({page:e,matches:r,...o}){let i=In(),{manifest:a,routeModules:l}=xg(),{basename:c}=wg(),{loaderData:d,matches:h}=bE(),p=w.useMemo(()=>tm(e,r,h,a,i,"data"),[e,r,h,a,i]),g=w.useMemo(()=>tm(e,r,h,a,i,"assets"),[e,r,h,a,i]),v=w.useMemo(()=>{if(e===i.pathname+i.search+i.hash)return[];let C=new Set,E=!1;if(r.forEach(P=>{let D=a.routes[P.route.id];!D||!D.hasLoader||(!p.some(N=>N.route.id===P.route.id)&&P.route.id in d&&l[P.route.id]?.shouldRevalidate||D.hasClientLoader?E=!0:C.add(P.route.id))}),C.size===0)return[];let b=vE(e,c,"data");return E&&C.size>0&&b.searchParams.set("_routes",r.filter(P=>C.has(P.route.id)).map(P=>P.route.id).join(",")),[b.pathname+b.search]},[c,d,i,a,p,r,e,l]),x=w.useMemo(()=>SE(g,a),[g,a]),S=OE(g);return w.createElement(w.Fragment,null,v.map(C=>w.createElement("link",{key:C,rel:"prefetch",as:"fetch",href:C,...o})),x.map(C=>w.createElement("link",{key:C,rel:"modulepreload",href:C,...o})),S.map(({key:C,link:E})=>w.createElement("link",{key:C,...E})))}function ME(...e){return r=>{e.forEach(o=>{typeof o=="function"?o(r):o!=null&&(o.current=r)})}}var Sg=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";try{Sg&&(window.__reactRouterVersion="7.7.1")}catch{}function TE(e,r){return vS({basename:r?.basename,unstable_getContext:r?.unstable_getContext,future:r?.future,history:Ix({window:r?.window}),hydrationData:_E(),routes:e,mapRouteProperties:ZS,hydrationRouteProperties:eE,dataStrategy:r?.dataStrategy,patchRoutesOnNavigation:r?.patchRoutesOnNavigation,window:r?.window}).initialize()}function _E(){let e=window?.__staticRouterHydrationData;return e&&e.errors&&(e={...e,errors:NE(e.errors)}),e}function NE(e){if(!e)return null;let r=Object.entries(e),o={};for(let[i,a]of r)if(a&&a.__type==="RouteErrorResponse")o[i]=new Xa(a.status,a.statusText,a.data,a.internal===!0);else if(a&&a.__type==="Error"){if(a.__subType){let l=window[a.__subType];if(typeof l=="function")try{let c=new l(a.message);c.stack="",o[i]=c}catch{}}if(o[i]==null){let l=new Error(a.message);l.stack="",o[i]=l}}else o[i]=a;return o}var Eg=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,cd=w.forwardRef(function({onClick:r,discover:o="render",prefetch:i="none",relative:a,reloadDocument:l,replace:c,state:d,target:h,to:p,preventScrollReset:g,viewTransition:v,...x},S){let{basename:C}=w.useContext(Cn),E=typeof p=="string"&&Eg.test(p),b,P=!1;if(typeof p=="string"&&E&&(b=p,Sg))try{let Z=new URL(window.location.href),se=p.startsWith("//")?new URL(Z.protocol+p):new URL(p),Me=an(se.pathname,C);se.origin===Z.origin&&Me!=null?p=Me+se.search+se.hash:P=!0}catch{rt(!1,`<Link to="${p}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let D=FS(p,{relative:a}),[N,j,H]=PE(i,x),M=IE(p,{replace:c,state:d,target:h,preventScrollReset:g,relative:a,viewTransition:v});function K(Z){r&&r(Z),Z.defaultPrevented||M(Z)}let W=w.createElement("a",{...x,...H,href:b||D,onClick:P||l?r:K,ref:ME(S,j),target:h,"data-discover":!E&&o==="render"?"true":void 0});return N&&!E?w.createElement(w.Fragment,null,W,w.createElement(RE,{page:D})):W});cd.displayName="Link";var Cg=w.forwardRef(function({"aria-current":r="page",caseSensitive:o=!1,className:i="",end:a=!1,style:l,to:c,viewTransition:d,children:h,...p},g){let v=ls(c,{relative:p.relative}),x=In(),S=w.useContext(as),{navigator:C,basename:E}=w.useContext(Cn),b=S!=null&&UE(v)&&d===!0,P=C.encodeLocation?C.encodeLocation(v).pathname:v.pathname,D=x.pathname,N=S&&S.navigation&&S.navigation.location?S.navigation.location.pathname:null;o||(D=D.toLowerCase(),N=N?N.toLowerCase():null,P=P.toLowerCase()),N&&E&&(N=an(N,E)||N);const j=P!=="/"&&P.endsWith("/")?P.length-1:P.length;let H=D===P||!a&&D.startsWith(P)&&D.charAt(j)==="/",M=N!=null&&(N===P||!a&&N.startsWith(P)&&N.charAt(P.length)==="/"),K={isActive:H,isPending:M,isTransitioning:b},W=H?r:void 0,Z;typeof i=="function"?Z=i(K):Z=[i,H?"active":null,M?"pending":null,b?"transitioning":null].filter(Boolean).join(" ");let se=typeof l=="function"?l(K):l;return w.createElement(cd,{...p,"aria-current":W,className:Z,ref:g,style:se,to:c,viewTransition:d},typeof h=="function"?h(K):h)});Cg.displayName="NavLink";var DE=w.forwardRef(({discover:e="render",fetcherKey:r,navigate:o,reloadDocument:i,replace:a,state:l,method:c=$a,action:d,onSubmit:h,relative:p,preventScrollReset:g,viewTransition:v,...x},S)=>{let C=zE(),E=$E(d,{relative:p}),b=c.toLowerCase()==="get"?"get":"post",P=typeof d=="string"&&Eg.test(d),D=N=>{if(h&&h(N),N.defaultPrevented)return;N.preventDefault();let j=N.nativeEvent.submitter,H=j?.getAttribute("formmethod")||c;C(j||N.currentTarget,{fetcherKey:r,method:H,navigate:o,replace:a,state:l,relative:p,preventScrollReset:g,viewTransition:v})};return w.createElement("form",{ref:S,method:b,action:E,onSubmit:i?h:D,...x,"data-discover":!P&&e==="render"?"true":void 0})});DE.displayName="Form";function AE(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function kg(e){let r=w.useContext(ro);return Ae(r,AE(e)),r}function IE(e,{target:r,replace:o,state:i,preventScrollReset:a,relative:l,viewTransition:c}={}){let d=dl(),h=In(),p=ls(e,{relative:l});return w.useCallback(g=>{if(fE(g,r)){g.preventDefault();let v=o!==void 0?o:Rr(h)===Rr(p);d(e,{replace:v,state:i,preventScrollReset:a,relative:l,viewTransition:c})}},[h,d,p,o,i,r,e,a,l,c])}function iO(e){rt(typeof URLSearchParams<"u","You cannot use the `useSearchParams` hook in a browser that does not support the URLSearchParams API. If you need to support Internet Explorer 11, we recommend you load a polyfill such as https://github.com/ungap/url-search-params.");let r=w.useRef(zc(e)),o=w.useRef(!1),i=In(),a=w.useMemo(()=>hE(i.search,o.current?null:r.current),[i.search]),l=dl(),c=w.useCallback((d,h)=>{const p=zc(typeof d=="function"?d(new URLSearchParams(a)):d);o.current=!0,l("?"+p,h)},[l,a]);return[a,c]}var FE=0,jE=()=>`__${String(++FE)}__`;function zE(){let{router:e}=kg("useSubmit"),{basename:r}=w.useContext(Cn),o=YS();return w.useCallback(async(i,a={})=>{let{action:l,method:c,encType:d,formData:h,body:p}=gE(i,r);if(a.navigate===!1){let g=a.fetcherKey||jE();await e.fetch(g,o,a.action||l,{preventScrollReset:a.preventScrollReset,formData:h,body:p,formMethod:a.method||c,formEncType:a.encType||d,flushSync:a.flushSync})}else await e.navigate(a.action||l,{preventScrollReset:a.preventScrollReset,formData:h,body:p,formMethod:a.method||c,formEncType:a.encType||d,replace:a.replace,state:a.state,fromRouteId:o,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,r,o])}function $E(e,{relative:r}={}){let{basename:o}=w.useContext(Cn),i=w.useContext(ln);Ae(i,"useFormAction must be used inside a RouteContext");let[a]=i.matches.slice(-1),l={...ls(e||".",{relative:r})},c=In();if(e==null){l.search=c.search;let d=new URLSearchParams(l.search),h=d.getAll("index");if(h.some(g=>g==="")){d.delete("index"),h.filter(v=>v).forEach(v=>d.append("index",v));let g=d.toString();l.search=g?`?${g}`:""}}return(!e||e===".")&&a.route.index&&(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index"),o!=="/"&&(l.pathname=l.pathname==="/"?o:Nn([o,l.pathname])),Rr(l)}function UE(e,{relative:r}={}){let o=w.useContext(od);Ae(o!=null,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:i}=kg("useViewTransitionState"),a=ls(e,{relative:r});if(!o.isTransitioning)return!1;let l=an(o.currentLocation.pathname,i)||o.currentLocation.pathname,c=an(o.nextLocation.pathname,i)||o.nextLocation.pathname;return Ja(a.pathname,c)!=null||Ja(a.pathname,l)!=null}var hl=jm();const HE=Im(hl);function BE(e){return w.createElement(nE,{flushSync:hl.flushSync,...e})}const Xe={home:{path:"/",getHref:()=>"/"},auth:{register:{path:"/auth/register",getHref:e=>`/auth/register${e?`?redirectTo=${encodeURIComponent(e)}`:""}`},login:{path:"/auth/login",getHref:e=>`/auth/login${e?`?redirectTo=${encodeURIComponent(e)}`:""}`}},app:{root:{path:"/app",getHref:()=>"/app"},dashboard:{path:"",getHref:()=>"/app"},applications:{path:"applications",getHref:()=>"/app/applications"},savedJobs:{path:"saved-jobs",getHref:()=>"/app/saved-jobs"},jobs:{path:"jobs",getHref:()=>"/app/jobs"},companies:{path:"companies",getHref:()=>"/app/companies"},profile:{path:"profile",getHref:()=>"/app/profile"},settings:{path:"settings",getHref:()=>"/app/settings"}},jobs:{root:{path:"/jobs",getHref:()=>"/jobs"},detail:{path:"/jobs/:id",getHref:e=>`/jobs/${e}`}},companies:{root:{path:"/companies",getHref:()=>"/companies"},detail:{path:"/companies/:id",getHref:e=>`/companies/${e}`}},about:{path:"/about",getHref:()=>"/about"},contact:{path:"/contact",getHref:()=>"/contact"},privacy:{path:"/privacy",getHref:()=>"/privacy"},terms:{path:"/terms",getHref:()=>"/terms"}},nm=e=>{let r;const o=new Set,i=(p,g)=>{const v=typeof p=="function"?p(r):p;if(!Object.is(v,r)){const x=r;r=g??(typeof v!="object"||v===null)?v:Object.assign({},r,v),o.forEach(S=>S(r,x))}},a=()=>r,d={setState:i,getState:a,getInitialState:()=>h,subscribe:p=>(o.add(p),()=>o.delete(p))},h=r=e(i,a,d);return d},VE=e=>e?nm(e):nm,WE=e=>e;function KE(e,r=WE){const o=jt.useSyncExternalStore(e.subscribe,()=>r(e.getState()),()=>r(e.getInitialState()));return jt.useDebugValue(o),o}const QE=e=>{const r=VE(e),o=i=>KE(r,i);return Object.assign(o,r),o},GE=e=>QE;function YE(e,r){let o;try{o=e()}catch{return}return{getItem:a=>{var l;const c=h=>h===null?null:JSON.parse(h,void 0),d=(l=o.getItem(a))!=null?l:null;return d instanceof Promise?d.then(c):c(d)},setItem:(a,l)=>o.setItem(a,JSON.stringify(l,void 0)),removeItem:a=>o.removeItem(a)}}const $c=e=>r=>{try{const o=e(r);return o instanceof Promise?o:{then(i){return $c(i)(o)},catch(i){return this}}}catch(o){return{then(i){return this},catch(i){return $c(i)(o)}}}},JE=(e,r)=>(o,i,a)=>{let l={storage:YE(()=>localStorage),partialize:E=>E,version:0,merge:(E,b)=>({...b,...E}),...r},c=!1;const d=new Set,h=new Set;let p=l.storage;if(!p)return e((...E)=>{console.warn(`[zustand persist middleware] Unable to update item '${l.name}', the given storage is currently unavailable.`),o(...E)},i,a);const g=()=>{const E=l.partialize({...i()});return p.setItem(l.name,{state:E,version:l.version})},v=a.setState;a.setState=(E,b)=>{v(E,b),g()};const x=e((...E)=>{o(...E),g()},i,a);a.getInitialState=()=>x;let S;const C=()=>{var E,b;if(!p)return;c=!1,d.forEach(D=>{var N;return D((N=i())!=null?N:x)});const P=((b=l.onRehydrateStorage)==null?void 0:b.call(l,(E=i())!=null?E:x))||void 0;return $c(p.getItem.bind(p))(l.name).then(D=>{if(D)if(typeof D.version=="number"&&D.version!==l.version){if(l.migrate){const N=l.migrate(D.state,D.version);return N instanceof Promise?N.then(j=>[!0,j]):[!0,N]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,D.state];return[!1,void 0]}).then(D=>{var N;const[j,H]=D;if(S=l.merge(H,(N=i())!=null?N:x),o(S,!0),j)return g()}).then(()=>{P?.(S,void 0),S=i(),c=!0,h.forEach(D=>D(S))}).catch(D=>{P?.(void 0,D)})};return a.persist={setOptions:E=>{l={...l,...E},E.storage&&(p=E.storage)},clearStorage:()=>{p?.removeItem(l.name)},getOptions:()=>l,rehydrate:()=>C(),hasHydrated:()=>c,onHydrate:E=>(d.add(E),()=>{d.delete(E)}),onFinishHydration:E=>(h.add(E),()=>{h.delete(E)})},l.skipHydration||C(),S||x},XE=JE,qE="work-finder-auth",dd=GE()(XE((e,r)=>({user:null,isAuthenticated:!1,isInitializing:!0,accessToken:null,refreshToken:null,login:async(o,i)=>{try{const a={user:{id:"1",name:"John Doe",email:o,role:"user",isVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token",refreshToken:"mock-refresh-token"};e({user:a.user,isAuthenticated:!0,accessToken:a.accessToken,refreshToken:a.refreshToken,isInitializing:!1})}catch(a){throw console.error("Login failed:",a),a}},register:async o=>{try{const i={user:{id:"1",name:o.name,email:o.email,role:"user",isVerified:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},accessToken:"mock-access-token",refreshToken:"mock-refresh-token"};e({user:i.user,isAuthenticated:!0,accessToken:i.accessToken,refreshToken:i.refreshToken,isInitializing:!1})}catch(i){throw console.error("Registration failed:",i),i}},logout:async()=>{try{e({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}catch(o){console.error("Logout failed:",o),e({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}},getCurrentUser:async()=>{try{const{accessToken:o}=r();if(!o){e({isInitializing:!1});return}const i={id:"1",name:"John Doe",email:"<EMAIL>",role:"user",isVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e({user:i,isAuthenticated:!0,isInitializing:!1})}catch(o){console.error("Get current user failed:",o),e({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null,isInitializing:!1})}},updateUser:o=>{const{user:i}=r();i&&e({user:{...i,...o,updatedAt:new Date().toISOString()}})},setInitialized:()=>{e({isInitializing:!1})},setAuthFromLocal:o=>{e({user:o,isAuthenticated:!0,isInitializing:!1})},refreshAccessToken:async()=>{try{const{refreshToken:o}=r();if(!o)throw new Error("No refresh token available");const i={accessToken:"new-mock-access-token"};return e({accessToken:i.accessToken}),i.accessToken}catch(o){return console.error("Token refresh failed:",o),e({user:null,isAuthenticated:!1,accessToken:null,refreshToken:null}),null}}}),{name:qE,partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated,accessToken:e.accessToken,refreshToken:e.refreshToken})})),ZE=({children:e})=>{const r=In(),{isAuthenticated:o}=dd();return o?F.jsx(F.Fragment,{children:e}):F.jsx(iE,{to:Xe.auth.login.getHref(r.pathname),replace:!0})};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),t1=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,o,i)=>i?i.toUpperCase():o.toLowerCase()),rm=e=>{const r=t1(e);return r.charAt(0).toUpperCase()+r.slice(1)},bg=(...e)=>e.filter((r,o,i)=>!!r&&r.trim()!==""&&i.indexOf(r)===o).join(" ").trim(),n1=e=>{for(const r in e)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var r1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o1=w.forwardRef(({color:e="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:a="",children:l,iconNode:c,...d},h)=>w.createElement("svg",{ref:h,...r1,width:r,height:r,stroke:e,strokeWidth:i?Number(o)*24/Number(r):o,className:bg("lucide",a),...!l&&!n1(d)&&{"aria-hidden":"true"},...d},[...c.map(([p,g])=>w.createElement(p,g)),...Array.isArray(l)?l:[l]]));/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _r=(e,r)=>{const o=w.forwardRef(({className:i,...a},l)=>w.createElement(o1,{ref:l,iconNode:r,className:bg(`lucide-${e1(rm(e))}`,`lucide-${e}`,i),...a}));return o.displayName=rm(e),o};/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i1=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],s1=_r("briefcase",i1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a1=[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]],l1=_r("building-2",a1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u1=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],c1=_r("file-text",u1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d1=[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]],f1=_r("heart",d1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h1=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],p1=_r("house",h1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],g1=_r("panel-left",m1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],y1=_r("settings",v1);/**
 * @license lucide-react v0.526.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w1=[["circle",{cx:"12",cy:"8",r:"5",key:"1hypcn"}],["path",{d:"M20 21a8 8 0 0 0-16 0",key:"rfgkzh"}]],om=_r("user-round",w1);function im(e,r){if(typeof e=="function")return e(r);e!=null&&(e.current=r)}function pl(...e){return r=>{let o=!1;const i=e.map(a=>{const l=im(a,r);return!o&&typeof l=="function"&&(o=!0),l});if(o)return()=>{for(let a=0;a<i.length;a++){const l=i[a];typeof l=="function"?l():im(e[a],null)}}}}function $t(...e){return w.useCallback(pl(...e),e)}function ts(e){const r=S1(e),o=w.forwardRef((i,a)=>{const{children:l,...c}=i,d=w.Children.toArray(l),h=d.find(C1);if(h){const p=h.props.children,g=d.map(v=>v===h?w.Children.count(p)>1?w.Children.only(null):w.isValidElement(p)?p.props.children:null:v);return F.jsx(r,{...c,ref:a,children:w.isValidElement(p)?w.cloneElement(p,void 0,g):null})}return F.jsx(r,{...c,ref:a,children:l})});return o.displayName=`${e}.Slot`,o}var x1=ts("Slot");function S1(e){const r=w.forwardRef((o,i)=>{const{children:a,...l}=o;if(w.isValidElement(a)){const c=b1(a),d=k1(l,a.props);return a.type!==w.Fragment&&(d.ref=i?pl(i,c):c),w.cloneElement(a,d)}return w.Children.count(a)>1?w.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}var E1=Symbol("radix.slottable");function C1(e){return w.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===E1}function k1(e,r){const o={...r};for(const i in r){const a=e[i],l=r[i];/^on[A-Z]/.test(i)?a&&l?o[i]=(...d)=>{const h=l(...d);return a(...d),h}:a&&(o[i]=a):i==="style"?o[i]={...a,...l}:i==="className"&&(o[i]=[a,l].filter(Boolean).join(" "))}return{...e,...o}}function b1(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(r=Object.getOwnPropertyDescriptor(e,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}function Pg(e){var r,o,i="";if(typeof e=="string"||typeof e=="number")i+=e;else if(typeof e=="object")if(Array.isArray(e)){var a=e.length;for(r=0;r<a;r++)e[r]&&(o=Pg(e[r]))&&(i&&(i+=" "),i+=o)}else for(o in e)e[o]&&(i&&(i+=" "),i+=o);return i}function Rg(){for(var e,r,o=0,i="",a=arguments.length;o<a;o++)(e=arguments[o])&&(r=Pg(e))&&(i&&(i+=" "),i+=r);return i}const sm=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,am=Rg,P1=(e,r)=>o=>{var i;if(r?.variants==null)return am(e,o?.class,o?.className);const{variants:a,defaultVariants:l}=r,c=Object.keys(a).map(p=>{const g=o?.[p],v=l?.[p];if(g===null)return null;const x=sm(g)||sm(v);return a[p][x]}),d=o&&Object.entries(o).reduce((p,g)=>{let[v,x]=g;return x===void 0||(p[v]=x),p},{}),h=r==null||(i=r.compoundVariants)===null||i===void 0?void 0:i.reduce((p,g)=>{let{class:v,className:x,...S}=g;return Object.entries(S).every(C=>{let[E,b]=C;return Array.isArray(b)?b.includes({...l,...d}[E]):{...l,...d}[E]===b})?[...p,v,x]:p},[]);return am(e,c,h,o?.class,o?.className)},fd="-",R1=e=>{const r=L1(e),{conflictingClassGroups:o,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:c=>{const d=c.split(fd);return d[0]===""&&d.length!==1&&d.shift(),Og(d,r)||O1(c)},getConflictingClassGroupIds:(c,d)=>{const h=o[c]||[];return d&&i[c]?[...h,...i[c]]:h}}},Og=(e,r)=>{if(e.length===0)return r.classGroupId;const o=e[0],i=r.nextPart.get(o),a=i?Og(e.slice(1),i):void 0;if(a)return a;if(r.validators.length===0)return;const l=e.join(fd);return r.validators.find(({validator:c})=>c(l))?.classGroupId},lm=/^\[(.+)\]$/,O1=e=>{if(lm.test(e)){const r=lm.exec(e)[1],o=r?.substring(0,r.indexOf(":"));if(o)return"arbitrary.."+o}},L1=e=>{const{theme:r,classGroups:o}=e,i={nextPart:new Map,validators:[]};for(const a in o)Uc(o[a],i,a,r);return i},Uc=(e,r,o,i)=>{e.forEach(a=>{if(typeof a=="string"){const l=a===""?r:um(r,a);l.classGroupId=o;return}if(typeof a=="function"){if(M1(a)){Uc(a(i),r,o,i);return}r.validators.push({validator:a,classGroupId:o});return}Object.entries(a).forEach(([l,c])=>{Uc(c,um(r,l),o,i)})})},um=(e,r)=>{let o=e;return r.split(fd).forEach(i=>{o.nextPart.has(i)||o.nextPart.set(i,{nextPart:new Map,validators:[]}),o=o.nextPart.get(i)}),o},M1=e=>e.isThemeGetter,T1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let r=0,o=new Map,i=new Map;const a=(l,c)=>{o.set(l,c),r++,r>e&&(r=0,i=o,o=new Map)};return{get(l){let c=o.get(l);if(c!==void 0)return c;if((c=i.get(l))!==void 0)return a(l,c),c},set(l,c){o.has(l)?o.set(l,c):a(l,c)}}},Hc="!",Bc=":",_1=Bc.length,N1=e=>{const{prefix:r,experimentalParseClassName:o}=e;let i=a=>{const l=[];let c=0,d=0,h=0,p;for(let C=0;C<a.length;C++){let E=a[C];if(c===0&&d===0){if(E===Bc){l.push(a.slice(h,C)),h=C+_1;continue}if(E==="/"){p=C;continue}}E==="["?c++:E==="]"?c--:E==="("?d++:E===")"&&d--}const g=l.length===0?a:a.substring(h),v=D1(g),x=v!==g,S=p&&p>h?p-h:void 0;return{modifiers:l,hasImportantModifier:x,baseClassName:v,maybePostfixModifierPosition:S}};if(r){const a=r+Bc,l=i;i=c=>c.startsWith(a)?l(c.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:c,maybePostfixModifierPosition:void 0}}if(o){const a=i;i=l=>o({className:l,parseClassName:a})}return i},D1=e=>e.endsWith(Hc)?e.substring(0,e.length-1):e.startsWith(Hc)?e.substring(1):e,A1=e=>{const r=Object.fromEntries(e.orderSensitiveModifiers.map(i=>[i,!0]));return i=>{if(i.length<=1)return i;const a=[];let l=[];return i.forEach(c=>{c[0]==="["||r[c]?(a.push(...l.sort(),c),l=[]):l.push(c)}),a.push(...l.sort()),a}},I1=e=>({cache:T1(e.cacheSize),parseClassName:N1(e),sortModifiers:A1(e),...R1(e)}),F1=/\s+/,j1=(e,r)=>{const{parseClassName:o,getClassGroupId:i,getConflictingClassGroupIds:a,sortModifiers:l}=r,c=[],d=e.trim().split(F1);let h="";for(let p=d.length-1;p>=0;p-=1){const g=d[p],{isExternal:v,modifiers:x,hasImportantModifier:S,baseClassName:C,maybePostfixModifierPosition:E}=o(g);if(v){h=g+(h.length>0?" "+h:h);continue}let b=!!E,P=i(b?C.substring(0,E):C);if(!P){if(!b){h=g+(h.length>0?" "+h:h);continue}if(P=i(C),!P){h=g+(h.length>0?" "+h:h);continue}b=!1}const D=l(x).join(":"),N=S?D+Hc:D,j=N+P;if(c.includes(j))continue;c.push(j);const H=a(P,b);for(let M=0;M<H.length;++M){const K=H[M];c.push(N+K)}h=g+(h.length>0?" "+h:h)}return h};function z1(){let e=0,r,o,i="";for(;e<arguments.length;)(r=arguments[e++])&&(o=Lg(r))&&(i&&(i+=" "),i+=o);return i}const Lg=e=>{if(typeof e=="string")return e;let r,o="";for(let i=0;i<e.length;i++)e[i]&&(r=Lg(e[i]))&&(o&&(o+=" "),o+=r);return o};function $1(e,...r){let o,i,a,l=c;function c(h){const p=r.reduce((g,v)=>v(g),e());return o=I1(p),i=o.cache.get,a=o.cache.set,l=d,d(h)}function d(h){const p=i(h);if(p)return p;const g=j1(h,o);return a(h,g),g}return function(){return l(z1.apply(null,arguments))}}const ut=e=>{const r=o=>o[e]||[];return r.isThemeGetter=!0,r},Mg=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Tg=/^\((?:(\w[\w-]*):)?(.+)\)$/i,U1=/^\d+\/\d+$/,H1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,B1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,V1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,W1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,K1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,jo=e=>U1.test(e),Ne=e=>!!e&&!Number.isNaN(Number(e)),Cr=e=>!!e&&Number.isInteger(Number(e)),mc=e=>e.endsWith("%")&&Ne(e.slice(0,-1)),Gn=e=>H1.test(e),Q1=()=>!0,G1=e=>B1.test(e)&&!V1.test(e),_g=()=>!1,Y1=e=>W1.test(e),J1=e=>K1.test(e),X1=e=>!he(e)&&!pe(e),q1=e=>Yo(e,Ag,_g),he=e=>Mg.test(e),Zr=e=>Yo(e,Ig,G1),gc=e=>Yo(e,rC,Ne),cm=e=>Yo(e,Ng,_g),Z1=e=>Yo(e,Dg,J1),La=e=>Yo(e,Fg,Y1),pe=e=>Tg.test(e),Vi=e=>Jo(e,Ig),eC=e=>Jo(e,oC),dm=e=>Jo(e,Ng),tC=e=>Jo(e,Ag),nC=e=>Jo(e,Dg),Ma=e=>Jo(e,Fg,!0),Yo=(e,r,o)=>{const i=Mg.exec(e);return i?i[1]?r(i[1]):o(i[2]):!1},Jo=(e,r,o=!1)=>{const i=Tg.exec(e);return i?i[1]?r(i[1]):o:!1},Ng=e=>e==="position"||e==="percentage",Dg=e=>e==="image"||e==="url",Ag=e=>e==="length"||e==="size"||e==="bg-size",Ig=e=>e==="length",rC=e=>e==="number",oC=e=>e==="family-name",Fg=e=>e==="shadow",iC=()=>{const e=ut("color"),r=ut("font"),o=ut("text"),i=ut("font-weight"),a=ut("tracking"),l=ut("leading"),c=ut("breakpoint"),d=ut("container"),h=ut("spacing"),p=ut("radius"),g=ut("shadow"),v=ut("inset-shadow"),x=ut("text-shadow"),S=ut("drop-shadow"),C=ut("blur"),E=ut("perspective"),b=ut("aspect"),P=ut("ease"),D=ut("animate"),N=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],H=()=>[...j(),pe,he],M=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto","contain","none"],W=()=>[pe,he,h],Z=()=>[jo,"full","auto",...W()],se=()=>[Cr,"none","subgrid",pe,he],Me=()=>["auto",{span:["full",Cr,pe,he]},Cr,pe,he],Re=()=>[Cr,"auto",pe,he],ne=()=>["auto","min","max","fr",pe,he],re=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],be=()=>["start","end","center","stretch","center-safe","end-safe"],X=()=>["auto",...W()],ie=()=>[jo,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...W()],_=()=>[e,pe,he],V=()=>[...j(),dm,cm,{position:[pe,he]}],Q=()=>["no-repeat",{repeat:["","x","y","space","round"]}],O=()=>["auto","cover","contain",tC,q1,{size:[pe,he]}],B=()=>[mc,Vi,Zr],oe=()=>["","none","full",p,pe,he],le=()=>["",Ne,Vi,Zr],Oe=()=>["solid","dashed","dotted","double"],Pe=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ue=()=>[Ne,mc,dm,cm],_e=()=>["","none",C,pe,he],ze=()=>["none",Ne,pe,he],Ie=()=>["none",Ne,pe,he],mt=()=>[Ne,pe,he],Yt=()=>[jo,"full",...W()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[Gn],breakpoint:[Gn],color:[Q1],container:[Gn],"drop-shadow":[Gn],ease:["in","out","in-out"],font:[X1],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[Gn],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[Gn],shadow:[Gn],spacing:["px",Ne],text:[Gn],"text-shadow":[Gn],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",jo,he,pe,b]}],container:["container"],columns:[{columns:[Ne,he,pe,d]}],"break-after":[{"break-after":N()}],"break-before":[{"break-before":N()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:H()}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:K()}],"overscroll-x":[{"overscroll-x":K()}],"overscroll-y":[{"overscroll-y":K()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:Z()}],"inset-x":[{"inset-x":Z()}],"inset-y":[{"inset-y":Z()}],start:[{start:Z()}],end:[{end:Z()}],top:[{top:Z()}],right:[{right:Z()}],bottom:[{bottom:Z()}],left:[{left:Z()}],visibility:["visible","invisible","collapse"],z:[{z:[Cr,"auto",pe,he]}],basis:[{basis:[jo,"full","auto",d,...W()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[Ne,jo,"auto","initial","none",he]}],grow:[{grow:["",Ne,pe,he]}],shrink:[{shrink:["",Ne,pe,he]}],order:[{order:[Cr,"first","last","none",pe,he]}],"grid-cols":[{"grid-cols":se()}],"col-start-end":[{col:Me()}],"col-start":[{"col-start":Re()}],"col-end":[{"col-end":Re()}],"grid-rows":[{"grid-rows":se()}],"row-start-end":[{row:Me()}],"row-start":[{"row-start":Re()}],"row-end":[{"row-end":Re()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":ne()}],"auto-rows":[{"auto-rows":ne()}],gap:[{gap:W()}],"gap-x":[{"gap-x":W()}],"gap-y":[{"gap-y":W()}],"justify-content":[{justify:[...re(),"normal"]}],"justify-items":[{"justify-items":[...be(),"normal"]}],"justify-self":[{"justify-self":["auto",...be()]}],"align-content":[{content:["normal",...re()]}],"align-items":[{items:[...be(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...be(),{baseline:["","last"]}]}],"place-content":[{"place-content":re()}],"place-items":[{"place-items":[...be(),"baseline"]}],"place-self":[{"place-self":["auto",...be()]}],p:[{p:W()}],px:[{px:W()}],py:[{py:W()}],ps:[{ps:W()}],pe:[{pe:W()}],pt:[{pt:W()}],pr:[{pr:W()}],pb:[{pb:W()}],pl:[{pl:W()}],m:[{m:X()}],mx:[{mx:X()}],my:[{my:X()}],ms:[{ms:X()}],me:[{me:X()}],mt:[{mt:X()}],mr:[{mr:X()}],mb:[{mb:X()}],ml:[{ml:X()}],"space-x":[{"space-x":W()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":W()}],"space-y-reverse":["space-y-reverse"],size:[{size:ie()}],w:[{w:[d,"screen",...ie()]}],"min-w":[{"min-w":[d,"screen","none",...ie()]}],"max-w":[{"max-w":[d,"screen","none","prose",{screen:[c]},...ie()]}],h:[{h:["screen","lh",...ie()]}],"min-h":[{"min-h":["screen","lh","none",...ie()]}],"max-h":[{"max-h":["screen","lh",...ie()]}],"font-size":[{text:["base",o,Vi,Zr]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,pe,gc]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",mc,he]}],"font-family":[{font:[eC,he,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,pe,he]}],"line-clamp":[{"line-clamp":[Ne,"none",pe,gc]}],leading:[{leading:[l,...W()]}],"list-image":[{"list-image":["none",pe,he]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",pe,he]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:_()}],"text-color":[{text:_()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Oe(),"wavy"]}],"text-decoration-thickness":[{decoration:[Ne,"from-font","auto",pe,Zr]}],"text-decoration-color":[{decoration:_()}],"underline-offset":[{"underline-offset":[Ne,"auto",pe,he]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:W()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",pe,he]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",pe,he]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:V()}],"bg-repeat":[{bg:Q()}],"bg-size":[{bg:O()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Cr,pe,he],radial:["",pe,he],conic:[Cr,pe,he]},nC,Z1]}],"bg-color":[{bg:_()}],"gradient-from-pos":[{from:B()}],"gradient-via-pos":[{via:B()}],"gradient-to-pos":[{to:B()}],"gradient-from":[{from:_()}],"gradient-via":[{via:_()}],"gradient-to":[{to:_()}],rounded:[{rounded:oe()}],"rounded-s":[{"rounded-s":oe()}],"rounded-e":[{"rounded-e":oe()}],"rounded-t":[{"rounded-t":oe()}],"rounded-r":[{"rounded-r":oe()}],"rounded-b":[{"rounded-b":oe()}],"rounded-l":[{"rounded-l":oe()}],"rounded-ss":[{"rounded-ss":oe()}],"rounded-se":[{"rounded-se":oe()}],"rounded-ee":[{"rounded-ee":oe()}],"rounded-es":[{"rounded-es":oe()}],"rounded-tl":[{"rounded-tl":oe()}],"rounded-tr":[{"rounded-tr":oe()}],"rounded-br":[{"rounded-br":oe()}],"rounded-bl":[{"rounded-bl":oe()}],"border-w":[{border:le()}],"border-w-x":[{"border-x":le()}],"border-w-y":[{"border-y":le()}],"border-w-s":[{"border-s":le()}],"border-w-e":[{"border-e":le()}],"border-w-t":[{"border-t":le()}],"border-w-r":[{"border-r":le()}],"border-w-b":[{"border-b":le()}],"border-w-l":[{"border-l":le()}],"divide-x":[{"divide-x":le()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":le()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...Oe(),"hidden","none"]}],"divide-style":[{divide:[...Oe(),"hidden","none"]}],"border-color":[{border:_()}],"border-color-x":[{"border-x":_()}],"border-color-y":[{"border-y":_()}],"border-color-s":[{"border-s":_()}],"border-color-e":[{"border-e":_()}],"border-color-t":[{"border-t":_()}],"border-color-r":[{"border-r":_()}],"border-color-b":[{"border-b":_()}],"border-color-l":[{"border-l":_()}],"divide-color":[{divide:_()}],"outline-style":[{outline:[...Oe(),"none","hidden"]}],"outline-offset":[{"outline-offset":[Ne,pe,he]}],"outline-w":[{outline:["",Ne,Vi,Zr]}],"outline-color":[{outline:_()}],shadow:[{shadow:["","none",g,Ma,La]}],"shadow-color":[{shadow:_()}],"inset-shadow":[{"inset-shadow":["none",v,Ma,La]}],"inset-shadow-color":[{"inset-shadow":_()}],"ring-w":[{ring:le()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:_()}],"ring-offset-w":[{"ring-offset":[Ne,Zr]}],"ring-offset-color":[{"ring-offset":_()}],"inset-ring-w":[{"inset-ring":le()}],"inset-ring-color":[{"inset-ring":_()}],"text-shadow":[{"text-shadow":["none",x,Ma,La]}],"text-shadow-color":[{"text-shadow":_()}],opacity:[{opacity:[Ne,pe,he]}],"mix-blend":[{"mix-blend":[...Pe(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Pe()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[Ne]}],"mask-image-linear-from-pos":[{"mask-linear-from":ue()}],"mask-image-linear-to-pos":[{"mask-linear-to":ue()}],"mask-image-linear-from-color":[{"mask-linear-from":_()}],"mask-image-linear-to-color":[{"mask-linear-to":_()}],"mask-image-t-from-pos":[{"mask-t-from":ue()}],"mask-image-t-to-pos":[{"mask-t-to":ue()}],"mask-image-t-from-color":[{"mask-t-from":_()}],"mask-image-t-to-color":[{"mask-t-to":_()}],"mask-image-r-from-pos":[{"mask-r-from":ue()}],"mask-image-r-to-pos":[{"mask-r-to":ue()}],"mask-image-r-from-color":[{"mask-r-from":_()}],"mask-image-r-to-color":[{"mask-r-to":_()}],"mask-image-b-from-pos":[{"mask-b-from":ue()}],"mask-image-b-to-pos":[{"mask-b-to":ue()}],"mask-image-b-from-color":[{"mask-b-from":_()}],"mask-image-b-to-color":[{"mask-b-to":_()}],"mask-image-l-from-pos":[{"mask-l-from":ue()}],"mask-image-l-to-pos":[{"mask-l-to":ue()}],"mask-image-l-from-color":[{"mask-l-from":_()}],"mask-image-l-to-color":[{"mask-l-to":_()}],"mask-image-x-from-pos":[{"mask-x-from":ue()}],"mask-image-x-to-pos":[{"mask-x-to":ue()}],"mask-image-x-from-color":[{"mask-x-from":_()}],"mask-image-x-to-color":[{"mask-x-to":_()}],"mask-image-y-from-pos":[{"mask-y-from":ue()}],"mask-image-y-to-pos":[{"mask-y-to":ue()}],"mask-image-y-from-color":[{"mask-y-from":_()}],"mask-image-y-to-color":[{"mask-y-to":_()}],"mask-image-radial":[{"mask-radial":[pe,he]}],"mask-image-radial-from-pos":[{"mask-radial-from":ue()}],"mask-image-radial-to-pos":[{"mask-radial-to":ue()}],"mask-image-radial-from-color":[{"mask-radial-from":_()}],"mask-image-radial-to-color":[{"mask-radial-to":_()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[Ne]}],"mask-image-conic-from-pos":[{"mask-conic-from":ue()}],"mask-image-conic-to-pos":[{"mask-conic-to":ue()}],"mask-image-conic-from-color":[{"mask-conic-from":_()}],"mask-image-conic-to-color":[{"mask-conic-to":_()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:V()}],"mask-repeat":[{mask:Q()}],"mask-size":[{mask:O()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",pe,he]}],filter:[{filter:["","none",pe,he]}],blur:[{blur:_e()}],brightness:[{brightness:[Ne,pe,he]}],contrast:[{contrast:[Ne,pe,he]}],"drop-shadow":[{"drop-shadow":["","none",S,Ma,La]}],"drop-shadow-color":[{"drop-shadow":_()}],grayscale:[{grayscale:["",Ne,pe,he]}],"hue-rotate":[{"hue-rotate":[Ne,pe,he]}],invert:[{invert:["",Ne,pe,he]}],saturate:[{saturate:[Ne,pe,he]}],sepia:[{sepia:["",Ne,pe,he]}],"backdrop-filter":[{"backdrop-filter":["","none",pe,he]}],"backdrop-blur":[{"backdrop-blur":_e()}],"backdrop-brightness":[{"backdrop-brightness":[Ne,pe,he]}],"backdrop-contrast":[{"backdrop-contrast":[Ne,pe,he]}],"backdrop-grayscale":[{"backdrop-grayscale":["",Ne,pe,he]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[Ne,pe,he]}],"backdrop-invert":[{"backdrop-invert":["",Ne,pe,he]}],"backdrop-opacity":[{"backdrop-opacity":[Ne,pe,he]}],"backdrop-saturate":[{"backdrop-saturate":[Ne,pe,he]}],"backdrop-sepia":[{"backdrop-sepia":["",Ne,pe,he]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":W()}],"border-spacing-x":[{"border-spacing-x":W()}],"border-spacing-y":[{"border-spacing-y":W()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",pe,he]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[Ne,"initial",pe,he]}],ease:[{ease:["linear","initial",P,pe,he]}],delay:[{delay:[Ne,pe,he]}],animate:[{animate:["none",D,pe,he]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[E,pe,he]}],"perspective-origin":[{"perspective-origin":H()}],rotate:[{rotate:ze()}],"rotate-x":[{"rotate-x":ze()}],"rotate-y":[{"rotate-y":ze()}],"rotate-z":[{"rotate-z":ze()}],scale:[{scale:Ie()}],"scale-x":[{"scale-x":Ie()}],"scale-y":[{"scale-y":Ie()}],"scale-z":[{"scale-z":Ie()}],"scale-3d":["scale-3d"],skew:[{skew:mt()}],"skew-x":[{"skew-x":mt()}],"skew-y":[{"skew-y":mt()}],transform:[{transform:[pe,he,"","none","gpu","cpu"]}],"transform-origin":[{origin:H()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:Yt()}],"translate-x":[{"translate-x":Yt()}],"translate-y":[{"translate-y":Yt()}],"translate-z":[{"translate-z":Yt()}],"translate-none":["translate-none"],accent:[{accent:_()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:_()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",pe,he]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":W()}],"scroll-mx":[{"scroll-mx":W()}],"scroll-my":[{"scroll-my":W()}],"scroll-ms":[{"scroll-ms":W()}],"scroll-me":[{"scroll-me":W()}],"scroll-mt":[{"scroll-mt":W()}],"scroll-mr":[{"scroll-mr":W()}],"scroll-mb":[{"scroll-mb":W()}],"scroll-ml":[{"scroll-ml":W()}],"scroll-p":[{"scroll-p":W()}],"scroll-px":[{"scroll-px":W()}],"scroll-py":[{"scroll-py":W()}],"scroll-ps":[{"scroll-ps":W()}],"scroll-pe":[{"scroll-pe":W()}],"scroll-pt":[{"scroll-pt":W()}],"scroll-pr":[{"scroll-pr":W()}],"scroll-pb":[{"scroll-pb":W()}],"scroll-pl":[{"scroll-pl":W()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",pe,he]}],fill:[{fill:["none",..._()]}],"stroke-w":[{stroke:[Ne,Vi,Zr,gc]}],stroke:[{stroke:["none",..._()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},sC=$1(iC);function Ko(...e){return sC(Rg(e))}const aC=P1("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function fm({className:e,variant:r,size:o,asChild:i=!1,...a}){const l=i?x1:"button";return F.jsx(l,{"data-slot":"button",className:Ko(aC({variant:r,size:o,className:e})),...a})}function je(e,r,{checkForDefaultPrevented:o=!0}={}){return function(a){if(e?.(a),o===!1||!a.defaultPrevented)return r?.(a)}}function sO(e,r){const o=w.createContext(r),i=l=>{const{children:c,...d}=l,h=w.useMemo(()=>d,Object.values(d));return F.jsx(o.Provider,{value:h,children:c})};i.displayName=e+"Provider";function a(l){const c=w.useContext(o);if(c)return c;if(r!==void 0)return r;throw new Error(`\`${l}\` must be used within \`${e}\``)}return[i,a]}function us(e,r=[]){let o=[];function i(l,c){const d=w.createContext(c),h=o.length;o=[...o,c];const p=v=>{const{scope:x,children:S,...C}=v,E=x?.[e]?.[h]||d,b=w.useMemo(()=>C,Object.values(C));return F.jsx(E.Provider,{value:b,children:S})};p.displayName=l+"Provider";function g(v,x){const S=x?.[e]?.[h]||d,C=w.useContext(S);if(C)return C;if(c!==void 0)return c;throw new Error(`\`${v}\` must be used within \`${l}\``)}return[p,g]}const a=()=>{const l=o.map(c=>w.createContext(c));return function(d){const h=d?.[e]||l;return w.useMemo(()=>({[`__scope${e}`]:{...d,[e]:h}}),[d,h])}};return a.scopeName=e,[i,lC(a,...r)]}function lC(...e){const r=e[0];if(e.length===1)return r;const o=()=>{const i=e.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(l){const c=i.reduce((d,{useScope:h,scopeName:p})=>{const v=h(l)[`__scope${p}`];return{...d,...v}},{});return w.useMemo(()=>({[`__scope${r.scopeName}`]:c}),[c])}};return o.scopeName=r.scopeName,o}var Or=globalThis?.document?w.useLayoutEffect:()=>{},uC=Fm[" useInsertionEffect ".trim().toString()]||Or;function jg({prop:e,defaultProp:r,onChange:o=()=>{},caller:i}){const[a,l,c]=cC({defaultProp:r,onChange:o}),d=e!==void 0,h=d?e:a;{const g=w.useRef(e!==void 0);w.useEffect(()=>{const v=g.current;v!==d&&console.warn(`${i} is changing from ${v?"controlled":"uncontrolled"} to ${d?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),g.current=d},[d,i])}const p=w.useCallback(g=>{if(d){const v=dC(g)?g(e):g;v!==e&&c.current?.(v)}else l(g)},[d,e,l,c]);return[h,p]}function cC({defaultProp:e,onChange:r}){const[o,i]=w.useState(e),a=w.useRef(o),l=w.useRef(r);return uC(()=>{l.current=r},[r]),w.useEffect(()=>{a.current!==o&&(l.current?.(o),a.current=o)},[o,a]),[o,i,l]}function dC(e){return typeof e=="function"}var fC=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],Ot=fC.reduce((e,r)=>{const o=ts(`Primitive.${r}`),i=w.forwardRef((a,l)=>{const{asChild:c,...d}=a,h=c?o:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),F.jsx(h,{...d,ref:l})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{});function zg(e,r){e&&hl.flushSync(()=>e.dispatchEvent(r))}function $g(e){const r=e+"CollectionProvider",[o,i]=us(r),[a,l]=o(r,{collectionRef:{current:null},itemMap:new Map}),c=E=>{const{scope:b,children:P}=E,D=jt.useRef(null),N=jt.useRef(new Map).current;return F.jsx(a,{scope:b,itemMap:N,collectionRef:D,children:P})};c.displayName=r;const d=e+"CollectionSlot",h=ts(d),p=jt.forwardRef((E,b)=>{const{scope:P,children:D}=E,N=l(d,P),j=$t(b,N.collectionRef);return F.jsx(h,{ref:j,children:D})});p.displayName=d;const g=e+"CollectionItemSlot",v="data-radix-collection-item",x=ts(g),S=jt.forwardRef((E,b)=>{const{scope:P,children:D,...N}=E,j=jt.useRef(null),H=$t(b,j),M=l(g,P);return jt.useEffect(()=>(M.itemMap.set(j,{ref:j,...N}),()=>void M.itemMap.delete(j))),F.jsx(x,{[v]:"",ref:H,children:D})});S.displayName=g;function C(E){const b=l(e+"CollectionConsumer",E);return jt.useCallback(()=>{const D=b.collectionRef.current;if(!D)return[];const N=Array.from(D.querySelectorAll(`[${v}]`));return Array.from(b.itemMap.values()).sort((M,K)=>N.indexOf(M.ref.current)-N.indexOf(K.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:c,Slot:p,ItemSlot:S},C,i]}var hC=w.createContext(void 0);function Ug(e){const r=w.useContext(hC);return e||r||"ltr"}function Yn(e){const r=w.useRef(e);return w.useEffect(()=>{r.current=e}),w.useMemo(()=>(...o)=>r.current?.(...o),[])}function pC(e,r=globalThis?.document){const o=Yn(e);w.useEffect(()=>{const i=a=>{a.key==="Escape"&&o(a)};return r.addEventListener("keydown",i,{capture:!0}),()=>r.removeEventListener("keydown",i,{capture:!0})},[o,r])}var mC="DismissableLayer",Vc="dismissableLayer.update",gC="dismissableLayer.pointerDownOutside",vC="dismissableLayer.focusOutside",hm,Hg=w.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Bg=w.forwardRef((e,r)=>{const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:l,onInteractOutside:c,onDismiss:d,...h}=e,p=w.useContext(Hg),[g,v]=w.useState(null),x=g?.ownerDocument??globalThis?.document,[,S]=w.useState({}),C=$t(r,K=>v(K)),E=Array.from(p.layers),[b]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),P=E.indexOf(b),D=g?E.indexOf(g):-1,N=p.layersWithOutsidePointerEventsDisabled.size>0,j=D>=P,H=xC(K=>{const W=K.target,Z=[...p.branches].some(se=>se.contains(W));!j||Z||(a?.(K),c?.(K),K.defaultPrevented||d?.())},x),M=SC(K=>{const W=K.target;[...p.branches].some(se=>se.contains(W))||(l?.(K),c?.(K),K.defaultPrevented||d?.())},x);return pC(K=>{D===p.layers.size-1&&(i?.(K),!K.defaultPrevented&&d&&(K.preventDefault(),d()))},x),w.useEffect(()=>{if(g)return o&&(p.layersWithOutsidePointerEventsDisabled.size===0&&(hm=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(g)),p.layers.add(g),pm(),()=>{o&&p.layersWithOutsidePointerEventsDisabled.size===1&&(x.body.style.pointerEvents=hm)}},[g,x,o,p]),w.useEffect(()=>()=>{g&&(p.layers.delete(g),p.layersWithOutsidePointerEventsDisabled.delete(g),pm())},[g,p]),w.useEffect(()=>{const K=()=>S({});return document.addEventListener(Vc,K),()=>document.removeEventListener(Vc,K)},[]),F.jsx(Ot.div,{...h,ref:C,style:{pointerEvents:N?j?"auto":"none":void 0,...e.style},onFocusCapture:je(e.onFocusCapture,M.onFocusCapture),onBlurCapture:je(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:je(e.onPointerDownCapture,H.onPointerDownCapture)})});Bg.displayName=mC;var yC="DismissableLayerBranch",wC=w.forwardRef((e,r)=>{const o=w.useContext(Hg),i=w.useRef(null),a=$t(r,i);return w.useEffect(()=>{const l=i.current;if(l)return o.branches.add(l),()=>{o.branches.delete(l)}},[o.branches]),F.jsx(Ot.div,{...e,ref:a})});wC.displayName=yC;function xC(e,r=globalThis?.document){const o=Yn(e),i=w.useRef(!1),a=w.useRef(()=>{});return w.useEffect(()=>{const l=d=>{if(d.target&&!i.current){let h=function(){Vg(gC,o,p,{discrete:!0})};const p={originalEvent:d};d.pointerType==="touch"?(r.removeEventListener("click",a.current),a.current=h,r.addEventListener("click",a.current,{once:!0})):h()}else r.removeEventListener("click",a.current);i.current=!1},c=window.setTimeout(()=>{r.addEventListener("pointerdown",l)},0);return()=>{window.clearTimeout(c),r.removeEventListener("pointerdown",l),r.removeEventListener("click",a.current)}},[r,o]),{onPointerDownCapture:()=>i.current=!0}}function SC(e,r=globalThis?.document){const o=Yn(e),i=w.useRef(!1);return w.useEffect(()=>{const a=l=>{l.target&&!i.current&&Vg(vC,o,{originalEvent:l},{discrete:!1})};return r.addEventListener("focusin",a),()=>r.removeEventListener("focusin",a)},[r,o]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}function pm(){const e=new CustomEvent(Vc);document.dispatchEvent(e)}function Vg(e,r,o,{discrete:i}){const a=o.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:o});r&&a.addEventListener(e,r,{once:!0}),i?zg(a,l):a.dispatchEvent(l)}var vc=0;function EC(){w.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??mm()),document.body.insertAdjacentElement("beforeend",e[1]??mm()),vc++,()=>{vc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),vc--}},[])}function mm(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var yc="focusScope.autoFocusOnMount",wc="focusScope.autoFocusOnUnmount",gm={bubbles:!1,cancelable:!0},CC="FocusScope",Wg=w.forwardRef((e,r)=>{const{loop:o=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:l,...c}=e,[d,h]=w.useState(null),p=Yn(a),g=Yn(l),v=w.useRef(null),x=$t(r,E=>h(E)),S=w.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;w.useEffect(()=>{if(i){let E=function(N){if(S.paused||!d)return;const j=N.target;d.contains(j)?v.current=j:br(v.current,{select:!0})},b=function(N){if(S.paused||!d)return;const j=N.relatedTarget;j!==null&&(d.contains(j)||br(v.current,{select:!0}))},P=function(N){if(document.activeElement===document.body)for(const H of N)H.removedNodes.length>0&&br(d)};document.addEventListener("focusin",E),document.addEventListener("focusout",b);const D=new MutationObserver(P);return d&&D.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",E),document.removeEventListener("focusout",b),D.disconnect()}}},[i,d,S.paused]),w.useEffect(()=>{if(d){ym.add(S);const E=document.activeElement;if(!d.contains(E)){const P=new CustomEvent(yc,gm);d.addEventListener(yc,p),d.dispatchEvent(P),P.defaultPrevented||(kC(LC(Kg(d)),{select:!0}),document.activeElement===E&&br(d))}return()=>{d.removeEventListener(yc,p),setTimeout(()=>{const P=new CustomEvent(wc,gm);d.addEventListener(wc,g),d.dispatchEvent(P),P.defaultPrevented||br(E??document.body,{select:!0}),d.removeEventListener(wc,g),ym.remove(S)},0)}}},[d,p,g,S]);const C=w.useCallback(E=>{if(!o&&!i||S.paused)return;const b=E.key==="Tab"&&!E.altKey&&!E.ctrlKey&&!E.metaKey,P=document.activeElement;if(b&&P){const D=E.currentTarget,[N,j]=bC(D);N&&j?!E.shiftKey&&P===j?(E.preventDefault(),o&&br(N,{select:!0})):E.shiftKey&&P===N&&(E.preventDefault(),o&&br(j,{select:!0})):P===D&&E.preventDefault()}},[o,i,S.paused]);return F.jsx(Ot.div,{tabIndex:-1,...c,ref:x,onKeyDown:C})});Wg.displayName=CC;function kC(e,{select:r=!1}={}){const o=document.activeElement;for(const i of e)if(br(i,{select:r}),document.activeElement!==o)return}function bC(e){const r=Kg(e),o=vm(r,e),i=vm(r.reverse(),e);return[o,i]}function Kg(e){const r=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const a=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||a?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;o.nextNode();)r.push(o.currentNode);return r}function vm(e,r){for(const o of e)if(!PC(o,{upTo:r}))return o}function PC(e,{upTo:r}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(r!==void 0&&e===r)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function RC(e){return e instanceof HTMLInputElement&&"select"in e}function br(e,{select:r=!1}={}){if(e&&e.focus){const o=document.activeElement;e.focus({preventScroll:!0}),e!==o&&RC(e)&&r&&e.select()}}var ym=OC();function OC(){let e=[];return{add(r){const o=e[0];r!==o&&o?.pause(),e=wm(e,r),e.unshift(r)},remove(r){e=wm(e,r),e[0]?.resume()}}}function wm(e,r){const o=[...e],i=o.indexOf(r);return i!==-1&&o.splice(i,1),o}function LC(e){return e.filter(r=>r.tagName!=="A")}var MC=Fm[" useId ".trim().toString()]||(()=>{}),TC=0;function Wc(e){const[r,o]=w.useState(MC());return Or(()=>{o(i=>i??String(TC++))},[e]),e||(r?`radix-${r}`:"")}const _C=["top","right","bottom","left"],Lr=Math.min,Qt=Math.max,Za=Math.round,Ta=Math.floor,Dn=e=>({x:e,y:e}),NC={left:"right",right:"left",bottom:"top",top:"bottom"},DC={start:"end",end:"start"};function Kc(e,r,o){return Qt(e,Lr(r,o))}function Jn(e,r){return typeof e=="function"?e(r):e}function Xn(e){return e.split("-")[0]}function Xo(e){return e.split("-")[1]}function hd(e){return e==="x"?"y":"x"}function pd(e){return e==="y"?"height":"width"}const AC=new Set(["top","bottom"]);function _n(e){return AC.has(Xn(e))?"y":"x"}function md(e){return hd(_n(e))}function IC(e,r,o){o===void 0&&(o=!1);const i=Xo(e),a=md(e),l=pd(a);let c=a==="x"?i===(o?"end":"start")?"right":"left":i==="start"?"bottom":"top";return r.reference[l]>r.floating[l]&&(c=el(c)),[c,el(c)]}function FC(e){const r=el(e);return[Qc(e),r,Qc(r)]}function Qc(e){return e.replace(/start|end/g,r=>DC[r])}const xm=["left","right"],Sm=["right","left"],jC=["top","bottom"],zC=["bottom","top"];function $C(e,r,o){switch(e){case"top":case"bottom":return o?r?Sm:xm:r?xm:Sm;case"left":case"right":return r?jC:zC;default:return[]}}function UC(e,r,o,i){const a=Xo(e);let l=$C(Xn(e),o==="start",i);return a&&(l=l.map(c=>c+"-"+a),r&&(l=l.concat(l.map(Qc)))),l}function el(e){return e.replace(/left|right|bottom|top/g,r=>NC[r])}function HC(e){return{top:0,right:0,bottom:0,left:0,...e}}function Qg(e){return typeof e!="number"?HC(e):{top:e,right:e,bottom:e,left:e}}function tl(e){const{x:r,y:o,width:i,height:a}=e;return{width:i,height:a,top:o,left:r,right:r+i,bottom:o+a,x:r,y:o}}function Em(e,r,o){let{reference:i,floating:a}=e;const l=_n(r),c=md(r),d=pd(c),h=Xn(r),p=l==="y",g=i.x+i.width/2-a.width/2,v=i.y+i.height/2-a.height/2,x=i[d]/2-a[d]/2;let S;switch(h){case"top":S={x:g,y:i.y-a.height};break;case"bottom":S={x:g,y:i.y+i.height};break;case"right":S={x:i.x+i.width,y:v};break;case"left":S={x:i.x-a.width,y:v};break;default:S={x:i.x,y:i.y}}switch(Xo(r)){case"start":S[c]-=x*(o&&p?-1:1);break;case"end":S[c]+=x*(o&&p?-1:1);break}return S}const BC=async(e,r,o)=>{const{placement:i="bottom",strategy:a="absolute",middleware:l=[],platform:c}=o,d=l.filter(Boolean),h=await(c.isRTL==null?void 0:c.isRTL(r));let p=await c.getElementRects({reference:e,floating:r,strategy:a}),{x:g,y:v}=Em(p,i,h),x=i,S={},C=0;for(let E=0;E<d.length;E++){const{name:b,fn:P}=d[E],{x:D,y:N,data:j,reset:H}=await P({x:g,y:v,initialPlacement:i,placement:x,strategy:a,middlewareData:S,rects:p,platform:c,elements:{reference:e,floating:r}});g=D??g,v=N??v,S={...S,[b]:{...S[b],...j}},H&&C<=50&&(C++,typeof H=="object"&&(H.placement&&(x=H.placement),H.rects&&(p=H.rects===!0?await c.getElementRects({reference:e,floating:r,strategy:a}):H.rects),{x:g,y:v}=Em(p,x,h)),E=-1)}return{x:g,y:v,placement:x,strategy:a,middlewareData:S}};async function ns(e,r){var o;r===void 0&&(r={});const{x:i,y:a,platform:l,rects:c,elements:d,strategy:h}=e,{boundary:p="clippingAncestors",rootBoundary:g="viewport",elementContext:v="floating",altBoundary:x=!1,padding:S=0}=Jn(r,e),C=Qg(S),b=d[x?v==="floating"?"reference":"floating":v],P=tl(await l.getClippingRect({element:(o=await(l.isElement==null?void 0:l.isElement(b)))==null||o?b:b.contextElement||await(l.getDocumentElement==null?void 0:l.getDocumentElement(d.floating)),boundary:p,rootBoundary:g,strategy:h})),D=v==="floating"?{x:i,y:a,width:c.floating.width,height:c.floating.height}:c.reference,N=await(l.getOffsetParent==null?void 0:l.getOffsetParent(d.floating)),j=await(l.isElement==null?void 0:l.isElement(N))?await(l.getScale==null?void 0:l.getScale(N))||{x:1,y:1}:{x:1,y:1},H=tl(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:d,rect:D,offsetParent:N,strategy:h}):D);return{top:(P.top-H.top+C.top)/j.y,bottom:(H.bottom-P.bottom+C.bottom)/j.y,left:(P.left-H.left+C.left)/j.x,right:(H.right-P.right+C.right)/j.x}}const VC=e=>({name:"arrow",options:e,async fn(r){const{x:o,y:i,placement:a,rects:l,platform:c,elements:d,middlewareData:h}=r,{element:p,padding:g=0}=Jn(e,r)||{};if(p==null)return{};const v=Qg(g),x={x:o,y:i},S=md(a),C=pd(S),E=await c.getDimensions(p),b=S==="y",P=b?"top":"left",D=b?"bottom":"right",N=b?"clientHeight":"clientWidth",j=l.reference[C]+l.reference[S]-x[S]-l.floating[C],H=x[S]-l.reference[S],M=await(c.getOffsetParent==null?void 0:c.getOffsetParent(p));let K=M?M[N]:0;(!K||!await(c.isElement==null?void 0:c.isElement(M)))&&(K=d.floating[N]||l.floating[C]);const W=j/2-H/2,Z=K/2-E[C]/2-1,se=Lr(v[P],Z),Me=Lr(v[D],Z),Re=se,ne=K-E[C]-Me,re=K/2-E[C]/2+W,be=Kc(Re,re,ne),X=!h.arrow&&Xo(a)!=null&&re!==be&&l.reference[C]/2-(re<Re?se:Me)-E[C]/2<0,ie=X?re<Re?re-Re:re-ne:0;return{[S]:x[S]+ie,data:{[S]:be,centerOffset:re-be-ie,...X&&{alignmentOffset:ie}},reset:X}}}),WC=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(r){var o,i;const{placement:a,middlewareData:l,rects:c,initialPlacement:d,platform:h,elements:p}=r,{mainAxis:g=!0,crossAxis:v=!0,fallbackPlacements:x,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:C="none",flipAlignment:E=!0,...b}=Jn(e,r);if((o=l.arrow)!=null&&o.alignmentOffset)return{};const P=Xn(a),D=_n(d),N=Xn(d)===d,j=await(h.isRTL==null?void 0:h.isRTL(p.floating)),H=x||(N||!E?[el(d)]:FC(d)),M=C!=="none";!x&&M&&H.push(...UC(d,E,C,j));const K=[d,...H],W=await ns(r,b),Z=[];let se=((i=l.flip)==null?void 0:i.overflows)||[];if(g&&Z.push(W[P]),v){const re=IC(a,c,j);Z.push(W[re[0]],W[re[1]])}if(se=[...se,{placement:a,overflows:Z}],!Z.every(re=>re<=0)){var Me,Re;const re=(((Me=l.flip)==null?void 0:Me.index)||0)+1,be=K[re];if(be&&(!(v==="alignment"?D!==_n(be):!1)||se.every(_=>_.overflows[0]>0&&_n(_.placement)===D)))return{data:{index:re,overflows:se},reset:{placement:be}};let X=(Re=se.filter(ie=>ie.overflows[0]<=0).sort((ie,_)=>ie.overflows[1]-_.overflows[1])[0])==null?void 0:Re.placement;if(!X)switch(S){case"bestFit":{var ne;const ie=(ne=se.filter(_=>{if(M){const V=_n(_.placement);return V===D||V==="y"}return!0}).map(_=>[_.placement,_.overflows.filter(V=>V>0).reduce((V,Q)=>V+Q,0)]).sort((_,V)=>_[1]-V[1])[0])==null?void 0:ne[0];ie&&(X=ie);break}case"initialPlacement":X=d;break}if(a!==X)return{reset:{placement:X}}}return{}}}};function Cm(e,r){return{top:e.top-r.height,right:e.right-r.width,bottom:e.bottom-r.height,left:e.left-r.width}}function km(e){return _C.some(r=>e[r]>=0)}const KC=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(r){const{rects:o}=r,{strategy:i="referenceHidden",...a}=Jn(e,r);switch(i){case"referenceHidden":{const l=await ns(r,{...a,elementContext:"reference"}),c=Cm(l,o.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:km(c)}}}case"escaped":{const l=await ns(r,{...a,altBoundary:!0}),c=Cm(l,o.floating);return{data:{escapedOffsets:c,escaped:km(c)}}}default:return{}}}}},Gg=new Set(["left","top"]);async function QC(e,r){const{placement:o,platform:i,elements:a}=e,l=await(i.isRTL==null?void 0:i.isRTL(a.floating)),c=Xn(o),d=Xo(o),h=_n(o)==="y",p=Gg.has(c)?-1:1,g=l&&h?-1:1,v=Jn(r,e);let{mainAxis:x,crossAxis:S,alignmentAxis:C}=typeof v=="number"?{mainAxis:v,crossAxis:0,alignmentAxis:null}:{mainAxis:v.mainAxis||0,crossAxis:v.crossAxis||0,alignmentAxis:v.alignmentAxis};return d&&typeof C=="number"&&(S=d==="end"?C*-1:C),h?{x:S*g,y:x*p}:{x:x*p,y:S*g}}const GC=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(r){var o,i;const{x:a,y:l,placement:c,middlewareData:d}=r,h=await QC(r,e);return c===((o=d.offset)==null?void 0:o.placement)&&(i=d.arrow)!=null&&i.alignmentOffset?{}:{x:a+h.x,y:l+h.y,data:{...h,placement:c}}}}},YC=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(r){const{x:o,y:i,placement:a}=r,{mainAxis:l=!0,crossAxis:c=!1,limiter:d={fn:b=>{let{x:P,y:D}=b;return{x:P,y:D}}},...h}=Jn(e,r),p={x:o,y:i},g=await ns(r,h),v=_n(Xn(a)),x=hd(v);let S=p[x],C=p[v];if(l){const b=x==="y"?"top":"left",P=x==="y"?"bottom":"right",D=S+g[b],N=S-g[P];S=Kc(D,S,N)}if(c){const b=v==="y"?"top":"left",P=v==="y"?"bottom":"right",D=C+g[b],N=C-g[P];C=Kc(D,C,N)}const E=d.fn({...r,[x]:S,[v]:C});return{...E,data:{x:E.x-o,y:E.y-i,enabled:{[x]:l,[v]:c}}}}}},JC=function(e){return e===void 0&&(e={}),{options:e,fn(r){const{x:o,y:i,placement:a,rects:l,middlewareData:c}=r,{offset:d=0,mainAxis:h=!0,crossAxis:p=!0}=Jn(e,r),g={x:o,y:i},v=_n(a),x=hd(v);let S=g[x],C=g[v];const E=Jn(d,r),b=typeof E=="number"?{mainAxis:E,crossAxis:0}:{mainAxis:0,crossAxis:0,...E};if(h){const N=x==="y"?"height":"width",j=l.reference[x]-l.floating[N]+b.mainAxis,H=l.reference[x]+l.reference[N]-b.mainAxis;S<j?S=j:S>H&&(S=H)}if(p){var P,D;const N=x==="y"?"width":"height",j=Gg.has(Xn(a)),H=l.reference[v]-l.floating[N]+(j&&((P=c.offset)==null?void 0:P[v])||0)+(j?0:b.crossAxis),M=l.reference[v]+l.reference[N]+(j?0:((D=c.offset)==null?void 0:D[v])||0)-(j?b.crossAxis:0);C<H?C=H:C>M&&(C=M)}return{[x]:S,[v]:C}}}},XC=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(r){var o,i;const{placement:a,rects:l,platform:c,elements:d}=r,{apply:h=()=>{},...p}=Jn(e,r),g=await ns(r,p),v=Xn(a),x=Xo(a),S=_n(a)==="y",{width:C,height:E}=l.floating;let b,P;v==="top"||v==="bottom"?(b=v,P=x===(await(c.isRTL==null?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(P=v,b=x==="end"?"top":"bottom");const D=E-g.top-g.bottom,N=C-g.left-g.right,j=Lr(E-g[b],D),H=Lr(C-g[P],N),M=!r.middlewareData.shift;let K=j,W=H;if((o=r.middlewareData.shift)!=null&&o.enabled.x&&(W=N),(i=r.middlewareData.shift)!=null&&i.enabled.y&&(K=D),M&&!x){const se=Qt(g.left,0),Me=Qt(g.right,0),Re=Qt(g.top,0),ne=Qt(g.bottom,0);S?W=C-2*(se!==0||Me!==0?se+Me:Qt(g.left,g.right)):K=E-2*(Re!==0||ne!==0?Re+ne:Qt(g.top,g.bottom))}await h({...r,availableWidth:W,availableHeight:K});const Z=await c.getDimensions(d.floating);return C!==Z.width||E!==Z.height?{reset:{rects:!0}}:{}}}};function ml(){return typeof window<"u"}function qo(e){return Yg(e)?(e.nodeName||"").toLowerCase():"#document"}function Gt(e){var r;return(e==null||(r=e.ownerDocument)==null?void 0:r.defaultView)||window}function Fn(e){var r;return(r=(Yg(e)?e.ownerDocument:e.document)||window.document)==null?void 0:r.documentElement}function Yg(e){return ml()?e instanceof Node||e instanceof Gt(e).Node:!1}function Sn(e){return ml()?e instanceof Element||e instanceof Gt(e).Element:!1}function An(e){return ml()?e instanceof HTMLElement||e instanceof Gt(e).HTMLElement:!1}function bm(e){return!ml()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Gt(e).ShadowRoot}const qC=new Set(["inline","contents"]);function cs(e){const{overflow:r,overflowX:o,overflowY:i,display:a}=En(e);return/auto|scroll|overlay|hidden|clip/.test(r+i+o)&&!qC.has(a)}const ZC=new Set(["table","td","th"]);function ek(e){return ZC.has(qo(e))}const tk=[":popover-open",":modal"];function gl(e){return tk.some(r=>{try{return e.matches(r)}catch{return!1}})}const nk=["transform","translate","scale","rotate","perspective"],rk=["transform","translate","scale","rotate","perspective","filter"],ok=["paint","layout","strict","content"];function gd(e){const r=vd(),o=Sn(e)?En(e):e;return nk.some(i=>o[i]?o[i]!=="none":!1)||(o.containerType?o.containerType!=="normal":!1)||!r&&(o.backdropFilter?o.backdropFilter!=="none":!1)||!r&&(o.filter?o.filter!=="none":!1)||rk.some(i=>(o.willChange||"").includes(i))||ok.some(i=>(o.contain||"").includes(i))}function ik(e){let r=Mr(e);for(;An(r)&&!Qo(r);){if(gd(r))return r;if(gl(r))return null;r=Mr(r)}return null}function vd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}const sk=new Set(["html","body","#document"]);function Qo(e){return sk.has(qo(e))}function En(e){return Gt(e).getComputedStyle(e)}function vl(e){return Sn(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Mr(e){if(qo(e)==="html")return e;const r=e.assignedSlot||e.parentNode||bm(e)&&e.host||Fn(e);return bm(r)?r.host:r}function Jg(e){const r=Mr(e);return Qo(r)?e.ownerDocument?e.ownerDocument.body:e.body:An(r)&&cs(r)?r:Jg(r)}function rs(e,r,o){var i;r===void 0&&(r=[]),o===void 0&&(o=!0);const a=Jg(e),l=a===((i=e.ownerDocument)==null?void 0:i.body),c=Gt(a);if(l){const d=Gc(c);return r.concat(c,c.visualViewport||[],cs(a)?a:[],d&&o?rs(d):[])}return r.concat(a,rs(a,[],o))}function Gc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xg(e){const r=En(e);let o=parseFloat(r.width)||0,i=parseFloat(r.height)||0;const a=An(e),l=a?e.offsetWidth:o,c=a?e.offsetHeight:i,d=Za(o)!==l||Za(i)!==c;return d&&(o=l,i=c),{width:o,height:i,$:d}}function yd(e){return Sn(e)?e:e.contextElement}function Vo(e){const r=yd(e);if(!An(r))return Dn(1);const o=r.getBoundingClientRect(),{width:i,height:a,$:l}=Xg(r);let c=(l?Za(o.width):o.width)/i,d=(l?Za(o.height):o.height)/a;return(!c||!Number.isFinite(c))&&(c=1),(!d||!Number.isFinite(d))&&(d=1),{x:c,y:d}}const ak=Dn(0);function qg(e){const r=Gt(e);return!vd()||!r.visualViewport?ak:{x:r.visualViewport.offsetLeft,y:r.visualViewport.offsetTop}}function lk(e,r,o){return r===void 0&&(r=!1),!o||r&&o!==Gt(e)?!1:r}function no(e,r,o,i){r===void 0&&(r=!1),o===void 0&&(o=!1);const a=e.getBoundingClientRect(),l=yd(e);let c=Dn(1);r&&(i?Sn(i)&&(c=Vo(i)):c=Vo(e));const d=lk(l,o,i)?qg(l):Dn(0);let h=(a.left+d.x)/c.x,p=(a.top+d.y)/c.y,g=a.width/c.x,v=a.height/c.y;if(l){const x=Gt(l),S=i&&Sn(i)?Gt(i):i;let C=x,E=Gc(C);for(;E&&i&&S!==C;){const b=Vo(E),P=E.getBoundingClientRect(),D=En(E),N=P.left+(E.clientLeft+parseFloat(D.paddingLeft))*b.x,j=P.top+(E.clientTop+parseFloat(D.paddingTop))*b.y;h*=b.x,p*=b.y,g*=b.x,v*=b.y,h+=N,p+=j,C=Gt(E),E=Gc(C)}}return tl({width:g,height:v,x:h,y:p})}function wd(e,r){const o=vl(e).scrollLeft;return r?r.left+o:no(Fn(e)).left+o}function Zg(e,r,o){o===void 0&&(o=!1);const i=e.getBoundingClientRect(),a=i.left+r.scrollLeft-(o?0:wd(e,i)),l=i.top+r.scrollTop;return{x:a,y:l}}function uk(e){let{elements:r,rect:o,offsetParent:i,strategy:a}=e;const l=a==="fixed",c=Fn(i),d=r?gl(r.floating):!1;if(i===c||d&&l)return o;let h={scrollLeft:0,scrollTop:0},p=Dn(1);const g=Dn(0),v=An(i);if((v||!v&&!l)&&((qo(i)!=="body"||cs(c))&&(h=vl(i)),An(i))){const S=no(i);p=Vo(i),g.x=S.x+i.clientLeft,g.y=S.y+i.clientTop}const x=c&&!v&&!l?Zg(c,h,!0):Dn(0);return{width:o.width*p.x,height:o.height*p.y,x:o.x*p.x-h.scrollLeft*p.x+g.x+x.x,y:o.y*p.y-h.scrollTop*p.y+g.y+x.y}}function ck(e){return Array.from(e.getClientRects())}function dk(e){const r=Fn(e),o=vl(e),i=e.ownerDocument.body,a=Qt(r.scrollWidth,r.clientWidth,i.scrollWidth,i.clientWidth),l=Qt(r.scrollHeight,r.clientHeight,i.scrollHeight,i.clientHeight);let c=-o.scrollLeft+wd(e);const d=-o.scrollTop;return En(i).direction==="rtl"&&(c+=Qt(r.clientWidth,i.clientWidth)-a),{width:a,height:l,x:c,y:d}}function fk(e,r){const o=Gt(e),i=Fn(e),a=o.visualViewport;let l=i.clientWidth,c=i.clientHeight,d=0,h=0;if(a){l=a.width,c=a.height;const p=vd();(!p||p&&r==="fixed")&&(d=a.offsetLeft,h=a.offsetTop)}return{width:l,height:c,x:d,y:h}}const hk=new Set(["absolute","fixed"]);function pk(e,r){const o=no(e,!0,r==="fixed"),i=o.top+e.clientTop,a=o.left+e.clientLeft,l=An(e)?Vo(e):Dn(1),c=e.clientWidth*l.x,d=e.clientHeight*l.y,h=a*l.x,p=i*l.y;return{width:c,height:d,x:h,y:p}}function Pm(e,r,o){let i;if(r==="viewport")i=fk(e,o);else if(r==="document")i=dk(Fn(e));else if(Sn(r))i=pk(r,o);else{const a=qg(e);i={x:r.x-a.x,y:r.y-a.y,width:r.width,height:r.height}}return tl(i)}function ev(e,r){const o=Mr(e);return o===r||!Sn(o)||Qo(o)?!1:En(o).position==="fixed"||ev(o,r)}function mk(e,r){const o=r.get(e);if(o)return o;let i=rs(e,[],!1).filter(d=>Sn(d)&&qo(d)!=="body"),a=null;const l=En(e).position==="fixed";let c=l?Mr(e):e;for(;Sn(c)&&!Qo(c);){const d=En(c),h=gd(c);!h&&d.position==="fixed"&&(a=null),(l?!h&&!a:!h&&d.position==="static"&&!!a&&hk.has(a.position)||cs(c)&&!h&&ev(e,c))?i=i.filter(g=>g!==c):a=d,c=Mr(c)}return r.set(e,i),i}function gk(e){let{element:r,boundary:o,rootBoundary:i,strategy:a}=e;const c=[...o==="clippingAncestors"?gl(r)?[]:mk(r,this._c):[].concat(o),i],d=c[0],h=c.reduce((p,g)=>{const v=Pm(r,g,a);return p.top=Qt(v.top,p.top),p.right=Lr(v.right,p.right),p.bottom=Lr(v.bottom,p.bottom),p.left=Qt(v.left,p.left),p},Pm(r,d,a));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}}function vk(e){const{width:r,height:o}=Xg(e);return{width:r,height:o}}function yk(e,r,o){const i=An(r),a=Fn(r),l=o==="fixed",c=no(e,!0,l,r);let d={scrollLeft:0,scrollTop:0};const h=Dn(0);function p(){h.x=wd(a)}if(i||!i&&!l)if((qo(r)!=="body"||cs(a))&&(d=vl(r)),i){const S=no(r,!0,l,r);h.x=S.x+r.clientLeft,h.y=S.y+r.clientTop}else a&&p();l&&!i&&a&&p();const g=a&&!i&&!l?Zg(a,d):Dn(0),v=c.left+d.scrollLeft-h.x-g.x,x=c.top+d.scrollTop-h.y-g.y;return{x:v,y:x,width:c.width,height:c.height}}function xc(e){return En(e).position==="static"}function Rm(e,r){if(!An(e)||En(e).position==="fixed")return null;if(r)return r(e);let o=e.offsetParent;return Fn(e)===o&&(o=o.ownerDocument.body),o}function tv(e,r){const o=Gt(e);if(gl(e))return o;if(!An(e)){let a=Mr(e);for(;a&&!Qo(a);){if(Sn(a)&&!xc(a))return a;a=Mr(a)}return o}let i=Rm(e,r);for(;i&&ek(i)&&xc(i);)i=Rm(i,r);return i&&Qo(i)&&xc(i)&&!gd(i)?o:i||ik(e)||o}const wk=async function(e){const r=this.getOffsetParent||tv,o=this.getDimensions,i=await o(e.floating);return{reference:yk(e.reference,await r(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function xk(e){return En(e).direction==="rtl"}const Sk={convertOffsetParentRelativeRectToViewportRelativeRect:uk,getDocumentElement:Fn,getClippingRect:gk,getOffsetParent:tv,getElementRects:wk,getClientRects:ck,getDimensions:vk,getScale:Vo,isElement:Sn,isRTL:xk};function nv(e,r){return e.x===r.x&&e.y===r.y&&e.width===r.width&&e.height===r.height}function Ek(e,r){let o=null,i;const a=Fn(e);function l(){var d;clearTimeout(i),(d=o)==null||d.disconnect(),o=null}function c(d,h){d===void 0&&(d=!1),h===void 0&&(h=1),l();const p=e.getBoundingClientRect(),{left:g,top:v,width:x,height:S}=p;if(d||r(),!x||!S)return;const C=Ta(v),E=Ta(a.clientWidth-(g+x)),b=Ta(a.clientHeight-(v+S)),P=Ta(g),N={rootMargin:-C+"px "+-E+"px "+-b+"px "+-P+"px",threshold:Qt(0,Lr(1,h))||1};let j=!0;function H(M){const K=M[0].intersectionRatio;if(K!==h){if(!j)return c();K?c(!1,K):i=setTimeout(()=>{c(!1,1e-7)},1e3)}K===1&&!nv(p,e.getBoundingClientRect())&&c(),j=!1}try{o=new IntersectionObserver(H,{...N,root:a.ownerDocument})}catch{o=new IntersectionObserver(H,N)}o.observe(e)}return c(!0),l}function Ck(e,r,o,i){i===void 0&&(i={});const{ancestorScroll:a=!0,ancestorResize:l=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:d=typeof IntersectionObserver=="function",animationFrame:h=!1}=i,p=yd(e),g=a||l?[...p?rs(p):[],...rs(r)]:[];g.forEach(P=>{a&&P.addEventListener("scroll",o,{passive:!0}),l&&P.addEventListener("resize",o)});const v=p&&d?Ek(p,o):null;let x=-1,S=null;c&&(S=new ResizeObserver(P=>{let[D]=P;D&&D.target===p&&S&&(S.unobserve(r),cancelAnimationFrame(x),x=requestAnimationFrame(()=>{var N;(N=S)==null||N.observe(r)})),o()}),p&&!h&&S.observe(p),S.observe(r));let C,E=h?no(e):null;h&&b();function b(){const P=no(e);E&&!nv(E,P)&&o(),E=P,C=requestAnimationFrame(b)}return o(),()=>{var P;g.forEach(D=>{a&&D.removeEventListener("scroll",o),l&&D.removeEventListener("resize",o)}),v?.(),(P=S)==null||P.disconnect(),S=null,h&&cancelAnimationFrame(C)}}const kk=GC,bk=YC,Pk=WC,Rk=XC,Ok=KC,Om=VC,Lk=JC,Mk=(e,r,o)=>{const i=new Map,a={platform:Sk,...o},l={...a.platform,_c:i};return BC(e,r,{...a,platform:l})};var Tk=typeof document<"u",_k=function(){},Ha=Tk?w.useLayoutEffect:_k;function nl(e,r){if(e===r)return!0;if(typeof e!=typeof r)return!1;if(typeof e=="function"&&e.toString()===r.toString())return!0;let o,i,a;if(e&&r&&typeof e=="object"){if(Array.isArray(e)){if(o=e.length,o!==r.length)return!1;for(i=o;i--!==0;)if(!nl(e[i],r[i]))return!1;return!0}if(a=Object.keys(e),o=a.length,o!==Object.keys(r).length)return!1;for(i=o;i--!==0;)if(!{}.hasOwnProperty.call(r,a[i]))return!1;for(i=o;i--!==0;){const l=a[i];if(!(l==="_owner"&&e.$$typeof)&&!nl(e[l],r[l]))return!1}return!0}return e!==e&&r!==r}function rv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Lm(e,r){const o=rv(e);return Math.round(r*o)/o}function Sc(e){const r=w.useRef(e);return Ha(()=>{r.current=e}),r}function Nk(e){e===void 0&&(e={});const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a,elements:{reference:l,floating:c}={},transform:d=!0,whileElementsMounted:h,open:p}=e,[g,v]=w.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[x,S]=w.useState(i);nl(x,i)||S(i);const[C,E]=w.useState(null),[b,P]=w.useState(null),D=w.useCallback(_=>{_!==M.current&&(M.current=_,E(_))},[]),N=w.useCallback(_=>{_!==K.current&&(K.current=_,P(_))},[]),j=l||C,H=c||b,M=w.useRef(null),K=w.useRef(null),W=w.useRef(g),Z=h!=null,se=Sc(h),Me=Sc(a),Re=Sc(p),ne=w.useCallback(()=>{if(!M.current||!K.current)return;const _={placement:r,strategy:o,middleware:x};Me.current&&(_.platform=Me.current),Mk(M.current,K.current,_).then(V=>{const Q={...V,isPositioned:Re.current!==!1};re.current&&!nl(W.current,Q)&&(W.current=Q,hl.flushSync(()=>{v(Q)}))})},[x,r,o,Me,Re]);Ha(()=>{p===!1&&W.current.isPositioned&&(W.current.isPositioned=!1,v(_=>({..._,isPositioned:!1})))},[p]);const re=w.useRef(!1);Ha(()=>(re.current=!0,()=>{re.current=!1}),[]),Ha(()=>{if(j&&(M.current=j),H&&(K.current=H),j&&H){if(se.current)return se.current(j,H,ne);ne()}},[j,H,ne,se,Z]);const be=w.useMemo(()=>({reference:M,floating:K,setReference:D,setFloating:N}),[D,N]),X=w.useMemo(()=>({reference:j,floating:H}),[j,H]),ie=w.useMemo(()=>{const _={position:o,left:0,top:0};if(!X.floating)return _;const V=Lm(X.floating,g.x),Q=Lm(X.floating,g.y);return d?{..._,transform:"translate("+V+"px, "+Q+"px)",...rv(X.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:V,top:Q}},[o,d,X.floating,g.x,g.y]);return w.useMemo(()=>({...g,update:ne,refs:be,elements:X,floatingStyles:ie}),[g,ne,be,X,ie])}const Dk=e=>{function r(o){return{}.hasOwnProperty.call(o,"current")}return{name:"arrow",options:e,fn(o){const{element:i,padding:a}=typeof e=="function"?e(o):e;return i&&r(i)?i.current!=null?Om({element:i.current,padding:a}).fn(o):{}:i?Om({element:i,padding:a}).fn(o):{}}}},Ak=(e,r)=>({...kk(e),options:[e,r]}),Ik=(e,r)=>({...bk(e),options:[e,r]}),Fk=(e,r)=>({...Lk(e),options:[e,r]}),jk=(e,r)=>({...Pk(e),options:[e,r]}),zk=(e,r)=>({...Rk(e),options:[e,r]}),$k=(e,r)=>({...Ok(e),options:[e,r]}),Uk=(e,r)=>({...Dk(e),options:[e,r]});var Hk="Arrow",ov=w.forwardRef((e,r)=>{const{children:o,width:i=10,height:a=5,...l}=e;return F.jsx(Ot.svg,{...l,ref:r,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?o:F.jsx("polygon",{points:"0,0 30,0 15,10"})})});ov.displayName=Hk;var Bk=ov;function Vk(e){const[r,o]=w.useState(void 0);return Or(()=>{if(e){o({width:e.offsetWidth,height:e.offsetHeight});const i=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const l=a[0];let c,d;if("borderBoxSize"in l){const h=l.borderBoxSize,p=Array.isArray(h)?h[0]:h;c=p.inlineSize,d=p.blockSize}else c=e.offsetWidth,d=e.offsetHeight;o({width:c,height:d})});return i.observe(e,{box:"border-box"}),()=>i.unobserve(e)}else o(void 0)},[e]),r}var xd="Popper",[iv,sv]=us(xd),[Wk,av]=iv(xd),lv=e=>{const{__scopePopper:r,children:o}=e,[i,a]=w.useState(null);return F.jsx(Wk,{scope:r,anchor:i,onAnchorChange:a,children:o})};lv.displayName=xd;var uv="PopperAnchor",cv=w.forwardRef((e,r)=>{const{__scopePopper:o,virtualRef:i,...a}=e,l=av(uv,o),c=w.useRef(null),d=$t(r,c);return w.useEffect(()=>{l.onAnchorChange(i?.current||c.current)}),i?null:F.jsx(Ot.div,{...a,ref:d})});cv.displayName=uv;var Sd="PopperContent",[Kk,Qk]=iv(Sd),dv=w.forwardRef((e,r)=>{const{__scopePopper:o,side:i="bottom",sideOffset:a=0,align:l="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:h=!0,collisionBoundary:p=[],collisionPadding:g=0,sticky:v="partial",hideWhenDetached:x=!1,updatePositionStrategy:S="optimized",onPlaced:C,...E}=e,b=av(Sd,o),[P,D]=w.useState(null),N=$t(r,ue=>D(ue)),[j,H]=w.useState(null),M=Vk(j),K=M?.width??0,W=M?.height??0,Z=i+(l!=="center"?"-"+l:""),se=typeof g=="number"?g:{top:0,right:0,bottom:0,left:0,...g},Me=Array.isArray(p)?p:[p],Re=Me.length>0,ne={padding:se,boundary:Me.filter(Yk),altBoundary:Re},{refs:re,floatingStyles:be,placement:X,isPositioned:ie,middlewareData:_}=Nk({strategy:"fixed",placement:Z,whileElementsMounted:(...ue)=>Ck(...ue,{animationFrame:S==="always"}),elements:{reference:b.anchor},middleware:[Ak({mainAxis:a+W,alignmentAxis:c}),h&&Ik({mainAxis:!0,crossAxis:!1,limiter:v==="partial"?Fk():void 0,...ne}),h&&jk({...ne}),zk({...ne,apply:({elements:ue,rects:_e,availableWidth:ze,availableHeight:Ie})=>{const{width:mt,height:Yt}=_e.reference,un=ue.floating.style;un.setProperty("--radix-popper-available-width",`${ze}px`),un.setProperty("--radix-popper-available-height",`${Ie}px`),un.setProperty("--radix-popper-anchor-width",`${mt}px`),un.setProperty("--radix-popper-anchor-height",`${Yt}px`)}}),j&&Uk({element:j,padding:d}),Jk({arrowWidth:K,arrowHeight:W}),x&&$k({strategy:"referenceHidden",...ne})]}),[V,Q]=pv(X),O=Yn(C);Or(()=>{ie&&O?.()},[ie,O]);const B=_.arrow?.x,oe=_.arrow?.y,le=_.arrow?.centerOffset!==0,[Oe,Pe]=w.useState();return Or(()=>{P&&Pe(window.getComputedStyle(P).zIndex)},[P]),F.jsx("div",{ref:re.setFloating,"data-radix-popper-content-wrapper":"",style:{...be,transform:ie?be.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Oe,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:F.jsx(Kk,{scope:o,placedSide:V,onArrowChange:H,arrowX:B,arrowY:oe,shouldHideArrow:le,children:F.jsx(Ot.div,{"data-side":V,"data-align":Q,...E,ref:N,style:{...E.style,animation:ie?void 0:"none"}})})})});dv.displayName=Sd;var fv="PopperArrow",Gk={top:"bottom",right:"left",bottom:"top",left:"right"},hv=w.forwardRef(function(r,o){const{__scopePopper:i,...a}=r,l=Qk(fv,i),c=Gk[l.placedSide];return F.jsx("span",{ref:l.onArrowChange,style:{position:"absolute",left:l.arrowX,top:l.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[l.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[l.placedSide],visibility:l.shouldHideArrow?"hidden":void 0},children:F.jsx(Bk,{...a,ref:o,style:{...a.style,display:"block"}})})});hv.displayName=fv;function Yk(e){return e!==null}var Jk=e=>({name:"transformOrigin",options:e,fn(r){const{placement:o,rects:i,middlewareData:a}=r,c=a.arrow?.centerOffset!==0,d=c?0:e.arrowWidth,h=c?0:e.arrowHeight,[p,g]=pv(o),v={start:"0%",center:"50%",end:"100%"}[g],x=(a.arrow?.x??0)+d/2,S=(a.arrow?.y??0)+h/2;let C="",E="";return p==="bottom"?(C=c?v:`${x}px`,E=`${-h}px`):p==="top"?(C=c?v:`${x}px`,E=`${i.floating.height+h}px`):p==="right"?(C=`${-h}px`,E=c?v:`${S}px`):p==="left"&&(C=`${i.floating.width+h}px`,E=c?v:`${S}px`),{data:{x:C,y:E}}}});function pv(e){const[r,o="center"]=e.split("-");return[r,o]}var Xk=lv,qk=cv,Zk=dv,eb=hv,tb="Portal",mv=w.forwardRef((e,r)=>{const{container:o,...i}=e,[a,l]=w.useState(!1);Or(()=>l(!0),[]);const c=o||a&&globalThis?.document?.body;return c?HE.createPortal(F.jsx(Ot.div,{...i,ref:r}),c):null});mv.displayName=tb;function nb(e,r){return w.useReducer((o,i)=>r[o][i]??o,e)}var ds=e=>{const{present:r,children:o}=e,i=rb(r),a=typeof o=="function"?o({present:i.isPresent}):w.Children.only(o),l=$t(i.ref,ob(a));return typeof o=="function"||i.isPresent?w.cloneElement(a,{ref:l}):null};ds.displayName="Presence";function rb(e){const[r,o]=w.useState(),i=w.useRef(null),a=w.useRef(e),l=w.useRef("none"),c=e?"mounted":"unmounted",[d,h]=nb(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return w.useEffect(()=>{const p=_a(i.current);l.current=d==="mounted"?p:"none"},[d]),Or(()=>{const p=i.current,g=a.current;if(g!==e){const x=l.current,S=_a(p);e?h("MOUNT"):S==="none"||p?.display==="none"?h("UNMOUNT"):h(g&&x!==S?"ANIMATION_OUT":"UNMOUNT"),a.current=e}},[e,h]),Or(()=>{if(r){let p;const g=r.ownerDocument.defaultView??window,v=S=>{const E=_a(i.current).includes(S.animationName);if(S.target===r&&E&&(h("ANIMATION_END"),!a.current)){const b=r.style.animationFillMode;r.style.animationFillMode="forwards",p=g.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=b)})}},x=S=>{S.target===r&&(l.current=_a(i.current))};return r.addEventListener("animationstart",x),r.addEventListener("animationcancel",v),r.addEventListener("animationend",v),()=>{g.clearTimeout(p),r.removeEventListener("animationstart",x),r.removeEventListener("animationcancel",v),r.removeEventListener("animationend",v)}}else h("ANIMATION_END")},[r,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:w.useCallback(p=>{i.current=p?getComputedStyle(p):null,o(p)},[])}}function _a(e){return e?.animationName||"none"}function ob(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(r=Object.getOwnPropertyDescriptor(e,"ref")?.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o?e.props.ref:e.props.ref||e.ref)}var Ec="rovingFocusGroup.onEntryFocus",ib={bubbles:!1,cancelable:!0},fs="RovingFocusGroup",[Yc,gv,sb]=$g(fs),[ab,vv]=us(fs,[sb]),[lb,ub]=ab(fs),yv=w.forwardRef((e,r)=>F.jsx(Yc.Provider,{scope:e.__scopeRovingFocusGroup,children:F.jsx(Yc.Slot,{scope:e.__scopeRovingFocusGroup,children:F.jsx(cb,{...e,ref:r})})}));yv.displayName=fs;var cb=w.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,orientation:i,loop:a=!1,dir:l,currentTabStopId:c,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:h,onEntryFocus:p,preventScrollOnEntryFocus:g=!1,...v}=e,x=w.useRef(null),S=$t(r,x),C=Ug(l),[E,b]=jg({prop:c,defaultProp:d??null,onChange:h,caller:fs}),[P,D]=w.useState(!1),N=Yn(p),j=gv(o),H=w.useRef(!1),[M,K]=w.useState(0);return w.useEffect(()=>{const W=x.current;if(W)return W.addEventListener(Ec,N),()=>W.removeEventListener(Ec,N)},[N]),F.jsx(lb,{scope:o,orientation:i,dir:C,loop:a,currentTabStopId:E,onItemFocus:w.useCallback(W=>b(W),[b]),onItemShiftTab:w.useCallback(()=>D(!0),[]),onFocusableItemAdd:w.useCallback(()=>K(W=>W+1),[]),onFocusableItemRemove:w.useCallback(()=>K(W=>W-1),[]),children:F.jsx(Ot.div,{tabIndex:P||M===0?-1:0,"data-orientation":i,...v,ref:S,style:{outline:"none",...e.style},onMouseDown:je(e.onMouseDown,()=>{H.current=!0}),onFocus:je(e.onFocus,W=>{const Z=!H.current;if(W.target===W.currentTarget&&Z&&!P){const se=new CustomEvent(Ec,ib);if(W.currentTarget.dispatchEvent(se),!se.defaultPrevented){const Me=j().filter(X=>X.focusable),Re=Me.find(X=>X.active),ne=Me.find(X=>X.id===E),be=[Re,ne,...Me].filter(Boolean).map(X=>X.ref.current);Sv(be,g)}}H.current=!1}),onBlur:je(e.onBlur,()=>D(!1))})})}),wv="RovingFocusGroupItem",xv=w.forwardRef((e,r)=>{const{__scopeRovingFocusGroup:o,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...d}=e,h=Wc(),p=l||h,g=ub(wv,o),v=g.currentTabStopId===p,x=gv(o),{onFocusableItemAdd:S,onFocusableItemRemove:C,currentTabStopId:E}=g;return w.useEffect(()=>{if(i)return S(),()=>C()},[i,S,C]),F.jsx(Yc.ItemSlot,{scope:o,id:p,focusable:i,active:a,children:F.jsx(Ot.span,{tabIndex:v?0:-1,"data-orientation":g.orientation,...d,ref:r,onMouseDown:je(e.onMouseDown,b=>{i?g.onItemFocus(p):b.preventDefault()}),onFocus:je(e.onFocus,()=>g.onItemFocus(p)),onKeyDown:je(e.onKeyDown,b=>{if(b.key==="Tab"&&b.shiftKey){g.onItemShiftTab();return}if(b.target!==b.currentTarget)return;const P=hb(b,g.orientation,g.dir);if(P!==void 0){if(b.metaKey||b.ctrlKey||b.altKey||b.shiftKey)return;b.preventDefault();let N=x().filter(j=>j.focusable).map(j=>j.ref.current);if(P==="last")N.reverse();else if(P==="prev"||P==="next"){P==="prev"&&N.reverse();const j=N.indexOf(b.currentTarget);N=g.loop?pb(N,j+1):N.slice(j+1)}setTimeout(()=>Sv(N))}}),children:typeof c=="function"?c({isCurrentTabStop:v,hasTabStop:E!=null}):c})})});xv.displayName=wv;var db={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function fb(e,r){return r!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function hb(e,r,o){const i=fb(e.key,o);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(i))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(i)))return db[i]}function Sv(e,r=!1){const o=document.activeElement;for(const i of e)if(i===o||(i.focus({preventScroll:r}),document.activeElement!==o))return}function pb(e,r){return e.map((o,i)=>e[(r+i)%e.length])}var mb=yv,gb=xv,vb=function(e){if(typeof document>"u")return null;var r=Array.isArray(e)?e[0]:e;return r.ownerDocument.body},zo=new WeakMap,Na=new WeakMap,Da={},Cc=0,Ev=function(e){return e&&(e.host||Ev(e.parentNode))},yb=function(e,r){return r.map(function(o){if(e.contains(o))return o;var i=Ev(o);return i&&e.contains(i)?i:(console.error("aria-hidden",o,"in not contained inside",e,". Doing nothing"),null)}).filter(function(o){return!!o})},wb=function(e,r,o,i){var a=yb(r,Array.isArray(e)?e:[e]);Da[o]||(Da[o]=new WeakMap);var l=Da[o],c=[],d=new Set,h=new Set(a),p=function(v){!v||d.has(v)||(d.add(v),p(v.parentNode))};a.forEach(p);var g=function(v){!v||h.has(v)||Array.prototype.forEach.call(v.children,function(x){if(d.has(x))g(x);else try{var S=x.getAttribute(i),C=S!==null&&S!=="false",E=(zo.get(x)||0)+1,b=(l.get(x)||0)+1;zo.set(x,E),l.set(x,b),c.push(x),E===1&&C&&Na.set(x,!0),b===1&&x.setAttribute(o,"true"),C||x.setAttribute(i,"true")}catch(P){console.error("aria-hidden: cannot operate on ",x,P)}})};return g(r),d.clear(),Cc++,function(){c.forEach(function(v){var x=zo.get(v)-1,S=l.get(v)-1;zo.set(v,x),l.set(v,S),x||(Na.has(v)||v.removeAttribute(i),Na.delete(v)),S||v.removeAttribute(o)}),Cc--,Cc||(zo=new WeakMap,zo=new WeakMap,Na=new WeakMap,Da={})}},xb=function(e,r,o){o===void 0&&(o="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),a=vb(e);return a?(i.push.apply(i,Array.from(a.querySelectorAll("[aria-live], script"))),wb(i,a,o,"aria-hidden")):function(){return null}},Mn=function(){return Mn=Object.assign||function(r){for(var o,i=1,a=arguments.length;i<a;i++){o=arguments[i];for(var l in o)Object.prototype.hasOwnProperty.call(o,l)&&(r[l]=o[l])}return r},Mn.apply(this,arguments)};function Cv(e,r){var o={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&r.indexOf(i)<0&&(o[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)r.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(o[i[a]]=e[i[a]]);return o}function Sb(e,r,o){if(o||arguments.length===2)for(var i=0,a=r.length,l;i<a;i++)(l||!(i in r))&&(l||(l=Array.prototype.slice.call(r,0,i)),l[i]=r[i]);return e.concat(l||Array.prototype.slice.call(r))}var Ba="right-scroll-bar-position",Va="width-before-scroll-bar",Eb="with-scroll-bars-hidden",Cb="--removed-body-scroll-bar-size";function kc(e,r){return typeof e=="function"?e(r):e&&(e.current=r),e}function kb(e,r){var o=w.useState(function(){return{value:e,callback:r,facade:{get current(){return o.value},set current(i){var a=o.value;a!==i&&(o.value=i,o.callback(i,a))}}}})[0];return o.callback=r,o.facade}var bb=typeof window<"u"?w.useLayoutEffect:w.useEffect,Mm=new WeakMap;function Pb(e,r){var o=kb(null,function(i){return e.forEach(function(a){return kc(a,i)})});return bb(function(){var i=Mm.get(o);if(i){var a=new Set(i),l=new Set(e),c=o.current;a.forEach(function(d){l.has(d)||kc(d,null)}),l.forEach(function(d){a.has(d)||kc(d,c)})}Mm.set(o,e)},[e]),o}function Rb(e){return e}function Ob(e,r){r===void 0&&(r=Rb);var o=[],i=!1,a={read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return o.length?o[o.length-1]:e},useMedium:function(l){var c=r(l,i);return o.push(c),function(){o=o.filter(function(d){return d!==c})}},assignSyncMedium:function(l){for(i=!0;o.length;){var c=o;o=[],c.forEach(l)}o={push:function(d){return l(d)},filter:function(){return o}}},assignMedium:function(l){i=!0;var c=[];if(o.length){var d=o;o=[],d.forEach(l),c=o}var h=function(){var g=c;c=[],g.forEach(l)},p=function(){return Promise.resolve().then(h)};p(),o={push:function(g){c.push(g),p()},filter:function(g){return c=c.filter(g),o}}}};return a}function Lb(e){e===void 0&&(e={});var r=Ob(null);return r.options=Mn({async:!0,ssr:!1},e),r}var kv=function(e){var r=e.sideCar,o=Cv(e,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=r.read();if(!i)throw new Error("Sidecar medium not found");return w.createElement(i,Mn({},o))};kv.isSideCarExport=!0;function Mb(e,r){return e.useMedium(r),kv}var bv=Lb(),bc=function(){},yl=w.forwardRef(function(e,r){var o=w.useRef(null),i=w.useState({onScrollCapture:bc,onWheelCapture:bc,onTouchMoveCapture:bc}),a=i[0],l=i[1],c=e.forwardProps,d=e.children,h=e.className,p=e.removeScrollBar,g=e.enabled,v=e.shards,x=e.sideCar,S=e.noRelative,C=e.noIsolation,E=e.inert,b=e.allowPinchZoom,P=e.as,D=P===void 0?"div":P,N=e.gapMode,j=Cv(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),H=x,M=Pb([o,r]),K=Mn(Mn({},j),a);return w.createElement(w.Fragment,null,g&&w.createElement(H,{sideCar:bv,removeScrollBar:p,shards:v,noRelative:S,noIsolation:C,inert:E,setCallbacks:l,allowPinchZoom:!!b,lockRef:o,gapMode:N}),c?w.cloneElement(w.Children.only(d),Mn(Mn({},K),{ref:M})):w.createElement(D,Mn({},K,{className:h,ref:M}),d))});yl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};yl.classNames={fullWidth:Va,zeroRight:Ba};var Tb=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function _b(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var r=Tb();return r&&e.setAttribute("nonce",r),e}function Nb(e,r){e.styleSheet?e.styleSheet.cssText=r:e.appendChild(document.createTextNode(r))}function Db(e){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(e)}var Ab=function(){var e=0,r=null;return{add:function(o){e==0&&(r=_b())&&(Nb(r,o),Db(r)),e++},remove:function(){e--,!e&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},Ib=function(){var e=Ab();return function(r,o){w.useEffect(function(){return e.add(r),function(){e.remove()}},[r&&o])}},Pv=function(){var e=Ib(),r=function(o){var i=o.styles,a=o.dynamic;return e(i,a),null};return r},Fb={left:0,top:0,right:0,gap:0},Pc=function(e){return parseInt(e||"",10)||0},jb=function(e){var r=window.getComputedStyle(document.body),o=r[e==="padding"?"paddingLeft":"marginLeft"],i=r[e==="padding"?"paddingTop":"marginTop"],a=r[e==="padding"?"paddingRight":"marginRight"];return[Pc(o),Pc(i),Pc(a)]},zb=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Fb;var r=jb(e),o=document.documentElement.clientWidth,i=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,i-o+r[2]-r[0])}},$b=Pv(),Wo="data-scroll-locked",Ub=function(e,r,o,i){var a=e.left,l=e.top,c=e.right,d=e.gap;return o===void 0&&(o="margin"),`
  .`.concat(Eb,` {
   overflow: hidden `).concat(i,`;
   padding-right: `).concat(d,"px ").concat(i,`;
  }
  body[`).concat(Wo,`] {
    overflow: hidden `).concat(i,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(i,";"),o==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(l,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(d,"px ").concat(i,`;
    `),o==="padding"&&"padding-right: ".concat(d,"px ").concat(i,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ba,` {
    right: `).concat(d,"px ").concat(i,`;
  }
  
  .`).concat(Va,` {
    margin-right: `).concat(d,"px ").concat(i,`;
  }
  
  .`).concat(Ba," .").concat(Ba,` {
    right: 0 `).concat(i,`;
  }
  
  .`).concat(Va," .").concat(Va,` {
    margin-right: 0 `).concat(i,`;
  }
  
  body[`).concat(Wo,`] {
    `).concat(Cb,": ").concat(d,`px;
  }
`)},Tm=function(){var e=parseInt(document.body.getAttribute(Wo)||"0",10);return isFinite(e)?e:0},Hb=function(){w.useEffect(function(){return document.body.setAttribute(Wo,(Tm()+1).toString()),function(){var e=Tm()-1;e<=0?document.body.removeAttribute(Wo):document.body.setAttribute(Wo,e.toString())}},[])},Bb=function(e){var r=e.noRelative,o=e.noImportant,i=e.gapMode,a=i===void 0?"margin":i;Hb();var l=w.useMemo(function(){return zb(a)},[a]);return w.createElement($b,{styles:Ub(l,!r,a,o?"":"!important")})},Jc=!1;if(typeof window<"u")try{var Aa=Object.defineProperty({},"passive",{get:function(){return Jc=!0,!0}});window.addEventListener("test",Aa,Aa),window.removeEventListener("test",Aa,Aa)}catch{Jc=!1}var $o=Jc?{passive:!1}:!1,Vb=function(e){return e.tagName==="TEXTAREA"},Rv=function(e,r){if(!(e instanceof Element))return!1;var o=window.getComputedStyle(e);return o[r]!=="hidden"&&!(o.overflowY===o.overflowX&&!Vb(e)&&o[r]==="visible")},Wb=function(e){return Rv(e,"overflowY")},Kb=function(e){return Rv(e,"overflowX")},_m=function(e,r){var o=r.ownerDocument,i=r;do{typeof ShadowRoot<"u"&&i instanceof ShadowRoot&&(i=i.host);var a=Ov(e,i);if(a){var l=Lv(e,i),c=l[1],d=l[2];if(c>d)return!0}i=i.parentNode}while(i&&i!==o.body);return!1},Qb=function(e){var r=e.scrollTop,o=e.scrollHeight,i=e.clientHeight;return[r,o,i]},Gb=function(e){var r=e.scrollLeft,o=e.scrollWidth,i=e.clientWidth;return[r,o,i]},Ov=function(e,r){return e==="v"?Wb(r):Kb(r)},Lv=function(e,r){return e==="v"?Qb(r):Gb(r)},Yb=function(e,r){return e==="h"&&r==="rtl"?-1:1},Jb=function(e,r,o,i,a){var l=Yb(e,window.getComputedStyle(r).direction),c=l*i,d=o.target,h=r.contains(d),p=!1,g=c>0,v=0,x=0;do{if(!d)break;var S=Lv(e,d),C=S[0],E=S[1],b=S[2],P=E-b-l*C;(C||P)&&Ov(e,d)&&(v+=P,x+=C);var D=d.parentNode;d=D&&D.nodeType===Node.DOCUMENT_FRAGMENT_NODE?D.host:D}while(!h&&d!==document.body||h&&(r.contains(d)||r===d));return(g&&Math.abs(v)<1||!g&&Math.abs(x)<1)&&(p=!0),p},Ia=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Nm=function(e){return[e.deltaX,e.deltaY]},Dm=function(e){return e&&"current"in e?e.current:e},Xb=function(e,r){return e[0]===r[0]&&e[1]===r[1]},qb=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Zb=0,Uo=[];function eP(e){var r=w.useRef([]),o=w.useRef([0,0]),i=w.useRef(),a=w.useState(Zb++)[0],l=w.useState(Pv)[0],c=w.useRef(e);w.useEffect(function(){c.current=e},[e]),w.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var E=Sb([e.lockRef.current],(e.shards||[]).map(Dm),!0).filter(Boolean);return E.forEach(function(b){return b.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),E.forEach(function(b){return b.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var d=w.useCallback(function(E,b){if("touches"in E&&E.touches.length===2||E.type==="wheel"&&E.ctrlKey)return!c.current.allowPinchZoom;var P=Ia(E),D=o.current,N="deltaX"in E?E.deltaX:D[0]-P[0],j="deltaY"in E?E.deltaY:D[1]-P[1],H,M=E.target,K=Math.abs(N)>Math.abs(j)?"h":"v";if("touches"in E&&K==="h"&&M.type==="range")return!1;var W=_m(K,M);if(!W)return!0;if(W?H=K:(H=K==="v"?"h":"v",W=_m(K,M)),!W)return!1;if(!i.current&&"changedTouches"in E&&(N||j)&&(i.current=H),!H)return!0;var Z=i.current||H;return Jb(Z,b,E,Z==="h"?N:j)},[]),h=w.useCallback(function(E){var b=E;if(!(!Uo.length||Uo[Uo.length-1]!==l)){var P="deltaY"in b?Nm(b):Ia(b),D=r.current.filter(function(H){return H.name===b.type&&(H.target===b.target||b.target===H.shadowParent)&&Xb(H.delta,P)})[0];if(D&&D.should){b.cancelable&&b.preventDefault();return}if(!D){var N=(c.current.shards||[]).map(Dm).filter(Boolean).filter(function(H){return H.contains(b.target)}),j=N.length>0?d(b,N[0]):!c.current.noIsolation;j&&b.cancelable&&b.preventDefault()}}},[]),p=w.useCallback(function(E,b,P,D){var N={name:E,delta:b,target:P,should:D,shadowParent:tP(P)};r.current.push(N),setTimeout(function(){r.current=r.current.filter(function(j){return j!==N})},1)},[]),g=w.useCallback(function(E){o.current=Ia(E),i.current=void 0},[]),v=w.useCallback(function(E){p(E.type,Nm(E),E.target,d(E,e.lockRef.current))},[]),x=w.useCallback(function(E){p(E.type,Ia(E),E.target,d(E,e.lockRef.current))},[]);w.useEffect(function(){return Uo.push(l),e.setCallbacks({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:x}),document.addEventListener("wheel",h,$o),document.addEventListener("touchmove",h,$o),document.addEventListener("touchstart",g,$o),function(){Uo=Uo.filter(function(E){return E!==l}),document.removeEventListener("wheel",h,$o),document.removeEventListener("touchmove",h,$o),document.removeEventListener("touchstart",g,$o)}},[]);var S=e.removeScrollBar,C=e.inert;return w.createElement(w.Fragment,null,C?w.createElement(l,{styles:qb(a)}):null,S?w.createElement(Bb,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function tP(e){for(var r=null;e!==null;)e instanceof ShadowRoot&&(r=e.host,e=e.host),e=e.parentNode;return r}const nP=Mb(bv,eP);var Mv=w.forwardRef(function(e,r){return w.createElement(yl,Mn({},e,{ref:r,sideCar:nP}))});Mv.classNames=yl.classNames;var Xc=["Enter"," "],rP=["ArrowDown","PageUp","Home"],Tv=["ArrowUp","PageDown","End"],oP=[...rP,...Tv],iP={ltr:[...Xc,"ArrowRight"],rtl:[...Xc,"ArrowLeft"]},sP={ltr:["ArrowLeft"],rtl:["ArrowRight"]},hs="Menu",[os,aP,lP]=$g(hs),[oo,_v]=us(hs,[lP,sv,vv]),wl=sv(),Nv=vv(),[uP,io]=oo(hs),[cP,ps]=oo(hs),Dv=e=>{const{__scopeMenu:r,open:o=!1,children:i,dir:a,onOpenChange:l,modal:c=!0}=e,d=wl(r),[h,p]=w.useState(null),g=w.useRef(!1),v=Yn(l),x=Ug(a);return w.useEffect(()=>{const S=()=>{g.current=!0,document.addEventListener("pointerdown",C,{capture:!0,once:!0}),document.addEventListener("pointermove",C,{capture:!0,once:!0})},C=()=>g.current=!1;return document.addEventListener("keydown",S,{capture:!0}),()=>{document.removeEventListener("keydown",S,{capture:!0}),document.removeEventListener("pointerdown",C,{capture:!0}),document.removeEventListener("pointermove",C,{capture:!0})}},[]),F.jsx(Xk,{...d,children:F.jsx(uP,{scope:r,open:o,onOpenChange:v,content:h,onContentChange:p,children:F.jsx(cP,{scope:r,onClose:w.useCallback(()=>v(!1),[v]),isUsingKeyboardRef:g,dir:x,modal:c,children:i})})})};Dv.displayName=hs;var dP="MenuAnchor",Ed=w.forwardRef((e,r)=>{const{__scopeMenu:o,...i}=e,a=wl(o);return F.jsx(qk,{...a,...i,ref:r})});Ed.displayName=dP;var Cd="MenuPortal",[fP,Av]=oo(Cd,{forceMount:void 0}),Iv=e=>{const{__scopeMenu:r,forceMount:o,children:i,container:a}=e,l=io(Cd,r);return F.jsx(fP,{scope:r,forceMount:o,children:F.jsx(ds,{present:o||l.open,children:F.jsx(mv,{asChild:!0,container:a,children:i})})})};Iv.displayName=Cd;var sn="MenuContent",[hP,kd]=oo(sn),Fv=w.forwardRef((e,r)=>{const o=Av(sn,e.__scopeMenu),{forceMount:i=o.forceMount,...a}=e,l=io(sn,e.__scopeMenu),c=ps(sn,e.__scopeMenu);return F.jsx(os.Provider,{scope:e.__scopeMenu,children:F.jsx(ds,{present:i||l.open,children:F.jsx(os.Slot,{scope:e.__scopeMenu,children:c.modal?F.jsx(pP,{...a,ref:r}):F.jsx(mP,{...a,ref:r})})})})}),pP=w.forwardRef((e,r)=>{const o=io(sn,e.__scopeMenu),i=w.useRef(null),a=$t(r,i);return w.useEffect(()=>{const l=i.current;if(l)return xb(l)},[]),F.jsx(bd,{...e,ref:a,trapFocus:o.open,disableOutsidePointerEvents:o.open,disableOutsideScroll:!0,onFocusOutside:je(e.onFocusOutside,l=>l.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>o.onOpenChange(!1)})}),mP=w.forwardRef((e,r)=>{const o=io(sn,e.__scopeMenu);return F.jsx(bd,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>o.onOpenChange(!1)})}),gP=ts("MenuContent.ScrollLock"),bd=w.forwardRef((e,r)=>{const{__scopeMenu:o,loop:i=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:h,onEscapeKeyDown:p,onPointerDownOutside:g,onFocusOutside:v,onInteractOutside:x,onDismiss:S,disableOutsideScroll:C,...E}=e,b=io(sn,o),P=ps(sn,o),D=wl(o),N=Nv(o),j=aP(o),[H,M]=w.useState(null),K=w.useRef(null),W=$t(r,K,b.onContentChange),Z=w.useRef(0),se=w.useRef(""),Me=w.useRef(0),Re=w.useRef(null),ne=w.useRef("right"),re=w.useRef(0),be=C?Mv:w.Fragment,X=C?{as:gP,allowPinchZoom:!0}:void 0,ie=V=>{const Q=se.current+V,O=j().filter(ue=>!ue.disabled),B=document.activeElement,oe=O.find(ue=>ue.ref.current===B)?.textValue,le=O.map(ue=>ue.textValue),Oe=OP(le,Q,oe),Pe=O.find(ue=>ue.textValue===Oe)?.ref.current;(function ue(_e){se.current=_e,window.clearTimeout(Z.current),_e!==""&&(Z.current=window.setTimeout(()=>ue(""),1e3))})(Q),Pe&&setTimeout(()=>Pe.focus())};w.useEffect(()=>()=>window.clearTimeout(Z.current),[]),EC();const _=w.useCallback(V=>ne.current===Re.current?.side&&MP(V,Re.current?.area),[]);return F.jsx(hP,{scope:o,searchRef:se,onItemEnter:w.useCallback(V=>{_(V)&&V.preventDefault()},[_]),onItemLeave:w.useCallback(V=>{_(V)||(K.current?.focus(),M(null))},[_]),onTriggerLeave:w.useCallback(V=>{_(V)&&V.preventDefault()},[_]),pointerGraceTimerRef:Me,onPointerGraceIntentChange:w.useCallback(V=>{Re.current=V},[]),children:F.jsx(be,{...X,children:F.jsx(Wg,{asChild:!0,trapped:a,onMountAutoFocus:je(l,V=>{V.preventDefault(),K.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:F.jsx(Bg,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:p,onPointerDownOutside:g,onFocusOutside:v,onInteractOutside:x,onDismiss:S,children:F.jsx(mb,{asChild:!0,...N,dir:P.dir,orientation:"vertical",loop:i,currentTabStopId:H,onCurrentTabStopIdChange:M,onEntryFocus:je(h,V=>{P.isUsingKeyboardRef.current||V.preventDefault()}),preventScrollOnEntryFocus:!0,children:F.jsx(Zk,{role:"menu","aria-orientation":"vertical","data-state":Zv(b.open),"data-radix-menu-content":"",dir:P.dir,...D,...E,ref:W,style:{outline:"none",...E.style},onKeyDown:je(E.onKeyDown,V=>{const O=V.target.closest("[data-radix-menu-content]")===V.currentTarget,B=V.ctrlKey||V.altKey||V.metaKey,oe=V.key.length===1;O&&(V.key==="Tab"&&V.preventDefault(),!B&&oe&&ie(V.key));const le=K.current;if(V.target!==le||!oP.includes(V.key))return;V.preventDefault();const Pe=j().filter(ue=>!ue.disabled).map(ue=>ue.ref.current);Tv.includes(V.key)&&Pe.reverse(),PP(Pe)}),onBlur:je(e.onBlur,V=>{V.currentTarget.contains(V.target)||(window.clearTimeout(Z.current),se.current="")}),onPointerMove:je(e.onPointerMove,is(V=>{const Q=V.target,O=re.current!==V.clientX;if(V.currentTarget.contains(Q)&&O){const B=V.clientX>re.current?"right":"left";ne.current=B,re.current=V.clientX}}))})})})})})})});Fv.displayName=sn;var vP="MenuGroup",Pd=w.forwardRef((e,r)=>{const{__scopeMenu:o,...i}=e;return F.jsx(Ot.div,{role:"group",...i,ref:r})});Pd.displayName=vP;var yP="MenuLabel",jv=w.forwardRef((e,r)=>{const{__scopeMenu:o,...i}=e;return F.jsx(Ot.div,{...i,ref:r})});jv.displayName=yP;var rl="MenuItem",Am="menu.itemSelect",xl=w.forwardRef((e,r)=>{const{disabled:o=!1,onSelect:i,...a}=e,l=w.useRef(null),c=ps(rl,e.__scopeMenu),d=kd(rl,e.__scopeMenu),h=$t(r,l),p=w.useRef(!1),g=()=>{const v=l.current;if(!o&&v){const x=new CustomEvent(Am,{bubbles:!0,cancelable:!0});v.addEventListener(Am,S=>i?.(S),{once:!0}),zg(v,x),x.defaultPrevented?p.current=!1:c.onClose()}};return F.jsx(zv,{...a,ref:h,disabled:o,onClick:je(e.onClick,g),onPointerDown:v=>{e.onPointerDown?.(v),p.current=!0},onPointerUp:je(e.onPointerUp,v=>{p.current||v.currentTarget?.click()}),onKeyDown:je(e.onKeyDown,v=>{const x=d.searchRef.current!=="";o||x&&v.key===" "||Xc.includes(v.key)&&(v.currentTarget.click(),v.preventDefault())})})});xl.displayName=rl;var zv=w.forwardRef((e,r)=>{const{__scopeMenu:o,disabled:i=!1,textValue:a,...l}=e,c=kd(rl,o),d=Nv(o),h=w.useRef(null),p=$t(r,h),[g,v]=w.useState(!1),[x,S]=w.useState("");return w.useEffect(()=>{const C=h.current;C&&S((C.textContent??"").trim())},[l.children]),F.jsx(os.ItemSlot,{scope:o,disabled:i,textValue:a??x,children:F.jsx(gb,{asChild:!0,...d,focusable:!i,children:F.jsx(Ot.div,{role:"menuitem","data-highlighted":g?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...l,ref:p,onPointerMove:je(e.onPointerMove,is(C=>{i?c.onItemLeave(C):(c.onItemEnter(C),C.defaultPrevented||C.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:je(e.onPointerLeave,is(C=>c.onItemLeave(C))),onFocus:je(e.onFocus,()=>v(!0)),onBlur:je(e.onBlur,()=>v(!1))})})})}),wP="MenuCheckboxItem",$v=w.forwardRef((e,r)=>{const{checked:o=!1,onCheckedChange:i,...a}=e;return F.jsx(Wv,{scope:e.__scopeMenu,checked:o,children:F.jsx(xl,{role:"menuitemcheckbox","aria-checked":ol(o)?"mixed":o,...a,ref:r,"data-state":Od(o),onSelect:je(a.onSelect,()=>i?.(ol(o)?!0:!o),{checkForDefaultPrevented:!1})})})});$v.displayName=wP;var Uv="MenuRadioGroup",[xP,SP]=oo(Uv,{value:void 0,onValueChange:()=>{}}),Hv=w.forwardRef((e,r)=>{const{value:o,onValueChange:i,...a}=e,l=Yn(i);return F.jsx(xP,{scope:e.__scopeMenu,value:o,onValueChange:l,children:F.jsx(Pd,{...a,ref:r})})});Hv.displayName=Uv;var Bv="MenuRadioItem",Vv=w.forwardRef((e,r)=>{const{value:o,...i}=e,a=SP(Bv,e.__scopeMenu),l=o===a.value;return F.jsx(Wv,{scope:e.__scopeMenu,checked:l,children:F.jsx(xl,{role:"menuitemradio","aria-checked":l,...i,ref:r,"data-state":Od(l),onSelect:je(i.onSelect,()=>a.onValueChange?.(o),{checkForDefaultPrevented:!1})})})});Vv.displayName=Bv;var Rd="MenuItemIndicator",[Wv,EP]=oo(Rd,{checked:!1}),Kv=w.forwardRef((e,r)=>{const{__scopeMenu:o,forceMount:i,...a}=e,l=EP(Rd,o);return F.jsx(ds,{present:i||ol(l.checked)||l.checked===!0,children:F.jsx(Ot.span,{...a,ref:r,"data-state":Od(l.checked)})})});Kv.displayName=Rd;var CP="MenuSeparator",Qv=w.forwardRef((e,r)=>{const{__scopeMenu:o,...i}=e;return F.jsx(Ot.div,{role:"separator","aria-orientation":"horizontal",...i,ref:r})});Qv.displayName=CP;var kP="MenuArrow",Gv=w.forwardRef((e,r)=>{const{__scopeMenu:o,...i}=e,a=wl(o);return F.jsx(eb,{...a,...i,ref:r})});Gv.displayName=kP;var bP="MenuSub",[aO,Yv]=oo(bP),Ki="MenuSubTrigger",Jv=w.forwardRef((e,r)=>{const o=io(Ki,e.__scopeMenu),i=ps(Ki,e.__scopeMenu),a=Yv(Ki,e.__scopeMenu),l=kd(Ki,e.__scopeMenu),c=w.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:h}=l,p={__scopeMenu:e.__scopeMenu},g=w.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return w.useEffect(()=>g,[g]),w.useEffect(()=>{const v=d.current;return()=>{window.clearTimeout(v),h(null)}},[d,h]),F.jsx(Ed,{asChild:!0,...p,children:F.jsx(zv,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":o.open,"aria-controls":a.contentId,"data-state":Zv(o.open),...e,ref:pl(r,a.onTriggerChange),onClick:v=>{e.onClick?.(v),!(e.disabled||v.defaultPrevented)&&(v.currentTarget.focus(),o.open||o.onOpenChange(!0))},onPointerMove:je(e.onPointerMove,is(v=>{l.onItemEnter(v),!v.defaultPrevented&&!e.disabled&&!o.open&&!c.current&&(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{o.onOpenChange(!0),g()},100))})),onPointerLeave:je(e.onPointerLeave,is(v=>{g();const x=o.content?.getBoundingClientRect();if(x){const S=o.content?.dataset.side,C=S==="right",E=C?-5:5,b=x[C?"left":"right"],P=x[C?"right":"left"];l.onPointerGraceIntentChange({area:[{x:v.clientX+E,y:v.clientY},{x:b,y:x.top},{x:P,y:x.top},{x:P,y:x.bottom},{x:b,y:x.bottom}],side:S}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(v),v.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:je(e.onKeyDown,v=>{const x=l.searchRef.current!=="";e.disabled||x&&v.key===" "||iP[i.dir].includes(v.key)&&(o.onOpenChange(!0),o.content?.focus(),v.preventDefault())})})})});Jv.displayName=Ki;var Xv="MenuSubContent",qv=w.forwardRef((e,r)=>{const o=Av(sn,e.__scopeMenu),{forceMount:i=o.forceMount,...a}=e,l=io(sn,e.__scopeMenu),c=ps(sn,e.__scopeMenu),d=Yv(Xv,e.__scopeMenu),h=w.useRef(null),p=$t(r,h);return F.jsx(os.Provider,{scope:e.__scopeMenu,children:F.jsx(ds,{present:i||l.open,children:F.jsx(os.Slot,{scope:e.__scopeMenu,children:F.jsx(bd,{id:d.contentId,"aria-labelledby":d.triggerId,...a,ref:p,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:g=>{c.isUsingKeyboardRef.current&&h.current?.focus(),g.preventDefault()},onCloseAutoFocus:g=>g.preventDefault(),onFocusOutside:je(e.onFocusOutside,g=>{g.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:je(e.onEscapeKeyDown,g=>{c.onClose(),g.preventDefault()}),onKeyDown:je(e.onKeyDown,g=>{const v=g.currentTarget.contains(g.target),x=sP[c.dir].includes(g.key);v&&x&&(l.onOpenChange(!1),d.trigger?.focus(),g.preventDefault())})})})})})});qv.displayName=Xv;function Zv(e){return e?"open":"closed"}function ol(e){return e==="indeterminate"}function Od(e){return ol(e)?"indeterminate":e?"checked":"unchecked"}function PP(e){const r=document.activeElement;for(const o of e)if(o===r||(o.focus(),document.activeElement!==r))return}function RP(e,r){return e.map((o,i)=>e[(r+i)%e.length])}function OP(e,r,o){const a=r.length>1&&Array.from(r).every(p=>p===r[0])?r[0]:r,l=o?e.indexOf(o):-1;let c=RP(e,Math.max(l,0));a.length===1&&(c=c.filter(p=>p!==o));const h=c.find(p=>p.toLowerCase().startsWith(a.toLowerCase()));return h!==o?h:void 0}function LP(e,r){const{x:o,y:i}=e;let a=!1;for(let l=0,c=r.length-1;l<r.length;c=l++){const d=r[l],h=r[c],p=d.x,g=d.y,v=h.x,x=h.y;g>i!=x>i&&o<(v-p)*(i-g)/(x-g)+p&&(a=!a)}return a}function MP(e,r){if(!r)return!1;const o={x:e.clientX,y:e.clientY};return LP(o,r)}function is(e){return r=>r.pointerType==="mouse"?e(r):void 0}var TP=Dv,_P=Ed,NP=Iv,DP=Fv,AP=Pd,IP=jv,FP=xl,jP=$v,zP=Hv,$P=Vv,UP=Kv,HP=Qv,BP=Gv,VP=Jv,WP=qv,Sl="DropdownMenu",[KP,lO]=us(Sl,[_v]),Lt=_v(),[QP,ey]=KP(Sl),ty=e=>{const{__scopeDropdownMenu:r,children:o,dir:i,open:a,defaultOpen:l,onOpenChange:c,modal:d=!0}=e,h=Lt(r),p=w.useRef(null),[g,v]=jg({prop:a,defaultProp:l??!1,onChange:c,caller:Sl});return F.jsx(QP,{scope:r,triggerId:Wc(),triggerRef:p,contentId:Wc(),open:g,onOpenChange:v,onOpenToggle:w.useCallback(()=>v(x=>!x),[v]),modal:d,children:F.jsx(TP,{...h,open:g,onOpenChange:v,dir:i,modal:d,children:o})})};ty.displayName=Sl;var ny="DropdownMenuTrigger",ry=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,disabled:i=!1,...a}=e,l=ey(ny,o),c=Lt(o);return F.jsx(_P,{asChild:!0,...c,children:F.jsx(Ot.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...a,ref:pl(r,l.triggerRef),onPointerDown:je(e.onPointerDown,d=>{!i&&d.button===0&&d.ctrlKey===!1&&(l.onOpenToggle(),l.open||d.preventDefault())}),onKeyDown:je(e.onKeyDown,d=>{i||(["Enter"," "].includes(d.key)&&l.onOpenToggle(),d.key==="ArrowDown"&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(d.key)&&d.preventDefault())})})})});ry.displayName=ny;var GP="DropdownMenuPortal",oy=e=>{const{__scopeDropdownMenu:r,...o}=e,i=Lt(r);return F.jsx(NP,{...i,...o})};oy.displayName=GP;var iy="DropdownMenuContent",sy=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=ey(iy,o),l=Lt(o),c=w.useRef(!1);return F.jsx(DP,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:r,onCloseAutoFocus:je(e.onCloseAutoFocus,d=>{c.current||a.triggerRef.current?.focus(),c.current=!1,d.preventDefault()}),onInteractOutside:je(e.onInteractOutside,d=>{const h=d.detail.originalEvent,p=h.button===0&&h.ctrlKey===!0,g=h.button===2||p;(!a.modal||g)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});sy.displayName=iy;var YP="DropdownMenuGroup",JP=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(AP,{...a,...i,ref:r})});JP.displayName=YP;var XP="DropdownMenuLabel",qP=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(IP,{...a,...i,ref:r})});qP.displayName=XP;var ZP="DropdownMenuItem",ay=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(FP,{...a,...i,ref:r})});ay.displayName=ZP;var eR="DropdownMenuCheckboxItem",tR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(jP,{...a,...i,ref:r})});tR.displayName=eR;var nR="DropdownMenuRadioGroup",rR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(zP,{...a,...i,ref:r})});rR.displayName=nR;var oR="DropdownMenuRadioItem",iR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx($P,{...a,...i,ref:r})});iR.displayName=oR;var sR="DropdownMenuItemIndicator",aR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(UP,{...a,...i,ref:r})});aR.displayName=sR;var lR="DropdownMenuSeparator",ly=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(HP,{...a,...i,ref:r})});ly.displayName=lR;var uR="DropdownMenuArrow",cR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(BP,{...a,...i,ref:r})});cR.displayName=uR;var dR="DropdownMenuSubTrigger",fR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(VP,{...a,...i,ref:r})});fR.displayName=dR;var hR="DropdownMenuSubContent",pR=w.forwardRef((e,r)=>{const{__scopeDropdownMenu:o,...i}=e,a=Lt(o);return F.jsx(WP,{...a,...i,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});pR.displayName=hR;var mR=ty,gR=ry,vR=oy,yR=sy,wR=ay,xR=ly;function SR({...e}){return F.jsx(mR,{"data-slot":"dropdown-menu",...e})}function ER({...e}){return F.jsx(gR,{"data-slot":"dropdown-menu-trigger",...e})}function CR({className:e,sideOffset:r=4,...o}){return F.jsx(vR,{children:F.jsx(yR,{"data-slot":"dropdown-menu-content",sideOffset:r,className:Ko("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...o})})}function Rc({className:e,inset:r,variant:o="default",...i}){return F.jsx(wR,{"data-slot":"dropdown-menu-item","data-inset":r,"data-variant":o,className:Ko("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function kR({className:e,...r}){return F.jsx(xR,{"data-slot":"dropdown-menu-separator",className:Ko("bg-border -mx-1 my-1 h-px",e),...r})}const bR=()=>F.jsx(cd,{className:"flex items-center text-white",to:Xe.home.getHref(),children:F.jsxs("div",{className:"flex items-center space-x-2",children:[F.jsx("div",{className:"w-8 h-8 bg-white rounded-lg flex items-center justify-center",children:F.jsx("span",{className:"text-blue-600 font-bold text-lg",children:"W"})}),F.jsx("span",{className:"text-sm font-semibold text-white",children:"WorkFinder"})]})}),PR=()=>{const{state:e,location:r}=JS(),[o,i]=w.useState(0);return w.useEffect(()=>{i(0)},[r?.pathname]),w.useEffect(()=>{if(e==="loading"){const a=setInterval(()=>{i(l=>{if(l===100)return clearInterval(a),100;const c=l+10;return c>100?100:c})},300);return()=>{clearInterval(a)}}},[e]),e!=="loading"?null:F.jsx("div",{className:"fixed left-0 top-0 h-1 bg-blue-500 transition-all duration-200 ease-in-out z-50",style:{width:`${o}%`}})};function RR({children:e}){const r=dl(),{logout:o}=dd(),i=()=>{o(),r(Xe.auth.login.getHref())},a=[{name:"Dashboard",to:Xe.app.dashboard.getHref(),icon:p1},{name:"Jobs",to:Xe.app.jobs.getHref(),icon:s1},{name:"Companies",to:Xe.app.companies.getHref(),icon:l1},{name:"Saved Jobs",to:Xe.app.savedJobs.getHref(),icon:f1},{name:"Applications",to:Xe.app.applications.getHref(),icon:c1}];return F.jsxs("div",{className:"flex min-h-screen w-full flex-col bg-muted/40",children:[F.jsx(PR,{}),F.jsx("aside",{className:"fixed inset-y-0 left-0 z-10 hidden w-60 flex-col border-r bg-gray-900 sm:flex",children:F.jsxs("nav",{className:"flex flex-col items-center gap-4 px-2 py-4",children:[F.jsx("div",{className:"flex h-16 shrink-0 items-center px-4",children:F.jsx(bR,{})}),a.map(l=>F.jsxs(Cg,{to:l.to,end:l.name==="Dashboard",className:({isActive:c})=>Ko("text-gray-300 hover:bg-gray-700 hover:text-white","group flex flex-1 w-full items-center rounded-md p-2 text-base font-medium",c&&"bg-gray-800 text-white"),children:[F.jsx(l.icon,{className:Ko("text-gray-400 group-hover:text-gray-300","mr-4 size-6 shrink-0"),"aria-hidden":"true"}),l.name]},l.name))]})}),F.jsxs("div",{className:"flex flex-col sm:gap-4 sm:py-4 sm:pl-60",children:[F.jsxs("header",{className:"sticky top-0 z-30 flex h-14 items-center justify-between gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:justify-end sm:border-0 sm:bg-transparent sm:px-6",children:[F.jsxs(fm,{size:"icon",variant:"outline",className:"sm:hidden",children:[F.jsx(g1,{className:"size-5"}),F.jsx("span",{className:"sr-only",children:"Toggle Menu"})]}),F.jsxs(SR,{children:[F.jsx(ER,{asChild:!0,children:F.jsxs(fm,{variant:"outline",size:"icon",className:"overflow-hidden rounded-full",children:[F.jsx("span",{className:"sr-only",children:"Open user menu"}),F.jsx(om,{className:"size-6 rounded-full"})]})}),F.jsxs(CR,{align:"end",children:[F.jsxs(Rc,{onClick:()=>r(Xe.app.profile.getHref()),className:"cursor-pointer",children:[F.jsx(om,{className:"mr-2 h-4 w-4"}),"Profile"]}),F.jsxs(Rc,{onClick:()=>r(Xe.app.settings.getHref()),className:"cursor-pointer",children:[F.jsx(y1,{className:"mr-2 h-4 w-4"}),"Settings"]}),F.jsx(kR,{}),F.jsx(Rc,{onClick:i,className:"cursor-pointer text-red-600",children:"Sign Out"})]})]})]}),F.jsx("main",{className:"grid flex-1 items-start gap-4 p-4 sm:px-6 sm:py-0 md:gap-8",children:e})]})]})}function OR(){return F.jsx(RR,{children:F.jsx(sE,{})})}function LR(){return F.jsx("div",{className:"min-h-screen flex items-center justify-center",children:F.jsxs("div",{className:"text-center",children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Something went wrong"}),F.jsx("p",{className:"text-gray-600 mb-4",children:"We're sorry, but something went wrong. Please try again later."}),F.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Reload page"})]})})}const kt=e=>r=>{const{clientLoader:o,clientAction:i,default:a,...l}=r;return{...l,loader:o?.(e),action:i?.(e),Component:a}},MR=e=>TE([{path:Xe.auth.register.path,lazy:()=>Ct(()=>import("./register-CA28aslY.js"),__vite__mapDeps([0,1])).then(kt(e))},{path:Xe.auth.login.path,lazy:()=>Ct(()=>import("./login-BHX2pz7i.js"),__vite__mapDeps([2,1,3])).then(kt(e))},{path:"/",lazy:()=>Ct(()=>import("./app-layout-CXDDf663.js"),__vite__mapDeps([4,3,5,6])).then(kt(e)),children:[{path:Xe.home.path,lazy:()=>Ct(()=>import("./landing-BKHutIY8.js"),__vite__mapDeps([7,8,5,9,10,11,12,13,14,15,16,17,18])).then(kt(e))},{path:Xe.jobs.root.path,lazy:()=>Ct(()=>import("./jobs-DUschxiO.js"),__vite__mapDeps([19,11,8,5,12,20,21,6,9,16,14,17,15])).then(kt(e))},{path:Xe.jobs.detail.path,lazy:()=>Ct(()=>import("./job-detail-Dwtoevrd.js"),__vite__mapDeps([22,11,12,20,23,6,24,9,16,17,14,25,26,27,28])).then(kt(e))},{path:Xe.companies.root.path,lazy:()=>Ct(()=>import("./companies-DisZcvyu.js"),__vite__mapDeps([29,11,8,5,12,18,13,17,9,21])).then(kt(e))},{path:Xe.companies.detail.path,lazy:()=>Ct(()=>import("./company-detail-CNMwiuYK.js"),__vite__mapDeps([30,11,12,31,10,13,14,9,15,16,17,24,26,28])).then(kt(e))},{path:Xe.about.path,lazy:()=>Ct(()=>import("./about-D4UVXCQu.js"),__vite__mapDeps([32,11,33])).then(kt(e))},{path:Xe.contact.path,lazy:()=>Ct(()=>import("./contact-1_D-2N_s.js"),__vite__mapDeps([34,11,33])).then(kt(e))},{path:Xe.privacy.path,lazy:()=>Ct(()=>import("./privacy-my1FivdW.js"),__vite__mapDeps([35,11,33])).then(kt(e))},{path:Xe.terms.path,lazy:()=>Ct(()=>import("./terms-bmwGTExJ.js"),__vite__mapDeps([36,11,33])).then(kt(e))},{path:Xe.app.root.path,element:F.jsx(ZE,{children:F.jsx(OR,{})}),ErrorBoundary:LR,children:[{path:Xe.app.dashboard.path,lazy:()=>Ct(()=>import("./dashboard-FQr6FgnI.js"),[]).then(kt(e))},{path:Xe.app.applications.path,lazy:()=>Ct(()=>import("./applications-BjDiF06n.js"),__vite__mapDeps([37,11,12,31,23,6,16,17,27,26,28,38])).then(kt(e))},{path:Xe.app.savedJobs.path,lazy:()=>Ct(()=>import("./saved-jobs-k6vquDU0.js"),__vite__mapDeps([39,11,12,23,6,14,16,28,9,15,25,38])).then(kt(e))}]}]},{path:"*",lazy:()=>Ct(()=>import("./not-found-DEnjBj40.js"),[]).then(kt(e))}]),TR=()=>{const e=_x(),r=w.useMemo(()=>MR(e),[e]);return F.jsx(BE,{router:r})};function _R(){return F.jsx(TR,{})}const NR=w.createContext(null),Oc={didCatch:!1,error:null};class DR extends w.Component{constructor(r){super(r),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=Oc}static getDerivedStateFromError(r){return{didCatch:!0,error:r}}resetErrorBoundary(){const{error:r}=this.state;if(r!==null){for(var o,i,a=arguments.length,l=new Array(a),c=0;c<a;c++)l[c]=arguments[c];(o=(i=this.props).onReset)===null||o===void 0||o.call(i,{args:l,reason:"imperative-api"}),this.setState(Oc)}}componentDidCatch(r,o){var i,a;(i=(a=this.props).onError)===null||i===void 0||i.call(a,r,o)}componentDidUpdate(r,o){const{didCatch:i}=this.state,{resetKeys:a}=this.props;if(i&&o.error!==null&&AR(r.resetKeys,a)){var l,c;(l=(c=this.props).onReset)===null||l===void 0||l.call(c,{next:a,prev:r.resetKeys,reason:"keys"}),this.setState(Oc)}}render(){const{children:r,fallbackRender:o,FallbackComponent:i,fallback:a}=this.props,{didCatch:l,error:c}=this.state;let d=r;if(l){const h={error:c,resetErrorBoundary:this.resetErrorBoundary};if(typeof o=="function")d=o(h);else if(i)d=w.createElement(i,h);else if(a!==void 0)d=a;else throw c}return w.createElement(NR.Provider,{value:{didCatch:l,error:c,resetErrorBoundary:this.resetErrorBoundary}},d)}}function AR(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];return e.length!==r.length||e.some((o,i)=>!Object.is(o,r[i]))}var uy=(e=>(e.BASE="base",e.BODY="body",e.HEAD="head",e.HTML="html",e.LINK="link",e.META="meta",e.NOSCRIPT="noscript",e.SCRIPT="script",e.STYLE="style",e.TITLE="title",e.FRAGMENT="Symbol(react.fragment)",e))(uy||{}),Lc={link:{rel:["amphtml","canonical","alternate"]},script:{type:["application/ld+json"]},meta:{charset:"",name:["generator","robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]}};Object.values(uy);var Ld={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"};Object.entries(Ld).reduce((e,[r,o])=>(e[o]=r,e),{});var ss="data-rh",IR=e=>Array.isArray(e)?e.join(""):e,FR=(e,r)=>{const o=Object.keys(e);for(let i=0;i<o.length;i+=1)if(r[o[i]]&&r[o[i]].includes(e[o[i]]))return!0;return!1},Mc=(e,r)=>Array.isArray(e)?e.reduce((o,i)=>(FR(i,r)?o.priority.push(i):o.default.push(i),o),{priority:[],default:[]}):{default:e,priority:[]},jR=["noscript","script","style"],qc=(e,r=!0)=>r===!1?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;"),cy=e=>Object.keys(e).reduce((r,o)=>{const i=typeof e[o]<"u"?`${o}="${e[o]}"`:`${o}`;return r?`${r} ${i}`:i},""),zR=(e,r,o,i)=>{const a=cy(o),l=IR(r);return a?`<${e} ${ss}="true" ${a}>${qc(l,i)}</${e}>`:`<${e} ${ss}="true">${qc(l,i)}</${e}>`},$R=(e,r,o=!0)=>r.reduce((i,a)=>{const l=a,c=Object.keys(l).filter(p=>!(p==="innerHTML"||p==="cssText")).reduce((p,g)=>{const v=typeof l[g]>"u"?g:`${g}="${qc(l[g],o)}"`;return p?`${p} ${v}`:v},""),d=l.innerHTML||l.cssText||"",h=jR.indexOf(e)===-1;return`${i}<${e} ${ss}="true" ${c}${h?"/>":`>${d}</${e}>`}`},""),dy=(e,r={})=>Object.keys(e).reduce((o,i)=>{const a=Ld[i];return o[a||i]=e[i],o},r),UR=(e,r,o)=>{const i={key:r,[ss]:!0},a=dy(o,i);return[jt.createElement("title",a,r)]},Wa=(e,r)=>r.map((o,i)=>{const a={key:i,[ss]:!0};return Object.keys(o).forEach(l=>{const d=Ld[l]||l;if(d==="innerHTML"||d==="cssText"){const h=o.innerHTML||o.cssText;a.dangerouslySetInnerHTML={__html:h}}else a[d]=o[l]}),jt.createElement(e,a)}),rn=(e,r,o=!0)=>{switch(e){case"title":return{toComponent:()=>UR(e,r.title,r.titleAttributes),toString:()=>zR(e,r.title,r.titleAttributes,o)};case"bodyAttributes":case"htmlAttributes":return{toComponent:()=>dy(r),toString:()=>cy(r)};default:return{toComponent:()=>Wa(e,r),toString:()=>$R(e,r,o)}}},HR=({metaTags:e,linkTags:r,scriptTags:o,encode:i})=>{const a=Mc(e,Lc.meta),l=Mc(r,Lc.link),c=Mc(o,Lc.script);return{priorityMethods:{toComponent:()=>[...Wa("meta",a.priority),...Wa("link",l.priority),...Wa("script",c.priority)],toString:()=>`${rn("meta",a.priority,i)} ${rn("link",l.priority,i)} ${rn("script",c.priority,i)}`},metaTags:a.default,linkTags:l.default,scriptTags:c.default}},BR=e=>{const{baseTag:r,bodyAttributes:o,encode:i=!0,htmlAttributes:a,noscriptTags:l,styleTags:c,title:d="",titleAttributes:h,prioritizeSeoTags:p}=e;let{linkTags:g,metaTags:v,scriptTags:x}=e,S={toComponent:()=>{},toString:()=>""};return p&&({priorityMethods:S,linkTags:g,metaTags:v,scriptTags:x}=HR(e)),{priority:S,base:rn("base",r,i),bodyAttributes:rn("bodyAttributes",o,i),htmlAttributes:rn("htmlAttributes",a,i),link:rn("link",g,i),meta:rn("meta",v,i),noscript:rn("noscript",l,i),script:rn("script",x,i),style:rn("style",c,i),title:rn("title",{title:d,titleAttributes:h},i)}},VR=BR,Fa=[],fy=!!(typeof window<"u"&&window.document&&window.document.createElement),WR=class{instances=[];canUseDOM=fy;context;value={setHelmet:e=>{this.context.helmet=e},helmetInstances:{get:()=>this.canUseDOM?Fa:this.instances,add:e=>{(this.canUseDOM?Fa:this.instances).push(e)},remove:e=>{const r=(this.canUseDOM?Fa:this.instances).indexOf(e);(this.canUseDOM?Fa:this.instances).splice(r,1)}}};constructor(e,r){this.context=e,this.canUseDOM=r||!1,r||(e.helmet=VR({baseTag:[],bodyAttributes:{},htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))}},KR={},QR=jt.createContext(KR),GR=class hy extends w.Component{static canUseDOM=fy;helmetData;constructor(r){super(r),this.helmetData=new WR(this.props.context||{},hy.canUseDOM)}render(){return jt.createElement(QR.Provider,{value:this.helmetData.value},this.props.children)}};const YR=new Tx({defaultOptions:{queries:{staleTime:300*1e3,gcTime:600*1e3,retry:(e,r)=>r?.response?.status>=400&&r?.response?.status<500?r?.response?.status===401&&e<1:e<3,refetchOnWindowFocus:!1},mutations:{retry:!1}}});function JR({children:e}){const{getCurrentUser:r,isInitializing:o,setInitialized:i}=dd();return w.useEffect(()=>{o&&(async()=>{try{localStorage.getItem("work-finder-auth")?await r():i()}catch(l){console.error("Auth initialization failed:",l),i()}})()},[r,o,i]),o?F.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:F.jsxs("div",{className:"text-center",children:[F.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),F.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):F.jsx(F.Fragment,{children:e})}function XR(){return F.jsx("div",{className:"flex h-screen w-screen items-center justify-center",children:F.jsxs("div",{className:"text-center",children:[F.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Something went wrong"}),F.jsx("p",{className:"text-gray-600 mb-4",children:"We're sorry, but something went wrong. Please try again later."}),F.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Reload page"})]})})}function qR(){return F.jsx("div",{className:"flex h-screen w-screen items-center justify-center",children:F.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})})}function ZR({children:e}){return F.jsx(w.Suspense,{fallback:F.jsx(qR,{}),children:F.jsx(DR,{FallbackComponent:XR,children:F.jsx(N0,{i18n:yt,children:F.jsx(GR,{children:F.jsx(Nx,{client:YR,children:F.jsxs(JR,{children:[e,!1]})})})})})})}i0.createRoot(document.getElementById("root")).render(F.jsx(w.StrictMode,{children:F.jsx(ZR,{children:F.jsx(_R,{})})}));export{gb as $,$g as A,s1 as B,qk as C,Or as D,mv as E,hl as F,Yn as G,f1 as H,_0 as I,xb as J,EC as K,cd as L,Mv as M,ts as N,sE as O,Ot as P,Wg as Q,jt as R,y1 as S,Bg as T,Zk as U,eb as V,Zc as W,iE as X,c1 as Y,vv as Z,mb as _,dl as a,sO as a0,x1 as a1,P1 as a2,In as b,iO as c,_r as d,Ko as e,fm as f,rO as g,tO as h,nO as i,F as j,eO as k,us as l,ds as m,jg as n,$t as o,Xe as p,je as q,w as r,Vk as s,oO as t,dd as u,l1 as v,sv as w,Ug as x,Xk as y,Wc as z};
