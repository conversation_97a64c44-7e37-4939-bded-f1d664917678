import{a as h,j as e,d as c,g}from"./index-BOPfWTHD.js";import{C as x,a as m}from"./mock-data-CbJZR1PP.js";import{B as i}from"./badge-Bzyz8_QN.js";import{A as o,S as j,T as p}from"./trending-up-B7H7FfFe.js";import{U as u}from"./users-DpHMEXct.js";import{M as N}from"./map-pin-BR21nmoc.js";const v=({company:s,onViewCompany:l})=>{const r=(t,a)=>({startup:"1-50",small:"51-200",medium:"201-500",large:"501-1000",enterprise:"1000+"})[t]||`${a}+`,d=t=>{const a=t.locations.find(n=>n.isHeadquarters);return a?`${a.city}, ${a.state}`:"Multiple Locations"};return e.jsxs(x,{className:"group hover:shadow-xl transition-all duration-300 border-0 shadow-md hover:-translate-y-1 bg-white overflow-hidden",children:[s.coverImage&&e.jsxs("div",{className:"h-32 bg-gradient-to-r from-blue-500 to-indigo-600 relative overflow-hidden",children:[e.jsx("img",{src:s.coverImage,alt:s.name,className:"w-full h-full object-cover opacity-80"}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"})]}),e.jsxs(m,{className:"p-6 -mt-6 relative z-10",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[s.logo&&e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:s.logo,alt:s.name,className:"w-16 h-16 rounded-xl object-cover border-4 border-white shadow-md bg-white"}),s.isVerified&&e.jsx("div",{className:"absolute -top-1 -right-1 bg-blue-600 text-white rounded-full p-1",children:e.jsx("div",{className:"w-3 h-3 flex items-center justify-center",children:e.jsx("span",{className:"text-xs",children:"✓"})})})]}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"font-bold text-lg text-gray-900 group-hover:text-blue-600 transition-colors line-clamp-1",children:s.name}),e.jsx("p",{className:"text-gray-600 text-sm mb-2",children:s.industry}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-500",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(j,{className:"h-4 w-4 text-yellow-400 mr-1"}),e.jsx("span",{children:s.stats.averageRating.toFixed(1)})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(u,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:r(s.size,s.stats.totalEmployees)})]})]})]})]})}),e.jsx("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:s.description}),e.jsxs("div",{className:"space-y-2 mb-4",children:[e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(N,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsx("span",{children:d(s)})]}),e.jsxs("div",{className:"flex items-center text-gray-600 text-sm",children:[e.jsx(g,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s.stats.totalJobs," open positions"]})]}),s.stats.hiringGrowth>0&&e.jsxs("div",{className:"flex items-center text-green-600 text-sm",children:[e.jsx(p,{className:"h-4 w-4 mr-2 flex-shrink-0"}),e.jsxs("span",{children:[s.stats.hiringGrowth,"% hiring growth"]})]})]}),s.specialties&&s.specialties.length>0&&e.jsx("div",{className:"mb-4",children:e.jsxs("div",{className:"flex flex-wrap gap-1",children:[s.specialties.slice(0,3).map((t,a)=>e.jsx(i,{variant:"outline",className:"text-xs",children:t},a)),s.specialties.length>3&&e.jsxs(i,{variant:"outline",className:"text-xs",children:["+",s.specialties.length-3," more"]})]})}),e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex gap-2 flex-wrap",children:[e.jsx(i,{variant:"secondary",className:"text-xs capitalize",children:s.size}),e.jsx(i,{variant:"secondary",className:"text-xs capitalize",children:s.type}),s.isSponsored&&e.jsx(i,{className:"text-xs bg-purple-100 text-purple-800 border-purple-200",children:"Sponsored"})]})}),e.jsxs(c,{onClick:()=>l(s.id),className:"w-full group-hover:bg-blue-600 transition-colors",variant:"outline",children:["View Company",e.jsx(o,{className:"ml-2 h-4 w-4"})]})]})]})},b=()=>e.jsxs(x,{className:"border-0 shadow-md bg-white overflow-hidden",children:[e.jsx("div",{className:"h-32 bg-gray-200 animate-pulse"}),e.jsxs(m,{className:"p-6 -mt-6 relative z-10",children:[e.jsx("div",{className:"flex items-start justify-between mb-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-200 rounded-xl animate-pulse"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"w-32 h-5 bg-gray-200 rounded animate-pulse mb-2"}),e.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse mb-2"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsx("div",{className:"w-16 h-4 bg-gray-200 rounded animate-pulse"}),e.jsx("div",{className:"w-16 h-4 bg-gray-200 rounded animate-pulse"})]})]})]})}),e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse mb-4"}),e.jsx("div",{className:"space-y-2 mb-4",children:Array.from({length:3}).map((s,l)=>e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-4 h-4 bg-gray-200 rounded animate-pulse mr-2"}),e.jsx("div",{className:"w-24 h-4 bg-gray-200 rounded animate-pulse"})]},l))}),e.jsx("div",{className:"flex gap-2 mb-4",children:Array.from({length:3}).map((s,l)=>e.jsx("div",{className:"w-16 h-6 bg-gray-200 rounded animate-pulse"},l))}),e.jsx("div",{className:"w-full h-10 bg-gray-200 rounded animate-pulse"})]})]}),A=({companies:s,isLoading:l=!1})=>{const r=h(),d=a=>{r(`/companies/${a}`)},t=()=>{r("/companies")};return e.jsx("section",{className:"py-16 px-4 bg-white",children:e.jsxs("div",{className:"container mx-auto max-w-7xl",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsxs("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-900 mb-4",children:["Top ",e.jsx("span",{className:"text-blue-600",children:"Companies"})]}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Explore amazing companies that are actively hiring and building the future"})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12",children:l?Array.from({length:6}).map((a,n)=>e.jsx(b,{},n)):s.map(a=>e.jsx(v,{company:a,onViewCompany:d},a.id))}),e.jsx("div",{className:"text-center",children:e.jsxs(c,{onClick:t,size:"lg",className:"px-8 py-3 text-base font-semibold",children:["View All Companies",e.jsx(o,{className:"ml-2 h-5 w-5"})]})})]})})};export{A as F};
