# shadcn/ui Best Practices and Patterns

## Core Principles

Always follow shadcn/ui patterns and best practices when building components. This ensures consistency, accessibility, and maintainability across the codebase.

## Form Components

### 1. Always use shadcn/ui Form components with React Hook Form + Zod

**Required Pattern:**
```tsx
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"

const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

function MyForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON><PERSON>ver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    console.log(values)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input placeholder="Enter your email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <Input type="password" placeholder="Enter your password" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  )
}
```

### 2. Input Components

**Always use shadcn/ui Input component:**
```tsx
import { Input } from "@/components/ui/input"

// Good
<Input type="email" placeholder="Enter email" />

// Bad - Don't use raw HTML inputs
<input type="email" placeholder="Enter email" />
```

### 3. Button Components

**Always use shadcn/ui Button component:**
```tsx
import { Button } from "@/components/ui/button"

// Good
<Button variant="default" size="default">Click me</Button>
<Button variant="outline">Secondary</Button>
<Button variant="destructive">Delete</Button>

// Bad - Don't use raw HTML buttons
<button className="bg-blue-500">Click me</button>
```

## Typography and Fonts

### Global Font Configuration

**Use Jost as the primary font family globally:**
```css
/* In your global CSS or Tailwind config */
body {
  font-family: 'Jost', sans-serif;
}
```

**Avoid inline font specifications:**
```tsx
// Bad
<div className="font-['Inter:Medium',_sans-serif]">Text</div>
<div className="font-['Jost:Medium',_sans-serif]">Text</div>

// Good - Use Tailwind font utilities
<div className="font-medium">Text</div>
<div className="font-semibold">Text</div>
<div className="font-bold">Text</div>
```

## Component Structure

### 1. Use proper component composition
```tsx
// Good - Composable components
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    Content here
  </CardContent>
</Card>

// Bad - Monolithic components
<div className="border rounded-lg p-4">
  <h3>Title</h3>
  <p>Description</p>
  <div>Content</div>
</div>
```

### 2. Consistent naming and file structure
```
components/
  ui/           # shadcn/ui components
    button.tsx
    input.tsx
    form.tsx
  custom/       # Custom components
    login-form.tsx
    header.tsx
```

## Accessibility

### Always include proper accessibility attributes:
```tsx
// Good
<FormLabel htmlFor="email">Email</FormLabel>
<Input id="email" aria-describedby="email-error" />
<FormMessage id="email-error" />

// Bad
<label>Email</label>
<input />
<span>Error message</span>
```

## Error Handling

### Use FormMessage for validation errors:
```tsx
// Good
<FormField
  name="email"
  render={({ field }) => (
    <FormItem>
      <FormLabel>Email</FormLabel>
      <FormControl>
        <Input {...field} />
      </FormControl>
      <FormMessage /> {/* Automatically shows validation errors */}
    </FormItem>
  )}
/>

// Bad
{errors.email && <span className="text-red-500">{errors.email.message}</span>}
```

## Required Components to Install

Ensure these shadcn/ui components are installed:
```bash
npx shadcn-ui@latest add form
npx shadcn-ui@latest add input
npx shadcn-ui@latest add button
npx shadcn-ui@latest add label
npx shadcn-ui@latest add card
```

## Enforcement Rules

1. **NEVER use raw HTML form elements** - Always use shadcn/ui components
2. **ALWAYS use Form component** with React Hook Form + Zod for forms
3. **NEVER use inline font specifications** - Use global Jost font and Tailwind utilities
4. **ALWAYS follow component composition patterns** from shadcn/ui documentation
5. **ALWAYS include proper accessibility attributes** using shadcn/ui patterns
6. **ALWAYS use FormMessage** for error display instead of custom error handling
