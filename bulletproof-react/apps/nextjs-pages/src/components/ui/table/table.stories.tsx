import { <PERSON>a, StoryObj } from '@storybook/react';

import { Table } from './table';

const meta: Meta<typeof Table> = {
  component: Table,
};

export default meta;

type User = {
  id: string;
  createdAt: number;
  name: string;
  title: string;
  role: string;
  email: string;
};

type Story = StoryObj<typeof Table<User>>;

const data: User[] = [
  {
    id: '1',
    createdAt: Date.now(),
    name: '<PERSON>',
    title: 'Regional Paradigm Technician',
    role: 'Admin',
    email: '<EMAIL>',
  },
  {
    id: '2',
    createdAt: Date.now(),
    name: '<PERSON>',
    title: 'Product Directives Officer',
    role: 'Owner',
    email: '<EMAIL>',
  },
];

export const Default: Story = {
  args: {
    data,
    columns: [
      {
        title: 'Name',
        field: 'name',
      },
      {
        title: 'Title',
        field: 'title',
      },
      {
        title: 'Role',
        field: 'role',
      },
      {
        title: 'Email',
        field: 'email',
      },
    ],
  },
};
