# 📚 Additional Resources

## React

- [Official Documentation](https://react.dev/)
- [Tao Of React](https://alexkondov.com/tao-of-react/)
- [React Handbook](https://reacthandbook.dev/)
- [React Philosophies](https://github.com/mithi/react-philosophies)
- [React Patterns](https://reactpatterns.com/)
- [React Typescript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

## JavaScript

- [You Dont Know JS](https://github.com/getify/You-Dont-Know-JS)
- [JavaScript Info](https://javascript.info/)
- [33 Concepts Every JavaScript Developer Should Know](https://github.com/leonardomso/33-js-concepts#8-iife-modules-and-namespaces)
- [JavaScript to Know for React](https://kentcdodds.com/blog/javascript-to-know-for-react)

## Best Practices

- [patterns.dev](https://www.patterns.dev/)
- [Naming Cheatsheet](https://github.com/kettanaito/naming-cheatsheet)
- [Clean Code Javascript](https://github.com/ryanmcdermott/clean-code-javascript)
