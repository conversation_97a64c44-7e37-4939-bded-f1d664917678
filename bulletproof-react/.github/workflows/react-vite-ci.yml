name: React Vite CI
on:
  push:
    branches: ["*"]
    paths-ignore:
      - "README.md"
      - "docs/**"
  pull_request:
    branches: [master]
    paths-ignore:
      - "README.md"
      - "docs/**"
jobs:
  all-cli-checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./apps/react-vite
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Set environment variables
        run: mv .env.example .env
      - name: Install dependencies
        run: yarn install
      - name: Build application
        run: yarn build
      - name: Run tests
        run: yarn test
      - name: Run linter
        run: yarn lint
      - name: Check types
        run: yarn check-types
  e2e:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./apps/react-vite
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Set environment variables
        run: mv .env.example-e2e .env
      - name: Install dependencies
        run: npm install -g yarn && yarn
      - name: Install Playwright Browsers
        run: yarn playwright install --with-deps
      - name: Run Playwright tests
        run: yarn test-e2e
      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: |
            playwright-report/
            mocked-db.json
          retention-days: 30
